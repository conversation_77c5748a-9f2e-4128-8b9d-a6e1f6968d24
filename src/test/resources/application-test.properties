server.port=8080

# datasource
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=***********************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=wST6dB7eeGYb4vMQ

spring.datasource.dynamic.datasource.aliyun.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.aliyun.url=*************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.aliyun.username=dtest
spring.datasource.dynamic.datasource.aliyun.password=N^lpUm$zIkH3ndmj


spring.datasource.dynamic.datasource.enterpriseinfo.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.enterpriseinfo.url=**************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.enterpriseinfo.username=cmp_admin
spring.datasource.dynamic.datasource.enterpriseinfo.password=$PT!h1@S*Ay7Sa$v


spring.datasource.dynamic.druid.initial-size=1
spring.datasource.dynamic.druid.min-idle=1
spring.datasource.dynamic.druid.max-active=5


# ????????
logging.level.com.haier.devops.bill.service.impl=trace
logging.level.com.haier.devops.bill.controller=trace

# jenkins
jenkins.config.adpci.url=http://adpci.haier.net
jenkins.config.adpci.username=admin
jenkins.config.adpci.password=cicd123

# REDIS (RedisProperties)
spring.redis.client-type=jedis
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=2
# Redis\u670D\u52A1\u5668\u5730\u5740
spring.redis.host=************
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.redis.password=Cicd@test7
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-active=4
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-wait=-1
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.max-idle=4
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.min-idle=0
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=0


#\u62E8\u6D4B\u5185\u7F51\u73AF\u5883url
dtest.one.url= https://dtest.haiersmarthomes.com/
#\u62E8\u6D4Baws\u73AF\u5883url
dtest.two.url= https://dtestind.haiersmarthomes.com/
#\u5F53\u524D\u73AF\u5883\u5916\u7F51
nowEnv = 001

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://devops-xxl-job.qd-hongdao.haier.net/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=default_token

### xxl-job executor appname
xxl.job.executor.appname=bill-center
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=
xxl.job.executor.port=9999
### xxl-job executor log-path
xxl.job.executor.logpath=/Volumes/dev/work/logs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30

# hwork \u8D26\u5355
api.hds.bill.url=https://gw-qd-aliyun-stage.haier.net
api.hds.bill.token=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
