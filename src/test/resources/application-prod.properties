server.port=8080

# datasource
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=KRa1F9YE1CVMvJM3


spring.datasource.dynamic.datasource.enterpriseinfo.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.enterpriseinfo.url=******************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.enterpriseinfo.username=root
spring.datasource.dynamic.datasource.enterpriseinfo.password=KRa1F9YE1CVMvJM3


spring.datasource.dynamic.datasource.clouddb.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.clouddb.url=******************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.clouddb.username=root
spring.datasource.dynamic.datasource.clouddb.password=KRa1F9YE1CVMvJM3

spring.datasource.dynamic.datasource.pg.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.pg.url=*****************************************************************************************************
spring.datasource.dynamic.datasource.pg.username=root
spring.datasource.dynamic.datasource.pg.password=cicd123

spring.datasource.dynamic.druid.initial-size=10
spring.datasource.dynamic.druid.min-idle=10
spring.datasource.dynamic.druid.max-active=200
spring.datasource.dynamic.druid.max-wait=300000
spring.datasource.dynamic.druid.test-while-idle=true
spring.datasource.dynamic.druid.validation-query=SELECT 1

spring.datasource.dynamic.druid.connect-timeout=120000
spring.datasource.dynamic.druid.socket-timeout=30000
spring.datasource.dynamic.druid.query-timeout=60000

spring.datasource.dynamic.druid.remove-abandoned=true
spring.datasource.dynamic.druid.remove-abandoned-timeout=300
spring.datasource.dynamic.druid.log-abandoned=true


logging.level.com.haier.devops=info
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.nologging.NoLoggingImpl
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl


# REDIS (RedisProperties)
spring.redis.client-type=jedis
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=0
# Redis\u670D\u52A1\u5668\u5730\u5740
#spring.redis.host=redis-0e52ecb3-3449-4446-89dc-796b9b039b4e.cn-east-5.dcs.myhuaweicloud.com
spring.redis.host=*************
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
#spring.redis.password=Fstx5HHSgWZ46Fdu
spring.redis.password=ommp&123
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-active=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-wait=3600000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.min-idle=0
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=0

# oss
aliyun.oss.endpoint=http://oss-cn-qingdao.aliyuncs.com
aliyun.oss.access-key-id=LTAI5tHfJNtebdRM4TNTMFz3
aliyun.oss.access-key-secret=******************************
aliyun.oss.bucket-name=haier-hdshcms-bill-center-prd

# imm
aliyun.imm.endpoint=imm.cn-qingdao.aliyuncs.com
aliyun.imm.access-key-id=LTAI5tHfJNtebdRM4TNTMFz3
aliyun.imm.access-key-secret=******************************

# \u6279\u91CF\u4FDD\u5B58
persist.batch.size=2500
# \u6279\u91CF\u67E5\u8BE2
query.batch.size=1000

xxl.job=disabled
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://xxl-job-admin.haier.net/xxl-job-admin
### xxl-job, access token
xxl.job.accessToken=default_token
### xxl-job executor appname
xxl.job.executor.appname=bill-center
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=
xxl.job.executor.port=9999
### xxl-job executor log-path
xxl.job.executor.logpath=/Volumes/dev/work/logs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30
### \u6267\u884C\u5668\u7EC4\u4EF6\u662F\u5426\u5F00\u542F
xxl.job.enable=true


# hwork\u6743\u9650\u4E2D\u5FC3\u57DF\u540D
api.hwork.authority.domain=https://hwork-internal.haier.net
api.hwork.authority.app-id=hybrid_cloud_management_prod
api.hwork.authority.secret=bfa43d64bb624a2fbf5d0adc9f3f9c31

# hwork im
api.howrk.domain=https://hwork.haier.net
api.howrk.app-id=wjXlDuC96C0ePtpG
api.howrk.secret=I9RuVxMIUCG48j6CKQb0FlcH

#hds\u53D1\u6D88\u606F\u6A21\u7248
hds.notice.template_sms=10001823
hds.notice.template_email=10001821
hds.notice.template_hwork=10001835
hds.notice.template_ihaier2=10001817
hds.notice.template_ihaier2_2=10002010
#hds\u53D1\u6D88\u606Fsendor
hds.notice.sendor_sms=HCMS
hds.notice.sendor_email=HCMS<<EMAIL>>
hds.notice.sendor_hwork=hcms
hds.notice.sendor_ihaier2=HCMS


domain.code=szyhjsz


# hwork \u8D26\u5355
api.hds.bill.url=https://gw-qd-aliyun.haier.net
api.hds.bill.token=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
