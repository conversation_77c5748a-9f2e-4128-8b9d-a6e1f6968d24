server.port=9800

# datasource
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=*******************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=hcms_test
spring.datasource.dynamic.datasource.master.password=MI/tNnGjaI/fDA==


spring.datasource.dynamic.datasource.enterpriseinfo.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.enterpriseinfo.url=********************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.enterpriseinfo.username=hcms_test
spring.datasource.dynamic.datasource.enterpriseinfo.password=MI/tNnGjaI/fDA==


spring.datasource.dynamic.datasource.pg.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.dynamic.datasource.pg.url=*****************************************************************************************************
spring.datasource.dynamic.datasource.pg.username=root
spring.datasource.dynamic.datasource.pg.password=wST6dB7eeGYb4vMQ


spring.datasource.dynamic.druid.initial-size=1
spring.datasource.dynamic.druid.min-idle=1
spring.datasource.dynamic.druid.max-active=15
spring.datasource.dynamic.druid.connect-timeout=300000
spring.datasource.dynamic.druid.socket-timeout=600000
spring.datasource.dynamic.druid.validation-query-timeout=20000
spring.datasource.dynamic.druid.keep-alive=true


logging.level.com.haier.devops.bill.service.impl=trace
logging.level.com.haier.devops.bill.controller=trace


# REDIS (RedisProperties)
spring.redis.client-type=jedis
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=6
# Redis\u670D\u52A1\u5668\u5730\u5740
spring.redis.host=*************
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.redis.password=ommp&123
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-active=4
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-wait=3600000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.max-idle=10
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.min-idle=0
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=0


# oss
aliyun.oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
aliyun.oss.access-key-id=LTAI5tHfJNtebdRM4TNTMFz3
aliyun.oss.access-key-secret=******************************
aliyun.oss.bucket-name=haier-hdshcms-bill-center

# hwork\u6743\u9650\u4E2D\u5FC3\u57DF\u540D
api.hwork.authority.domain=https://pre-hwork.haier.net
api.hwork.authority.app-id=hybrid_cloud_management_pre
api.hwork.authority.secret=eeff6ab7d88340c1831cc76c0e3472cd

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://localhost:9080/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=default_token

### xxl-job executor appname
xxl.job.executor.appname=bill-center
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=
xxl.job.executor.port=9999
### xxl-job executor log-path
xxl.job.executor.logpath=/Volumes/dev/work/logs/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30

# hwork \u8D26\u5355
api.hds.bill.url=https://gw-qd-aliyun-stage.haier.net
api.hds.bill.token=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
