###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/costunit-sum?startCycle=2023-03&endCycle=2023-04

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/product-pie?startCycle=2023-04&endCycle=2023-04

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/costunit-sum?startCycle=2023-03&endCycle=2023-04

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/costunit-product?startCycle=2023-03&endCycle=2023-04&page=2&per_page=10

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/scode-summary?startCycle=2023-03&endCycle=2023-04&page=1&per_page=10

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/scode-summary?startCycle=2023-05&endCycle=2023-05&page=2&per_page=10

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/product-summary?startCycle=2023-03&endCycle=2023-04&page=2&per_page=10
Content-Type: application/json;charset=utf-8

{
  "scode": "S00015",
  "startCycle": "2023-03",
  "endCycle": "2023-04",
  "page": 2,
  "per_page": 5
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/scode-detail
Content-Type: application/json;charset=utf-8

{
  "params": [],
  "scode": "S00015",
  "startCycle": "2023-03",
  "endCycle": "2023-04",
  "page": 80,
  "per_page": 10
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/scode-detail/download
Content-Type: application/json;charset=utf-8

{
  "params": [{
    "column": "accountName",
    "operator": "=",
    "values": ["hr690j"]
  },{
    "column": "id",
    "operator": "=",
    "values": [3838342]
  }],
  "scode": "S00015",
  "startCycle": "2023-03",
  "endCycle": "2023-04",
  "page": 1,
  "per_page": 20
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/column-values
Content-Type: application/json;charset=utf-8

{
  "params": [],
  "scode": "S00015",
  "startCycle": "2023-03",
  "endCycle": "2023-04",
  "targetColumn": "billingCycle"
}

###
GET {{host}}:{{port}}/api/v1/hcms/bill/nodes/latest-month

###
GET {{host}}:{{port}}/api/v1/hcms/bill/nodes/cloud-fee-trend

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/product-pie?startCycle=2023-04&endCycle=2023-05&vendor=aliyun
###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/product-pie?startCycle=2023-04&endCycle=2023-05
###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/vendor-pie?startCycle=2023-04&endCycle=2023-05

###
GET {{host}}:{{port}}/api/v1/hcms/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05
###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05

###
https://hcms.qd-aliyun-test-internal.haier.net/hcms/api/proxy/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05
Content-Type: application/json;charset=utf-8

{
  "params": [],
  "startCycle": "2023-03",
  "endCycle": "2023-04",
  "page": 1,
  "per_page": 20
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/download/general
Content-Type: application/json;charset=utf-8

{
  "scodes": ["S03055"],
  "vendor": "aliyun",
  "startCycle": "2023-07",
  "endCycle": "2023-09",
  "submitter": "graysonphoenix"
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/download/detail
#POST http://hcms-bill.qd-aliyun-test-internal.haier.net/api/v1/hcms/bill/download/detail
#POST http://localhost:8080/api/v1/hcms/bill/download/detail
Content-Type: application/json;charset=utf-8

{
  "params": [],
  "submitter": "graysonphenix",
  "scode": "S03055",
  "startCycle": "2023-09",
  "endCycle": "2023-09"
}

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/download/history?submitter=A0059681&page=1&per_page=10

###
GET http://hcms.qd-aliyun-test-internal.haier.net/hcms/api/proxy/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05
###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05



###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aliyun/trigger-bill-serialization
Content-Type: application/x-www-form-urlencoded

billingCycle=2023-09

###
GET http://localhost:8080/swagger-resources
###
GET http://localhost:8080/v3/api-docs

###
GET http://localhost:8080/api/v1/hcms/bill/bills?page=1&per_page=10&billingCycle=2023-01&vendor=aliyun&productCode=rds&costUnit=海尔商城-S00419

###
GET https://hcms.qd-aliyun-test-internal.haier.net/api/v1/hcms/bill/nodes/scode-summary?page=1&per_page=20&startCycle=2023-03&endCycle=2023-05
X-API-KEY: 9ebaa700-22c5-4eaa-a7d4-00f53313b035


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aliyun/trigger-bill-substitution
Content-Type: application/x-www-form-urlencoded

taskId=4d875ddf-7ec5-439a-8322-15fe4a707d1d
#taskId=21a3dda2-fe94-428b-9a3b-fa8957e14ced

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/recent-cloud-fee?startCycle=2023-03&endCycle=2023-05
X-User: ********


###
GET https://gw-qd-aliyun.haier.net/console/cmdb-new/api/v2/projects
Authorization: 06QOwwXEumtFLfQnbaYj4TO7dypQcQcP

###
GET https://gw-qd-aliyun.haier.net/console/cmdb-new/api/v2/projects-by-alm/S01594
Authorization: 06QOwwXEumtFLfQnbaYj4TO7dypQcQcP
Content-Type:  "application/json"

###

GET https://gw-qd-aliyun.haier.net/console/cmdbx/api/v4/project/getProjectDetail?almSCode=S02166
Authorization: 06QOwwXEumtFLfQnbaYj4TO7dypQcQcP


###
# POST http://{{host}}:{{port}}/api/hcms/v1/contract/generate
POST https://hcms.qd-aliyun-dmz-ack-internal.haier.net/api/hcms/v1/contract/generate
Content-Type: application/x-www-form-urlencoded
X-User: ********

budgetId=158

###

POST http://localhost:8080/api/v1/hcms/bill/contract/generate
Content-Type: application/x-www-form-urlencoded
X-User: ********

budgetId=148

###
POST http://{{host}}:{{port}}/api/hcms/v1/contract/reset
Content-Type: application/x-www-form-urlencoded
X-User: ********

budgetId=121


###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/cmdb_product_overview/listByPage
X-User: ********


###
POST http://localhost:8000/api/hcms/v1/contract/31/stage/47/budget/153/report
Content-Type: application/json;charset=utf-8
X-API-KEY: f3083bb3-c3ae-4af6-b1f9-dd5f76d4407e
X-Request-User: ********




###
GET http://hcms-api.haier.net/api/v1/hcms/resource/cmdb/rds?page=1&per_page=500&host=************
X-API-KEY: 9ebaa700-22c5-4eaa-a7d4-00f53313b035



###
POST http://localhost:8080/api/v1/hcms/bill/aggregated/getConsumptionAnalysis
Content-Type: application/json;charset=utf-8
X-User: ********

{"scodes":"S02166"}

###
GET http://localhost:8080/api/v1/hcms/bill/aggregated/getCloudAccountList?vendor=aliyun
X-API-KEY: 9ebaa700-22c5-4eaa-a7d4-00f53313b035


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aggregated/getConsumptionAnalysis/usd
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "scodes": "S04075,S04074,S04072,S04056,S04055,S04053,S04052,S04048,S04045,S04044,S04042,S04040,S04035,S04034,S04031,S04030,S04029,S04028,S04027,S04022,S04018,S04017,S04016,S04015,S04014,S04007,S04006,S04003,S04001,S04000,S03999,S03998,S03997,S03996,S03995,S03994,S03993,S03992,S03980,S03978,S03958,S03896,S03920,S03909,S03904,S03880,S03910,S03907,S03881,S03905,S03924,S03889,S03895,S03885,S03898,S03911,S03897,S03918,S03892,S03903,S03884,S03921,S03923,S03902,S03894,S03899,S03900,S03890,S03908,S03925,S03916,S03891,S03906,S03882,S03927,S03862,S03860,S03859,S03857,S03850,S03849,S03820,S000000,S03666,S03657,S03655,S03654,S00825,S01107,S01137,S00509,S01228,S01254,S02069,S00277,S01570,S01660,S01571,S03129,S01652,S03653,S01651,S00103,S01665,S00849,S01690,S01686,S00022,S00977,S01763,S00989,S01781,S01266,S01861,S01862,S01359,S01864,S01389,S01866,S01420,S01873,S00101,S00430,S00778,S01936,S01506,S01994,S01496,S01521,S02039,S01551,S02059,S01556,S01568,S02076,S02079,S02078,S00076,S02092,S02108,S00486,S02193,S02228,S01849,S02230,S02214,S02231,S02244,S02242,S02269,S02277,S02274,S01000,S03018,S03044,S03078,S03092,S03102,S00278,S03111,S00785,S03134,S03173,S03160,S03194,S03296,S03299,S03316,S03345,S03431,S02125,S02140,S03435,S02171,S03501,S02187,S03554,S03648,S03645,S03642,S03616,S01940,S00349,S01952,S01049,S01341,S00990,S01965,S01370,S01852,S00875,S01316,S03098,S00695,S03605,S03612,S03610,S03611,S03613,S03604,S03608,S03607,S03609,S03606,S03603,S03601,S03597,S02072,S03592,S03590,S03575,S02058,S03202,S02021,S03443,S03584,S01444,S01442,S01372,S03307,S03583,S03223,S02160,S02012,S00419,S03587,S03582,S03204,S02297,S01008,S03579,S01259,S02289,S03578,S03588,S03308,S03581,S03576,S03306,S03060,S00713,S03580,S02287,S03577,S01612,S03586,S03585,S03574,S01877,S03571,S03570,S03569,S03568,S03566,S03565,S03564,S03563,S03562,S03561,S03560,S03558,S03557,S02276,S03551,S03547,S03517,S03542,S03541,S03540,S03539,S03538,S03536,S03534,S03546,S03545,S03529,S03526,S01492,S03525,S03527,S03524,S03521,S03519,S03518,S03142,S03514,S03516,S03502,S03466,S03614,S00417,S01440,S02043,S00064,S01415,S03506,S01680,S03330,S02002,S02181,S03507,S01970,S01466,S02204,S03398,S03498,S01538,S01342,S03007,S03109,S01580,S01980,S00752,S03492,S03494,S03493,S03491,S03488,S03478,S03027,S00263,S01671,S01715,S01716,S03479,S01139,S01237,S01435,S01434,S00434,S03426,S01540,S01322,S01448,S01034,S03029,S01885,S00928,S01767,S02170,S02266,S02240,S03245,S02185,S03425,S03505,S01364,S03509,S01148,S03365,S03386,S00960,S01704,S01800,S03071,S01282,S02028,S00967,S01368,S01229,S02008,S00344,S03037,S01938,S03212,S03180,S01646,S03421,S03652,S03485,S03651,S03444,S00319,S02137,S03486,S03522,S02278,S02097,S01373,S01809,S02166,S03496,S03482,S03438,S03617,S03312,S01668,S01607,S03621,S03622,S03620,S02015,S00723,S00727,S03200,S01325,S00733,S01955,S03480,S01768,S03618,S03025,S01122,S02282,S01460,S00314,S01702,S01146,S03468,S00988,S01856,S02120,S03084,S03275,S01948,S01797,S01865,S00927,S03041,S00792,S00827,S01409,S03270,S01429,S01515,S01477,S03619,S01691,S03198,S03261,S01110,S03023,S02211,S03453,S03465,S01331,S03395,S03463,S02165,S03489,S02270,S02163,S03462,S03147,S03467,S03451,S02239,S01606,S01740,S03141,S03236,S01240,S02293,S02294,S01136,S01355,S02149,S03499,S03061,S03055,S03035,S00396,S00394,S02126,S00759,S01854,S00887,S03512,S01847,S02030,S02234,S02094,S03054,S01697,S01645,S02018,S01923,S00073,S02048,S01759,S01471,S01542,S02298,S02169,S02155,S01319,S01958,S02284,S02183,S00979,S03510,S03511,S00889,S00763,S01053,S01333,S01721,S01403,S00031,S00055,S01126,S01971,S01693,S00863,S03159,S03484,S01672,S00019,S01387,S03234,S00002,S00015,S00902,S01407,S03495,S01156,S01961,S00089,S03490,S00062,S01718,S02005,S02179,S02153,S02227,S01594,S03149,S00814,S02102,S01251,S03474,S01803,S03447,S03452,S03450,S03259,S03481,S03362,S02118,S02132,S03167,S01949,S03446,S03448,S01824,S01898,S01352,S01101,S03192,S03432,S03150,S04143,S04142,S04138,S04137,S04139,S04129,S04124,S04123,S04122,S04118,S04114,S04113,S04112,S04111,S04108,S04109,S04110,S04103,S04102,S04093,S04076,S04090,S04184,S04174,S04168,S04158,S04159,S04155,S04154"
}

###
GET localhost:8080/api/v1/hcms/bill/aggregated/getDayBillAnalysis
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "scodes": "S04075,S04074,S04072,S04056,S04055,S04053,S04052,S04048,S04045,S04044,S04042,S04040,S04035,S04034,S04031,S04030,S04029,S04028,S04027,S04022,S04018,S04017,S04016,S04015,S04014,S04007,S04006,S04003,S04001,S04000,S03999,S03998,S03997,S03996,S03995,S03994,S03993,S03992,S03980,S03978,S03958,S03896,S03920,S03909,S03904,S03880,S03910,S03907,S03881,S03905,S03924,S03889,S03895,S03885,S03898,S03911,S03897,S03918,S03892,S03903,S03884,S03921,S03923,S03902,S03894,S03899,S03900,S03890,S03908,S03925,S03916,S03891,S03906,S03882,S03927,S03862,S03860,S03859,S03857,S03850,S03849,S03820,S000000,S03666,S03657,S03655,S03654,S00825,S01107,S01137,S00509,S01228,S01254,S02069,S00277,S01570,S01660,S01571,S03129,S0165",
  "month": "2024-06"
}


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/contract/generate
X-User: ********
Content-Type: application/x-www-form-urlencoded

budgetId=152

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/contract/reset
X-User: ********
Content-Type: application/x-www-form-urlencoded

budgetId=156


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/contract/sign
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "budgetId": 156,
  "users": [["01427821"], ["********"], ["22068521"], ["20115778", "01431814"]]
}



###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/detail/listByPage
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "scode": "S03562,S04028,S01740,S03167,S01885,S04015,S03485,S01409,S01237,S01373,S03519,S01763,S03911,S03521,S01551,S04158,S03474,S02181,S02048,S03994,S03529,S03488,S03446,S04076,S03902,S02097,S02008,S03480,S03601,S01136,S03495,S00902,S03192,S01955,S01389,S04030,S03516,S00073,S01803,S04040,S02015,S01809,S03316,S01540,S02169,S00967,S01718,S04113,S03007,S01492,S04042,S01646,S00314,S01440,S03425,S01322,S00827,S04112,S03958,S00089,S03478,S02132,S01768,S01342,S01580,S03270,S01415,S02240,S01606,S03558,S01435,S01854,S04016,S02108,S04048,S02270,S03909,S00814,S03035,S03563,S03540,S02094,S04138,S03890,S01034,S02234,S01704,S03236,S03438,S03592,S03061,S01994,S02282,S03448,S03421,S03141,S01980,S03506,S01693,S03857,S03234,S03467,S01594,S01429,S01146,S03481,S01352,S01542,S03545,S03025,S02277,S03029,S03666,S04143,S03546,S03299,S02069,S04044,S03525,S01971,S00263,S03538,S03462,S03648,S01938,S03275,S03616,S03614,S02185,S03522,S01847,S03491,S01139,S00889,S04139,S00960,S02030,S04017,S03261,S03541,S03514,S02018,S03259,S03044,S02153,S00015,S03330,S01948,S04124,S03023,S02072,S02120,S03444,S04159,S03027,S04109,S01716,S02002,S03564,S00055,S03432,S03569,S03054,S00863,S04108,S03526,S03084,S01568,S02278,S03362,S02284,S03134,S01691,S01862,S01970,S00394,S04055,S02211,S00396,S03655,S00733,S03505,S01110,S02276,S03484,S04018,S02166,S01368,S03603,S03850,S01471,S04003,S03517,S03435,S03486,S02028,S04184,S01856,S01333,S03492,S00759,S03466,S03245,S03507,S00979,S01122,S03920,S00319,S00723,S02179,S03426,S03510,S03512,S00509,S04129,S00434,S01824,S01702,S01496,S03451,S01331,S03536,S01865,S01697,S03566,S03820,S03511,S01923,S03862,S01466,S04027,S03992,S02102,S04031,S00792,S03465,S03560,S04093,S03198,S02118,S03561,S02005,S03575,S03018,S03891,S02155,S03173,S01949,S01898,S03111,S03129,S02165,S01403,S03194,S03453,S02239,S02137,S01800,S01958,S01645,S01282,S01325,S02293,S00031,S01319,S02227,S04114,S00928,S01797,S02266,S02294,S04142,S04102,S03386,S00019,S04022,S00927,S04122,S03496,S00887,S01672,S03479,S04103,S03568,S01680,S03365,S03452,S01387,S04155,S04056,S03502,S04014,S00763,S01053,S01570,S01407,S03557,S01448,S03978,S03398,S03499,S01477,S01538,S01460,S04035,S01861,S03071,S03200,S03150,S03395,S03617,S03037,S03565,S01434,S01515,S03450,S02170,S00002,S03149,S03896,S01229,S00344,S03180,S00988,S03489,S02204,S02183,S03463,S01156,S01660,S02163,S03527,S03109,S00752,S00062,S04029,S03498,S04123,S04168,S04045,S03509,S01126,S01101,S03147,S03447,S03570,S03494,S000000,S01759,S01251,S02126,S01767,S03142,S01715,S02298,S03055,S03490,S01961,S00064,S01240,S03571,S03468,S01721,S02059,S03482,S02149,S02274,S03493,S01686,S03980,S03212,S03539,S04034,S01671,S03542,S02043,S04110,S03657,S03534,S03518,S03041,S01355",
  "productCode": "",
  "type": 1,
  "startCycle": "2024-07-01",
  "endCycle": "2024-07-31",
  "sortNo": null,
  "subscriptionType": "PayAsYouGo",
  "page": 1,
  "per_page": 15
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/getConsumptionStepByStep
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "startDate": "2024-10-01",
  "endDate": "2024-10-31",
  "type": 1,
  "scode": "S04056,S04055,S04045,S04044",
  "filter": "product"
}
###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/getConsumptionStepByStep
X-User: ********
Content-Type: application/json;charset=utf-8


{"startDate":"2024-11-01","endDate":"2024-11-30","type":1,"scode":"S04573,S04567,S04568,S04565,S04566,S04564,S04563,S04561,S04560,S04562,S04533,S04540,S04557,S04548,S04550,S04551,S04547,S04545,S04553,S04549,S04531,S04544,S04520,S04518,S04519,S04529,S04528,S04527,S04526,S04525,S04522,S04552,S04554,S04543,S04542,S04541,S04539,S04538,S04537,S04535,S04536,S04534,S04556,S04555,S04546,S04523,S04524,S04494,S04499,S04484,S04483,S04482,S04479,S04461,S04441,S04438,S04437,S04433,S04432,S04431,S04428,S04430,S04429,S04426,S04427,S04425,S04421,S04416,S04417,S04418,S04419,S04423,S04358,S04356,S04357,S04352,S04422,S04409,S04410,S04436,S04359,S04290,S04266,S04260,S04251,S04250,S04249,S04239,S01485,S01324,S00104,S04184,S04174,S04168,S04158,S04159,S04155,S04143,S04142,S04138,S04137,S04139,S04129,S04154,S04124,S04123,S04122,S04118,S04114,S04113,S04112,S04111,S04108,S04109,S04110,S04103,S04102,S04093,S04076,S04075,S04074,S04072,S04090,S04056,S04055,S04053,S04052,S04048,S04045,S04044,S04042,S04040,S04035,S04034,S04031,S04030,S04029,S04028,S04027,S04022,S04018,S04017,S04016,S04015,S04014,S04007,S04006,S04003,S04001,S04000,S03999,S03998,S03997,S03996,S03995,S03994,S03993,S03992,S03980,S03978,S03958,S03896,S03920,S03909,S03904,S03880,S03910,S03907,S03881,S03905,S03924,S03889,S03895,S03885,S03898,S03911,S03897,S03918,S03892,S03903,S03884,S03921,S03923,S03902,S03894,S03899,S03900,S03890,S03908,S03925,S03914,S03916,S03891,S03906,S03927,S03862,S03860,S03859,S03857,S03850,S03849,S03833,S03820,S000000,S03666,S03657,S03655,S03654,S00825,S01107,S01137,S00509,S01228,S01254,S02069,S00277,S01570,S01571,S03129,S01652,S03653,S01651,S00103,S01665,S00849,S01690,S01686,S00022,S00977,S01763,S00989,S01781,S01266,S01321,S01861,S01862,S01359,S01864,S01389,S01866,S01420,S01873,S00101,S00430,S00778,S01936,S01506,S01994,S01496,S01521,S02039,S01551,S02059,S01556,S01568,S02076,S02079,S02078,S00076,S02092,S02108,S00486,S02193,S02228,S01849,S02230,S02214,S02231,S02244,S02242,S02269,S02277,S02274,S01000,S03018,S03044,S03126,S03078,S03092,S03102,S00278,S03111,S00785,S03134,S03173,S03160,S03176,S03194,S03296,S03299,S03316,S03345,S03431,S02125,S02140,S03435,S02171,S03501,S02187,S03554,S03648,S03645,S03642,S03616,S01940,S00349,S01952,S01049,S01341,S00990,S01965,S01370,S01852,S00875,S01316,S03098,S00695,S03605,S03612,S03610,S03611,S03613,S03604,S03608,S03607,S03609,S03606,S03603,S03601,S03597,S02072,S03592,S03590,S03575,S02058,S03202,S02021,S03443,S03584,S01444,S01442,S01372,S03307,S03583,S03223,S02160,S02012,S00419,S03587,S03582,S03204,S02297,S01008,S03579,S01259,S02289,S03578,S03588,S03308,S03581,S03576,S03306,S03060,S00713,S03580,S02287,S03577,S01612,S03586,S03585,S03574,S01877,S03571,S03570,S03569,S03568,S03566,S03565,S03564,S03563,S03562,S03561,S03560,S03558,S03557,S02276,S03551,S03547,S03517,S03542,S03541,S03540,S03539,S03538,S03536,S03534,S03546,S03545,S03529,S03526,S01492,S03525,S03527,S03524,S03521,S03519,S03518,S03142,S03514,S03516,S03502,S03466,S03614,S00417,S01440,S02043,S00064,S01415,S03506,S01680,S03330,S02002,S02181,S01970,S01466,S02204,S03398,S03498,S01538,S01342,S03007,S03109,S01580,S01980,S00752,S03492,S03494,S03493,S03491,S03488,S03478,S03027,S00263,S01671,S01715,S01716,S03479,S01139,S01237,S01435,S01434,S00434,S03426,S01540,S01322,S03513,S01448,S01034,S03029,S01885,S00928,S01767,S02170,S02266,S02240,S03245,S03363,S03425,S03505,S01364,S03509,S01577,S01148,S00075,S01381,S01102,S01573,S01233,S02086,S00289,S01636,S02188,S02017,S03365,S03386,S00960,S01704,S01800,S03071,S01282,S02028,S00967,S01368,S01229,S02008,S00344,S03037,S03130,S01938,S03212,S03180,S01646,S03421,S03652,S03485,S03651,S03444,S02137,S03486,S03522,S02278,S02097,S01373,S01809,S02166,S03496,S03482,S03438,S03617,S03312,S01668,S01607,S03621,S03622,S03620,S02015,S00723,S00727,S03200,S01325,S00733,S01955,S03480,S03487,S01768,S03618,S03025,S01122,S02282,S01460,S00314,S01702,S01146,S03468,S00988,S01856,S02120,S03084,S03275,S01797,S01865,S00927,S03041,S00792,S00827,S01409,S03270,S01429,S01515,S01477,S03619,S01691,S03198,S03261,S01110,S01764,S03023,S02211,S03453,S03465,S03168,S01331,S03395,S03463,S02165,S03489,S02270,S02163,S03462,S03147,S02025,S03467,S03451,S02239,S01606,S01740,S03141,S03236,S01240,S02293,S02294,S01136,S01355,S02149,S03499,S03061,S03055,S03035,S00396,S00394,S00395,S02126,S00759,S01854,S00887,S03512,S01847,S02030,S02234,S02094,S03054,S01697,S01645,S02018,S01923,S00073,S02048,S01759,S01471,S01542,S02298,S02169,S02155,S01319,S01958,S02284,S00979,S03510,S03511,S00889,S00763,S01053,S01333,S02288,S03476,S01721,S01403,S00031,S00055,S01126,S01971,S01693,S00863,S03159,S03484,S01672,S00019,S01387,S03234,S00002,S00015,S00902,S01407,S03495,S01156,S01961,S00089,S03490,S00062,S01718,S02005,S02179,S02153,S02227,S01594,S03149,S00814,S02102,S01251,S03474,S01803,S03447,S03452,S03450,S03283,S03259,S03481,S03362,S02118,S03167,S01949,S03446,S03448,S01824,S01898,S01352,S01101,S03192,S03432,S03150,S01696","filter":"product"}


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/getConsumerTrends/usd
X-User: ********
Content-Type: application/json;charset=utf-8


{
  "startDate": "2025-04-01",
  "endDate": "2025-09-30",
  "type": 2,
  "scode": "S03498,S03488,S01646,S02166,S03147,S02025,S03467,S04076"
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/getUserSCodes
X-User: ********
Content-Type: application/json;charset=utf-8

{"departIds":"50095679"}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/getMainAssets
X-User: ********
Content-Type: application/json;charset=utf-8

{"scode":"S03467"}


###
GET http://hcms.qd-huawei-test-internal.haier.net/api/v1/hcms/resource/cmdb/rds?page=1&per_page=500&host=*************
X-API-KEY: 9ebaa700-22c5-4eaa-a7d4-00f53313b035

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/getConsumptionStepByStep
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "startDate": "2025-05-01",
  "endDate": "2025-05-31",
  "type": 1,
  "scode": "S04137,S04362,S04363,S04364,S04425,S03159,S02055,S04461,S04613,S04630,S00727,S03614,S01877,S03574,S03585,S03586,S01612,S03577,S02287,S03580,S00713,S03060,S03306,S03576,S03308,S03588,S03578,S02289,S01259,S03579,S01008,S02297,S03204,S03582,S03587,S00419,S02012,S02160,S03223,S03583,S03307,S01372,S01442,S01444,S03584,S03443,S02021,S03202,S02058,S03604,S01370,S03203,S01940,S01049,S03554,S00278,S01000,S01571,S01359,S00989,S00977,S03849,S03898,S03894,S03889,S03910,S03981",
  "filter": "vendor",
  "vendors": []
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/nodes/get/dimension/consumption
X-User: ********
Content-Type: application/json;charset=utf-8


{
  "startCycle": "2024-12-01",
  "endCycle": "2025-02-28",
  "scode": "S03245",
  "dataType": "bill",
  "cycleType": 1,
  "dataDimensions": [
    "subProduct",
    "vendor"
  ]
}

###
GET https://gw-qd-aliyun.haier.net/hdshcms/hcms-resource-center/api/v1/hcms/resource/cmdb/host?private_ip=*************
Authorization: B4quzKKQ4tJBCMXE7Rog62mjki4hGFlF
X-API-KEY: acb6faaf-fa13-41a3-af68-ab01b73b15f4

###
GET http://hcms-api.haier.net/api/v1/hcms/resource/cmdb/host?private_ip=*************
X-API-KEY: acb6faaf-fa13-41a3-af68-ab01b73b15f4

###
POST http://hcms-api.haier.net/api/v1/hcms/resource/cmdb/search
Content-Type: application/json;charset=utf-8
X-API-KEY: 9ebaa700-22c5-4eaa-a7d4-00f53313b035

{
  "resource_type": "host",
  "page": 1,
  "per_page": 20,
  "params": [
    {
      "column": "update_time",
      "operator": ">=",
      "values": ["yyyy-MM-dd HH:mm:ss"]
    }
  ]
}


###
GET https://hcms-api.haier.net/api/hcms/v1/contract
X-User: ********

###
GET http://hcms-api.haier.net/api/resource-application/v2/instance_flavors/db/mysql?vendor=huaweicloud&region=qingdao
X-User: ********

###
GET http://hcms-api.haier.net/api/resource-application/v2/resource/applications?scode=S02166&pageNum=1&pageSize=10
X-User: ********

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/paas/recentHalfYearPaasBill
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "scodes": "S03498,S03488,S01646,S02166,S03147,S02025,S03467,S04076"
}


###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/idmOrg/getAuthedOrgByName
X-User: ********

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/getUserSCodes
X-User: ********
Content-Type: application/json;charset=utf-8

{"departIds":"10146611"}



###
GET https://console.hwork.haier.net/api/hcms/v1/proxy/bill/nodes/getMainAssets



###
POST https://gw-qd-aliyun.haier.net/hwork/server-console-manager/gw/platform/platform/v1/application/pageOut
Authorization: 06QOwwXEumtFLfQnbaYj4TO7dypQcQcP
Content-Type: application/json


{
  "pageIndex": 1,
  "pageSize": 10
}
###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/cmdb_product_overview/getInfoByAggregatedId?aggregatedId=b86b802540aad5b705856df605046867
X-User: ********


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/serviceFree/create
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "vendor": "aws",
  "startDate": "2023-04-01",
  "endDate": "2023-06-30",
  "rate": 7.0592,
  "type": "2",
  "currency": "USD"
}



###
POST https://gw-qd-aliyun-test.haier.net/nlfbpt/op-server-console-new/gw/platform/platform/v1/application/getAuthAppForIt
Authorization: 1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
Content-Type: application/json;charset=utf-8

{
  "userCode": "********",
  "domain": null,
  "productIds": null
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/getItUserAuth
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "domain": "",
  "productIds": [],
  "userCode": "********"
}

###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/detail/listByPage
X-User: 22019228
Scope: hwork_multi_namespace
Content-Type: application/json;charset=utf-8

{
  "scode": "S00395,S02294,S03167,S00344,S00967,S02008,S03283,S01368,S01229,S02028,S01955,S03130,S03259,S02120,S03517,S03447,S03452,S03450,S04018,S04045,S03451,S02149,S00394,S01355,S01136,S02293,S01704,S04421,S03192,S01800",
  "type": 1,
  "startCycle": "2024-12-01",
  "endCycle": "2024-12-31",
  "sortNo": null,
  "subscriptionType": "",
  "page": 1,
  "per_page": 15
}

###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/acceptance/order?orderId=AL8AE679D1S00875202410

###
GET http://{{host}}:{{port}}/swagger-resources

###
POST https://p-api-cn-sp000454-i.hwork.haier.net/api/hcms/v1/proxy/bill/aggregated/getConsumptionAnalysis
Content-Type: application/json;charset=utf-8
Authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

{
  "scodes": "S02166"
}

###
POST {{host}}:{{port}}/api/v1/hcms/bill/aggregated/getConsumptionAnalysis
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "scodes": "S00395,S02294,S03167,S00344,S00967,S02008,S03283,S01368,S01229,S02028,S01955,S03130,S03259,S02120,S03517,S03447,S03452,S03450,S04018,S04045,S03451,S02149,S00394,S01355,S01136,S02293,S01704,S04421,S03192,S01800"
}


###
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/orders
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "startDate": "2024-11",
  "endDate": "2025-02",
  "page": 1,
  "per_page": 10,
  "billVo": {
    "budgetCode": "IT-KF-2024-012",
    "type": 2
  }
}
### 导入云桌面账单
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/importing
Content-Type: multipart/form-data; boundary=----WebAppBoundary

------WebAppBoundary
Content-Disposition: form-data; name="file"; filename="template.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< /Users/<USER>/Downloads/hwork_acceptance_order_template_1.xlsx
------WebAppBoundary--

### 导入预算配置
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/budget/importing
Content-Type: multipart/form-data; boundary=----WebAppBoundary

------WebAppBoundary
Content-Disposition: form-data; name="file"; filename="budget_config_template.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< /Users/<USER>/Downloads/budget_config_template.xlsx
------WebAppBoundary--


###
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/batchPush
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "ids": [
    402
  ]
}


###
PUT {{host}}:{{port}}/api/v1/hcms/bill/acceptance/order
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "id": 397,
  "sysCode": "DN04",
  "budgetCode": "IT-YW-2025-048",
  "userCode": "19028115",
  "milestoneId": "20293322",
  "payDate": "2024-12"
}
###

GET {{host}}:{{port}}/api/v1/hcms/bill/acceptance/budget
X-User: ********


###
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/check-budget
Content-Type: application/json;charset=utf-8
X-User: ********

{
  "ids": [
    8,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    20,
    21
  ]
}


###
GET {{host}}:{{port}}/api/v1/hcms/bill/raw-bill-products/listAliasProductsWithVendors?vendors=aliyun,huawei
X-User: ********


###
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/orders
Content-Type: application/json;charset=utf-8
X-User: ********


{
  "startDate": "2024-12",
  "endDate": "2025-03",
  "page": 1,
  "per_page": 10,
  "billVo": {
    "userCode": "none"
  }
}




###
POST {{host}}:{{port}}/api/v1/hcms/bill/acceptance/orders
Content-Type: application/json;charset=utf-8
X-User: ********


{
  "startDate": "2024-12",
  "endDate": "2025-03",
  "page": 1,
  "per_page": 10,
  "billVo": {
    "userCode": "none"
  }
}

### show_all=true可以查询出下线的资源
GET http://hcms-api.haier.net/api/v1/hcms/resource/cmdb/rds?instance_id=rm-2ze1u7f686m864t34&show_all=true
#Content-Type: application/x-www-form-urlencoded
x-api-key: b6952f1e-6f9d-4480-af82-24ca6f5c45d6



### 节省计划offering查询
GET {{host}}:{{port}}/api/v1/hcms/bill/aws/savingplan/offerings?accountName=GSOP&region=eu-central-1
X-User: ********
Content-Type: application/x-www-form-urlencoded




###
GET http://{{host}}:{{port}}/v3/api-docs
###
GET http://{{host}}:{{port}}/swagger-config



###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aws/commitment/list
X-User: ********
Content-Type: application/json;charset=utf-8

{
  "page": 1,
  "per_page": 10,
  "type": "ecs",
  "filter": {
    "resourceId": "i-08328fcc774bc3277"
  }
}


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aws/describeRiOffering
X-User: ********
Content-Type: application/json;charset=utf-8

{"model":"db.m5.large","ids":[184]}


###
POST http://{{host}}:{{port}}/api/v1/hcms/bill/aws/purchaseRi
X-User: ********
Content-Type: application/json;charset=utf-8

{"offeringId":"29cb0a83-269f-4a1b-9924-c47acfab1302","ids":[184]}


###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/aws/commitment/history?vendor=aws&type=ecs&resourceId=i-08328fcc774bc3277&page=1&per_page=10
X-User: ********


###
GET https://gw-qd-aliyun.haier.net/console/cmdbx/api/v4/project/getProjectDetail?almSCode=S02166
Authorization: 06QOwwXEumtFLfQnbaYj4TO7dypQcQcP


###
GET http://{{host}}:{{port}}/api/v1/hcms/bill/acceptance/getOrdersByMilestoneId?milestoneId=1696760003254751233
X-User: ********