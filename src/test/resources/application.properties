spring.profiles.active=@pom.spring.profiles.active@

# mybatis-plus
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.global-config.banner=false
mybatis.type-aliases-package=com.haier.devops.aru.entity
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl


# logback
logback.logDir=${user.home}/log
logback.appName=aru
logback.fileType=log


# druid

spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# ?????????????
spring.datasource.dynamic.druid.initialSize=5
# ???????
spring.datasource.dynamic.druid.minIdle=5
# ???????
spring.datasource.dynamic.druid.maxActive=50
# ????????????????
spring.datasource.dynamic.druid.maxWait=120000
spring.datasource.dynamic.druid.query-timeout=60000
spring.datasource.druid.socket-timeout=60000

# ???????????????????????????????
spring.datasource.dynamic.druid.timeBetweenEvictionRunsMillis=60000
# ????????????????
spring.datasource.dynamic.druid.minEvictableIdleTimeMillis=300000
# ???????????sql??????????
spring.datasource.dynamic.druid.validationQuery=SELECT 1 FROM DUAL
# ?????true?????????????????????????????????timeBetweenEvictionRunsMillis???validationQuery????????
spring.datasource.dynamic.druid.testWhileIdle=true
# ???????validationQuery?????????????????????
spring.datasource.dynamic.druid.testOnBorrow=true
# ???????validationQuery?????????????????????
spring.datasource.dynamic.druid.testOnReturn=false
# ????preparedStatement????PSCache?PSCache???????????????????oracle??mysql??????
spring.datasource.dynamic.druid.poolPreparedStatements=true
# ?????????filters????????sql?????'wall'?????
#spring.datasource.dynamic.druid.filters=stat,wall,slf4j

# \u5141\u8BB8\u6267\u884C\u591A\u4E2A\u8BED\u53E5
spring.datasource.dynamic.druid.filter.wall.config.multi-statement-allow=true
spring.datasource.dynamic.druid.filter.stat.log-slow-sql=true
spring.datasource.dynamic.druid.filter.stat.slow-sql-millis=5000
spring.datasource.dynamic.druid.filter.stat.merge-sql=false


# ???PSCache???????0????0??poolPreparedStatements???????true?
spring.datasource.dynamic.druid.maxPoolPreparedStatementPerConnectionSize=20
# ????DruidDataSource?????
spring.datasource.dynamic.druid.useGlobalDataSourceStat=true
# ??connectProperties?????mergeSql????SQL??
spring.datasource.dynamic.druid.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500


# feign
feign.compression.request.enabled=true
feign.compression.request.mime-types=text/xml,application/xml,application/json
feign.compression.request.min-request-size=2048
feign.compression.response.enabled=true

feign.client.config.default.connectTimeout=2000
feign.client.config.default.readTimeout=5000

feign.client.config.hds.connectTimeout=1000
feign.client.config.hds.readTimeout=2000
#feign.client.config.hds.request-interceptors[0]=com.haier.devops.aru.config.HdsAuthenticationConfig.HdsAuthenticationInterceptor

feign.okhttp.follow-redirects=true
feign.okhttp.connect-timeout=5000
feign.okhttp.retry-on-connection-failure=true
feign.okhttp.read-timeout=5000
feign.okhttp.write-timeout=5000
feign.okhttp.max-idle-connections=5
feign.okhttp.keep-alive-duration=15000


# hds\u67E5\u8BE2\u9879\u76EE\u5217\u8868
api.hds.project.token=06QOwwXEumtFLfQnbaYj4TO7dypQcQcP
api.hds.project.url=https://gw-qd-aliyun.haier.net

api.hds.user.token=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
api.hds.user.url=https://gw-qd-aliyun.haier.net


# hcms\u67E5\u8BE2\u7528\u6237\u4FE1\u606F
api.hcms.user.token=9ebaa700-22c5-4eaa-a7d4-00f53313b035
api.hcms.user.url=https://hcms.qd-aliyun-test-internal.haier.net

# \u6279\u91CF\u4FDD\u5B58
persist.batch.size=5000
# \u6279\u91CF\u67E5\u8BE2
query.batch.size=2000

# substitution configuration
substitution.request.substitution-requests[0].vendor=aliyun
substitution.request.substitution-requests[0].account-name=<EMAIL>
substitution.request.substitution-requests[0].scode=\u5171\u4EAB\u5E26\u5BBD

substitution.request.substitution-requests[1].vendor=aliyun
substitution.request.substitution-requests[1].account-name=
substitution.request.substitution-requests[1].scode=S03055

substitution.request.substitution-requests[2].vendor=aliyun
substitution.request.substitution-requests[2].account-name=<EMAIL>
substitution.request.substitution-requests[2].scode=\u5BB9\u5668\u4E91\u5206\u644A

substitution.request.substitution-requests[3].vendor=aliyun
substitution.request.substitution-requests[3].account-name=hr690n
substitution.request.substitution-requests[3].scode=\u5BB9\u5668\u4E91\u5206\u644A

# \u4E2A\u4EBA\u4FE1\u606F\u7F13\u5B58\u5931\u6548\u65F6\u95F4(minutes)
cache.personal-info.expire-time=5

# hds\u67E5\u8BE2\u9879\u76EE\u5217\u8868
api.hds.token=06QOwwXEumtFLfQnbaYj4TO7dypQcQcP
api.hds.url=https://gw-qd-aliyun.haier.net

# \u7BA1\u7406\u5458\u6743\u9650\u7684\u7EC4\u7EC7\u7F16\u7801
hcms_admin_orgids=********

# hwork\u6743\u9650\u4E2D\u5FC3\u57DF\u540D
api.hwork.authority.domain=https://pre-hwork-internal.haier.net
api.hwork.authority.app-id=hybrid_cloud_management_pre
api.hwork.authority.secret=eeff6ab7d88340c1831cc76c0e3472cd

# hwork im
api.howrk.domain=https://pre-hwork.haier.net
api.howrk.app-id=3dmVIj2XTbTiO4mK
api.howrk.secret=JEgAS7GJnLoXABA99ed6QhSK

#hds\u53D1\u6D88\u606F\u6A21\u7248
hds.notice.template_sms=********
hds.notice.template_email=********
hds.notice.template_hwork=********
hds.notice.template_ihaier2=********
#hds\u53D1\u6D88\u606Fsendor
hds.notice.sendor_sms=HCMS
hds.notice.sendor_email=HCMS<<EMAIL>>
hds.notice.sendor_hwork=hcms
hds.notice.sendor_ihaier2=HCMS

domain.code=*********

signature.client.user=HCMS
signature.client.pass=gqODmGap
signature.client.public-key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBBZdhwL1qgabjtRD5a1XfaMQfhQDMlyj7Ou5h8bilhZsv9G7jSCS8abpEh7xjb65jwYadifeGybjXL52fg8EYNr94Zoetvg0Ey7nTvySptrfwpY2YTGs7kVKYBahly5te2j0orYM637jGDB+YkMoRZvaj/8vSUCG3LEGdXkCocQIDAQAB

feishu.app-id=cli_a480637b2577500c
feishu.app-secret=s9rpPIQ7bUSGw2RyEtwsWefiSPhmyQ2K

#spring.mvc.pathmatch.matching-strategy=ant_path_matcher


# bigdata
automat.api.app-id=2c9f8d3895d743a80195ea3924500003
automat.api.app-key=050b39dc-437e-4b7c-a87f-ec94cf1da9f0
#automat.api.app-id=2c9f889a95a8ef0a0195cc53d78d0024
#automat.api.app-key=58af38fd-d4f1-498d-af4c-39572434faf1
automat.api.gateway-key=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
automat.api.url=https://gw-qd-aliyun.haier.net/dmc/data-service
automat.api.pool.max-per-route=5
automat.api.pool.max-total=10
# minutes
automat.api.pool.keep-live-mins=2
# milliseconds
automat.api.pool.connect-timeout-millis=3000
# milliseconds
automat.api.pool.socket-timeout-millis=3000


statusCallBack=https://dtest.haiersmarthomes.com/adp/test/SendOperateLog
aws.api.savingsplans.url=https://savingsplans.amazonaws.com
