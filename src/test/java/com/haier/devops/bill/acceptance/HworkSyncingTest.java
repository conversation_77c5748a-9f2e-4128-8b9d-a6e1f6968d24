package com.haier.devops.bill.acceptance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.api.HworkAcceptanceApi;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import com.haier.devops.bill.common.service.ApplicationAggregationService;
import com.haier.devops.bill.common.service.ResourceAggregationService;
import com.haier.devops.bill.substitution.Constant;
import com.haier.devops.bill.acceptance.api.HworkAcceptanceController;
import com.haier.devops.bill.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.Executable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.PlatformTransactionManager;

import java.math.BigDecimal;
import java.util.List;

import static com.haier.devops.bill.acceptance.AcceptanceStageEnum.ACTUAL;
import static com.haier.devops.bill.common.constant.CommonConstant.HWORK_SYNCING_STATUS_SYNCED;
import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class HworkSyncingTest extends BillCenterApplicationBaseTest {
    @Autowired
    HworkSyncing hworkSyncing;
    @Autowired
    HworkAcceptanceApi hworkAcceptanceApi;

    @Autowired
    HworkAcceptanceController acceptanceController;

    @Autowired
    ApplicationAggregationService applicationAggregationService;
    @Autowired
    ResourceAggregationService resourceAggregationService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    public static void main(String[] args) {
        String vendor = "huawei";
        String account = "hr690g";
        String scode = "S04583";
        System.out.println(Md5Util.Sum(vendor + account + Constant.PAAS_PRODUCT_CODE + scode));
    }

    @Test
    void persistApplicationAggregation() {
        String vendor = "huawei";
        String billingCycle = "2025-07";

        // assert no exception
        Executable executable = () -> hworkSyncing.persistApplicationAggregation(vendor, billingCycle, ACTUAL);
        assertDoesNotThrow(executable);
    }

    @Test
    void testQuery() {
        String milestoneId = "1696760003254751233";
        List<ApplicationAggregation> aggregations = applicationAggregationService.getBaseMapper().selectList(new LambdaQueryWrapper<ApplicationAggregation>()
                .eq(ApplicationAggregation::getAlmMilestoneId, milestoneId)
                .eq(ApplicationAggregation::getStatus, HWORK_SYNCING_STATUS_SYNCED));
        assertNotNull(aggregations);
    }

    @Test
    void persistResourceAggregation() {
        String vendor = "aliyun";
        String billingCycle = "2024-12";

        hworkSyncing.persistResourceAggregation(vendor, billingCycle, null);
    }


    @Test
    void syncApplicationAggregations() {
        String vendor = "aliyun_dedicated";
        String billingCycle = "2024-12";
//        String account = "hr690j";


        Executable executable = () -> hworkSyncing.syncApplicationAggregation(vendor, billingCycle/*, account*/);
        assertDoesNotThrow(executable);
    }

    @Test
    public void syncResourceAggregations() {
        String vendor = "aliyun_dedicated";
        String billingCycle = "2025-03";

        Executable executable = () -> hworkSyncing.syncResourceAggregation(vendor, billingCycle);
        assertDoesNotThrow(executable);
    }

    @Test
    void testOneTimeSyncResourceAggregation() {
        List<ResourceAggregation> resourceAggregations = resourceAggregationService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<ResourceAggregation>().last("limit 200"));
        HworkAcceptanceApi.ResourceAggregationResultWrapper resultWrapper = syncResourceAggregation(resourceAggregations);
        assertNotNull(resultWrapper);
    }

    HworkAcceptanceApi.ResourceAggregationResultWrapper syncResourceAggregation(List<ResourceAggregation> aggregations) {
        BigDecimal detailTotalAmount = aggregations.stream()
                .map(ResourceAggregation::getCostAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        HworkAcceptanceApi.ResourceAggregationWrapper body = new HworkAcceptanceApi.ResourceAggregationWrapper(aggregations.size(), detailTotalAmount, aggregations);
        return hworkAcceptanceApi.syncResourceAggregation(body);
    }

    @Test
    void testPush() {
        String mileStoneId = "1862412811613433858";
        ResponseEntityWrapper<Boolean> response = acceptanceController.pushByMileStone(mileStoneId);
        assertNotNull(response);
    }

}