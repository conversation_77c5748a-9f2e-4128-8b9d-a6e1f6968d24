package com.haier.devops.bill;

import com.huaweicloud.sdk.core.auth.GlobalCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectRequest;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectResponse;
import com.huaweicloud.sdk.eps.v1.region.EpsRegion;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2025年02月13日 15:49
 */
public class HuaweiTest {
    public static void main(String[] args) {
        // The AK and SK used for authentication are hard-coded or stored in plaintext, which has great security risks. It is recommended that the AK and SK be stored in ciphertext in configuration files or environment variables and decrypted during use to ensure security.
        // In this example, AK and SK are stored in environment variables for authentication. Before running this example, set environment variables CLOUD_SDK_AK and CLOUD_SDK_SK in the local environment
        String ak = "JXFHDCZHK8IGMJIO3OT7";
        String sk = "8cydc84Z0VZZeCo3Vxr2idXe3q3QppesHNlYqfEj";

        ICredential auth = new GlobalCredentials()
                .withAk(ak)
                .withSk(sk);

        EpsClient client = EpsClient.newBuilder()
                .withCredential(auth)
                .withRegion(EpsRegion.valueOf("cn-north-4"))
                .build();
        ListEnterpriseProjectRequest request = new ListEnterpriseProjectRequest();
        request.withLimit(1000);
        request.withOffset(0);
        try {
            ListEnterpriseProjectResponse response = client.listEnterpriseProject(request);
            System.out.println(response.toString());
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
    }
}
