package com.haier.devops.bill.export;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import com.haier.devops.bill.export.vo.OmnibearingExportApplicationVo;
import com.haier.devops.bill.export.vo.UploadResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Arrays;

public class OmnibearingExportTest extends BillCenterApplicationBaseTest {
    @Autowired
    @Qualifier("omnibearingExporter")
    Exporter<OmnibearingExportApplicationVo> omnibearingExporter;

    @Qualifier("detailExporter")
    Exporter<DetailExportApplicationVo> detailExporter;

    @Test
    public void testOmnibearingExport() {
        OmnibearingExportApplicationVo omnibearingExportVo = new OmnibearingExportApplicationVo();
        omnibearingExportVo.setScodes(Arrays.asList("S01690".split(",")));
        omnibearingExportVo.setPodIp("127.0.0.1");
        omnibearingExportVo.setSubmitter("<EMAIL>");
        omnibearingExportVo.setSerialNo("xxxxxxxxxxxxxxxxx");
        omnibearingExportVo.setStartCycle("2023-07");
        omnibearingExportVo.setEndCycle("2023-07");
        UploadResult uploadResult = omnibearingExporter.doExport(omnibearingExportVo);
    }

    @Test
    public void testDetailExport() {

        if (1 <= 2) {
            System.out.println(" ==> --> test");
        }
    }
}
