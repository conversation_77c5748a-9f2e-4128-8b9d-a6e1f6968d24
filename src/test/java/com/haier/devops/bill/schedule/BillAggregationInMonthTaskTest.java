package com.haier.devops.bill.schedule;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.service.AggregatedResolveService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BillAggregationInMonthTaskTest extends BillCenterApplicationBaseTest {
    @Autowired
    private BillAggregationInMonthTask aggregationInMonthTask;


    @Autowired
    private AggregatedResolveService aggregatedResolveService;

    @Test
    void aggregate() {
        aggregationInMonthTask.triggerAggregationTask("aliyun", null, null);
    }

    @Test
    void aggregateByAccount() {
        String vendor = "aliyun";
        String start = "2025-02";
        String end = "2025-02";
        String account = "hr690x";

        // 清除旧的汇总账单
        aggregatedResolveService.clearOldAggregatedBillByAccount(vendor, start, end, account);

        // 按月汇总的账单
        List<AggregatedBill> groupedAggregatedBills = aggregatedResolveService.getAggregatedBillsByAccount(start, end, vendor, account);

        aggregatedResolveService.saveOrUpdateWhenAggregatingInMonth(groupedAggregatedBills);

    }
}