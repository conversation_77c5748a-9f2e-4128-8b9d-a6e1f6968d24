package com.haier.devops.bill.orgnization;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.entity.IdmOrg;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class OrganizationCacheTest extends BillCenterApplicationBaseTest {
//    @Autowired
    OrganizationCache orgCache;

    @Test
    public void testOrgTree() {
        orgCache.preloadCache();
        List<String> orgIds = Arrays.asList("10146611", "20004694", "50095679");
        IdmOrg topOrg = orgCache.getTopLevelOrg(orgIds);
        List<IdmOrg> allSubOrgs = orgCache.getAllSubOrgs(topOrg.getOrgId());
        assertTrue(allSubOrgs.size() > 1);
    }

}