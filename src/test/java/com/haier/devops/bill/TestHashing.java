package com.haier.devops.bill;


import java.util.Base64;

public class TestHashing  {

    public static String encode(String appCode) {
        return Base64.getEncoder().encodeToString(appCode.getBytes());
    }

    public static String decode(String encodedCode) {
        byte[] decodedBytes = Base64.getDecoder().decode(encodedCode);
        return new String(decodedBytes);
    }

    public static void main(String[] args) {
        String appCode = "S01234_2024-10-24";
        String encoded = encode(appCode);
        String decoded = decode(encoded);

        System.out.println("Encoded: " + encoded);
        System.out.println("Decoded: " + decoded);
    }
}