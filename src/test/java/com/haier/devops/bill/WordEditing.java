package com.haier.devops.bill;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

public class WordEditing {
    public static void main(String[] args) throws IOException, InvalidFormatException {
        // 打开文档
        FileInputStream fis = new FileInputStream(new File("/Users/<USER>/template.docx"));
        XWPFDocument document = new XWPFDocument(fis);

        // 替换文本
//        replaceTextInParagraphs(document, "budget", "大运维");
//        replaceTextInParagraphs(document, "param_vendor", "华为");
//        replaceTextInParagraphs(document, "param1", "混合云管理平台");
//        replaceTextInParagraphs(document, "cost", "22222");


        replaceTextInTables(document, "budget", "大运维");
        replaceTextInTables(document, "param_vendor", "华为");
        replaceTextInTables(document, "param1", "混合云管理平台");
        replaceTextInTables(document, "cost", "22222");

        // 在“签名：”后插入图片
        insertImageAfterText(document, "签名：", "/Users/<USER>/signature.png");

        // 保存文档
        FileOutputStream fos = new FileOutputStream("output.docx");
        document.write(fos);
        fos.close();
        document.close();
        fis.close();
    }


    private static void replaceTextInParagraphs(XWPFDocument document, String searchText, String replacementText) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs != null) {
                for (XWPFRun run : runs) {
                    String text = run.getText(0);
                    if (text != null && text.contains(searchText)) {
                        text = text.replace(searchText, replacementText);
                        run.setText(text, 0);
                    }
                }
            }
        }
    }

    private static void replaceTextInTables(XWPFDocument document, String searchText, String replacementText) {

        List<XWPFHeader> headers = document.getHeaderList();
        for (XWPFHeader header : headers) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs != null) {
                    for (XWPFRun run : runs) {
                        String text = run.getText(0);
                        if (text != null && text.contains(searchText)) {
                            text = text.replace(searchText, replacementText);
                            run.setText(text, 0);
                        }
                    }
                }
            }

            for (XWPFTable table : header.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            List<XWPFRun> runs = paragraph.getRuns();
                            if (runs != null) {
                                for (XWPFRun run : runs) {
                                    String text = run.getText(0);
                                    if (text != null && text.contains(searchText)) {
                                        text = text.replace(searchText, replacementText);
                                        run.setText(text, 0);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

/*
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        if (runs != null) {
                            for (XWPFRun run : runs) {
                                String text = run.getText(0);
                                if (text != null && text.contains(searchText)) {
                                    text = text.replace(searchText, replacementText);
                                    run.setText(text, 0);
                                }
                            }
                        }
                    }
                }
            }
        }
*/
    }

    private static void insertImageAfterText(XWPFDocument document, String searchText, String imagePath) throws IOException, InvalidFormatException {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            insertImageAfterTextInParagraph(paragraph, searchText, imagePath);
        }
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        insertImageAfterTextInParagraph(paragraph, searchText, imagePath);
                    }
                }
            }
        }
    }

    private static void insertImageAfterTextInParagraph(XWPFParagraph paragraph, String searchText, String imagePath) throws IOException, InvalidFormatException {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs != null) {
            for (int i = 0; i < runs.size(); i++) {
                XWPFRun run = runs.get(i);
                String text = run.getText(0);
                if (text != null && text.contains(searchText)) {
                    // 分割文本，保持签名文字和图片插入
                    String beforeText = text.substring(0, text.indexOf(searchText) + searchText.length());
                    String afterText = text.substring(text.indexOf(searchText) + searchText.length());

                    run.setText(beforeText, 0);  // 设置“签名：”文本

                    // 插入图片
                    XWPFRun imageRun = paragraph.insertNewRun(i + 1);
                    try (FileInputStream is = new FileInputStream(imagePath)) {
                        imageRun.addPicture(is, Document.PICTURE_TYPE_PNG, imagePath, Units.toEMU(50), Units.toEMU(25));
                    }

                    // 插入剩余文本
                    XWPFRun newRun = paragraph.insertNewRun(i + 2);
                    newRun.setText(afterText);
                }
            }
        }
    }


}
