package com.haier.devops.bill.privelege;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.vo.DomainSubProductVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DomainPrivilegeFinderTest extends BillCenterApplicationBaseTest {
    @Autowired
    private DomainPrivilegeFinder domainPrivilegeFinder;

    @Test
    void findDomainPrivileges() throws Exception {
        String userId = "01488712";
        List<DomainSubProductVo> domainPrivileges = domainPrivilegeFinder.findDomainPrivileges(userId, false);
        assertNotNull(domainPrivileges);
    }
}