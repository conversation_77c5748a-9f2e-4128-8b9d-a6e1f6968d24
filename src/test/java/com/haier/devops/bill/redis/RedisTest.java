package com.haier.devops.bill.redis;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.redis.Queue;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Date;

public class RedisTest extends BillCenterApplicationBaseTest {
    private Queue<DetailExportApplicationVo> detailQueue;

    public RedisTest(@Qualifier("detailExportQueue") Queue<DetailExportApplicationVo> detailQueue) {
        this.detailQueue = detailQueue;
    }

    @Test
    public void testDetailQueue() {
        DetailExportApplicationVo detailVo = new DetailExportApplicationVo();
        detailVo.setScode("S000001");
        detailVo.setSubmitter("submitter");
        detailVo.setSubmitTime(new Date());
        detailQueue.add(detailVo);
        Assertions.assertTrue(detailQueue.size() > 0);
    }
}
