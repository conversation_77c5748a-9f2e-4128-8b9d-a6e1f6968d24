package com.haier.devops.bill.sdks.haier;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

class SignClientTest extends BillCenterApplicationBaseTest {
    @Test
    public void testSign() throws IOException, URISyntaxException {
        String user = "HCMS";
        String pass = "gqODmGap";
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBBZdhwL1qgabjtRD5a1XfaMQfhQDMlyj7Ou5h8bilhZsv9G7jSCS8abpEh7xjb65jwYadifeGybjXL52fg8EYNr94Zoetvg0Ey7nTvySptrfwpY2YTGs7kVKYBahly5te2j0orYM637jGDB+YkMoRZvaj/8vSUCG3LEGdXkCocQIDAQAB";
        SignatureClient signClient = new SignatureClient(user, pass, publicKey);
        List<SignatureClient.SignInfo> signInfos = signClient.get("01488712");
        Assertions.assertNotNull(signInfos);
    }
}