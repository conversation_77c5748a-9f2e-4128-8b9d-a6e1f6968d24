package com.haier.devops.bill;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;

//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ActiveProfiles("dev")
public class MybatisPlusGenerator {
//    @Value("${spring.datasource.dynamic.datasource.master.url}")
//    private String url = "*********************************************************************************************************************************************************************************************************";
    private String url = "*************************************************************************************************************************************************************************************************************************************************************";
//    private String url = "*****************************************************************************************************";

//    @Value("${spring.datasource.dynamic.datasource.master.username}")
//    private String username = "hcms";
//    private String username = "root";
    private String username = "hcms_test";

//    @Value("${spring.datasource.dynamic.datasource.master.password}")
//    private String password = "GFL29Z7rAqsqVGJh";
    private String password = "MI/tNnGjaI/fDA==";
//    private String password = "wST6dB7eeGYb4vMQ";

    @Test
    public void generate() {
        FastAutoGenerator.create(url, username, password)
                .globalConfig(builder -> {
                    // 设置作者
                    builder.author("<EMAIL>")
//                            .enableSwagger() // 开启 swagger 模式
                            // 指定输出目录
                            .outputDir("/Users/<USER>/Developer/haier/devops/bill-center");
                })
                .packageConfig(builder -> {
                    builder.parent("com.haier.devops.bill") // 设置父包名
                            .moduleName("common") // 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.xml,
                                    // 设置mapperXml生成路径
                                    "/Users/<USER>/Developer/haier/devops/bill-center/src/main/resources/mapper"));
                })
                .strategyConfig(builder -> {
                    // 设置需要生成的表名
                    builder.addInclude("bc_bill_acceptance_resource")
                            .addTablePrefix("bc_") // 设置过滤表前缀
                    ;
                })
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
