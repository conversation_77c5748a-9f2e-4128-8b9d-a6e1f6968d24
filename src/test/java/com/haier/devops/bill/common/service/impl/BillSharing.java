package com.haier.devops.bill.common.service.impl;

import java.util.*;

public class BillSharing {
    public static void main(String[] args) {
        // 创建DAG
        Map<String, List<String>> graph = new HashMap<>();
        graph.put("bill1", Arrays.asList("person1", "person2", "person3"));
        graph.put("bill2", Arrays.asList("person1", "person2"));
        graph.put("bill3", Arrays.asList("person2", "person3"));

        // 计算每个人的贡献
        Map<String, Double> contributions = new HashMap<>();
        for (String node : topologicalSort(graph)) {
            if (!graph.containsKey(node)) {
                // 如果是人节点，直接返回1.0
                contributions.put(node, 1.0);
            } else {
                // 如果是账单节点，平均分摊贡献给出边所指向的人节点
                List<String> neighbors = graph.get(node);
                double contribution = contributions.get(node) / neighbors.size();
                for (String neighbor : neighbors) {
                    double neighborContribution = contributions.getOrDefault(neighbor, 0.0);
                    contributions.put(neighbor, neighborContribution + contribution);
                }
            }
        }

        // 输出结果
        for (Map.Entry<String, Double> entry : contributions.entrySet()) {
            System.out.println(entry.getKey() + " contributed " + entry.getValue() + " to the bill.");
        }
    }

    private static List<String> topologicalSort(Map<String, List<String>> graph) {
        Map<String, Integer> indegrees = new HashMap<>();
        for (List<String> neighbors : graph.values()) {
            for (String neighbor : neighbors) {
                indegrees.put(neighbor, indegrees.getOrDefault(neighbor, 0) + 1);
            }
        }
        Queue<String> queue = new LinkedList<>();
        for (String node : graph.keySet()) {
            if (!indegrees.containsKey(node)) {
                queue.offer(node);
            }
        }
        List<String> result = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            result.add(node);
            for (String neighbor : graph.getOrDefault(node, Collections.emptyList())) {
                indegrees.put(neighbor, indegrees.get(neighbor) - 1);
                if (indegrees.get(neighbor) == 0) {
                    queue.offer(neighbor);
                }
            }
        }
        return result;
    }
}
