package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.entity.RawBill;
import com.haier.devops.bill.common.service.RawBillService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class MultiThreadingTest extends BillCenterApplicationBaseTest {
    @Autowired
    private RawBillService rawBillService;

    @Test
    public void testMultiThreadingPullingData() {
        QueryWrapper<RawBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RawBill::getBillingCycle, "2023-03-31");
        Long totalCount = rawBillService.count(queryWrapper);

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(15);
        List<Future<List<RawBill>>> futures = new ArrayList<>();

        // 分批次拉取数据并处理
        int batchSize = 1500;
        int totalBatches = (int) Math.ceil((double) totalCount / batchSize);

        for (int i = 0; i < totalBatches; i++) {
            final int batchIndex = i;
            Future<List<RawBill>> future = executor.submit(() -> {
                int startIndex = batchIndex * batchSize;
                int endIndex = Math.min(startIndex + batchSize, totalCount.intValue());
                System.out.println("startIndex = " + startIndex + ", endIndex = " + endIndex);

                Page<RawBill> page = rawBillService.page(new Page<>(startIndex, endIndex), queryWrapper);
                return page.getRecords();
            });

            futures.add(future);
        }

        // 关闭线程池
        executor.shutdown();
        try {
            executor.awaitTermination(1, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        List<RawBill> totalData = new ArrayList<>();
        for (Future<List<RawBill>> future : futures) {
            try {
                totalData.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
        System.out.println("总条数为：" + totalData.size());
    }


    @Test
    public void testPage() {
        QueryWrapper<RawBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RawBill::getBillingCycle, "2023-03-31");
        Page page = rawBillService.page(new Page(0, 2000), queryWrapper);
        System.out.println(page.getSize());
        System.out.println(page.getPages());
    }
}
