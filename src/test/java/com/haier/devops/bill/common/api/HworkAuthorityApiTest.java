package com.haier.devops.bill.common.api;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class HworkAuthorityApiTest extends BillCenterApplicationBaseTest {
    @Autowired
    HworkAuthorityApi hworkAuthorityApi;
    String domainCode = "szyhjsz";
    String dimensionCode = "Domain";
    Integer pageNum = 1;
    Integer pageSzie = 50;

    @Test
    void testGetByDomainAndDimensionCode() {

        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.Pager> result = hworkAuthorityApi.getByDomainAndDimensionCode(
                new HworkAuthorityApi.GetByDomainAndDimensionCodeRequestWrapper(
                        domainCode, dimensionCode, pageNum, pageSzie
                ));

        assertNotNull(result);
    }

    @Test
    void testGetUserDetail() {
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> userDetail = hworkAuthorityApi.getUserDetail("01488712", true);
        assertNotNull(userDetail);
    }

    @Test
    void testQualifiedUsersByDataPermissionPage() {
        // 从第1页开始获取所有维度代码
        List<String> authorityCodes = getAuthorityCodesIterative(1);


        HworkAuthorityApi.QualifiedUsersByDataPermissionPageRequestWrapper requestWrapper = new HworkAuthorityApi.QualifiedUsersByDataPermissionPageRequestWrapper(
                domainCode, Collections.singletonList(new HworkAuthorityApi.DimensionObject(dimensionCode, authorityCodes))
        );

        Object result = hworkAuthorityApi.qualifiedUsersByDataPermissionPage(requestWrapper);

        assertNotNull(result);
    }

    private List<String> getAuthorityCodesIterative(Integer startPage) {
        List<String> dimensionCodes = new ArrayList<>();
        Integer currentPage = startPage;

        while (true) {
            final Integer pageNum = currentPage;
            Optional<HworkAuthorityApi.Pager> pagerOpt = Optional.ofNullable(hworkAuthorityApi.getByDomainAndDimensionCode(
                            new HworkAuthorityApi.GetByDomainAndDimensionCodeRequestWrapper(
                                    domainCode, dimensionCode, pageNum, pageSzie)))
                    .filter(result -> result.getCode() == 200)
                    .map(HworkAuthorityApi.ResultWrapper::getData);

            if (!pagerOpt.isPresent()) {
                break;
            }

            HworkAuthorityApi.Pager pager = pagerOpt.get();
            Optional.ofNullable(pager.getData())
                    .filter(dimensions -> !dimensions.isEmpty())
                    .ifPresent(dimensions ->
                            dimensionCodes.addAll(dimensions.stream()
                                    .map(HworkAuthorityApi.Dimension::getCode)
                                    .collect(Collectors.toList()))
                    );

            if (pager.getPage() >= pager.getTotalPage()) {
                break;
            }

            currentPage = pager.getPage() + 1;
        }

        return dimensionCodes;
    }
}