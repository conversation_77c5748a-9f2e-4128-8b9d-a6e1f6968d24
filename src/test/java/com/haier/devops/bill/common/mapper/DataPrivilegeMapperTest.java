package com.haier.devops.bill.common.mapper;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.service.DataPrivilegeService;
import com.haier.devops.bill.common.vo.UserDataPrivilegeVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DataPrivilegeMapperTest extends BillCenterApplicationBaseTest {
    @Autowired
    private DataPrivilegeService dataPrivilegeService;

    @Test
    void selectUserDataPrivilegeByDimension() {
        String dimension = "domain";
        String userId = "01488712";
        List<UserDataPrivilegeVo> userDataPrivilegeVos = dataPrivilegeService.selectUserDataPrivilegeByDimension(dimension, userId);
        assertNotNull(userDataPrivilegeVos);
        assertTrue(userDataPrivilegeVos.size() > 0);
    }
}