package com.haier.devops.bill.common.service.impl;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.service.AuthorityService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class AuthorityServiceImplTest extends BillCenterApplicationBaseTest {
    @Autowired
    private AuthorityService authorityService;

    @Test
    void getAuthorizedScodes() {
        String userCode = "01488712";
        List<String> authorizedScodes = authorityService.getAuthorizedScodes(userCode);
        assertFalse(CollectionUtils.isEmpty(authorizedScodes));
    }

    @Test
    void getUserAuthorities() {
        String userCode = "01488712";
        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> userAuthorities = authorityService.getUserAuthorities(userCode);
        assertFalse(CollectionUtils.isEmpty(userAuthorities.getData()));
    }
}