package com.haier.devops.bill.common.service;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.entity.IdmUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class IdmUserServiceTest extends BillCenterApplicationBaseTest {
    @Autowired
    private IdmUserService idmUserService;


    @Test
    public void testGetUser() {
        IdmUser idmUser = idmUserService.selectByUserCode("01488712");
        assertNotNull(idmUser);
        assertNotNull(idmUser.getFullname());
    }

}