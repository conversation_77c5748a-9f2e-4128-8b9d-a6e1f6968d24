package com.haier.devops.bill.common.service.impl;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.entity.GroupCosts;
import com.haier.devops.bill.common.service.GroupCostsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class GroupCostsServiceImplTest extends BillCenterApplicationBaseTest {
    @Autowired
    private GroupCostsService groupCostsService;

    @Test
    void getConfig() {
        List<GroupCosts> configs = groupCostsService.getConfig();
        assertTrue(configs.size() > 0);
    }
}