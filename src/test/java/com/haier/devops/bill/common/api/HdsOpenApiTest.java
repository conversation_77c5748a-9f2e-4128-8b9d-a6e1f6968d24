package com.haier.devops.bill.common.api;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.dto.SubProductsPageDto;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class HdsOpenApiTest extends BillCenterApplicationBaseTest {
    @Autowired
    HdsOpenApi hdsOpenApi;

    @Autowired
    HworkAuthorityApi hworkAuthorityApi;

    @Autowired
    CmdbProductOverviewService productOverviewService;

    @Test
    void correctProjectInfo() {
        HdsOpenApi.ResultWrapper result = hdsOpenApi.queryProjectsInfo();
        assertNotNull(result);
        result.getData().forEach(p -> log.info("{}, {}, {}", p.getAlmSCode(), p.getId(), p.getName()));
        for (HdsOpenApi.Project project : result.getData()) {
            int count = productOverviewService.correctProjectInfoByScode(project.getAlmSCode(), project.getId(), project.getName());
            log.info("scode: {}, count: {}", project.getAlmSCode(), count);
        }
    }

    @Test
    void queryAlmProject() {
        String scode = "S01803";
        HdsOpenApi.AlmProjectWrapper almProjectWrapper = hdsOpenApi.queryAlmProject(scode);
        assertNotNull(almProjectWrapper);
    }

    @Test
    void getSubProducts() {
        int page = 1;
        int pageSize = 200;

        SubProductsPageDto pageDto = new SubProductsPageDto();
        pageDto.setPageIndex(page);
        pageDto.setPageSize(pageSize);

        HdsOpenApi.ResultSubProduct result = hdsOpenApi.getSubProducts(pageDto);
        if (result.getCode() != 200) {
            return;
        }

        while (result.getData().getPage() < result.getData().getTotalPage()) {
            List<HdsOpenApi.SubProduct> subProducts = result.getData().getData();
            if (CollectionUtils.isEmpty(subProducts)) {
                continue;
            }

            for (HdsOpenApi.SubProduct subProduct : subProducts) {
                List<HdsOpenApi.User> users = subProduct.getUsers();
                if (CollectionUtils.isEmpty(users)) {
                    continue;
                }

                List<HdsOpenApi.User> itResolvers = users.stream()
                        .filter(user -> user.getGroupRoleCode().equals(""))
                        .collect(Collectors.toList());
                List<HdsOpenApi.User> developerChargers = users.stream()
                        .filter(user -> user.getGroupRoleCode().equals(""))
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(itResolvers)) {
                    itResolvers.forEach(this::printUserDept);
                }

                if (!CollectionUtils.isEmpty(developerChargers)) {
                    developerChargers.forEach(this::printUserDept);
                }
            }


            pageDto.setPageSize(++page);
            result = hdsOpenApi.getSubProducts(pageDto);
        }
    }

    void printUserDept(HdsOpenApi.User user) {
        if (null == user) {
            return;
        }
        String userId = user.getUserCode();
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> result = hworkAuthorityApi.getUserDetail(userId, true);
        if (result.getCode() == 200) {
            log.info("-->{}, {}", userId, result.getData().getDeptId());
        }
    }

    @Test
    void getUserInfo() {
        String userId = "01488712";
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> result = hworkAuthorityApi.getUserDetail(userId, true);

        assertNotNull(result);
    }


    @Test
    void getSubProductsNew() {
        int page = 1;
        int pageSize = 200;

        SubProductsPageDto pageDto = new SubProductsPageDto();
        pageDto.setPageIndex(page);
        pageDto.setPageSize(pageSize);

        Object result = hdsOpenApi.getSubProductsNew(pageDto);
        assertNotNull(result);
    }
}