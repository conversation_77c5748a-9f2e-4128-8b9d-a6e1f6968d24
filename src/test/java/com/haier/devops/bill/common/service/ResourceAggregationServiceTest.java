package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.controller.ResponseEnum;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class ResourceAggregationServiceTest extends BillCenterApplicationBaseTest {
    @Autowired
    private ApplicationAggregationService applicationAggregationService;
    @Autowired
    private ResourceAggregationService resourceAggregationService;
    @Test

    void test() {
        String orderId = "CDBD5F8B52DN08202502";
        ApplicationAggregation applicationAggregation = applicationAggregationService.getBaseMapper()
                .selectOne(new LambdaQueryWrapper<ApplicationAggregation>()
                        .eq(ApplicationAggregation::getOrderId, orderId));
        if (null == applicationAggregation) {
            log.error("没有找到该账单：{}", orderId);

        }
        List<ResourceAggregation> resourceAggregations = resourceAggregationService.selectByApplication(applicationAggregation);
        if (CollectionUtils.isEmpty(resourceAggregations)) {
            log.error("没有找到该账单的明细：orderId = {}, vendor = {}, account = {}, billingCycle = {}, scode = {}",
                    orderId,
                    applicationAggregation.getFactory(),
                    applicationAggregation.getAccount(),
                    applicationAggregation.getPayDate(),
                    applicationAggregation.getSysCode());
        }

    }

}