package com.haier.devops.bill;

public class ExtendingTest {
    public static void main(String[] args) {
        new ChildClass().execute();
    }
}

abstract class ParentClass {
    abstract void execute();

    void doExecute() {
        System.out.println("Executing " + getName());
    }

    abstract String getName();
}

class ChildClass extends ParentClass {
    @Override
    void execute() {
        doExecute();
    }

    @Override
    String getName() {
        return "ChildClass";
    }
}
