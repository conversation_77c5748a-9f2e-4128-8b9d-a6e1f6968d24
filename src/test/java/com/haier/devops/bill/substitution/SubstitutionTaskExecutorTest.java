package com.haier.devops.bill.substitution;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class SubstitutionTaskExecutorTest extends BillCenterApplicationBaseTest {
    @Autowired
    SubstitutionTaskExecutor executor;
    String billingCycle = "2025-05";
    String type = Constant.UCS;

    @Test
    void create() {
        executor.create(type, billingCycle);
    }

    @Test
    void migrate() {
        executor.migrate(type, billingCycle);
    }

    @Test
    void calculate() {
        executor.calculate(type, billingCycle);
    }

    @Test
    void dryDryClean() {
        executor.dryClean(type, billingCycle);
    }

    @Test
    void substitute() {
        executor.substitute(type, billingCycle);
    }
}