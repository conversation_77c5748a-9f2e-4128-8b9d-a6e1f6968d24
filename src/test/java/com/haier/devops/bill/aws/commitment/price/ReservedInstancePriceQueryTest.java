package com.haier.devops.bill.aws.commitment.price;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ReservedInstancePriceQueryTest extends BillCenterApplicationBaseTest {

    @Autowired
    ReservedInstancePriceQuery priceQuery;

    @Test
    void queryPrice() {
        List<ResourceUsageAgreementVo> agreements = priceQuery.queryPrice();
        assertNotNull(agreements);
    }
}