package com.haier.devops.bill.aws.commitment.price;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class SavingPlanPriceUpdateTest extends BillCenterApplicationBaseTest {

    @Autowired
    SavingPlanPriceUpdate savingPlanPriceUpdate;

    @Test
    void testUpdate() {
        assertDoesNotThrow(() -> savingPlanPriceUpdate.update());
    }

}