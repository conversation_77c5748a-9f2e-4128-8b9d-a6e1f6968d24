package com.haier.devops.bill.aws.commitment.purchase;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class EC2SpPurchaseTest extends BillCenterApplicationBaseTest {
    @Autowired
    private EC2SpPurchase ec2SpPurchase;

    @Test
    void testPurchase() {
        assertDoesNotThrow(() -> ec2SpPurchase.purchase());
    }

}