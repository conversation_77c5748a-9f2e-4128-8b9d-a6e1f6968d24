package com.haier.devops.bill.aws.commitment.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.rds.model.ReservedDBInstancesOffering;
import software.amazon.awssdk.services.rds.model.RecurringCharge;
import software.amazon.awssdk.services.elasticache.model.ReservedCacheNodesOffering;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试AWS SDK对象转换为DTO并序列化的功能
 */
public class ReservedInstanceOfferingDtoTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testReservedDBInstancesOfferingDtoSerialization() throws Exception {
        // 创建模拟的AWS SDK对象
        ReservedDBInstancesOffering offering = ReservedDBInstancesOffering.builder()
                .reservedDBInstancesOfferingId("test-offering-id")
                .dbInstanceClass("db.m5.large")
                .duration(31536000) // 1 year in seconds
                .fixedPrice(1000.0)
                .usagePrice(0.1)
                .currencyCode("USD")
                .productDescription("MySQL")
                .offeringType("No Upfront")
                .multiAZ(false)
                .recurringCharges(Arrays.asList(
                        RecurringCharge.builder()
                                .recurringChargeAmount(0.1)
                                .recurringChargeFrequency("Hourly")
                                .build()
                ))
                .build();

        // 转换为DTO
        ReservedDBInstancesOfferingDto dto = ReservedDBInstancesOfferingDto.fromAwsModel(offering);

        // 验证转换结果
        assertNotNull(dto);
        assertEquals("test-offering-id", dto.getReservedDBInstancesOfferingId());
        assertEquals("db.m5.large", dto.getDbInstanceClass());
        assertEquals(31536000, dto.getDuration());
        assertEquals(1000.0, dto.getFixedPrice());
        assertEquals(0.1, dto.getUsagePrice());
        assertEquals("USD", dto.getCurrencyCode());
        assertEquals("MySQL", dto.getProductDescription());
        assertEquals("No Upfront", dto.getOfferingType());
        assertEquals(false, dto.getMultiAZ());
        assertNotNull(dto.getRecurringCharges());
        assertEquals(1, dto.getRecurringCharges().size());
        assertEquals(0.1, dto.getRecurringCharges().get(0).getRecurringChargeAmount());
        assertEquals("Hourly", dto.getRecurringCharges().get(0).getRecurringChargeFrequency());

        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(dto);
        assertNotNull(json);
        assertTrue(json.contains("test-offering-id"));
        assertTrue(json.contains("db.m5.large"));

        // 测试JSON反序列化
        ReservedDBInstancesOfferingDto deserializedDto = objectMapper.readValue(json, ReservedDBInstancesOfferingDto.class);
        assertNotNull(deserializedDto);
        assertEquals(dto.getReservedDBInstancesOfferingId(), deserializedDto.getReservedDBInstancesOfferingId());
        assertEquals(dto.getDbInstanceClass(), deserializedDto.getDbInstanceClass());
    }

    @Test
    public void testReservedCacheNodesOfferingDtoSerialization() throws Exception {
        // 创建模拟的AWS SDK对象
        ReservedCacheNodesOffering offering = ReservedCacheNodesOffering.builder()
                .reservedCacheNodesOfferingId("test-cache-offering-id")
                .cacheNodeType("cache.m5.large")
                .duration(31536000) // 1 year in seconds
                .fixedPrice(800.0)
                .usagePrice(0.08)
                .productDescription("redis")
                .offeringType("No Upfront")
                .recurringCharges(Arrays.asList(
                        software.amazon.awssdk.services.elasticache.model.RecurringCharge.builder()
                                .recurringChargeAmount(0.08)
                                .recurringChargeFrequency("Hourly")
                                .build()
                ))
                .build();

        // 转换为DTO
        ReservedCacheNodesOfferingDto dto = ReservedCacheNodesOfferingDto.fromAwsModel(offering);

        // 验证转换结果
        assertNotNull(dto);
        assertEquals("test-cache-offering-id", dto.getReservedCacheNodesOfferingId());
        assertEquals("cache.m5.large", dto.getCacheNodeType());
        assertEquals(31536000, dto.getDuration());
        assertEquals(800.0, dto.getFixedPrice());
        assertEquals(0.08, dto.getUsagePrice());
        assertEquals("redis", dto.getProductDescription());
        assertEquals("No Upfront", dto.getOfferingType());
        assertNotNull(dto.getRecurringCharges());
        assertEquals(1, dto.getRecurringCharges().size());

        // 测试JSON序列化
        String json = objectMapper.writeValueAsString(dto);
        assertNotNull(json);
        assertTrue(json.contains("test-cache-offering-id"));
        assertTrue(json.contains("cache.m5.large"));

        // 测试JSON反序列化
        ReservedCacheNodesOfferingDto deserializedDto = objectMapper.readValue(json, ReservedCacheNodesOfferingDto.class);
        assertNotNull(deserializedDto);
        assertEquals(dto.getReservedCacheNodesOfferingId(), deserializedDto.getReservedCacheNodesOfferingId());
        assertEquals(dto.getCacheNodeType(), deserializedDto.getCacheNodeType());
    }

    @Test
    public void testBatchConversion() {
        // 测试批量转换
        ReservedDBInstancesOffering offering1 = ReservedDBInstancesOffering.builder()
                .reservedDBInstancesOfferingId("offering-1")
                .dbInstanceClass("db.m5.large")
                .build();

        ReservedDBInstancesOffering offering2 = ReservedDBInstancesOffering.builder()
                .reservedDBInstancesOfferingId("offering-2")
                .dbInstanceClass("db.m5.xlarge")
                .build();

        List<ReservedDBInstancesOffering> offerings = Arrays.asList(offering1, offering2);
        List<ReservedDBInstancesOfferingDto> dtos = ReservedDBInstancesOfferingDto.fromAwsModelList(offerings);

        assertNotNull(dtos);
        assertEquals(2, dtos.size());
        assertEquals("offering-1", dtos.get(0).getReservedDBInstancesOfferingId());
        assertEquals("offering-2", dtos.get(1).getReservedDBInstancesOfferingId());
    }

    @Test
    public void testNullHandling() {
        // 测试null处理
        ReservedDBInstancesOfferingDto dto = ReservedDBInstancesOfferingDto.fromAwsModel(null);
        assertNull(dto);

        List<ReservedDBInstancesOfferingDto> dtos = ReservedDBInstancesOfferingDto.fromAwsModelList(null);
        assertNull(dtos);
    }
}
