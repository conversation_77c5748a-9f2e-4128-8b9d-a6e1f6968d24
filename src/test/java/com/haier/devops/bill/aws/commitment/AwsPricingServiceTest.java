package com.haier.devops.bill.aws.commitment;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.aws.commitment.sdk.AwsPricingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.services.pricing.model.DescribeServicesResponse;
import software.amazon.awssdk.services.pricing.model.GetProductsResponse;
import software.amazon.awssdk.services.pricing.model.Filter;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AwsPricingServiceTest extends BillCenterApplicationBaseTest {
    @Autowired
    private AwsPricingService pricingService;

    String accountName = "test";
    String regionStr = "eu-central-1";


    @Test
    public void testDescribeServices() {
//        String serviceCode = "AmazonRDS";
        String serviceCode = "AmazonElastiCache";
        DescribeServicesResponse serviceResp = pricingService.describeServices(accountName, regionStr, serviceCode, null, null, null);
        assertNotNull(serviceResp);
        assertTrue(serviceResp.sdkHttpResponse().isSuccessful());
    }

    /**
     * productFamily
     * instanceType
     * instanceFamily
     * tenancy
     * operatingSystem Linux/Unix
     * usagetype
     * operation
     * regionCode eu-central-1
     */
    @Test
    public void testGetAttributes() {
        String serviceCode = "AmazonRDS";
        String formatVersion = "aws_v1";
        String nextToken = null;
        Integer maxResults = 100;
        DescribeServicesResponse serviceResp = pricingService.describeServices(accountName, regionStr, serviceCode, formatVersion, nextToken, maxResults);
        assertNotNull(serviceResp);
        assertTrue(serviceResp.sdkHttpResponse().isSuccessful());
    }

    @Test
    public void testGetProducts() {
//        String serviceCode = "AmazonRDS";
        String serviceCode = "AmazonEC2";
        String formatVersion = "aws_v1";
        
        List<Filter> filters = new ArrayList<>();
        // Example filter: Get prices for general purpose t2.micro instances in US East (N. Virginia)
        // Note: Exact filter field names and values depend on the service and what you're looking for.
        // Common fields include: location, instanceType, termType, operatingSystem, tenancy, etc.
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("regionCode")
                .value("eu-central-1") // Location names can be found via DescribeServices attribute 'location'
                .build());
/*
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("instanceType")
                .value("db.m5.large")
                .build());
*/
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("tenancy")
                .value("shared")
                .build());
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("capacitystatus")
                .value("Used")
                .build());
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("operatingSystem")
                .value("Windows")
                .build());
        filters.add(Filter.builder()
                .type("TERM_MATCH")
                .field("preInstalledSw")
                .value("NA")
                .build());
        // Add more filters as needed, e.g., for termType (OnDemand, Reserved)
        
        String nextToken = null;
        Integer maxResults = 20;

        GetProductsResponse response = pricingService.getProducts(
                accountName, 
                regionStr, // clientRegion for the SDK client, Pricing API often uses us-east-1 or ap-south-1 endpoints
                serviceCode, 
                filters, 
                formatVersion, 
                nextToken, 
                maxResults);

        assertNotNull(response, "GetProductsResponse should not be null");
        assertTrue(response.sdkHttpResponse().isSuccessful(), "SDK HTTP Response should be successful");
        
        // Optional: Further assertions, e.g., checking if priceList is not empty
        // if (response.priceList() != null && !response.priceList().isEmpty()) {
        //     System.out.println("Found " + response.priceList().size() + " price list items.");
        //     // response.priceList().forEach(System.out::println);
        // } else {
        //     System.out.println("No price list items returned for the given filters or an issue occurred.");
        // }
    }

}