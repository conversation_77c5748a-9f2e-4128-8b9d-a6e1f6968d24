package com.haier.devops.bill.aws.commitment;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.aws.commitment.sdk.AwsReservedCacheNodeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.services.elasticache.model.DescribeReservedCacheNodesOfferingsResponse;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class AwsReservedCacheNodeServiceTest extends BillCenterApplicationBaseTest {
    @Autowired
    private AwsReservedCacheNodeService reservedCacheNodeService;

    String accountName = "test";
    String regionStr = "eu-central-1";

    @Test
    public void testDescribeReservedCacheNodesOfferings() {
        String duration = "********"; // 1 year in seconds
        String cacheNodeType = "cache.m5.large";

        DescribeReservedCacheNodesOfferingsResponse response = reservedCacheNodeService.describeReservedCacheNodesOfferings(
                accountName,
                regionStr,
                null,
                cacheNodeType,
                duration,
                "redis", // productDescription
                "No Upfront",
                100,
                null);

        assertNotNull(response);
    }
}
