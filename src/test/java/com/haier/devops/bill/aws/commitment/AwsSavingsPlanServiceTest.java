package com.haier.devops.bill.aws.commitment;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.aws.commitment.price.SavingPlanPriceQuery;
import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.services.savingsplans.model.*;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
class AwsSavingsPlanServiceTest extends BillCenterApplicationBaseTest {
    final static Long ONE_YEAR = 31536000L;
    final static Long THREE_YEARS = 94608000L;


    @Autowired
    AwsSavingsPlanService awsSavingsPlanService;

    @Autowired
    SavingPlanPriceQuery savingPlanPriceQuery;

    String accountName = "test";
    String regionStr = "ap-southeast-1";

    @Test
    void testDescribeSavingsPlansOfferings() {
        List<String> savingsPlansTypes = Collections.singletonList(SavingsPlanType.COMPUTE.toString());
        List<Long> durations = Collections.singletonList(ONE_YEAR);
        List<String> paymentOptions = Collections.singletonList("No Upfront");
        String productType = "EC2";

        DescribeSavingsPlansOfferingsResponse offeringsResponse =
                awsSavingsPlanService.describeSavingsPlansOfferings(
                        accountName,
                        regionStr,
                        productType,
                        null,
                        savingsPlansTypes,
                        durations,
                        paymentOptions,
                        null, "t2");

        assertNotNull(offeringsResponse);
        List<SavingsPlanOffering> savingsPlanOfferings = offeringsResponse.searchResults();
        assertNotNull(savingsPlanOfferings);
        assertTrue(savingsPlanOfferings.size() == 1);
    }

    @Test
    void testDescribeSavingsPlansOfferingRate() {
        String account = "GSOP";
        String region = "ap-southeast-1";
        String offeringId = "317445ce-ad79-4686-869c-284e656e23f1";
        String instanceType = "t2.micro";
        String tenancy = "shared";
        String os = "Linux/UNIX";

        DescribeSavingsPlansOfferingRatesResponse rateResponse =
                awsSavingsPlanService.describeSavingsPlansOfferingRates(
                        account,
                        region,
                        Collections.singletonList(offeringId),
                        Collections.singletonList(tenancy),
                        Collections.singletonList(os),
                        null,
                        Collections.singletonList(instanceType),
                        null,
                        null);

        List<SavingsPlanOfferingRate> rates = rateResponse.searchResults();
        if (rates == null || rates.isEmpty()) {
            log.error("no savings plan offering rate found: " +
                            "account={}, region={}, offeringId={}, instanceType={}, tenancy={}, os={}",
                    account,
                    region,
                    offeringId,
                    instanceType,
                    tenancy,
                    os);
        }

        if (rates.size() > 2) {
            log.error("more than one savings plan offering rate found: " +
                            "account={}, region={}, offeringId={}, instanceType={}, tenancy={}, os={}",
                    account,
                    region,
                    offeringId,
                    instanceType,
                    tenancy,
                    os
            );
        }

    }

    @Test
    void testSavingPlanPriceQuery() {
        List<ResourceUsageAgreementVo> agreements = savingPlanPriceQuery.queryPrice();
        assertNotNull(agreements);
    }


}