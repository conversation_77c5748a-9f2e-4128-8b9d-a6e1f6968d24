package com.haier.devops.bill.aws.commitment;

import com.haier.devops.bill.BillCenterApplicationBaseTest;
import com.haier.devops.bill.aws.commitment.sdk.AwsRdsReservedInstanceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.services.rds.model.DescribeReservedDbInstancesOfferingsResponse;

import static org.junit.jupiter.api.Assertions.assertNotNull;

class AwsRdsReservedInstanceServiceTest extends BillCenterApplicationBaseTest {
    @Autowired
    private AwsRdsReservedInstanceService reservedInstanceService;


    String accountName = "test";
    String regionStr = "eu-central-1";

    @Test
    public void testDescribeRdsReservedInstanceOfferings() {
        String duration = "********";
        String dbInstanceClass = "db.m5.large";

        DescribeReservedDbInstancesOfferingsResponse response = reservedInstanceService.describeRdsReservedInstanceOfferings(accountName,
                regionStr,
                null,
                dbInstanceClass,
                duration,
                "mysql", // productDescription，该参数必传
                "No Upfront",
                false,
                null,
                100,
                null);

        assertNotNull(response);
    }

}