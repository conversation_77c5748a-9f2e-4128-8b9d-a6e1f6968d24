package com.haier.devops.bill;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.common.api.*;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.service.AuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
public class FeignClientTest extends BillCenterApplicationBaseTest {
    @Autowired
    private HdsOpenApi hdsOpenApi;

    @Autowired
    private HcmsApi hcmsApi;

    @Autowired
    private HworkAuthorityApi hworkAuthorityApi;

    @Autowired
    private AuthorityService authorityService;

    @Autowired
    private HworkBillApi hworkBillApi;

    @Autowired
    private HworkAcceptanceApi hworkAcceptanceApi;

    @Test
    public void testQueryProjectsInfo() {
        HdsOpenApi.ResultWrapper result = hdsOpenApi.queryProjectsInfo();
        log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
        log.info(JSON.toJSONString(result));


    }

    @Test
    public void testQueryAlmProject() {
        String projectId = "S01594";
        HdsOpenApi.AlmProjectWrapper result = hdsOpenApi.queryAlmProject(projectId);
        log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
        log.info(result.toString());
    }

    @Test
    public void testQueryHaierUserInfo() {
        String userCode = "01488712";
        HcmsApi.ResultWrapper result = hcmsApi.queryHaierUserInfo(userCode);
        log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
        log.info(result.toString());
    }

    @Test
    public void testHworkGetAuthorities() {
        String userCode = "01478427";

        String domainCode = "szyhjsz";
        String dimensionCode = "Domain";


        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> userAuthorities = hworkAuthorityApi.getUserAuthorities(userCode, domainCode);
        log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
        log.info(JSON.toJSONString(userAuthorities));
    }

    @Test
    public void testHworkGetUserDetail() {
        String userCode = "01488712";

        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> user = hworkAuthorityApi.getUserDetail(userCode, true);
        log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
        log.info(JSON.toJSONString(user));
    }

    @Test
    public void testGetAuthorities() {
        String userCode = "01488712";
        List<String> authorizedScodes = authorityService.getAuthorizedScodes(userCode);
        if (CollectionUtils.isEmpty(authorizedScodes)) {
            return;
        }

        authorizedScodes.forEach(System.out::println);
    }

    @Test
    public void testGetOrgListByUserCode() {
        String userCode = "20026272";
        String domainCode = "T94594906";
        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> dataPrivilege =
                hworkAuthorityApi.getUserGroupDataPrivilege(userCode, domainCode);
        log.info("--> {}", JSONObject.toJSONString(dataPrivilege));
    }

    @Test
    public void testGetHdsSubProducts() {
        String userCode = "01488712";
        List<HdsSubProducts> authorizedSubProducts = authorityService.getAuthorizedSubProducts(userCode);
        if (CollectionUtils.isEmpty(authorizedSubProducts)) {
            return;
        }

        authorizedSubProducts.forEach(System.out::println);

    }

    @Test
    public void testGetAuthAppForIt() {
        String domain = null;
        List<String> productIds = null;
        String userCode = "01488712";
        ResultWrapper<HworkBillApi.AuthAppForItResponse> authAppForIt = hworkBillApi.getAuthAppForIt(
                new HworkBillApi.AuthAppForItRequest(domain, productIds, userCode));

        Assertions.assertNotNull(authAppForIt);

    }

    @Test
    public void testGetAlmMilestoneList() {
        ResultWrapper<List<Milestone>> milestoneList = hworkAcceptanceApi.getAlmMilestoneList();
        Assertions.assertNotNull(milestoneList);
    }

    public static void main(String[] args) {
        int size = 1048575;
        System.out.println(size / 1048576);
        System.out.println(size % 1048576);

    }
}
