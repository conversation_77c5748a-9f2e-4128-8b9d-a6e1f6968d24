package com.haier.devops.bill.monitor;

import com.haier.bigdata.automat.api.CacheType;
import com.haier.bigdata.automat.api.CommonReturnCode;
import com.haier.bigdata.automat.data.DataClient;
import com.haier.bigdata.automat.response.WebResponse;
import com.haier.devops.bill.BillCenterApplicationBaseTest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MonitorTest extends BillCenterApplicationBaseTest {
    @Autowired
    private DataClient dataClient;
    @Autowired
    private ServerUsage serverUsage;

    @Test
    void testMonitor() {
        Map<String,Object> args = new HashMap<>();
        String scode = "S01101,S03150";
        List<String> scodes = Arrays.asList(scode.split(","));
        //组装查询参数
        args.put("scodes", scodes);
//        WebResponse webResponse =  dataClient.executeJDBC("devops_jdbc_testhttp", args, CacheType.db_priority);
        WebResponse webResponse = dataClient.executeJDBCPage("devops_jdbc_testhttp", args, 1, 50, "id", CacheType.db_priority);

        Assertions.assertTrue(webResponse.getCode().equals(CommonReturnCode.APP_LIMIT));
    }

    @Test
    void testServerMonitor() {
        Map<String,Object> args = new HashMap<>();
        WebResponse webResponse = dataClient.executeJDBCPage("jdbc_server_monitor", args, 1, 10, "领域编码", CacheType.db_priority);
        Assertions.assertNotNull(webResponse);
    }

    @Test
    void testPersistServerUsageData() {
        serverUsage.persistServerUsage();
    }
}
