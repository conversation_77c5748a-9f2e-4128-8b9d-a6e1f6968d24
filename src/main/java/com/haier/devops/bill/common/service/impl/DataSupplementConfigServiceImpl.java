package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.DataSupplementConfig;
import com.haier.devops.bill.common.mapper.DataSupplementConfigMapper;
import com.haier.devops.bill.common.service.DataSupplementConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (DataSupplementConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07 15:09:58
 */
@Service("dataSupplementConfigService")
public class DataSupplementConfigServiceImpl extends ServiceImpl<DataSupplementConfigMapper, DataSupplementConfig> implements DataSupplementConfigService {
    @Resource
    private DataSupplementConfigMapper dataSupplementConfigDao;


}
