package com.haier.devops.bill.common.interceptor;

/**
 * <AUTHOR>
 */
public class LoginContextHolder {
    private static final ThreadLocal<User> userThreadLocal = new ThreadLocal<>();
    private static final ThreadLocal<String> namespaceScopeThreadLocal = new ThreadLocal<>();

    public static User getCurrentUser() {
        return userThreadLocal.get();
    }

    public static void setCurrentUser(User user) {
        userThreadLocal.set(user);
    }

    public static String getNamespaceScope() {
        return namespaceScopeThreadLocal.get();
    }

    public static void setNamespaceScope(String namespaceScope) {
        namespaceScopeThreadLocal.set(namespaceScope);
    }

    public static void clearContext() {
        userThreadLocal.remove();
        namespaceScopeThreadLocal.remove();
    }
}
