package com.haier.devops.bill.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: AggregatedBillVo
* @Description:  账单明细汇总
* @author: 张爱苹
* @date: 2024/1/12 14:01
*/
@Data
public class AggregatedBillVo implements Serializable {


    private static final long serialVersionUID = -5661681976024614454L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 聚合id
     *
     */
    private String aggregatedId;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账户id
     */
    private String accountId;

    /**
     * 云账号
     */
    private String accountName;

    /**
     * 产品编码
     *
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源实例id
     */
    private String instanceId;


    /**
     * S码
     */
    private String scode;


    /**
     * 项目编码
     */
    private String projectCode;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 账期
     */
    private String billingCycle;

    /**
     * 金额之和
     */
    private String summer;
    /**
     * 聚合粒度
     *
     */
    private String granularity;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}

