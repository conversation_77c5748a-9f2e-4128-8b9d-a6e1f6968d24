package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.BudgetAppRel;
import com.haier.devops.bill.common.mapper.BudgetAppRelMapper;
import com.haier.devops.bill.common.service.BudgetAppRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Service
@Slf4j
public class BudgetAppRelServiceImpl extends ServiceImpl<BudgetAppRelMapper, BudgetAppRel> implements BudgetAppRelService {
    private final BudgetAppRelMapper budgetAppRelMapper;

    public BudgetAppRelServiceImpl(BudgetAppRelMapper budgetAppRelMapper) {
        this.budgetAppRelMapper = budgetAppRelMapper;
    }

    @Override
    public BudgetAppRel findLatestByCodeAndVendor(String scode, String vendor) {
        // 根据scode和vendor查询，并按照账期降序排序，取第一条记录
        LambdaQueryWrapper<BudgetAppRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BudgetAppRel::getScode, scode)
                .eq(BudgetAppRel::getVendor, vendor)
                .eq(BudgetAppRel::getEnabled, 1)
                .orderByDesc(BudgetAppRel::getBillingCycle)
                .last("LIMIT 1");
        
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean checkAndAddBudgetAppRel(String scode, String vendor, String budgetCode, String billingCycle) {
        if (scode == null || vendor == null || budgetCode == null || billingCycle == null) {
            log.warn("检查预算配置关系时参数不完整: scode={}, vendor={}, budgetCode={}, billingCycle={}", scode, vendor, budgetCode, billingCycle);
            return false;
        }
        
        try {
            // 查询该scode和vendor最新的预算配置关系
            BudgetAppRel latestRel = findLatestByCodeAndVendor(scode, vendor);
            
            // 如果不存在关系，或者最新的预算编码与当前不一致，则添加新记录
            if (latestRel == null || !budgetCode.equals(latestRel.getBudgetCode())) {
                BudgetAppRel newRel = new BudgetAppRel();
                newRel.setScode(scode);
                newRel.setVendor(vendor);
                newRel.setBudgetCode(budgetCode);
                newRel.setBillingCycle(billingCycle);
                newRel.setCreateTime(LocalDateTime.now());
                newRel.setUpdateTime(LocalDateTime.now());
                newRel.setEnabled(1);
                
                return this.save(newRel);
            }
            
            return false; // 关系已存在且一致，无需添加
        } catch (Exception e) {
            log.error("检查并添加预算配置关系失败", e);
            return false;
        }
    }

    @Override
    public List<String> getBillingCycles(Integer id) {
        return budgetAppRelMapper.getBillingCycles(id);
    }
}
