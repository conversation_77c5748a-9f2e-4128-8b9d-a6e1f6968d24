package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.GroupCosts;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 开发组费用占比及金额 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
public interface GroupCostsMapper extends BaseMapper<GroupCosts> {

    @Select("select application_id, month, sum(costs) as costs" +
            " from va_t_group_costs" +
            " group by application_id")
    List<GroupCosts> getConfig();
}
