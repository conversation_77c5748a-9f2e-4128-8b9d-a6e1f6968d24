package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmLogDTO;
import com.haier.devops.bill.common.entity.CpAlarmLog;
import com.haier.devops.bill.common.service.AlarmLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* @ClassName: AlarmLogController
* @Description:  告警日志 控制类
* @author: 张爱苹
* @date: 2024/1/18 14:34
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/alarm/log")
public class AlarmLogController {

	@Autowired
	private AlarmLogService alarmLogService;

	/**
	 * 分页查询告警历史
	 *
	 * @return 分页查询告警历史
	 */
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<CpAlarmLog>> listByPage(AlarmLogDTO dto) {
		PageInfo<CpAlarmLog> pageInfo =
				alarmLogService.listByPage(dto);
		return new ResponseEntityWrapper<>(pageInfo);
	}


}
