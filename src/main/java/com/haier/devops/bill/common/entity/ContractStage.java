package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 合同验收阶段
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@TableName("bc_contract_stage")
@Data
public class ContractStage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 合同id
     */
    private Long contractId;

    private String stageName;

    private Date startTime;

    private Date endTime;

    private Date acceptanceDate;

    private String acceptanceState;

    private String detailUrl;
}
