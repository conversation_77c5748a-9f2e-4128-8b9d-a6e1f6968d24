package com.haier.devops.bill.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.EnterpriseProjectInfo;
import com.haier.devops.bill.common.mapper.EnterpriseProjectInfoMapper;
import com.haier.devops.bill.common.service.EnterpriseProjectInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
@Service
@DS("enterpriseinfo")
public class EnterpriseProjectInfoServiceImpl
        extends ServiceImpl<EnterpriseProjectInfoMapper, EnterpriseProjectInfo>
        implements EnterpriseProjectInfoService {
    private EnterpriseProjectInfoMapper enterpriseProjectInfoMapper;

    public EnterpriseProjectInfoServiceImpl(EnterpriseProjectInfoMapper enterpriseProjectInfoMapper) {
        this.enterpriseProjectInfoMapper = enterpriseProjectInfoMapper;
    }

    @Override
    public List<EnterpriseProjectInfo> selectByScodes(List<String> scodes) {
        return enterpriseProjectInfoMapper.selectList(
                new LambdaQueryWrapper<EnterpriseProjectInfo>().in(EnterpriseProjectInfo::getAppscode, scodes));
    }
}
