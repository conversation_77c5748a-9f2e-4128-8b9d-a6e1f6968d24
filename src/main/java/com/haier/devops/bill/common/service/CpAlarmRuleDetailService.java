package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.CpAlarmRuleDetail;
import com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo;

import java.util.List;
import java.util.Map;

/**
* @ClassName: CpAlarmRuleDetailService
* @Description:  告警规则明细表 服务类
* @author: 张爱苹
* @date: 2024/1/12 14:53
*/
public interface CpAlarmRuleDetailService extends IService<CpAlarmRuleDetail> {

    /**
    * @Description: 删除告警规则明细表
    * @author: 张爱苹
    * @date: 2024/1/15 10:35
    * @param id:
    * @Return: void
    */
    void deleteByCpAlarmConfigurationId(Integer id);

    /**
    * @Description:  获取告警规则明细表列表（到组级别）
    * @author: 张爱苹
    * @date: 2024/1/15 10:36
    * @param alarmLevelId:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.CpAlarmRuleDetail>
    */
    List<CpAlarmRuleDetailVo> getAlarmRuleDetailListByAlarmLevelId(Integer alarmLevelId);

    /**
    * @Description: 获取告警规则明细表列表(更新粒度)
    * @author: 张爱苹
    * @date: 2024/1/16 13:56
    * @param levelId:
    * @Return: java.util.List<com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo>
    */
    List<CpAlarmRuleDetailVo> queryAlarmRuleDetailListByAlarmLevelId(Map map);
}
