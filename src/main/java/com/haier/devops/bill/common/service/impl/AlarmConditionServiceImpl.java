package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.AlarmConditionDTO;
import com.haier.devops.bill.common.entity.AlarmCondition;
import com.haier.devops.bill.common.mapper.AlarmConditionMapper;
import com.haier.devops.bill.common.service.AlarmConditionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @ClassName: AlarmConditionServiceImpl
* @Description:  告警条件表 服务实现类
* @author: 张爱苹
* @date: 2024/1/31 10:23
*/
@Service
@Slf4j
public class AlarmConditionServiceImpl extends ServiceImpl<AlarmConditionMapper, AlarmCondition> implements AlarmConditionService {
    private Logger logger = LoggerFactory.getLogger(AlarmConditionServiceImpl.class);

    @Override
    public List<AlarmCondition> getAlarmConditionList(AlarmConditionDTO dto) {
        LambdaQueryWrapper<AlarmCondition> queryWrapper = new  LambdaQueryWrapper<>();
        if(!StringUtils.isEmpty(dto.getAlarmType())){
            queryWrapper.eq(AlarmCondition::getAlarmType,dto.getAlarmType());
        }
        return list(queryWrapper);
    }
}
