package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.mapper.RefinedRawBillMapper;
import com.haier.devops.bill.common.service.RefinedRawBillService;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (RefinedRawBill)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-29 10:54:41
 */
@Service("RefinedRawBillService")
public class RefinedRawBillServiceImpl extends ServiceImpl<RefinedRawBillMapper, RefinedRawBill> implements RefinedRawBillService {
    @Resource
    private RefinedRawBillMapper refinedRawBillMapper;

    @Override
    public PageInfo<BillingSummaryVo> getBillingSummaryData(String vendor, String accountName, String billingCycle, int page, int perPage) {
        return PageHelper
                .startPage(page, perPage)
                .doSelectPageInfo(
                        () -> refinedRawBillMapper.getBillingSummaryData(vendor, accountName, billingCycle)
               );
    }

    @Override
    public Integer getBillingSummaryCount(String vendor, String accountName, String billingCycle) {
        return refinedRawBillMapper.getBillingSummaryCount(vendor, accountName, billingCycle);
    }

    @Override
    public PageInfo<String> getPendingAggregatedId(String vendor, String accountName, String billingCycle, int page, int perPage) {
        return PageHelper
                .startPage(page, perPage)
                .doSelectPageInfo(
                        () -> refinedRawBillMapper.getPendingAggregatedId(vendor, accountName, billingCycle)
               );
    }

    @Override
    public List<BillingSummaryVo> getBillingSummaryDataWithInAggregatedId(SqlSession sqlSession, String vendor, String accountName, String billingCycle, List<String> aggregatedIds) {
        RefinedRawBillMapper mapper = sqlSession.getMapper(RefinedRawBillMapper.class);
        return mapper.getSummaryDataWithAggregatedIds(vendor, accountName, billingCycle, aggregatedIds);
    }
}
