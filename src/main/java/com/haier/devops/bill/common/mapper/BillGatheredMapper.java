package com.haier.devops.bill.common.mapper;

import com.haier.devops.bill.common.vo.BillGatheredVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
* @ClassName: BillGatheredMapper
* @Description:  每日账单汇总Mapper
* @author: 张爱苹
* @date: 2024/1/16 09:25
*/
@Repository
public interface BillGatheredMapper{

    /**
    * @Description: 获取前两日账单汇总数据
    * @author: 张爱苹
    * @date: 2024/1/16 09:27
    * @param map:
    * @Return: java.util.List<java.util.Map>
    */
    List<BillGatheredVo> getDailyCostInceaseAlarm(Map map);

    /**
    * @Description: 获取配置
    * @author: 张爱苹
    * @date: 2025/9/1 13:41
    * @param vendor:
    * @Return: java.util.List<java.util.Map>
    */
    List<Map> getConfigDetialList(String vendor);

    /**
    * @Description: 获取前两日账单汇总数据
    * @author: 张爱苹
    * @date: 2025/9/1 13:43
    * @param map:
    * @Return: java.util.List<com.haier.devops.bill.common.vo.BillGatheredVo>
    */
    List<BillGatheredVo> getDailyCostInceaseAlarmV2(Map map);
}
