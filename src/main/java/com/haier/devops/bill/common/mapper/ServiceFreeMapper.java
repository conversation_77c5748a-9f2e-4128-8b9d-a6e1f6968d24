package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.ServiceFreeCreateDTO;
import com.haier.devops.bill.common.dto.ServiceFreeDTO;
import com.haier.devops.bill.common.entity.ServiceFree;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: ServiceFreeMapper
* @Description: 服务费汇率
* @author: 张爱苹
* @date: 2024/3/8 13:19
*/
@Repository
public interface ServiceFreeMapper extends BaseMapper<ServiceFree> {

    List<ServiceFree> listByPage(@Param("dto") ServiceFreeDTO dto);

    ServiceFree queryTotalCycle(@Param("dto") ServiceFreeCreateDTO dto);

    /**
     * 查找有重叠的数据
     * @param dto
     * @return
     */
    List<ServiceFree> findOverlapItems(ServiceFreeCreateDTO dto);
}
