package com.haier.devops.bill.common.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ResourceInstance;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;

import java.util.List;
import java.util.Map;


/**
* @ClassName: ResourceInstanceService
* @Description: 资源实例
* @author: 张爱苹
* @date: 2024/3/12 14:24
*/
public interface ResourceInstanceService extends IService<ResourceInstance> {

    /**
    * @Description: 获取商品编码列表
    * @author: 张爱苹
    * @date: 2024/3/12 14:28
    * @param ruleFieldValueDTO:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getCommodityCodeList(String accountName);

    /**
    * @Description:  分页查询实例
    * @author: 张爱苹
    * @date: 2024/3/12 15:58
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CmdbProductOverview>
    */
    PageInfo<CmdbProductOverview> listByPage(ResourceInstanceDTO dto);

    /**
    * @Description: 获取待更新的实例信息
    * @author: 张爱苹
    * @date: 2024/3/19 15:10
    * @param aggregatedIdList:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.ResourceInstance>
    */
    ResourceInstanceVo queryResourceInstanceList(CmdbProductOverview cmdbProductOverviewList);

    /**
    * @Description: 获取实例信息
    * @author: 张爱苹
    * @date: 2024/3/20 10:15
    * @param resourceInstance:
    * @Return: com.haier.devops.bill.common.entity.ResourceInstance
    */
    ResourceInstance getResourceInstance(ResourceInstance resourceInstance);

    /**
    * @Description: 根据账号查实例信息
    * @author: 张爱苹
    * @date: 2024/3/21 10:25
    * @param accountIdentify:
    * @Return: java.util.List<java.lang.String>
    */
    List<Map> queryInfoByAccountId(String accountIdentify);

    void updateCostUnit(List<ResourceInstanceVo> resultList, String finalUnitName, Long finalUnitId);
}

