package com.haier.devops.bill.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.Expose;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 到资源的汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@TableName("bc_resource_aggregation")
@Data
public class ResourceAggregation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Expose(serialize = false)
    @JsonIgnore
    private Integer id;

    /**
     * 1:云资源，2:云桌面
     */
    @Expose
    private Integer billType;

    /**
     * 账期
     */
    @Expose
    @ExcelProperty("账期")
    private String payDate;

    /**
     * 系统编码（云资源：S码，云桌面：产业/平台编码）
     */
    @Expose
    @ExcelProperty("产业编码")
    private String sysCode;

    /**
     * 系统名称（云资源：子产品名称，云桌面：产业/平台）
     */
    @Expose
    @ExcelProperty("产业名称")
    private String sysName;

    @Expose
    @ExcelProperty(value = "费用金额(元)", index = 6)
    private BigDecimal costAmount;

    /**
     * 云厂商
     */
    @Expose
    private String factory;

    /**
     * 云账号
     */
    @Expose
    private String account;

    /**
     * 云产品名称
     */
    @Expose
    private String productName;

    @Expose
    private String instanceId;

    /**
     * 分拆项目
     */
    @Expose
    private String splitItem;

    /**
     * ip地址/域名
     */
    @Expose
    private String address;

    /**
     * 消费类型
     */
    @Expose
    private String costType;

    /**
     * 服务项
     */
    @Expose
    @ExcelProperty("服务项")
    private String serviceItem;

    /**
     * 服务内容
     */
    @Expose
    @ExcelProperty("服务内容")
    private String serviceContent;

    /**
     * 数量
     */
    @Expose
    @ExcelProperty("数量")
    private Integer serviceCount;

    /**
     * 同步状态
     */
    @Expose(serialize = false)
    @JsonIgnore
    private Integer status;
}
