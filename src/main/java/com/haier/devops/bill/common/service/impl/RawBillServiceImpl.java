package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.RawBill;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.mapper.RawBillMapper;
import com.haier.devops.bill.common.service.RawBillService;
import com.haier.devops.bill.common.service.RefinedRawBillService;
import com.haier.devops.bill.common.vo.ColumnValueVo;
import com.haier.devops.bill.common.vo.CostProcedureVo;
import com.haier.devops.bill.util.DateUtil;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
@Service
public class RawBillServiceImpl extends ServiceImpl<RawBillMapper, RawBill> implements RawBillService {
    private RawBillMapper rawBillMapper;
    @Autowired
    private RefinedRawBillService refinedRawBillService;
    private Lock lock = new ReentrantLock();
    @Resource
    Executor localBootAsyncExecutor;

    public RawBillServiceImpl(RawBillMapper rawBillMapper) {
        this.rawBillMapper = rawBillMapper;
    }

    @Override
    public PageInfo<RawBill> selectPage(QueryWrapper<RawBill> queryWrapper, int page, int perPage) {
        return PageHelper.startPage(page, perPage).doSelectPageInfo(() -> rawBillMapper.selectList(queryWrapper));
    }

    @Override
    public List<RawBill> selectList(QueryWrapper<RawBill> queryWrapper) {
        return rawBillMapper.selectList(queryWrapper);
    }

    @Override
    public List<ColumnValueVo> selectColumnValueCountByQueryWrapper(QueryWrapper<RawBill> queryWrapper, String column) {
        List<ColumnValueVo> list = new ArrayList<>();
        queryWrapper.select(column + " as value, count(1) as count");
        queryWrapper.groupBy(column);
        List<Map<String, Object>> mapList = getBaseMapper().selectMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapList)) {
            return list;
        }

        for (Map<String, Object> map : mapList) {
            if (null != map) {
                Object valueObj = map.get("value");
                Object countObj = map.get("count");

                if (null != valueObj) {
                    String value = String.valueOf(valueObj);
                    Integer count = null != countObj ? Integer.parseInt(String.valueOf(countObj)) : 0;
                    list.add(new ColumnValueVo(value, count));
                }
            }
        }

        return list;
    }

    @Override
    public PageInfo<CostProcedureVo> selectCostProcedure(String vendor, String costUnit, String productCode,
                                                         String billingCycle, int page, int perPage) {
        String startBillingCycle = billingCycle + "-01";
        String endBillingCycle = DateUtil.getNextMonthOf (billingCycle) + "-01";
        return PageHelper
                .startPage(page, perPage)
                .doSelectPageInfo(
                        () -> rawBillMapper
                                .selectCostProcedure(vendor, costUnit, productCode, startBillingCycle, endBillingCycle)
                );
    }

    public void updateRawBills(List<RawBill> list){
        List<List<RawBill>> partition = ListUtils.partition(list, 500);  // 将list分割成每个包含500个RawBill的列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();  // 创建一个存储CompletableFuture的列表
        for (List<RawBill> rawBillList : partition) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
//                        rawBillList.forEach(rawBill -> {
//                            updateById(rawBill);  // 更新RawBill对象中的数据
//                        });
                    rawBillMapper.updateBatchById(rawBillList);  // 更新RawBill列表中的数据
                } catch (Exception e) {
                    e.printStackTrace();  // 打印异常信息
                }
            });  // 使用localBootAsyncExecutor执行上述任务
            futures.add(future);  // 将任务future添加到列表中
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();  // 等待所有子线程的任务执行完毕
    }

    @Override
    public RefinedRawBill getResourceId(String aggregatedId) {
        LambdaQueryWrapper<RefinedRawBill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RefinedRawBill::getAggregatedId,aggregatedId);
        lambdaQueryWrapper.apply("(sub_product_code is null or sub_product_code = '')");
        lambdaQueryWrapper.last("limit 1");
        return refinedRawBillService.list(lambdaQueryWrapper).get(0);
    }

}

