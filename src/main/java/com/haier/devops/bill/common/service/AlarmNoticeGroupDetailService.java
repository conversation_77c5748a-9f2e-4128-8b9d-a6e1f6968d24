package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupDetailVo;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupDetailService
* @Description:  告警通知组明细表 服务类
* @author: 张爱苹
* @date: 2024/1/15 09:16
*/
public interface AlarmNoticeGroupDetailService extends IService<AlarmNoticeGroupDetail> {

    /**
    * @Description:  删除告警通知组明细表
    * @author: 张爱苹
    * @date: 2024/1/15 09:25
    * @param id:
    * @Return: void
    */
    void deleteAlarmNoticeGroupDetailByGroupId(Integer id);

    /**
    * @Description:   根据告警通知组id查询告警通知组明细表
    * @author: 张爱苹
    * @date: 2024/1/15 09:50
    * @param id:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail>
    */
    List<AlarmNoticeGroupDetailVo> getAlarmNoticeGroupDetailListByGroupId(Integer id);

    /**
    * @Description: 根据告警通知组id查询告警人员账号列表
    * @author: 张爱苹
    * @date: 2024/2/1 14:55
    * @param id:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getAccountListByGroupId(Integer id);
}
