package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.vo.MilestoneVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 云服务项目（ALM）的里程碑，包括所属项目和合同等信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
public interface MilestoneMapper extends BaseMapper<Milestone> {
    /**
     * 根据云厂商和账期查询里程碑
     * @param vendor
     * @param billingCycle
     * @return
     */
    List<MilestoneVo> queryByVendorAndBillingCycle(@Param("vendor") String vendor, @Param("billingCycle") String billingCycle);
}
