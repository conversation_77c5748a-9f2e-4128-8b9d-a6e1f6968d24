package com.haier.devops.bill.common.mapper;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 节点 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface NodeMapper {

    /**
     * 获取月份消费
     * @param date
     * @return
     */
    double getConsumptionAnalysis(@Param("date")String date, @Param("scodes")List<String> scodes);
    double forecastForTheMonth(@Param("scodes")List<String> scodes);

    /**
     * 获取上一年度总消费
     * @param scodes
     * @return
     */
    double getLastYearTotalAmount(@Param("scodes")List<String> scodes);

    List<Map<String, Object>> getConsumerTrends(@Param("vendors") List<String> vendors,
                                                @Param("start")String start,
                                                @Param("end")String end,
                                                @Param("scodes") List<String> scodes,
                                                @Param("type") String type,
                                                @Param("currencies") String... currencies);

    List<Map<String, Object>> getConsumerTrendsAll(@Param("vendors") List<String> vendors,
                                                @Param("start")String start,
                                                @Param("end")String end,
                                                @Param("scodes") List<String> scodes,
                                                @Param("type") String type,
                                                @Param("exchangeRate") BigDecimal exchangeRate);


    Map<String, Integer> getMainAssets(@Param("scodes") List<String> scodes,
                                       @Param("vendors") List<String> vendors,
                                       @Param("currencies") String... currencies);


    List<Map<String, Object>> getConsumptionStepByStep(@Param("start")String start,
                                                       @Param("end")String end,
                                                       @Param("scodes") List<String> scodes,
                                                       @Param("filter")String filter,
                                                       @Param("type") String type,
                                                       @Param("vendors") List<String> vendors,
                                                       @Param("currencies") String... currencies);

    List<Map<String, Object>> getConsumptionStepByStepAll(@Param("start")String start,
                                                       @Param("end")String end,
                                                       @Param("scodes") List<String> scodes,
                                                       @Param("filter")String filter,
                                                       @Param("type") String type,
                                                       @Param("vendors") List<String> vendors,
                                                       @Param("exchangeRate") BigDecimal exchangeRate);

    Map<String, Integer> getMainAssetsAll(@Param("scodes") List<String> scodes,
                                          @Param("vendors") List<String> vendors);

    List<Map> getEcsDetail(@Param("scodes") List<String> scodes,
                           @Param("vendors") List<String> vendors, @Param("startDate") String startDate, @Param("endDate") String endDate);

    List<Map> getDbDetail(@Param("scodes") List<String> scodes,
                          @Param("vendors") List<String> vendors, @Param("startDate") String startDate, @Param("endDate") String endDate);

    List<Map> getMwDetail(@Param("scodes") List<String> scodes,
                          @Param("vendors") List<String> vendors, @Param("startDate") String startDate, @Param("endDate") String endDate);
}
