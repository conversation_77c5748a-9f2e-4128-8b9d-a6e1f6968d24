package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * (DataSupplementConfig)实体类
 *
 * <AUTHOR>
 * @since 2023-12-07 15:09:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "$tableInfo.comment")
public class DataSupplementConfig implements Serializable {
    private static final long serialVersionUID = 482808431805705777L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;


    @Schema(description = "云厂商")
    private String vendor;


    @Schema(description = "产品编码")
    private String productCode;


    @Schema(description = "类名")
    private String className;


    @Schema(description = "方法名")
    private String methodName;

    @Schema(description = "备注")
    private String remark;


    @Schema(description = "创建时间")
    private Date createTime;


    @Schema(description = "创建人账号")
    private String createBy;


    @Schema(description = "更新时间")
    private Date updateTime;


    @Schema(description = "更新人账号")
    private String updateBy;


    @Schema(description = "删除标识 0：有效 1：无效")
    private String delFlag;

    @Schema(description = "排序")
    private Integer sort;


}

