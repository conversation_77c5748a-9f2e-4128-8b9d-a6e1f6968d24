package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.BudgetConfig;
import com.haier.devops.bill.common.vo.BudgetConfigVo;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
public interface BudgetConfigService extends IService<BudgetConfig> {
    /**
     * 根据S码查询预算配置
     * @param vendor
     * @param scodes
     * @return
     */
    List<BudgetConfigVo> queryByScodesAndBillingCycle(String vendor, Set<String> scodes);

    /**
     * 查询预算配置列表
     * @param subProductId
     * @param budgetCode
     * @param userCode
     * @param vendor
     * @param page
     * @param pageSize
     * @return
     */
    PageInfo<BudgetConfigVo> queryList(String subProductId,
                                       String budgetCode,
                                       String userCode,
                                       String vendor,
                                       int page,
                                       int pageSize
    );
}