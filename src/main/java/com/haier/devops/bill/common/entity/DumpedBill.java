package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@TableName("bc_dumped_bill")
@Data
public class DumpedBill implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账户名
     */
    private String accountName;

    /**
     * S码
     */
    private String scode;

    /**
     * 系统名称
     */
    private String sysName;

    /**
     * 账单开始时间
     */
    private String startTime;

    /**
     * 账单结束时间
     */
    private String endTime;

    /**
     * 预估金额
     */
    private String estimatedAmount;

    /**
     * 实际金额
     */
    private String actualAmount;

    /**
     * 预算编码
     */
    private String budgetCode;

    /**
     * 负责人工号（系统负责人/产业负责人） 
     */
    private String userCode;

    /**
     * 负责人姓名（系统负责人/产业负责人） 
     */
    private String userName;

    /**
     * alm里程碑id 
     */
    private String almMilestoneId;

    /**
     * 里程碑名称 
     */
    private String milestoneName;

    /**
     * 所属项目编码 
     */
    private String projectCode;

    /**
     * 所属项目名称 
     */
    private String projectName;

    /**
     * 所属合同编码 
     */
    private String contractCode;

    /**
     * 所属合同名称 
     */
    private String contractName;

    /**
     * 供应商V码 
     */
    private String supplierCode;

    /**
     * 供应商名称 
     */
    private String supplierName;

    /**
     * 账单生成时间 
     */
    private LocalDateTime generationTime;

}
