package com.haier.devops.bill.common.config;

import com.haier.devops.bill.common.api.HcmsApi;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

@Configuration
public class HcmsOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private static final String AUTHORIZATION_KEY = "X-API-KEY";

    private okhttp3.OkHttpClient hcmsOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    private HcmsUserConfigProperties hcmsUserConfigProperties;

    public HcmsOkHttpFeignConfiguration(OkHttpProperties properties,
                                        OkHttpClientFactory httpClientFactory,
                                        OkHttpClientConnectionPoolFactory connectionPoolFactory,
                                        HcmsUserConfigProperties hcmsUserConfigProperties) {
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
        this.hcmsUserConfigProperties = hcmsUserConfigProperties;
    }

    @Bean
    public HcmsApi hcmsApi() {
        this.hcmsOkHttp3Client =
                setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);

        this.hcmsOkHttp3Client = this.hcmsOkHttp3Client.newBuilder()
                .addInterceptor(authenticationInterceptor(AUTHORIZATION_KEY, hcmsUserConfigProperties.getToken()))
                .build();

        return Feign.builder()
                .client(new OkHttpClient(hcmsOkHttp3Client))
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .target(HcmsApi.class, hcmsUserConfigProperties.getUrl());
    }

    @PreDestroy
    public void destroy() {
        super.destroy(this.hcmsOkHttp3Client);
    }

    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "api.hcms.user")
    public static class HcmsUserConfigProperties {
        private String token;
        private String url;
    }
}
