package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;
import com.haier.devops.bill.common.mapper.PendingSubstitutionBillMapper;
import com.haier.devops.bill.common.service.PendingSubstitutionBillService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 待替换账单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Service
public class PendingSubstitutionBillServiceImpl extends ServiceImpl<PendingSubstitutionBillMapper, PendingSubstitutionBill> implements PendingSubstitutionBillService {
    private final PendingSubstitutionBillMapper pendingSubstitutionBillMapper;

    public PendingSubstitutionBillServiceImpl(PendingSubstitutionBillMapper pendingSubstitutionBillMapper) {
        this.pendingSubstitutionBillMapper = pendingSubstitutionBillMapper;
    }

    @Override
    public int clearDirtyData(String subTaskId) {
        return pendingSubstitutionBillMapper.delete(
                new LambdaQueryWrapper<PendingSubstitutionBill>()
                        .eq(PendingSubstitutionBill::getSubTaskId, subTaskId));
    }

    @Override
    public List<PendingSubstitutionBill> selectBySubTaskId(String subTaskId) {
        return pendingSubstitutionBillMapper.selectList(
                new LambdaQueryWrapper<PendingSubstitutionBill>()
                        .eq(PendingSubstitutionBill::getSubTaskId, subTaskId)
        );
    }

    @Override
    public String getSumBySubTaskId(String subTaskId) {
        return pendingSubstitutionBillMapper.getSumBySubTaskId(subTaskId);
    }
}
