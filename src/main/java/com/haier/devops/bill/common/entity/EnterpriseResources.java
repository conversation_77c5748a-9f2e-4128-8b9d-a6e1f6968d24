package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: EnterpriseProjects
* @Description: 企业项目资源
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
@TableName("bc_enterprise_resources")
@Data
@Builder
public class EnterpriseResources implements Serializable {

    private static final long serialVersionUID = 4620145504503569337L;

    private Long id;

    private String enterpriseProjectId;

    private String projectId;

    private String projectName;

    private String resourceId;

    private String resourceName;

    private String resourceType;

    private String delFlag;

    private String accountName;

}
