package com.haier.devops.bill.common.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.mapper.HdsSubProductsMapper;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.service.HdsSubProductsService;
import com.haier.devops.bill.common.vo.DomainSubProductVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
* @ClassName: HdsSubProductsServiceImpl
* @Description: hds子产品表 服务实现类
* @author: 张爱苹
* @date: 2024/1/11 17:58
*/
@Service
public class HdsSubProductsServiceImpl extends ServiceImpl<HdsSubProductsMapper, HdsSubProducts> implements HdsSubProductsService {

    @Autowired
    private HdsSubProductsMapper hdsSubProductsMapper;
    @Resource
    private HdsOpenApi hdsOpenApi;


    @Autowired
    private AuthorityService authorityService;

    @Override
    public List<String> getAllAppScodeList() {
        return hdsSubProductsMapper.getAllAppScodeList();
    }

    @Override
    public List<HdsSubProducts> getHdsSubProductList(String searchContent) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(!StringUtils.isEmpty(searchContent)){
            queryWrapper.apply("del_flag = '0' and (app_scode like '%"+searchContent+"%' or app_short_name like '%"+searchContent+"%' or app_name like '%"+searchContent+"%')");
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<HdsSubProducts> getAgentHdsSubProductList(String searchContent) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(!StringUtils.isEmpty(searchContent)){
            queryWrapper.apply("del_flag = '0' and (app_scode = '"+searchContent+"' or app_short_name = '"+searchContent+"' or app_name = '"+searchContent+"')");
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdateBatchByParams(List<HdsSubProducts> list) {

        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {//这里主要是查询唯一约束对应的记录是否存在
            LambdaQueryWrapper<HdsSubProducts> queryWrapper = Wrappers.<HdsSubProducts>lambdaQuery()
                    .eq(HdsSubProducts::getAppScode, entity.getAppScode());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            // Set update time to current time
            entity.setUpdateTime(new Date());

            LambdaUpdateWrapper<HdsSubProducts> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(HdsSubProducts::getAppScode, entity.getAppScode());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public List<HdsSubProducts> queryList(List<String> appScodeList) {
        return baseMapper.selectList(new QueryWrapper<HdsSubProducts>().in("app_scode", appScodeList).eq("del_flag", "0"));
    }

    @Override
    public List<DomainSubProductVo> getDomainSubProductVoList(List<String> domainIds) {
        return hdsSubProductsMapper.getDomainSubProductVoList(domainIds);
    }

    @Override
    public List<DomainSubProductVo> getAllDomainSubProductVoList() {
        return hdsSubProductsMapper.getAllDomainSubProductVoList();
    }

    public HdsSubProducts queryOne(String scode) {
        return baseMapper.selectOne(new QueryWrapper<HdsSubProducts>().eq("app_scode", scode));
    }

    @Override
    public List<HdsSubProducts> searchDomain(String query) {
        return hdsSubProductsMapper.searchDomain(StringUtils.isNotBlank(query) ? query.trim() : null);
    }

    public List<HdsOpenApi.Domain> getDomainList() {
        HdsOpenApi.DomainWrapper domainWrapper = hdsOpenApi.getDomainList();
        if (null == domainWrapper || domainWrapper.getCode() != 200) {
            throw new RuntimeException("查询领域信息失败");
        }
        List<HdsOpenApi.Domain> domainList = domainWrapper.getData();
        return domainList;
    }

    @Override
    public List<Map> getAccountByScode(String appScode) {
        HdsSubProducts hdsSubProducts = queryOne(appScode);
        if(hdsSubProducts == null){
            return null;
        }
        List<Map> list = new ArrayList();
        //系统负责人
        Map map = new HashMap();
        map.put("role", "系统负责人");
        map.put("account", hdsSubProducts.getOwner());
        map.put("phone",authorityService.getUserPhoneByUserCode(hdsSubProducts.getOwner()));
        list.add(map);
        //获取二线运维
        JsonObject jsonObject = new JsonObject();
        JsonArray jsonArray = new JsonArray();
        jsonArray.add("group_som_owner");
        jsonObject.add("groupRoleCodes", jsonArray);
        jsonObject.addProperty("pageIndex", 1);
        // 先默认为10
        jsonObject.addProperty("pageSize", 10);
        jsonObject.addProperty("scode", appScode);
        JSONObject result =hdsOpenApi.getTeamMember(jsonObject);
        if(result.getDouble("code") == 200.0){
            JSONObject data = result.getJSONObject("data");
            if(data.getDouble("total") > 0.0){
                JSONArray array = data.getJSONArray("data");
                for (int i = 0; i < array.size(); i++) {
                    Map pxp = new HashMap();
                    pxp.put("role", "二线运维");
                    String account = array.getJSONObject(i).getString("userCode");
                    pxp.put("account", account);
                    pxp.put("phone",authorityService.getUserPhoneByUserCode(account));
                    list.add(pxp);
                }
            }
        }
        return list;
    }
}
