package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.bigdata.automat.api.CacheType;
import com.haier.bigdata.automat.data.DataClient;
import com.haier.bigdata.automat.response.WebResponse;
import com.haier.devops.bill.common.entity.ServerUsageEntity;
import com.haier.devops.bill.common.mapper.ServerUsageMapper;
import com.haier.devops.bill.common.service.ServerUsageService;
import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务器使用效能服务实现类
 */
@Service
@Slf4j
public class ServerUsageServiceImpl extends ServiceImpl<ServerUsageMapper, ServerUsageEntity> implements ServerUsageService {

    private final DataClient dataClient;

    public ServerUsageServiceImpl(DataClient dataClient) {
        this.dataClient = dataClient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult<Boolean> syncServerUsageData() {
        try {
            log.info("开始同步服务器使用效能数据");
            LocalDate today = LocalDate.now();

            // 记录总数和已处理数
            int totalCount = 0;
            int processedCount = 0;
            int updatedCount = 0;
            int insertedCount = 0;

            // 分页查询数据
            int pageSize = 200;
            int currentPage = 1;
            boolean hasMoreData = true;

            while (hasMoreData) {
                Map<String, Object> args = new HashMap<>();
                String orderColumn = "领域编码";

                // 调用API获取数据
                WebResponse webResponse = dataClient.executeJDBCPage(
                    "jdbc_server_monitor",
                    args,
                    Integer.valueOf(currentPage),
                    Integer.valueOf(pageSize),
                    orderColumn,
                    CacheType.db_priority
                );

                if (webResponse == null || webResponse.getCode() != 200 || webResponse.getData() == null) {
                    log.error("获取服务器使用效能数据失败: {}", webResponse != null ? webResponse.getMessage() : "响应为空");
                    ServiceResult<Boolean> failResult = new ServiceResult<>();
                    failResult.error("获取服务器使用效能数据失败");
                    return failResult;
                }

                // 解析返回的数据
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) webResponse.getData();

                @SuppressWarnings("unchecked")
                Map<String, Object> listMap = (Map<String, Object>) dataMap.get("list");

                if (listMap == null) {
                    log.error("服务器使用效能数据格式异常: list为空");
                    ServiceResult<Boolean> failResult = new ServiceResult<>();
                    failResult.error("服务器使用效能数据格式异常");
                    return failResult;
                }

                // 获取总记录数
                Integer total = (Integer) listMap.get("total");
                if (total == null || total == 0) {
                    log.info("没有服务器使用效能数据需要同步");
                    return new ServiceResult<>(true);
                }

                if (totalCount == 0) {
                    totalCount = total;
                    log.info("服务器使用效能数据总记录数: {}", totalCount);
                }

                // 获取当前页的数据
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) listMap.get("data");

                if (dataList == null) {
                    log.warn("当前页数据为空，停止获取");
                    hasMoreData = false;
                    continue;
                }

                // 转换数据并处理
                LocalDateTime now = LocalDateTime.now();
                for (Map<String, Object> item : dataList) {
                    ServerUsageEntity entity = convertToEntity(item);
                    entity.setDay(today);

                    // 查询是否已存在记录
                    ServerUsageEntity existingEntity = getExistingEntity(entity.getDay(), entity.getCloudVendor(), entity.getInstanceId());

                    if (existingEntity != null) {
                        // 更新已有记录
                        entity.setId(existingEntity.getId());
                        entity.setCreateTime(existingEntity.getCreateTime());
                        entity.setUpdateTime(now);
                        updateById(entity);
                        updatedCount++;
                    } else {
                        // 插入新记录
                        entity.setCreateTime(now);
                        entity.setUpdateTime(now);
                        save(entity);
                        insertedCount++;
                    }

                    processedCount++;
                }

                log.info("已处理服务器使用效能数据: {}/{} (第{}页), 新增: {}, 更新: {}",
                        processedCount, totalCount, currentPage, insertedCount, updatedCount);

                // 判断是否还有更多数据
                // 1. 如果当前页数据为空，则没有更多数据
                // 2. 如果已处理的记录数已经达到或超过总记录数，则没有更多数据
                // 3. 如果当前页数据量小于页大小，说明已经是最后一页
                if (dataList.isEmpty() || processedCount >= totalCount || dataList.size() < pageSize) {
                    hasMoreData = false;
                    log.info("已到达最后一页，停止获取数据");
                } else {
                    currentPage++;
                    log.info("继续获取第{}页数据", currentPage);
                }
            }

            log.info("服务器使用效能数据同步完成，共处理 {} 条记录，新增: {}, 更新: {}",
                    processedCount, insertedCount, updatedCount);
            return new ServiceResult<>(true);

        } catch (Exception e) {
            log.error("同步服务器使用效能数据异常", e);
            ServiceResult<Boolean> failResult = new ServiceResult<>();
            failResult.error("同步服务器使用效能数据异常: " + e.getMessage());
            return failResult;
        }
    }

    @Override
    public Integer getResourceUsageQualified(String scode, String resourceUsageQualified) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);
        return baseMapper.getResourceUsageQualified(validScodes, resourceUsageQualified);
    }

    /**
     * 根据日期、云厂商和实例ID查询已存在的记录
     */
    private ServerUsageEntity getExistingEntity(LocalDate day, String cloudVendor, String instanceId) {
        LambdaQueryWrapper<ServerUsageEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServerUsageEntity::getDay, day)
                .eq(ServerUsageEntity::getCloudVendor, cloudVendor)
                .eq(ServerUsageEntity::getInstanceId, instanceId);
        return getOne(queryWrapper);
    }

    /**
     * 将API返回的数据转换为实体对象
     */
    private ServerUsageEntity convertToEntity(Map<String, Object> data) {
        ServerUsageEntity entity = new ServerUsageEntity();

        entity.setDomain(getStringValue(data, "领域"));
        entity.setDomainOwner(getStringValue(data, "领域负责人"));
        entity.setProductName(getStringValue(data, "产品名称"));
        entity.setSubProductName(getStringValue(data, "子产品名称"));
        entity.setSubProductOwner(getStringValue(data, "子产品负责人"));
        entity.setSCode(getStringValue(data, "S码"));
        entity.setCloudVendor(getStringValue(data, "云厂商"));
        entity.setInstanceId(getStringValue(data, "服务器实例ID"));
        entity.setRegion(getStringValue(data, "所属区域"));
        entity.setInstanceIp(getStringValue(data, "服务器实例IP"));
        entity.setResourceUsageQualified(getStringValue(data, "资源利用率是否达标"));
        entity.setCpuUsageQualified(getStringValue(data, "CPU利用率达标"));
        entity.setCpuPeakUsage30days(getBigDecimalValue(data, "近30日CPU峰值利用率"));
        entity.setCpuCount(getStringValue(data, "CPU数量"));
        entity.setMemoryUsageQualified(getStringValue(data, "内存利用率达标"));
        entity.setMemoryPeakUsage30days(getBigDecimalValue(data, "近30日内存峰值利用率"));
        entity.setMemorySize(getStringValue(data, "内存数量"));
        entity.setIsRegistered(getStringValue(data, "是否备案"));
        entity.setRegistrationReason(getStringValue(data, "备案原因"));

        return entity;
    }

    /**
     * 安全地获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 安全地获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null || "-".equals(value.toString())) {
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            log.warn("转换BigDecimal失败: key={}, value={}", key, value);
            return null;
        }
    }
}
