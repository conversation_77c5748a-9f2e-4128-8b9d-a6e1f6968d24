package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.RcMiddlewareInfo;
import com.haier.devops.bill.common.mapper.RcMiddlewareInfoMapper;
import com.haier.devops.bill.common.service.RcMiddlewareInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (RcMiddlewareInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-12 14:00:02
 */
@Service("rcMiddlewareInfoService")
public class RcMiddlewareInfoServiceImpl extends ServiceImpl<RcMiddlewareInfoMapper, RcMiddlewareInfo> implements RcMiddlewareInfoService {

    @Override
    public List<RcMiddlewareInfo> getMiddlewareInfoByScodeList(List<String> scodeList) {
        LambdaQueryWrapper<RcMiddlewareInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RcMiddlewareInfo::getIsDeleted,0);
        queryWrapper.in(RcMiddlewareInfo::getScode,scodeList);
        return list(queryWrapper);
    }
}
