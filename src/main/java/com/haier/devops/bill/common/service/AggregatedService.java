package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.AggregatedDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.AggregatedBill;

/**
* @ClassName: AggregatedBillService
* @Description:  账单明细汇总表 服务类
* @author: 张爱苹
* @date: 2024/1/31 10:22
*/
public interface AggregatedService extends IService<AggregatedBill> {


    void executeReconciliation(AggregatedDTO aggregatedDTO, ResourceInstanceDTO resourceInstanceDTO,String vendor);

    void executeReconciliation(ReconciliationBatchDTO dto, AggregatedDTO aggregatedDTO,String vendor);
}
