package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HuaweiApi;
import com.haier.devops.bill.common.entity.EnterpriseProjects;
import com.haier.devops.bill.common.mapper.EnterpriseProjectsMapper;
import com.haier.devops.bill.common.service.EnterpriseProjectsService;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.CreateEnterpriseProjectResponse;
import com.huaweicloud.sdk.eps.v1.model.EnableEnterpriseProjectResponse;
import com.huaweicloud.sdk.eps.v1.model.EpDetail;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* @ClassName: EnterpriseProjectsServiceImpl
* @Description: 企业项目
* @author: 张爱苹
* @date: 2025/2/13 14:58
*/
@Service
public class EnterpriseProjectsServiceImpl
        extends ServiceImpl<EnterpriseProjectsMapper, EnterpriseProjects> implements EnterpriseProjectsService {
    private static Logger logger = LoggerFactory.getLogger(EnterpriseProjectsServiceImpl.class);
    @Autowired
    private HuaweiApi huaweiApi;


    @Override
    public void insertEnterpriseProjects(String accountName) throws Exception{
        EpsClient epsClient = huaweiApi.getEpsClient(accountName,"cn-north-4");
        Integer offset = 0;
        Integer limit = 1000;
        ListEnterpriseProjectResponse response = huaweiApi.listEnterpriseProjects(epsClient,offset,limit);
        if(response == null){
            throw new RuntimeException("获取企业项目失败");
        }
        int total = response.getTotalCount();
        List<String> idList = new ArrayList<>();
        if(total > 0){
            List<EpDetail> list = response.getEnterpriseProjects();
            int totalPage = total % limit == 0 ? total / limit : total / limit + 1;
            for(int i = 2;i < totalPage;i++){
                offset = (i - 1) * limit;
                response = huaweiApi.listEnterpriseProjects(epsClient,offset,limit);
                if(response == null){
                    logger.error("获取企业项目失败,{},{},{}",accountName,offset,limit);
                    continue;
                }
                list.addAll(response.getEnterpriseProjects());
            }
            List<EnterpriseProjects> addList = new ArrayList<>();
            List<EnterpriseProjects> updateList = new ArrayList<>();

            list.stream().forEach(item->{
                EnterpriseProjects enterpriseProjects = EnterpriseProjects.builder()
                        .id(item.getId())
                        .name(item.getName())
                        .status(item.getStatus())
                        .delFlag("0")
                        .accountName(accountName)
                        .build();
                EnterpriseProjects old = getById(item.getId());
                if(old == null){
                    addList.add(enterpriseProjects);
                }else{
                    enterpriseProjects.setId(old.getId());
                    updateList.add(enterpriseProjects);
                }
                idList.add(item.getId());

            });
            if(CollectionUtils.isNotEmpty(addList)){
                saveBatch(addList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                updateBatchById(updateList);
            }
        }
        //删除不在idList的企业项目
        deleteBatchNoIds(idList,accountName);

    }

    @Override
    public List<EnterpriseProjects> getEnterpriseProjects(String accountName) {
        LambdaQueryWrapper<EnterpriseProjects> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseProjects::getAccountName,accountName);
        queryWrapper.eq(EnterpriseProjects::getDelFlag,"0");
        queryWrapper.eq(EnterpriseProjects::getStatus,1);
        List<EnterpriseProjects> list = list(queryWrapper);
        return list;
    }

    @Override
    public EnterpriseProjects getOneByScode(String scode,String accountName) {
        LambdaQueryWrapper<EnterpriseProjects> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseProjects::getAccountName,accountName);
        queryWrapper.eq(EnterpriseProjects::getDelFlag,"0");
        queryWrapper.likeLeft(EnterpriseProjects::getName,scode);
        List<EnterpriseProjects> list = list(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    public void enableEnterpriseProject(String id, String accountName) throws Exception{
        EnableEnterpriseProjectResponse response = huaweiApi.enableEnterpriseProject(huaweiApi.getEpsClient(accountName,"cn-north-4"),id);
        if(response == null){
            throw new RuntimeException("启用企业项目失败");
        }
    }

    @Override
    public String createEnterpriseProject(String name, String accountName) throws Exception{
        CreateEnterpriseProjectResponse response = huaweiApi.createEnterpriseProject(huaweiApi.getEpsClient(accountName,"cn-north-4"),name);
        if(response == null){
            throw new RuntimeException("创建企业项目失败");
        }
        return response.getEnterpriseProject().getId();
    }

    private void deleteBatchNoIds(List<String> idList, String accountName) {
        UpdateWrapper<EnterpriseProjects> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("account_name",accountName);
        updateWrapper.set("del_flag","1");
        updateWrapper.eq("del_flag","0");
        if(CollectionUtils.isNotEmpty(idList)){
            updateWrapper.notIn("id",idList);
        }
        update(updateWrapper);
    }
}
