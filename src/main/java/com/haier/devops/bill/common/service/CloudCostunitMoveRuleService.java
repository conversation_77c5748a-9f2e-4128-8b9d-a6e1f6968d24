package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.entity.CloudCostunitMoveRule;

/**
* @ClassName: ICloudCostunitMoveRuleService
* @Description: 财务单元分配规则
* @author: 张爱苹
* @date: 2024/3/12 15:18
*/
public interface CloudCostunitMoveRuleService extends IService<CloudCostunitMoveRule> {

    /**
    * @Description:  获取财务单元分配规则
    * @author: 张爱苹
    * @date: 2024/3/13 14:54
    * @param dto:
    * @Return: com.haier.devops.bill.common.entity.CloudCostunitMoveRule
    */
    CloudCostunitMoveRule getMoveRule(ReconciliationBatchDTO dto) throws Exception;
}
