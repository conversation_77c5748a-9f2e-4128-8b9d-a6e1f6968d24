package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.HostInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务器信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface HostInfoMapper extends BaseMapper<HostInfo> {
    List<HostInfo> selectByScodes(@Param("scodes") List<String> scodes,
                                  @Param("currencies") String... currencies);
}
