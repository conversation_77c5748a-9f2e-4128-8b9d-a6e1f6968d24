package com.haier.devops.bill.common.enums;

/**
 * @Description: 运维安全中心（堡垒机）
 * @Author: A0018437
 * @Date：2023-12-29
 */
public enum BastionhostEdnpointEnum {
    qing<PERSON>o("华北1（青岛）", "yundun-bastionhost.aliyuncs.com"),
    be<PERSON><PERSON>("华北2（北京）", "yundun-bastionhost.aliyuncs.com"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("华北3（张家口）", "bastionhost.cn-zhangjiakou.aliyuncs.com"),
    <PERSON><PERSON><PERSON><PERSON>("华北5（呼和浩特）", "bastionhost.cn-huhehaote.aliyuncs.com"),
    hangzhou("华东1（杭州）", "yundun-bastionhost.aliyuncs.com"),
    shanghai("华东2（上海）", "yundun-bastionhost.aliyuncs.com"),
    she<PERSON><PERSON>("华南1（深圳）", "yundun-bastionhost.aliyuncs.com"),
    <PERSON><PERSON>("华南2（河源）", "yundun-bastionhost.aliyuncs.com"),
    <PERSON><PERSON><PERSON>("西南1（成都）", "yundun-bastionhost.aliyuncs.com"),
    hongkong("中国（香港）", "bastionhost.cn-hongkong.aliyuncs.com"),
    tokyo("日本（东京）", "bastionhost.ap-northeast-1.aliyuncs.com"),
    ingapore("新加坡", "bastionhost.ap-southeast-1.aliyuncs.com"),
    ydney("澳大利亚（悉尼）", "bastionhost.ap-southeast-2.aliyuncs.com"),
    kualaLumpur("马来西亚（吉隆坡）", "bastionhost.ap-southeast-2.aliyuncs.com"),
    jakarta("印度尼西亚（雅加达）", "bastionhost.ap-southeast-2.aliyuncs.com");
    private final String product;

    private final String endpoint;

    BastionhostEdnpointEnum(String product, String endpoint){
        this.product = product;
        this.endpoint = endpoint;
    }

    // 根据键获取值的静态方法
    public static String getEndpoint(String key) {
        for (BastionhostEdnpointEnum enumValue : BastionhostEdnpointEnum.values()) {
            if (enumValue.product.equals(key)) {
                return enumValue.getValue();
            }
        }
        return null; // 如果未找到匹配的键，则返回null或者其他适当的值
    }

    private String getValue(){
        return endpoint;
    }
}
