package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.ExchangeRateApiResponse;
import com.haier.devops.bill.common.entity.ExchangeRate;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: ExchangeRateService
 * @Description: 汇率服务接口
 * @author: System
 * @date: 2025/06/30
 */
public interface ExchangeRateService extends IService<ExchangeRate> {

    /**
     * 从外部API获取最新汇率信息
     * @param baseCurrency 基础货币代码，默认为CNY
     * @return 汇率API响应
     * @throws Exception 获取失败时抛出异常
     */
    ExchangeRateApiResponse fetchLatestExchangeRates(String baseCurrency) throws Exception;

    /**
     * 同步汇率数据到数据库
     * @param apiResponse 汇率API响应数据
     * @throws Exception 同步失败时抛出异常
     */
    void syncExchangeRatesToDatabase(ExchangeRateApiResponse apiResponse) throws Exception;

    /**
     * 执行完整的汇率同步流程
     * @throws Exception 同步失败时抛出异常
     */
    void performFullSync() throws Exception;

    /**
     * 根据货币对和日期查询汇率
     * @param baseCurrency 基础货币
     * @param targetCurrency 目标货币
     * @param rateDate 汇率日期
     * @return 汇率信息
     */
    ExchangeRate getExchangeRate(String baseCurrency, String targetCurrency, Date rateDate);

    /**
     * 查询指定日期的所有汇率
     * @param rateDate 汇率日期
     * @return 汇率列表
     */
    List<ExchangeRate> getExchangeRatesByDate(Date rateDate);
}
