package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
/**
 * (AggregatedBill)实体类
 *
 * <AUTHOR>
 * @since 2024-02-29 15:27:09
 */
@Data
@Builder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
@TableName("bc_aggregated_bill")
public class AggregatedBill implements Serializable {
    private static final long serialVersionUID = -47484472757937015L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "S码")
    private String scode;


    @Schema(description = "账期")
    private LocalDateTime billingCycle;


    @Schema(description = "金额之和")
    private BigDecimal summer;

    @Schema(description = "应付金额之和")
    private BigDecimal payableSum;

    @Schema(description = "代金券金额之和")
    private BigDecimal voucherSum;

    @Schema(description = "现金金额之和")
    private BigDecimal cashSum;

    @Schema(description = "优惠券金额之和")
    private BigDecimal couponSum;

    @Schema(description = "原始金额之和")
    private BigDecimal originalSum;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "聚合id")
    private String aggregatedId;


    @Schema(description = "数据的粒度")
    private String granularity = Granularity.DAY.getValue();


    @Schema(description = "计算任务id")
    private String taskId;

    @Schema(description = "替换任务id")
    private String subTaskId;


    private Date createTime;


    private Date updateTime;


    public AggregatedBill() {
    }

    public AggregatedBill(BillingSummaryVo billingSummaryVo) {
        this.aggregatedId = billingSummaryVo.getAggregatedId();
        this.billingCycle = billingSummaryVo.getBillingCycle();
        this.summer = null == billingSummaryVo.getSummer() ? BigDecimal.ZERO : billingSummaryVo.getSummer();
        this.payableSum = null == billingSummaryVo.getPayableSum() ? BigDecimal.ZERO : billingSummaryVo.getPayableSum();
        this.voucherSum = null == billingSummaryVo.getVoucherSum() ? BigDecimal.ZERO : billingSummaryVo.getVoucherSum();
        this.cashSum = null == billingSummaryVo.getCashSum() ? BigDecimal.ZERO : billingSummaryVo.getCashSum();
        this.couponSum = null == billingSummaryVo.getCouponSum() ? BigDecimal.ZERO : billingSummaryVo.getCouponSum();
        this.scode = billingSummaryVo.getCostUnit();
        this.currency = billingSummaryVo.getCurrency();
    }

    public enum Granularity {
        DAY("day"),
        MONTH("month"),
        YEAR("year");

        private String value;

        Granularity(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}

