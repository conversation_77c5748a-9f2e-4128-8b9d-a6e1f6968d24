package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
* @ClassName: ResourceInstance
* @Description: 资源实例
* @author: 张爱苹
* @date: 2024/3/12 13:37
*/
@Data
@TableName("resource_instance")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="资源实例")
public class ResourceInstance implements Serializable {


    private static final long serialVersionUID = 4228613702943150088L;
    /**主键*/
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private java.lang.Integer id;
    /**财务单元*/
    private java.lang.String costUnit;
    /**财务单元id*/
    private java.lang.String costUnitId;
    /**资源的商品code*/
    @Schema(description = "资源的商品code")
    private java.lang.String commodityCode;
    /**资源属主的用户名*/
    @Schema(description = "资源属主的用户名")
    private java.lang.String resourceUserName;
    /**资源的商品名*/
    @Schema(description = "资源的商品名")
    private java.lang.String commodityName;
    /**资源的属主用户ID*/
    @Schema(description = "资源的属主用户ID")
    private long resourceUserId;
    /**资源分拆名*/
    @Schema(description = "资源分拆名")
    private java.lang.String apportionName;
    /**资源分拆code*/
    @Schema(description = "资源分拆code")
    private java.lang.String apportionCode;
    /**资源类型*/
    @Schema(description = "资源类型")
    private java.lang.String resourceType;
    /**资源的自定义昵称*/
    @Schema(description = "资源的自定义昵称")
    private java.lang.String resourceNick;
    /**资源的Tag标签*/
    @Schema(description = "资源的Tag标签")
    private java.lang.String resourceTag;
    /**资源的实例ID*/
    @Schema(description = "资源的实例ID")
    private java.lang.String resourceId;
    /**资源所属的资源组*/
    @Schema(description = "资源所属的资源组")
    private java.lang.String resourceGroup;
    /**资源实例相关的资源*/
    @Schema(description = "资源实例相关的资源")
    private java.lang.String relatedResources;
    /**资源状态*/
    @Schema(description = "资源状态")
    private java.lang.String resourceStatus;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "createTime")
    private java.util.Date createTime;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "updateTime")
    private java.util.Date updateTime;

    /**产品编码*/
    private String productCode;
}
