package com.haier.devops.bill.common.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.haier.devops.bill.common.entity.RawBillSyncLog;
import com.haier.devops.bill.common.service.RawBillSyncLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  用于记录已经同步的账单信息  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/hcms/bill/log-syncing")
public class RawBillSyncLogController {
    private static final String RESET_FLAG = "started";
    private RawBillSyncLogService syncLogService;

    public RawBillSyncLogController(RawBillSyncLogService syncLogService) {
        this.syncLogService = syncLogService;
    }

    /**
     * 重置账单同步标记
     * @param vendor
     * @param billingCycle
     * @param accountId
     * @return
     */
    @PostMapping("/reset")
    public ResponseEntityWrapper<Boolean> resetFlag(@RequestParam String vendor,
                                                    @RequestParam String billingCycle,
                                                    @RequestParam(required = false) String accountId) {

        LambdaUpdateWrapper<RawBillSyncLog> resetSyncingFlagWrapper =
                new LambdaUpdateWrapper<RawBillSyncLog>()
                        .eq(RawBillSyncLog::getVendor, vendor)
                        .eq(RawBillSyncLog::getBillingCycle, billingCycle);
        if (StringUtils.isNotBlank(accountId)) {
            resetSyncingFlagWrapper.eq(RawBillSyncLog::getAccountId, accountId);
        }
        resetSyncingFlagWrapper.set(RawBillSyncLog::getStatus, RESET_FLAG);

        boolean result = false;
        try {
            result = syncLogService.update(resetSyncingFlagWrapper);
        } catch (Exception e) {
            log.error("重置账单同步标记失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "重置账单同步标记失败", null);
        }

        return new ResponseEntityWrapper<>(result);
    }
}
