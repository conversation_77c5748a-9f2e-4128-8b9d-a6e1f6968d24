package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.vo.DomainSubProductVo;

import java.util.List;
import java.util.Map;

/**
* @ClassName: HdsSubProductsService
* @Description: hds子产品表 服务类
* @author: 张爱苹
* @date: 2024/1/11 17:55
*/
public interface HdsSubProductsService extends IService<HdsSubProducts> {

    /**
    * @Description: 获取所有可用的S码
    * @author: 张爱苹
    * @date: 2024/1/11 18:45
    * @param queryWrapper:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getAllAppScodeList();

    /**
    * @Description:  获取子产品列表
    * @author: 张爱苹
    * @date: 2024/1/15 15:42

    * @Return: java.util.List<com.haier.devops.bill.common.entity.HdsSubProducts>
    */
    List<HdsSubProducts> getHdsSubProductList(String searchContent);

    /**
     * @Description:  获取子产品列表
     * @author: 张爱苹
     * @date: 2024/1/15 15:42

     * @Return: java.util.List<com.haier.devops.bill.common.entity.HdsSubProducts>
     */
    List<HdsSubProducts> getAgentHdsSubProductList(String searchContent);


    boolean saveOrUpdateBatchByParams(List<HdsSubProducts> list);

    /**
    * @Description: 获取子产品列表
    * @author: 张爱苹
    * @date: 2024/3/21 15:09
    * @param appScodeList:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.HdsSubProducts>
    */
    List<HdsSubProducts> queryList(List<String> appScodeList);

    /**
     * 根据领域查询子产品信息
     * @param domainIds
     * @return
     */
    List<DomainSubProductVo> getDomainSubProductVoList(List<String> domainIds);


    /**
     * 查询所有领域子产品信息
     * @return
     */
    List<DomainSubProductVo> getAllDomainSubProductVoList();

    /**
    * @Description: 根据s码查询子产品
    * @author: 张爱苹
    * @date: 2025/2/17 15:09
    * @param scode:
    * @Return: com.haier.devops.bill.common.entity.HdsSubProducts
    */
    HdsSubProducts queryOne(String scode);

    /**
     * 领域模糊查询
     * @param query
     * @return
     */
    List<HdsSubProducts> searchDomain(String query);

    /**
    * @Description: 根据领域编码或名称查是否存在
    * @author: 张爱苹
    * @date: 2025/3/13 17:07
    * @param domain:
    * @Return: void
    */
    List<HdsOpenApi.Domain> getDomainList();

    /**
    * @Description: 获取应用负责人的工号
    * @author: 张爱苹
    * @date: 2025/6/30 15:19
    * @param appScode:
    * @Return: java.lang.String
    */
    List<Map> getAccountByScode(String appScode);
}
