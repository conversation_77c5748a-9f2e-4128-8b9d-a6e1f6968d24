package com.haier.devops.bill.common.controller;


import com.haier.devops.bill.common.dto.AlarmConditionDTO;
import com.haier.devops.bill.common.entity.AlarmCondition;
import com.haier.devops.bill.common.service.AlarmConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* @ClassName: AlarmCondtionController
* @Description:  告警条件控制层
* @author: 张爱苹
* @date: 2024/1/31 10:24
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/alarm/condition")
public class AlarmCondtionController {

	@Autowired
	private AlarmConditionService alarmConditionService;


	/**
	* @Description:  查询告警条件列表
	* @author: 张爱苹
	* @date: 2024/1/31 10:28
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<com.haier.devops.bill.common.entity.AlarmCondition>>
	*/
	@GetMapping("/list")
	public ResponseEntityWrapper<List<AlarmCondition>> list(AlarmConditionDTO dto) {
		List<AlarmCondition> list =
				alarmConditionService.getAlarmConditionList(dto);
		return new ResponseEntityWrapper<>(list);
	}

}
