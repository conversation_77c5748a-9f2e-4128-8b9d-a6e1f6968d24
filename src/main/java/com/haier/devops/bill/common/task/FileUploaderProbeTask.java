package com.haier.devops.bill.common.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.redis.Queue;
import com.haier.devops.bill.common.service.ExportLogService;
import com.haier.devops.bill.export.Exporter;
import com.haier.devops.bill.export.vo.ExportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 文件上传探针任务
 */
@Component
@Slf4j
public class FileUploaderProbeTask extends AbstractTask {
    private static final int TIMEOUT_OF_10_MINUTES = 60 * 10;

    @Value("${server.port}")
    private Integer serverPort;

    private Queue<ExportVo> uploadingProbeQueue;
    private ExportLogService exportLogService;


    public FileUploaderProbeTask(@Qualifier("uploadingProbeQueue") Queue<ExportVo> uploadingProbeQueue,
                                 ExportLogService exportLogService) {
        this.uploadingProbeQueue = uploadingProbeQueue;
        this.exportLogService = exportLogService;
    }

    @Override
    @SuppressWarnings("InfiniteLoopStatement")
    public void doExecute() {
        Calendar calendar = Calendar.getInstance();

        while (true) {
            try {
                List<ExportVo> exports = uploadingProbeQueue.getAll();
                for (ExportVo export : exports) {
                    LambdaQueryWrapper<ExportLog> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ExportLog::getSerialNo, export.getSerialNo());
                    ExportLog el = exportLogService.getOne(queryWrapper);
                    if (null == el) {
                        uploadingProbeQueue.remove(export);
                        continue;
                    }

                    // 不论成功还是失败都从队列中移除
                    if (Exporter.SUCCESS.equals(el.getIsReady())
                            || Exporter.FAILED.equals(el.getIsReady())
                            || Exporter.TIMEOUT.equals(el.getIsReady())) {
                        uploadingProbeQueue.remove(export);
                        continue;
                    }
                    TaskEntity task = TaskEntity.builder()
                            .taskId(export.getSerialNo())
                            .host(el.getPodIp())
                            .build();
                    // 检查节点是否在线
                    ResponseEntityWrapper<Boolean> result = checkIfTaskIsRunning(task);
                    if (!result.getData()) {
                        // 节点不在线
                        log.error("节点 {} 不在线", el.getPodIp());
                        changeExportLogStatus(export, Exporter.FAILED);
                        uploadingProbeQueue.remove(export);
                        continue;
                    }

                    // 检查是否超时
                    calendar.setTime(export.getSubmitTime());
                    calendar.add(Calendar.SECOND, TIMEOUT_OF_10_MINUTES);
                    Date expirationTime = calendar.getTime();
                    if (new Date().after(expirationTime)) {
                        changeExportLogStatus(export, Exporter.TIMEOUT);
                        uploadingProbeQueue.remove(export);
                    }
                }
            } catch (Exception e) {
                log.error("FileUploaderProbeTask error", e);
            }

            sleep(1);
        }
    }

    private void changeExportLogStatus(ExportVo export, int status) {
        LambdaUpdateWrapper<ExportLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExportLog::getSerialNo, export.getSerialNo());

        ExportLog entity = ExportLog.builder()
                .isReady(status)
                .build();
        exportLogService.update(entity, updateWrapper);
    }
}
