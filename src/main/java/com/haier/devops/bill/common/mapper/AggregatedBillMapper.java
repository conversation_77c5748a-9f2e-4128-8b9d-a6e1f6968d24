package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haier.devops.bill.aws.commitment.vo.AggregatedBillCommitmentVo;
import com.haier.devops.bill.common.dto.AggregatedBillDTO;
import com.haier.devops.bill.common.dto.BillGatheredDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.ReconciliationDTO;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.vo.*;
import com.haier.devops.bill.substitution.vo.PassBillVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* @ClassName: AggregatedBillMapper
* @Description:  账单明细汇总表Mapper
* @author: 张爱苹
* @date: 2024/1/31 10:21
*/
@Repository
public interface AggregatedBillMapper extends BaseMapper<AggregatedBill> {


    List<AggregatedBillVo> listByPage(@Param("dto") BillGatheredDTO dto);

    List<Map> countByS();

    List<AggregatedBillInfoVo> getInfoByAggregatedId(String aggregatedId);

    AggregatedBillInfoVo getBilingCycleByAggregatedId(String aggregatedId);

    void updateReconciliation(@Param("dto") ReconciliationDTO reconciliationDTO, @Param("aggregatedId") String aggregatedId);

    void updateReconciliationBatch(@Param("dto") ReconciliationBatchDTO dto);

    List<String> getLatestAggregatedIdList(@Param("startDate") String startDate,@Param("endDate") String endDate, @Param("list") List<String> list);


    /**
     * 多维度分析
     * @param queryWrapper
     * @param dataType
     * @param list
     * @param cycleType
     * @param scodes
     * @return
     */
    List<Map<String, Object>> DimensionDetails(
            @Param(Constants.WRAPPER) QueryWrapper<DimensionDetailVo> queryWrapper,
            @Param("vendor") String vendor,
            @Param("productCode") String productCode,
            @Param("productList")List<String> productList,
            @Param("dataType") String dataType,
            @Param("list") List<String> list,
            @Param("cycleType") String cycleType,
            @Param("scodes") List<String> scodes,
            @Param("domains") List<String> domains,
            @Param("exportAll") Boolean exportAll,
            @Param("currencies") String... currencies
    );

    List<Map<String, Object>> DimensionDetailsAll(
            @Param(Constants.WRAPPER) QueryWrapper<DimensionDetailVo> queryWrapper,
            @Param("vendor") String vendor,
            @Param("productCode") String productCode,
            @Param("productList")List<String> productList,
            @Param("dataType") String dataType,
            @Param("list") List<String> list,
            @Param("cycleType") String cycleType,
            @Param("scodes") List<String> scodes,
            @Param("domains") List<String> domains,
            @Param("exportAll") Boolean exportAll,
            @Param("exchangeRate") BigDecimal exchangeRate
    );

    void updateBatch(@Param("list") List<ReconciliationDTO> list);

    List<AggregatedBillVo> getScodeOfThePreviousCycle(@Param("dto") AggregatedBillDTO dto);

    Page<TechnicalArchitectureBillVo> getBillOfBillingCycle(Page<TechnicalArchitectureBillVo> page, @Param("dto") AggregatedBillDTO dto);

    List<BillDetailVo> listDetailByPage(@Param("dto") DetailBillParam request);

    /**
     * 查询到日的账单汇总
     * @param start 年月日
     * @param end 年月日
     * @param vendor
     * @param aggregatedId
     * @return
     */
    List<AggregatedBill> getGroupedAggregatedBillsBetweenDays(@Param("start") String start,
                                                              @Param("end") String end,
                                                              @Param("vendor") String vendor,
                                                              @Param("aggregatedIds") String... aggregatedId);


    /**
     * 按照账号查询账单汇总
     * @param start
     * @param end
     * @param vendor
     * @param account
     * @return
     */
    List<AggregatedBill> getAggregatedBillsByAccount(@Param("start") String start,
                                                     @Param("end") String end,
                                                     @Param("vendor") String vendor,
                                                     @Param("account") String account);

    /**
     * 清除到月的月度汇总
     * @param start 年月
     * @param end 年月
     * @param aggregatedId
     * @return
     */
    int clearAdjustedMonthBills(@Param("start") String start,
                                @Param("end") String end,
                                @Param("aggregatedIds") String... aggregatedId);

    /**
     * 清除月度汇总
     * @param vendor
     * @param start
     * @param end
     * @return
     */
    int clearMonthBills(@Param("vendor") String vendor,
                        @Param("start") String start,
                        @Param("end") String end);


    /**
     * 按照云厂商、开始时间、结束时间、账号清除旧的汇总账单
     * @param vendor
     * @param start
     * @param end
     * @param account
     */
    void clearMonthBillsByAccount(@Param("vendor") String vendor,
                                  @Param("start") String start,
                                  @Param("end") String end,
                                  @Param("account") String account);

    /**
     * 查询当前年度的账单汇总
     *
     * @param scodes
     * @return
     */
    BigDecimal getCurrentYearTotalAmount(@Param("scodes") List<String> scodes,
                                         @Param("currencies") String... currencies);

    /**
     * 查询去年的账单汇总
     *
     * @param scodes
     * @return
     */
    BigDecimal getLastYearTotalAmount(@Param("firstDayOfLastYear") String firstDayOfLastYear ,
                                      @Param("firstDayOfCurrentMonthLastYear") String firstDayOfCurrentMonthLastYear ,
                                      @Param("todayLastYear") String todayLastYear ,
                                      @Param("scodes") List<String> scodes,
                                      @Param("currencies") String... currencies);

    /**
     * 查询过去第n个月的总金额，n=1时表示上个月
     *
     * @param scodes
     * @return
     */
    BigDecimal getLastNthMonthTotalAmount(@Param("scodes") List<String> scodes,
                                          @Param("vendor") String vendor,
                                          @Param("nth") int nth,
                                          @Param("currencies") String... currencies);

    /**
     * 获取当前月份截止到昨天的总金额
     *
     * @param scodes
     * @return
     */
    BigDecimal getCurrentMonthTotalAmountUntilYesterday(@Param("scodes") List<String> scodes,
                                                        @Param("currencies") String... currencies);

    /**
     * 获取昨天的总金额
     *
     * @param scodes
     * @return
     */
    BigDecimal getTotalAmountOfYesterday(@Param("scodes") List<String> scodes,
                                         @Param("currencies") String... currencies);

    /**
     * 获取指定月的按天汇总金额
     *
     * @param scodes
     * @param vendor
     * @param month
     * @return
     */
    List<DayBillAnalysisVo> getMonthAggregatedBill(@Param("scodes") List<String> scodes,
                                                   @Param("vendor") String vendor,
                                                   @Param("month") String month,
                                                   @Param("currency") String... currency);

    /**
    * @Description: 消费明细季度统计
    * @author: 张爱苹
    * @date: 2024/8/15 10:43
    * @param request:
    * @Return: void
    */
    List<BillDetailVo> listDetailQByPage(@Param("dto") DetailBillParam request);

    /**
    * @Description: 消费明细年度统计
    * @author: 张爱苹
    * @date: 2024/8/15 10:43
    * @param request:
    * @Return: void
    */
    List<BillDetailVo> listDetailYByPage(@Param("dto") DetailBillParam request);

    /**
     * 获取阿里云容器待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getAliyunPaasPendingSubstitutionBills(String billingCycle);

    /**
     * 获取大数据HDOP云容器待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getBigDataHdopPaasPendingSubstitutionBills(String billingCycle);

    /**
     * 获取大数据Starrocks云容器待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getBigDataStarrocksPaasPendingSubstitutionBills(String billingCycle);

    /**
     * 获取阿里专属云容器待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getAliyunDedicatedPaasPendingSubstitutionBills(String billingCycle);


    /**
     * 获取华为容器云待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getHuaweiPaasPendingSubstitutionBills(String billingCycle);

    /**
     * 获取华为ucs产品待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getHuaweiUcsPendingSubstitutionBills(String billingCycle);


    /**
     * 获取云监控待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getCloudMonitorPendingSubstitutionBills(String billingCycle);

    /**
     * 获取aws节省计划、预留实例待替换账单
     * @param billingCycle
     * @return
     */
    List<AggregatedBill> getAwsCommitmentPendingSubstitutionBills(String billingCycle);

    /**
     * 获取aws节省计划、预留实例待替换账单（带资源信息）
     * @param billingCycle
     * @return
     */
    List<AggregatedBillCommitmentVo> getAwsPendingSubstitutionBills(String billingCycle);


    /**
     * 清除阿里容器云需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunPassDayBills(String billingCycle);


    /**
     * 清除大数据HDOP需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataHdopPassDayBills(String billingCycle);

    /**
     * 清除大数据Starrocks需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataStarrocksPassDayBills(String billingCycle);

    /**
     * 清除阿里容器云需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunPassMonthBills(String billingCycle);


    /**
     * 清除大数据HDOP需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataHdopPassMonthBills(String billingCycle);

    /**
     * 清除大数据Starrocks需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataStarrocksPassMonthBills(String billingCycle);

    /**
     * 清除阿里专属容器云需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunDedicatedPassDayBills(String billingCycle);

    /**
     * 清除阿里专属容器云需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunDedicatedPassMonthBills(String billingCycle);

    /**
     * 清除华为容器云需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanHuaweiPassDayBills(String billingCycle);

    /**
     * 清除华为UCS需要被替换的日账单
     * @param billingCycle
     * @return
     */
    int cleanHuaweiUcsDayBills(String billingCycle);

    /**
     * 清除华为UCS需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanHuaweiUcsMonthBills(String billingCycle);

    /**
     * 清除华为容器云需要被替换的月账单
     * @param billingCycle
     * @return
     */
    int cleanHuaweiPassMonthBills(String billingCycle);

    /**
     * 获取近半年容器云账单
     * @return
     */
    List<PassBillVo> getPassBill(@Param("vendor") String vendor,
                                 @Param("scodes") List<String> scodes,
                                 @Param("start") String start,
                                 @Param("end") String end,
                                 @Param("currency") String... currency);

    /**
     * 批量插入
     * @param bills
     * @return
     */
    int insertBatch(@Param("bills") List<AggregatedBill> bills);

    /**
     * 清理云监控日账单
     * @param billingCycle
     * @return
     */
    int cleanCloudMonitorDayBills(String billingCycle);

    /**
     * 清理云监控月账单
     * @param billingCycle
     * @return
     */
    int cleanCloudMonitorMonthBills(String billingCycle);

    List<Map> getAgentResourceApplications(@Param("vendor") String vendor, @Param("scodes") List<String> scodes, @Param("startDate") String startDate, @Param("endDate") String endDate);

    List<PassBillVo> getPassBillAll(@Param("vendor") String vendor,
                                 @Param("scodes") List<String> scodes,
                                 @Param("start") String start,
                                 @Param("end") String end,
                                 @Param("exchangeRate") BigDecimal exchangeRate);

    BigDecimal getLastNthMonthTotalAmountAll(@Param("scodes") List<String> scodes,
                                          @Param("vendor") String vendor,
                                          @Param("nth") int nth,
                                          @Param("exchangeRate") BigDecimal exchangeRate);

    List<BillDetailVo> listDetailByPageAll(@Param("dto") DetailBillParam request);
}
