package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictItemCreateDTO;
import com.haier.devops.bill.common.dto.SysDictItemDTO;
import com.haier.devops.bill.common.entity.SysDictItem;
import com.haier.devops.bill.common.service.SysDictItemService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* @ClassName: SysDictItemController
* @Description:  数据字典项
* @author: 张爱苹
* @date: 2024/2/4 10:43
*/
@Tag(name = "数据字典项")
@RestController
@RequestMapping("/api/v1/hcms/bill/dictItem")
@Slf4j
public class SysDictItemController {

	@Autowired
	private SysDictItemService sysDictItemService;

	/**
	 * 分页查询所有字典项
	 *
	 * @return 字典项列表
	 */
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<SysDictItem>> listByPage(SysDictItemDTO dto) {
		PageInfo<SysDictItem> pageInfo =
				sysDictItemService.listByPage(dto);
		return new ResponseEntityWrapper<>(pageInfo);
	}
	/**
	* @Description: 检查字典项值
	* @author: 张爱苹
	* @date: 2024/2/4 13:47
	* @param itemValue:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@GetMapping("/checkItem")
	public ResponseEntityWrapper check(@RequestParam("dictId") String dictId,@RequestParam("itemValue") String itemValue) {
		return new ResponseEntityWrapper<>(sysDictItemService.check(dictId,itemValue));
	}

	/**
	 * 创建字典项
	 *
	 * @param sysDictItemCreateDTO 字典项
	 * @return 创建成功的字典项
	 */
	@PostMapping("/create")
	public ResponseEntityWrapper createSysDictItem(@RequestBody @Validated SysDictItemCreateDTO sysDictItemCreateDTO) {
		try {
			sysDictItemService.createSysDictItem(sysDictItemCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 更新字典项
	 *
	 * @param id                 字典项ID
	 * @param sysDictItemCreateDTO 更新的字典项
	 * @return 更新后的字典项
	 */
	@PutMapping("/update/{id}")
	public ResponseEntityWrapper updateSysDictItem(@PathVariable("id") String id, @RequestBody @Validated SysDictItemCreateDTO sysDictItemCreateDTO) {
		try {
			sysDictItemService.updateSysDictItem(id, sysDictItemCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 删除字典项
	 *
	 * @param id 字典项ID
	 */
	@DeleteMapping("/delete/{id}")
	public ResponseEntityWrapper deleteSysDictItem(@PathVariable("id") String id) {
		try {
			sysDictItemService.deleteSysDictItem(id);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}


}
