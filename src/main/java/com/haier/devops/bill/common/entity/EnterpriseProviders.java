package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: EnterpriseProjects
* @Description: 企业项目支持的服务
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
@TableName("bc_enterprise_providers")
@Data
@Builder
public class EnterpriseProviders implements Serializable {

    private static final long serialVersionUID = -1841188068660942140L;

    private Long id;

    private String provider;

    private String providerI18nDisplay_name;

    private String resourceType;

    private String resourceTypeI18nDisplay_name;

    private String regions;

    private boolean global;

    private String delFlag;

}
