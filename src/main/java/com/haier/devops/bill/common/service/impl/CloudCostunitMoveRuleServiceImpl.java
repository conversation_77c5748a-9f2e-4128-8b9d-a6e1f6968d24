package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.CloudCostunitMoveRule;
import com.haier.devops.bill.common.enums.RuleFieldEnum;
import com.haier.devops.bill.common.mapper.CloudCostunitMoveRuleMapper;
import com.haier.devops.bill.common.service.CloudCostunitMoveRuleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
* @ClassName: CloudCostunitMoveRuleServiceImpl
* @Description: 财务单元分配规则
* @author: 张爱苹
* @date: 2024/3/12 15:19
*/
@Service
public class CloudCostunitMoveRuleServiceImpl extends ServiceImpl<CloudCostunitMoveRuleMapper, CloudCostunitMoveRule> implements CloudCostunitMoveRuleService {

    @Override
    public CloudCostunitMoveRule getMoveRule(ReconciliationBatchDTO dto) throws Exception{
        LambdaQueryWrapper<CloudCostunitMoveRule> queryWrapper = new LambdaQueryWrapper<>();
        ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
        queryWrapper.eq(CloudCostunitMoveRule::getCloud, resourceInstanceDTO.getVendor());
        queryWrapper.eq(CloudCostunitMoveRule::getAccount, resourceInstanceDTO.getAccountName());
        queryWrapper.eq(CloudCostunitMoveRule::getCommoditycode, resourceInstanceDTO.getCommodityCode());
        if(resourceInstanceDTO.getRuleField().equals(RuleFieldEnum.RESOURCEID.getKey())){
            queryWrapper.eq(CloudCostunitMoveRule::getResourceid, resourceInstanceDTO.getRuleFieldValue());
        }else if(resourceInstanceDTO.getRuleField().equals(RuleFieldEnum.RESOURCENICK.getKey())){
            queryWrapper.eq(CloudCostunitMoveRule::getResourcenick, resourceInstanceDTO.getRuleFieldValue());
        }else if(resourceInstanceDTO.getRuleField().equals(RuleFieldEnum.RESOURCETAG.getKey())){
            queryWrapper.eq(CloudCostunitMoveRule::getResourcetag, resourceInstanceDTO.getRuleFieldValue());
        }
        List<CloudCostunitMoveRule> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list) || list.size() > 1){
            throw new RuntimeException("规则匹配失败");
        }
        return list.get(0);

    }
}
