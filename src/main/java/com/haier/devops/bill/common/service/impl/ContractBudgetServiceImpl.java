package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ContractBudget;
import com.haier.devops.bill.common.mapper.ContractBudgetMapper;
import com.haier.devops.bill.common.service.ContractBudgetService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 合同预算 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class ContractBudgetServiceImpl extends ServiceImpl<ContractBudgetMapper, ContractBudget> implements ContractBudgetService {

}
