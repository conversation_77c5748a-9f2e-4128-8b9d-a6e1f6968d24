package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.IdmOrg;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.service.IdmOrgService;
import com.haier.devops.bill.common.vo.DepartIdVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (IdmOrg)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-23 16:49:30
 */
@RestController
    @RequestMapping("/api/v1/hcms/bill/idmOrg")
public class IdmOrgController {
    /**
     * 服务对象
     */
    @Resource
    private IdmOrgService idmOrgService;
    private final AuthorityService authorityService;

    public IdmOrgController(AuthorityService authorityService) {
        this.authorityService = authorityService;
    }

    @GetMapping("/getOrgTree")
    public ResponseEntityWrapper<List<DepartIdVo>> getOrgTree(@RequestParam String orgid) {
        List<DepartIdVo> departIdVos = idmOrgService.getOrgTree(orgid);
        return new ResponseEntityWrapper<>(departIdVos);
    }

    /**
     * 根据名称查询组织
     * name为空时，默认查用户有权限的那个部门
     * @param name
     * @return
     */
    @GetMapping("/getAuthedOrgByName")
    public ResponseEntityWrapper<List<IdmOrg>> getAuthedOrgByName(String name) {
        if (StringUtils.isBlank(name)) {
            // name为空时，默认查用户有权限的部门
            User user = LoginContextHolder.getCurrentUser();
            HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> userAuthorities
                    = authorityService.getUserAuthorities(user.getUserCode());
            if (!CollectionUtils.isEmpty(userAuthorities.getData())) {
                List<String> orgIds = userAuthorities.getData().stream()
                        .map(HworkAuthorityApi.Authority::getAuthorityCode)
                        .collect(Collectors.toList());
                List<IdmOrg> orgsByOrgId = idmOrgService.getOrgsByOrgId(orgIds);
                if (!CollectionUtils.isEmpty(orgsByOrgId)) {
                    return new ResponseEntityWrapper<>(orgsByOrgId);
                }
            }

            // 没有权限的话，返回用户所在部门
            String deptId = user.getDeptId();
            return new ResponseEntityWrapper<>(idmOrgService.getOrgsByOrgId(Collections.singletonList(deptId)));
        }

        return new ResponseEntityWrapper<>(idmOrgService.getAuthedOrgByName(name));
    }
}

