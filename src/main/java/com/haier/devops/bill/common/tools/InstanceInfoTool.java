package com.haier.devops.bill.common.tools;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haier.devops.bill.aliyun.factory.AliyunClientFactory;
import com.haier.devops.bill.aliyun.factory.HuaweiClientFactory;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.RcDatabaseInfo;
import com.haier.devops.bill.common.entity.RcHostInfo;
import com.haier.devops.bill.common.entity.RcMiddlewareInfo;
import com.haier.devops.bill.common.enums.ProductDBEnum;
import com.haier.devops.bill.common.service.RcDatabaseInfoService;
import com.haier.devops.bill.common.service.RcHostInfoService;
import com.haier.devops.bill.common.service.RcMiddlewareInfoService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Description: 实例信息
 * @Author: ********
 * @Date：2024-03-01
 */
@Component
public class InstanceInfoTool {
    @Resource
    AliyunClientFactory aliyunClientFactory;
    @Resource
    HuaweiClientFactory huaweiClientFactory;
    @Resource
    private RcHostInfoService rcHostInfoService;
    @Resource
    private RcDatabaseInfoService rcDatabaseInfoService;
    @Resource
    private RcMiddlewareInfoService rcMiddlewareInfoService;

    static String huaweiRegion = "af-south-1, ap-southeast-1, ap-southeast-2, ap-southeast-3, cn-east-2, cn-east-3, cn-north-1, cn-north-2, cn-north-4, cn-north-9, cn-south-1, " +
            "cn-south-2, cn-southwest-2, eu-west-0, eu-west-101, la-north-2, la-south-2, my-kualalumpur-1, na-mexico-1, sa-brazil-1";
    /**
     * 获取实例信息
     * @param cmdb CMDB产品概览
     * @throws Exception 异常
     */
    public void instanceInfo(CmdbProductOverview cmdb) {
        String enumValue = ProductDBEnum.getDB(cmdb.getProductCode());
        if (Strings.isNotBlank(enumValue)){
            Optional<Object> infoOptional = Optional.empty();
            // 根据产品类型查询对应的详细信息
            switch (enumValue){
                case "host":
                    // 查询主机信息
                    infoOptional = Optional.ofNullable(rcHostInfoService.getOne(new LambdaQueryWrapper<RcHostInfo>().eq(RcHostInfo::getInstanceId, cmdb.getInstanceId())));
                    break;
                case "database":
                    // 查询数据库信息
                    infoOptional = Optional.ofNullable(rcDatabaseInfoService.getOne(new LambdaQueryWrapper<RcDatabaseInfo>().eq(RcDatabaseInfo::getInstanceId, cmdb.getInstanceId())));
                    break;
                case "middleware":
                    // 查询中间件信息
                    infoOptional = Optional.ofNullable(rcMiddlewareInfoService.getOne(new LambdaQueryWrapper<RcMiddlewareInfo>().eq(RcMiddlewareInfo::getInstanceId, cmdb.getInstanceId())));
                    break;
            }

            // 如果查询到信息，则根据信息类型更新CMDB对象
            infoOptional.ifPresent( info -> {
                if (info instanceof RcHostInfo) {
                    // 更新为主机信息
                    RcHostInfo hostInfo = (RcHostInfo) info;
                    cmdb.setCreationTime(hostInfo.getCreationTime());
                    cmdb.setPrivateIp(hostInfo.getPrivateIp());
                } else if (info instanceof RcDatabaseInfo) {
                    // 更新为数据库信息
                    RcDatabaseInfo databaseInfo = (RcDatabaseInfo) info;
                    cmdb.setCreationTime(databaseInfo.getCreationTime());
                    cmdb.setPrivateIp(databaseInfo.getHost());
                } else {
                    // 更新为中间件信息
                    RcMiddlewareInfo middlewareInfo = (RcMiddlewareInfo) info;
                    cmdb.setCreationTime(middlewareInfo.getCreationTime());
                    cmdb.setPrivateIp(middlewareInfo.getPrivateEndpoints());
                }
            });
        }

    }
}
