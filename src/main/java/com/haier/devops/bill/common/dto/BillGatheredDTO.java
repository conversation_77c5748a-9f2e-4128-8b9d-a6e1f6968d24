package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: BillGatheredDTO
* @Description:  每日账单汇总dto
* @author: 张爱苹
* @date: 2024/1/22 10:04
*/
@Data
public class BillGatheredDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -5067976180259722036L;
    /**
     * 资源汇总id
     */
    @NotEmpty
    private String gatheringId;

    /**
     * 账期
     */
    @NotEmpty
    private String billingCycle;

    /**
     * 聚合粒度
     *
     */
    private String granularity;
}
