package com.haier.devops.bill.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* @ClassName: AlarmConfigurationCreateDTO
* @Description: 告警配置 dto
* @author: 张爱苹
* @date: 2024/1/11 11:14
*/
@Data
public class AlarmConfigurationCreateDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8897611471063972302L;

    private Integer id;
    /**
     * S码
     */
    private String appScode;

    /**
     * 产品id
     */
    @NotEmpty
    private List<Integer> productIdList;

    /**
     * 等级
     */
    @NotEmpty
    private List<CpAlarmLevelCreateDTO> bcCpAlarmLevelList;


    /**
     * 是否开启告警：0否；1是
     */
    @NotNull
    private Integer isEnableAlarm;

    /**
     *  告警金额
     */
    @NotNull
    private String warnAmount;


    /**
     * 有效期开始时间
     */
    @NotNull
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startValidPeriodTime;

    /**
     * 有效期结束时间
     */
    @NotNull
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endValidPeriodTime;

    /**
     * 告警类型：单日账单费用突增；（字典获取）
     */
    @NotEmpty
    private String alarmType;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 更新人姓名
     */
    private String updateByName;
}
