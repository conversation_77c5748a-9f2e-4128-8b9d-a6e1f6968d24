package com.haier.devops.bill.common.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 调整单元
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Validated
public class AdjustmentUnitVo {
    /**
     * 云厂商
     */
    @NotNull
    public String vendor;

    /**
     * 账户id
     */
    @NotNull
    public String accountId;

    /**
     * 资源id
     */
    @NotNull
    public String resourceId;

    /**
     * 目标S码
     */
    @NotNull
    public String targetCode;

    /**
     * 修改的月份
     */
    @NotEmpty
    public List<String> months;
}
