package com.haier.devops.bill.common.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import feign.Param;
import feign.RequestLine;
import lombok.Data;

import java.util.List;

/**
 * ALM预算API接口
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
public interface AlmBudgetApi {

    /**
     * 查询ALM预算信息
     * 
     * @param year 年份
     * @return 预算信息列表
     */
    @RequestLine("GET /newpro-alm/alm-spring-almbudget/VProjectYusuanApi/selectVProjectYusuan?year={year}")
    List<BudgetItem> getBudgetItems(@Param("year") String year);

    @Data
    class BudgetItem {
        private String id;
        
        @JsonProperty("business_id")
        private String businessId;
        
        @JsonProperty("budget_code")
        private String budgetCode;
        
        private String year;
        
        @JsonProperty("budget_name")
        private String budgetName;
        
        private String orgcode;
        private String orgname;
        
        @JsonProperty("dep_code")
        private String depCode;
        
        @JsonProperty("dep_name")
        private String depName;
        
        @JsonProperty("split_dep_code")
        private String splitDepCode;
        
        @JsonProperty("split_dep_name")
        private String splitDepName;
        
        @JsonProperty("budget_micro_code")
        private String budgetMicroCode;
        
        @JsonProperty("budget_micro_name")
        private String budgetMicroName;
        
        @JsonProperty("budget_type")
        private String budgetType;
        
        @JsonProperty("budget_type_name")
        private String budgetTypeName;
        
        @JsonProperty("budget_category")
        private String budgetCategory;
        
        @JsonProperty("budget_category_name")
        private String budgetCategoryName;
        
        @JsonProperty("expenses_category")
        private String expensesCategory;
        
        @JsonProperty("expenses_category_name")
        private String expensesCategoryName;
        
        @JsonProperty("exist_project")
        private String existProject;
        
        @JsonProperty("project_name")
        private String projectName;
        
        @JsonProperty("project_code")
        private String projectCode;
        
        @JsonProperty("beneficiary_code")
        private String beneficiaryCode;
        
        @JsonProperty("beneficiary_name")
        private String beneficiaryName;
        
        @JsonProperty("project_tag_code")
        private String projectTagCode;
        
        @JsonProperty("project_tag")
        private String projectTag;
        
        @JsonProperty("project_budget")
        private String projectBudget;
        
        @JsonProperty("synchronization_expenses")
        private String synchronizationExpenses;
        
        @JsonProperty("estimated_start_date")
        private String estimatedStartDate;
        
        @JsonProperty("estimated_end_date")
        private String estimatedEndDate;
        
        @JsonProperty("additional_remarks")
        private String additionalRemarks;
        
        @JsonProperty("argument_end")
        private String argumentEnd;
        
        @JsonProperty("budget_director_code")
        private String budgetDirectorCode;
        
        @JsonProperty("budget_director_name")
        private String budgetDirectorName;
        
        @JsonProperty("director_org_code")
        private String directorOrgCode;
        
        @JsonProperty("director_org_name")
        private String directorOrgName;
        
        @JsonProperty("director_platform_code")
        private String directorPlatformCode;
        
        @JsonProperty("director_platform_name")
        private String directorPlatformName;
        
        @JsonProperty("director_small_micro_code")
        private String directorSmallMicroCode;
        
        @JsonProperty("director_small_micro_name")
        private String directorSmallMicroName;
        
        @JsonProperty("director_dep_code")
        private String directorDepCode;
        
        @JsonProperty("director_dep_name")
        private String directorDepName;
        
        @JsonProperty("budget_acceptor_code")
        private String budgetAcceptorCode;
        
        @JsonProperty("budget_acceptor_name")
        private String budgetAcceptorName;
        
        @JsonProperty("acceptor_org_code")
        private String acceptorOrgCode;
        
        @JsonProperty("acceptor_org_name")
        private String acceptorOrgName;
        
        @JsonProperty("acceptor_platform_code")
        private String acceptorPlatformCode;
        
        @JsonProperty("acceptor_platform_name")
        private String acceptorPlatformName;
        
        @JsonProperty("acceptor_small_micro_code")
        private String acceptorSmallMicroCode;
        
        @JsonProperty("acceptor_small_micro_name")
        private String acceptorSmallMicroName;
        
        @JsonProperty("platform_user_code")
        private String platformUserCode;
        
        @JsonProperty("platform_user_name")
        private String platformUserName;
        
        @JsonProperty("accounting_company_code")
        private String accountingCompanyCode;
        
        @JsonProperty("accounting_company_name")
        private String accountingCompanyName;
        
        @JsonProperty("share_micro")
        private String shareMicro;
        
        @JsonProperty("amount_payable")
        private String amountPayable;
        
        @JsonProperty("year_payment_ratio")
        private String yearPaymentRatio;
        
        @JsonProperty("year_payment_amount")
        private String yearPaymentAmount;
        
        @JsonProperty("budget_status")
        private String budgetStatus;
        
        @JsonProperty("budget_balance")
        private String budgetBalance;
        
        @JsonProperty("create_user_code")
        private String createUserCode;
        
        @JsonProperty("create_user_name")
        private String createUserName;
        
        @JsonProperty("create_time")
        private String createTime;
        
        @JsonProperty("last_modified_code")
        private String lastModifiedCode;
        
        @JsonProperty("last_modified_name")
        private String lastModifiedName;
        
        @JsonProperty("last_modified_time")
        private String lastModifiedTime;
        
        @JsonProperty("del_flag")
        private String delFlag;
        
        @JsonProperty("file_uuid")
        private String fileUuid;
        
        @JsonProperty("file_name")
        private String fileName;
        
        @JsonProperty("file_type")
        private String fileType;
        
        @JsonProperty("alm_id")
        private String almId;
        
        private String origin;
        
        @JsonProperty("eam_project_name")
        private String eamProjectName;
        
        @JsonProperty("eam_project")
        private String eamProject;
        
        @JsonProperty("update_flag")
        private String updateFlag;
        
        private String remark1;
        private String remark2;
        private String remark3;
        
        @JsonProperty("benefit_owner_code")
        private String benefitOwnerCode;
        
        @JsonProperty("benefit_owner_name")
        private String benefitOwnerName;
        
        @JsonProperty("benefit_financial_director_code")
        private String benefitFinancialDirectorCode;
        
        @JsonProperty("benefit_financial_director_name")
        private String benefitFinancialDirectorName;
        
        private String rolename;
        
        @JsonProperty("user_code")
        private String userCode;
        
        @JsonProperty("user_name")
        private String userName;
        
        @JsonProperty("start_time")
        private String startTime;
        
        @JsonProperty("end_time")
        private String endTime;
        
        private String comment;
        
        @JsonProperty("project_objectives")
        private String projectObjectives;
        
        @JsonProperty("project_scope")
        private String projectScope;
    }
}
