package com.haier.devops.bill.common.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.ReconciliationTask;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;

import java.util.List;

/**
* @ClassName: IBcReconciliationTaskService
* @Description: 调账任务
* @author: 张爱苹
* @date: 2024/3/13 13:39
*/
public interface ReconciliationTaskService extends IService<ReconciliationTask> {

    /**
    * @Description:  查询调账任务列表
    * @author: 张爱苹
    * @date: 2024/3/19 11:02
    * @param type:
    * @param status:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.ReconciliationTask>
    */
    List<ReconciliationTask> queryList(String type);

    /**
    * @Description:  更新调账任务状态
    * @author: 张爱苹
    * @date: 2024/3/19 17:01
    * @param resultList:
    * @param i:
    * @Return: void
    */
    void updateTaskStatus(List<ResourceInstanceVo> resultList, int i,String errorMsg);
}
