package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
* @ClassName: BillGathered
* @Description: 每日账单汇总
* @author: 张爱苹
* @date: 2024/1/16 09:18
*/
@Data
public class BillGatheredVo implements Serializable {


    private static final long serialVersionUID = -7362088286757220241L;
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 记录的账号信息-账户名称
     */
    private String accountName;

    /**
     * 记录的账号信息-账户ID
     */
    private String accountId;

    /**
     * 账期
     */
    private String billingCycle;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源id
     */
    private String instanceId;


    /**
     * S码
     */
    private String scode;


    /**
     *  应用名称
     */
    private String appName;

    /**
     * 项目简称
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 产品明细
     */
    private String productDetail;

    /**
     * 汇总资源id
     */
    private String aggregatedId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 汇总
     */
    private String costSummary;

    /**
     * 告警等级id
     */
    private Integer levelId;
    /**
     * 告警等级 字典值
     */
    private String level;
    /**
     * 告警等级名称
     */
    private String levelName;

    /**
     * S码 s码不为空告警到应用维度，s码为空告警到产品维度
     */
    private String appScode;

    private BigDecimal warnAmount;

    private String  supplementId;

    private String owner;
}
