package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: ZabbixMonitorData
* @Description: zabbix监控数据 实体类
* @author: 张爱苹
* @date: 2024/4/17 10:33
*/
@Data
@TableName("mq_monitor_data")
public class ZabbixMonitorData implements Serializable {


    private static final long serialVersionUID = 108940534639920449L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String instanceId;

    private String tpsAvg;

    private String tpsMax;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String sourceType;

}
