package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: AlarmCondition
* @Description: 告警条件
* @author: 张爱苹
* @date: 2024/1/31 10:19
*/
@Data
@TableName("bc_alarm_condition")
public class AlarmCondition implements Serializable {
    private static final long serialVersionUID = 5739953335023939036L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 条件名称
     */
    private String conditionName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

}

