package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: CmdbProductOverviewDTO
* @Description:  cmdb总表
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class CmdbProductOverviewDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -2597676481668306147L;
    /**
     * S码
     */
    private String scode;

    /**
     * 云厂商
     */
    private String vendor;


    /**
     * 云账户
     */
    private String accountName;


    /**
     * 资源实例id
     */
    private String instanceId;

    /**
     * 云产品
     */
    private String productCode;

    /**
     * 分拆项
     */
    private String supplementId;

    private String scodeUnverified;

    private String reconciliationType;

    private String reconciliationStatus;

    private String createBy;

    private String startTime;

    private String endTime;

}
