package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.MigratedBill;
import com.haier.devops.bill.common.mapper.MigratedBillMapper;
import com.haier.devops.bill.common.service.MigratedBillService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账单明细汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Service
public class MigratedBillServiceImpl extends ServiceImpl<MigratedBillMapper, MigratedBill> implements MigratedBillService {
    private MigratedBillMapper migratedBillMapper;

    public MigratedBillServiceImpl(MigratedBillMapper migratedBillMapper) {
        this.migratedBillMapper = migratedBillMapper;
    }

    @Override
    public int cleanDirtyData(String subTaskId) {
        return migratedBillMapper.delete(new LambdaQueryWrapper<MigratedBill>()
                .eq(MigratedBill::getSubTaskId, subTaskId));
    }

    @Override
    public String getSumBySubTaskId(String subTaskId) {
        return migratedBillMapper.getSumBySubTaskId(subTaskId);
    }
}
