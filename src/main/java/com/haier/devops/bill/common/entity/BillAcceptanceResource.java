package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 账单确认验收资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@TableName("bc_bill_acceptance_resource")
public class BillAcceptanceResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 聚合ID
     */
    private String aggregatedId;

    /**
     * 账期（月）
     */
    private LocalDate billingCycle;

    /**
     * S码
     */
    private String scode;

    /**
     * 消费金额
     */
    private BigDecimal consumeAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 资源负责人
     */
    private String resourceOwner;

    /**
     * 系统负责人
     */
    private String systemOwner;

    /**
     * 领域负责人
     */
    private String domainOwner;

    /**
     * 资源批次ID
     */
    private String resourceBatchId;

    /**
     * 系统批次ID
     */
    private String systemBatchId;

    /**
     * 领域批次ID
     */
    private String domainBatchId;

    /**
     * 资源负责人确认状态 确认中、已确认、已中止
     */
    private String resourceStatus;

    /**
     * 系统负责人确认状态 确认中、已确认、已中止
     */
    private String systemStatus;

    /**
     * 领域负责人确认状态 确认中、已确认、已中止
     */
    private String domainStatus;

    /**
     * 申请核对角色
     */
    private String checkRole;

    /**
     * 申请核对人
     */
    private String checkUserId;

    /**
     * 申请核对状态 审核中、审核完成
     */
    private String checkStatus;

    /**
     * 是否已经重新生成
     */
    private Integer isReGenerated;

    /**
     * 申请核对描述
     */
    private String checkRemark;

    /**
     * 重新生成数据的原ID
     */
    private Long sourceId;

    /**
     * 是否已经转办
     */
    private Integer isReassign;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getAggregatedId() {
        return aggregatedId;
    }

    public void setAggregatedId(String aggregatedId) {
        this.aggregatedId = aggregatedId;
    }
    public LocalDate getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(LocalDate billingCycle) {
        this.billingCycle = billingCycle;
    }
    public String getScode() {
        return scode;
    }

    public void setScode(String scode) {
        this.scode = scode;
    }
    public BigDecimal getConsumeAmount() {
        return consumeAmount;
    }

    public void setConsumeAmount(BigDecimal consumeAmount) {
        this.consumeAmount = consumeAmount;
    }
    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
    public String getResourceOwner() {
        return resourceOwner;
    }

    public void setResourceOwner(String resourceOwner) {
        this.resourceOwner = resourceOwner;
    }
    public String getSystemOwner() {
        return systemOwner;
    }

    public void setSystemOwner(String systemOwner) {
        this.systemOwner = systemOwner;
    }
    public String getDomainOwner() {
        return domainOwner;
    }

    public void setDomainOwner(String domainOwner) {
        this.domainOwner = domainOwner;
    }
    public String getResourceBatchId() {
        return resourceBatchId;
    }

    public void setResourceBatchId(String resourceBatchId) {
        this.resourceBatchId = resourceBatchId;
    }
    public String getSystemBatchId() {
        return systemBatchId;
    }

    public void setSystemBatchId(String systemBatchId) {
        this.systemBatchId = systemBatchId;
    }
    public String getDomainBatchId() {
        return domainBatchId;
    }

    public void setDomainBatchId(String domainBatchId) {
        this.domainBatchId = domainBatchId;
    }
    public String getResourceStatus() {
        return resourceStatus;
    }

    public void setResourceStatus(String resourceStatus) {
        this.resourceStatus = resourceStatus;
    }
    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }
    public String getDomainStatus() {
        return domainStatus;
    }

    public void setDomainStatus(String domainStatus) {
        this.domainStatus = domainStatus;
    }
    public String getCheckRole() {
        return checkRole;
    }

    public void setCheckRole(String checkRole) {
        this.checkRole = checkRole;
    }
    public String getCheckUserId() {
        return checkUserId;
    }

    public void setCheckUserId(String checkUserId) {
        this.checkUserId = checkUserId;
    }
    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }
    public Integer getIsReGenerated() {
        return isReGenerated;
    }

    public void setIsReGenerated(Integer isReGenerated) {
        this.isReGenerated = isReGenerated;
    }
    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }
    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }
    public Integer getIsReassign() {
        return isReassign;
    }

    public void setIsReassign(Integer isReassign) {
        this.isReassign = isReassign;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BillAcceptanceResource{" +
            "id=" + id +
            ", aggregatedId=" + aggregatedId +
            ", billingCycle=" + billingCycle +
            ", scode=" + scode +
            ", consumeAmount=" + consumeAmount +
            ", currency=" + currency +
            ", resourceOwner=" + resourceOwner +
            ", systemOwner=" + systemOwner +
            ", domainOwner=" + domainOwner +
            ", resourceBatchId=" + resourceBatchId +
            ", systemBatchId=" + systemBatchId +
            ", domainBatchId=" + domainBatchId +
            ", resourceStatus=" + resourceStatus +
            ", systemStatus=" + systemStatus +
            ", domainStatus=" + domainStatus +
            ", checkRole=" + checkRole +
            ", checkUserId=" + checkUserId +
            ", checkStatus=" + checkStatus +
            ", isReGenerated=" + isReGenerated +
            ", checkRemark=" + checkRemark +
            ", sourceId=" + sourceId +
            ", isReassign=" + isReassign +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
        "}";
    }
}
