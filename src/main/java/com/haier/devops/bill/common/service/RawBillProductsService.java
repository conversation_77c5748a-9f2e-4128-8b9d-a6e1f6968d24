package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.RawBillProductsDTO;
import com.haier.devops.bill.common.dto.RawBillProductsWithVendorsDTO;
import com.haier.devops.bill.common.entity.RawBillProducts;
import com.haier.devops.bill.common.vo.RawBillProductTreeVo;

import java.util.List;
import java.util.Map;

/**
* @ClassName: RawBillProductsService
* @Description: 云产品 服务类
* @author: 张爱苹
* @date: 2024/1/15 16:04
*/
public interface RawBillProductsService extends IService<RawBillProducts> {

    /**
    * @Description:  获取云产品列表
    * @author: 张爱苹
    * @date: 2024/1/15 16:08
    * @param rawBillProductsDTO:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.RawBillProducts>
    */
    List<RawBillProducts> getRawBillProductList(RawBillProductsDTO rawBillProductsDTO);



    /**
     * @Description:  获取云产品树列表
     * @author: 张爱苹
     * @date: 2024/1/15 16:08
     * @param searchContent:
     * @Return: java.util.List<com.haier.devops.bill.common.entity.RawBillProducts>
     */
    List<RawBillProductTreeVo> getRawBillProductTreeList(String searchContent,String appScode);

    /**
    * @Description: 查询通用产品类型
    * @author: 张爱苹
    * @date: 2024/9/5 10:35
    * @param dto:
    * @Return: java.util.List<java.util.Map>
    */
    List<Map> getAliasProducts(RawBillProductsDTO dto);

   /**
    * @Description: 根据云产品编码或名称查询云产品
    * @author: 张爱苹
    * @date: 2025/3/14 13:14
    * @param product:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.RawBillProducts>
    */
    List<RawBillProducts> getProductsByProduct(String product,String vendor);

    /**
     * 查询通用产品类型(支持多个云厂商)
     * @param dto
     * @return
     */
    List<Map> getAliasProductsWithVendors(RawBillProductsWithVendorsDTO dto);
}
