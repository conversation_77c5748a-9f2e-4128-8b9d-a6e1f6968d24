package com.haier.devops.bill.common.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BillChargesProductsVo implements Serializable {
    private static final long serialVersionUID = 280844322502592954L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "云厂商")
    private String vendor;


    @Schema(description = "产品编码")
    private String productCode;


    @Schema(description = "产品名称")
    private String productName;


    @Schema(description = "计费项")
    private String billingItem;


    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Schema(description = "创建人账号")
    private String createBy;


    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Schema(description = "更新人账号")
    private String updateBy;


    @Schema(description = "删除标识 0：有效 1：无效")
    private String delFlag;


    @Schema(description = "产品主键")
    private String productId;

}
