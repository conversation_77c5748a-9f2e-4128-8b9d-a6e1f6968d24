package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.IamProjects;

import java.util.List;

/**
* @ClassName: IamProjectsService
* @Description: 项目
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
public interface IamProjectsService extends IService<IamProjects> {

    void insertIamProjects(String accountName) throws Exception;

    List<IamProjects> getIamProjects(String accountName);
}
