package com.haier.devops.bill.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "feign.okhttp")
public class OkHttpProperties {
    private boolean followRedirects = true;
    /**
     * 链接超时时间，单位毫秒
     */
    private int connectTimeout = 5000;
    private boolean disableSslValidation = true;

    /**
     * 读超时，单位毫秒
     */
    private int readTimeout = 5000;

    /**
     * 写超时，单位毫秒
     */
    private int writeTimeout = 5000;

    /**
     * 是否自动重连
     */
    private boolean retryOnConnectionFailure = true;

    /**
     * 最大空闲链接
     */
    private int maxIdleConnections = 10;

    /**
     * 默认保持5分钟
     */
    private long keepAliveDuration = 1000 * 60 * 5L;
}
