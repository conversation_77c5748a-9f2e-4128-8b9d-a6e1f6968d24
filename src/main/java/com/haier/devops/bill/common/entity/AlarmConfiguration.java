package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* @ClassName: AlarmConfiguration
* @Description: 告警配置实体类
* @author: 张爱苹
* @date: 2024/1/11 10:51
*/
@Data
@TableName("bc_cp_alarm_configuration")
@Builder
public class AlarmConfiguration {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * S码
     */
    private String appScode;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 是否开启告警：0否；1是
     */
    private Integer isEnableAlarm;

    /**
     * 有效期开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startValidPeriodTime;

    /**
     * 有效期结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endValidPeriodTime;

    /**
     * 告警类型：单日账单费用突增；（字典获取）
     */
    private String alarmType;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

    /**
     * 创建人姓名
     */
    private String createByName;

    /**
     * 更新人姓名
     */
    private String updateByName;

    /**
     *  告警金额
     */
    private String warnAmount;
}
