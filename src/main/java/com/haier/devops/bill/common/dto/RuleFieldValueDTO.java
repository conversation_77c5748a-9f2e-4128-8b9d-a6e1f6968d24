package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: AggregatedBillDTO
* @Description:  账单明细汇总
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class RuleFieldValueDTO implements Serializable {


    private static final long serialVersionUID = 6895460154756823270L;

    /**
     * 规则类型
     */
    @NotEmpty
    private String ruleType;

    /**
     * 账号
     */
    @NotEmpty
    private String accountName;

    /**
     * 商品名称
     */
    @NotEmpty
    private String commodityCode;

    /**
     * 规则字段
     */
    @NotEmpty
    private String ruleField;



}
