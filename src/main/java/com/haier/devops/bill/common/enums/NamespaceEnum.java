package com.haier.devops.bill.common.enums;

import java.util.HashMap;
import java.util.Map;

public enum NamespaceEnum {
    SINGLE_NAMESPACE("single_namespace"),
    HWORK_SINGLE_NAMESPACE("hwork_single_namespace"),
    HWORK_MULTI_NAMESPACE("hwork_multi_namespace"); 

    private String namespace;
    private static final Map<String, NamespaceEnum> NAMESPACE_MAP = new HashMap<>();

    static {
        for (NamespaceEnum namespaceEnum : values()) {
            NAMESPACE_MAP.put(namespaceEnum.getNamespace(), namespaceEnum);
        }
    }

    NamespaceEnum(String namespace) {
        this.namespace = namespace;
    }

    public String getNamespace() {
        return namespace;
    }

    public static Map<String, NamespaceEnum> getNamespaceMap() {
        return NAMESPACE_MAP;
    }
}
