package com.haier.devops.bill.common.api;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import feign.RequestLine;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.bind.annotation.RequestBody;

/**
* @ClassName: HworkApi
* @Description: IM接口
* @author: 张爱苹
* @date: 2024/1/24 13:17
*/
public interface HworkApi {

    @RequestLine("POST /api/fa/im/user/v1/open/group/create")
    ResultWrapper<Group> createGroup(@RequestBody JSONObject object);

    @Setter
    @Getter
    class ResultWrapper<T> {
        private int code;
        private String message;
        private String msg;
        private T data;
    }

    @Data
    class Group {
        @JsonProperty("accessSystemTag")
        private String accessSystemTag;
        @JsonProperty("ownerAccount")
        private String ownerAccount;
        @JsonProperty("groupId")
        private String groupId;
        @JsonProperty("robotId")
        private String robotId;
        @JsonProperty("groupName")
        private String groupName;
    }
}
