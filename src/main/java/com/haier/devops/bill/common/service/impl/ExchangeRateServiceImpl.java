package com.haier.devops.bill.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.ExchangeRateApiResponse;
import com.haier.devops.bill.common.entity.ExchangeRate;
import com.haier.devops.bill.common.mapper.ExchangeRateMapper;
import com.haier.devops.bill.common.service.ExchangeRateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * @ClassName: ExchangeRateServiceImpl
 * @Description: 汇率服务实现类
 * @author: System
 * @date: 2025/06/30
 */
@Slf4j
@Service
public class ExchangeRateServiceImpl extends ServiceImpl<ExchangeRateMapper, ExchangeRate> implements ExchangeRateService {

    @Autowired
    private ExchangeRateMapper exchangeRateMapper;

    @Value("${exchange.rate.api.url:https://v6.exchangerate-api.com/v6/************************/latest}")
    private String apiBaseUrl;

    @Value("${exchange.rate.api.timeout:10000}")
    private int apiTimeout;

    @Override
    public ExchangeRateApiResponse fetchLatestExchangeRates(String baseCurrency) throws Exception {
        if (StringUtils.isEmpty(baseCurrency)) {
            baseCurrency = "CNY";
        }

        String apiUrl = apiBaseUrl + "/" + baseCurrency;
        log.info("开始获取汇率数据，API URL: {}", apiUrl);

        HttpURLConnection connection = null;
        BufferedReader reader = null;
        
        try {
            URL url = new URL(apiUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(apiTimeout);
            connection.setReadTimeout(apiTimeout);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "Haier-DevOps-Bill-Center/1.0");

            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                throw new RuntimeException("汇率API请求失败，响应码: " + responseCode);
            }

            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }

            String responseBody = response.toString();
            log.info("汇率API响应: {}", responseBody);

            ExchangeRateApiResponse apiResponse = JSON.parseObject(responseBody, ExchangeRateApiResponse.class);
            
            if (!"success".equals(apiResponse.getResult())) {
                throw new RuntimeException("汇率API返回失败状态: " + apiResponse.getResult());
            }

            log.info("成功获取汇率数据，基础货币: {}, 汇率数量: {}", 
                    apiResponse.getBaseCode(), 
                    apiResponse.getConversionRates() != null ? apiResponse.getConversionRates().size() : 0);

            return apiResponse;

        } catch (Exception e) {
            log.error("获取汇率数据失败", e);
            throw new Exception("获取汇率数据失败: " + e.getMessage(), e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    log.warn("关闭reader失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncExchangeRatesToDatabase(ExchangeRateApiResponse apiResponse) throws Exception {
        if (apiResponse == null || apiResponse.getConversionRates() == null) {
            throw new IllegalArgumentException("汇率数据为空");
        }

        log.info("开始同步汇率数据到数据库，基础货币: {}", apiResponse.getBaseCode());

        // 获取当前日期作为汇率生效日期
        LocalDate currentDate = LocalDate.now();
        Date rateDate = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // API更新时间
        Date apiUpdateTime = null;
        if (apiResponse.getTimeLastUpdateUnix() != null) {
            apiUpdateTime = Date.from(Instant.ofEpochSecond(apiResponse.getTimeLastUpdateUnix()));
        }

        // 先删除当天的旧汇率数据
        exchangeRateMapper.deleteByDate(rateDate);
        log.info("已清理当天的旧汇率数据");

        List<ExchangeRate> exchangeRates = new ArrayList<>();
        int successCount = 0;
        int errorCount = 0;

        for (Map.Entry<String, Double> entry : apiResponse.getConversionRates().entrySet()) {
            try {
                String targetCurrency = entry.getKey();
                Double rate = entry.getValue();

                if (rate == null || rate <= 0) {
                    log.warn("跳过无效汇率数据: {} = {}", targetCurrency, rate);
                    errorCount++;
                    continue;
                }

                // 创建汇率记录
                ExchangeRate exchangeRate = new ExchangeRate();
                exchangeRate.setBaseCurrency(apiResponse.getBaseCode());
                exchangeRate.setTargetCurrency(targetCurrency);
                exchangeRate.setExchangeRate(BigDecimal.valueOf(rate));
                exchangeRate.setRateDate(rateDate);
                exchangeRate.setApiUpdateTime(apiUpdateTime);
                exchangeRate.setCreateTime(new Date());
                exchangeRate.setUpdateTime(new Date());
                exchangeRate.setStatus(1);

                exchangeRates.add(exchangeRate);
                successCount++;

                // 批量插入，每100条一批
                if (exchangeRates.size() >= 100) {
                    saveBatch(exchangeRates);
                    exchangeRates.clear();
                    log.info("已同步 {} 条汇率数据", successCount);
                }

            } catch (Exception e) {
                errorCount++;
                log.error("处理汇率数据失败: {} = {}", entry.getKey(), entry.getValue(), e);
            }
        }

        // 插入剩余的数据
        if (!exchangeRates.isEmpty()) {
            saveBatch(exchangeRates);
        }

        log.info("汇率数据同步完成，成功: {}, 失败: {}", successCount, errorCount);

        if (errorCount > 0 && successCount == 0) {
            throw new Exception("所有汇率数据同步失败");
        }
    }

    @Override
    public void performFullSync() throws Exception {
        log.info("开始执行完整汇率同步流程");

        try {
            // 1. 获取最新汇率数据
            ExchangeRateApiResponse apiResponse = fetchLatestExchangeRates("CNY");

            // 2. 同步到数据库
            syncExchangeRatesToDatabase(apiResponse);

            log.info("完整汇率同步流程执行成功");

        } catch (Exception e) {
            log.error("完整汇率同步流程执行失败", e);
            throw e;
        }
    }

    @Override
    public ExchangeRate getExchangeRate(String baseCurrency, String targetCurrency, Date rateDate) {
        return exchangeRateMapper.selectByDateAndCurrency(baseCurrency, targetCurrency, rateDate);
    }

    @Override
    public List<ExchangeRate> getExchangeRatesByDate(Date rateDate) {
        return exchangeRateMapper.selectByDate(rateDate);
    }
}
