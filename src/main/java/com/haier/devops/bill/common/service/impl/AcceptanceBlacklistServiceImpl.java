package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.AcceptanceBlacklist;
import com.haier.devops.bill.common.mapper.AcceptanceBlacklistMapper;
import com.haier.devops.bill.common.service.AcceptanceBlacklistService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 验收推送黑名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Service
public class AcceptanceBlacklistServiceImpl extends ServiceImpl<AcceptanceBlacklistMapper, AcceptanceBlacklist> implements AcceptanceBlacklistService {

    private final AcceptanceBlacklistMapper acceptanceBlacklistMapper;

    public AcceptanceBlacklistServiceImpl(AcceptanceBlacklistMapper acceptanceBlacklistMapper) {
        this.acceptanceBlacklistMapper = acceptanceBlacklistMapper;
    }

    @Override
    public Boolean isInBlacklist(String vendor, String sysCode) {
        return acceptanceBlacklistMapper.checkInBlacklist(vendor, sysCode);
    }
}
