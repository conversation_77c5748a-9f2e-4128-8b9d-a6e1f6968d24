package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.DocConfig;
import com.haier.devops.bill.common.mapper.DocConfigMapper;
import com.haier.devops.bill.common.service.DocConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账单中心文档配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Service
public class DocConfigServiceImpl
        extends ServiceImpl<DocConfigMapper, DocConfig> implements DocConfigService {

    private DocConfigMapper docConfigMapper;

    public DocConfigServiceImpl(DocConfigMapper docConfigMapper) {
        this.docConfigMapper = docConfigMapper;
    }

    @Override
    public DocConfig getLatestDocConfig(String type, String business) {
        Wrapper<DocConfig> wrapper = new LambdaQueryWrapper<DocConfig>()
                .eq(DocConfig::getType, type)
                .eq(DocConfig::getBusiness, business)
                .eq(DocConfig::getIsEnabled, 1)
                .orderByDesc(DocConfig::getCreateTime)
                .last("limit 1");
        return docConfigMapper.selectOne(wrapper);
    }

    @Override
    public DocConfig getDocConfig(String type, String business, String billingCycle) {
        Wrapper<DocConfig> wrapper = new LambdaQueryWrapper<DocConfig>()
                .eq(DocConfig::getType, type)
                .eq(DocConfig::getBusiness, business)
                .eq(DocConfig::getBillingCycle, billingCycle)
                .eq(DocConfig::getIsEnabled, 1)
                .orderByDesc(DocConfig::getCreateTime)
                .last("limit 1");
        return docConfigMapper.selectOne(wrapper);
    }
}
