package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 代金券
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@TableName("bc_voucher")
@Data
public class Voucher implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云厂商
     */
    @NotNull(message = "云厂商不能为空")
    private String vendor;

    /**
     * 云账号
     */
    @NotNull(message = "云账号不能为空")
    private String accountName;

    /**
     * general: 公用券  limited:定向券
     */
    @NotNull(message = "类型不能为空")
    private String type;

    /**
     * 金额
     */
    @DecimalMin(value = "0.0", inclusive = false, message = "金额不能小于0")
    private BigDecimal amount;

    /**
     * 账期
     */
    @NotNull(message = "账期不能为空")
    private String billingCycle;

    /**
     * 应用到的S码（逗号分隔）
     */
    private String scodes;

    private Integer isEnabled;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createdBy;

    private String updatedBy;
}
