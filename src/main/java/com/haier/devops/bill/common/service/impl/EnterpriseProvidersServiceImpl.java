package com.haier.devops.bill.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HuaweiApi;
import com.haier.devops.bill.common.entity.EnterpriseProviders;
import com.haier.devops.bill.common.mapper.EnterpriseProvidersMapper;
import com.haier.devops.bill.common.service.EnterpriseProvidersService;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.ListProvidersResponse;
import com.huaweicloud.sdk.eps.v1.model.ProviderResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* @ClassName: EnterpriseProjectsServiceImpl
* @Description: 企业项目支持的服务
* @author: 张爱苹
* @date: 2025/2/13 14:58
*/
@Service
public class EnterpriseProvidersServiceImpl
        extends ServiceImpl<EnterpriseProvidersMapper, EnterpriseProviders> implements EnterpriseProvidersService {
    private static Logger logger = LoggerFactory.getLogger(EnterpriseProvidersServiceImpl.class);


    @Autowired
    private HuaweiApi huaweiApi;

    @Override
    public void insertEnterpriseProviders(String accountName) throws Exception{
        EpsClient epsClient = huaweiApi.getEpsClient(accountName,"cn-north-4");
        Integer offset = 0;
        Integer limit = 100;
        ListProvidersResponse response = huaweiApi.listEnterpriseProviders(epsClient,offset,limit);
        if(response == null){
            throw new RuntimeException("获取企业项目支持的服务失败");
        }
        int total = response.getTotalCount();
        List<String> resourceTypeList = new ArrayList<>();
        if(total > 0){
            List<ProviderResponseBody> list = response.getProviders();
            int totalPage = total % limit == 0 ? total / limit : total / limit + 1;
            for(int i = 2;i < totalPage;i++){
                offset = (i - 1) * limit;
                response = huaweiApi.listEnterpriseProviders(epsClient,offset,limit);
                if(response == null){
                    logger.error("获取企业项目支持的服务失败,{},{},{}",accountName,offset,limit);
                    continue;
                }
                list.addAll(response.getProviders());
            }
            List<EnterpriseProviders> addList = new ArrayList<>();
            List<EnterpriseProviders> updateList = new ArrayList<>();

            list.stream().forEach(item->{
                item.getResourceTypes().stream().forEach(resourceType->{
                    EnterpriseProviders enterpriseProviders = EnterpriseProviders.builder()
                            .provider(item.getProvider())
                            .providerI18nDisplay_name(item.getProviderI18nDisplayName())
                            .resourceType(resourceType.getResourceType())
                            .resourceTypeI18nDisplay_name(resourceType.getResourceTypeI18nDisplayName())
                            .regions(JSON.toJSONString(resourceType.getRegions()))
                            .global(resourceType.getGlobal())
                            .delFlag("0").build();
                    EnterpriseProviders old = getOneByResourceType(resourceType.getResourceType());
                    if(old == null){
                        addList.add(enterpriseProviders);
                    }else{
                        enterpriseProviders.setId(old.getId());
                        updateList.add(enterpriseProviders);
                    }
                    resourceTypeList.add(resourceType.getResourceType());
                });
            });
            if(CollectionUtils.isNotEmpty(addList)){
                saveBatch(addList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                updateBatchById(updateList);
            }
        }
        //删除不在idList的企业项目支持的服务
        deleteBatchNoIds(resourceTypeList);
    }

    @Override
    public List<EnterpriseProviders> getEnterpriseProviders() {
        LambdaQueryWrapper<EnterpriseProviders> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EnterpriseProviders::getDelFlag,"0");
        return list(lambdaQueryWrapper);
    }

    private EnterpriseProviders getOneByResourceType(String resourceType) {
        LambdaQueryWrapper<EnterpriseProviders> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EnterpriseProviders::getResourceType,resourceType);
        lambdaQueryWrapper.eq(EnterpriseProviders::getDelFlag,"0");
        return getOne(lambdaQueryWrapper);
    }

    private void deleteBatchNoIds(List<String> resourceTypeList) {
        UpdateWrapper<EnterpriseProviders> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("del_flag","1");
        updateWrapper.eq("del_flag","0");
        if(CollectionUtils.isNotEmpty(resourceTypeList)){
            updateWrapper.notIn("resource_type",resourceTypeList);
        }
        update(updateWrapper);
    }
}
