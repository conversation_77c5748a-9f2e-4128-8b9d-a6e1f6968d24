package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* @ClassName: AlarmConfigurationCreateDTO
* @Description: 告警配置 dto
* @author: 张爱苹
* @date: 2024/1/11 11:14
*/
@Data
public class IsEnableAlarmDTO implements Serializable {


    private static final long serialVersionUID = 3983833639267102864L;
    private Integer id;

    /**
     * 是否开启告警：0否；1是
     */
    @NotNull
    private Integer isEnableAlarm;


}
