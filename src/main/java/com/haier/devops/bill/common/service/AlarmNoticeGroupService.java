package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupCreateDTO;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupDTO;
import com.haier.devops.bill.common.entity.AlarmNoticeGroup;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupVo;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupService
* @Description:  告警通知组服务
* @author: 张爱苹
* @date: 2024/1/12 17:23
*/
public interface AlarmNoticeGroupService extends IService<AlarmNoticeGroup> {
    /**
    * @Description: 删除通知组
    * @author: 张爱苹
    * @date: 2024/1/12 17:33
    * @param id:
    * @Return: void
    */
    void deleteAlarmNoticeGroup(Integer id)throws Exception;

    /**
    * @Description: 更新通知组
    * @author: 张爱苹
    * @date: 2024/1/12 17:34
    * @param id:
    * @param alarmNoticeGroupCreateDTO:
    * @Return: void
    */
    void updateAlarmNoticeGroup(Integer id, AlarmNoticeGroupCreateDTO alarmNoticeGroupCreateDTO)throws Exception;

    /**
    * @Description: 创建通知组
    * @author: 张爱苹
    * @date: 2024/1/12 17:34
    * @param alarmNoticeGroupCreateDTO:
    * @Return: void
    */
    void createAlarmNoticeGroup(AlarmNoticeGroupCreateDTO alarmNoticeGroupCreateDTO)throws Exception;

    /**
    * @Description: 根据id查询通知组
    * @author: 张爱苹
    * @date: 2024/1/12 17:34
    * @param id:
    * @Return: com.haier.devops.bill.common.entity.AlarmNoticeGroup
    */
    AlarmNoticeGroupVo getAlarmNoticeGroupById(Integer id) throws Exception;

    /**
    * @Description: 分页查询通知组
    * @author: 张爱苹
    * @date: 2024/1/12 17:34
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.AlarmNoticeGroup>
    */
    PageInfo<AlarmNoticeGroupVo> listByPage(AlarmNoticeGroupDTO dto)throws Exception;

    /**
    * @Description: 根据id查询通知组下的通知对象
    * @author: 张爱苹
    * @date: 2024/2/1 15:40
    * @param noticeObjIdArray:
    * @Return: java.util.List<com.haier.devops.bill.common.dto.AlarmNoticeObjDTO>
    */
    List<AlarmNoticeObjVo> getAlarmNoticeGroupByIdList(String[] noticeObjIdArray);

    /**
    * @Description: 根据账号获取用户信息
    * @author: 张爱苹
    * @date: 2024/2/1 18:38
    * @param account:
    * @Return: com.haier.devops.bill.common.vo.AlarmNoticeObjVo
    */
    AlarmUserInfo getAlarmNoticeObj(String account);

    /**
    * @Description: 保存人员信息
    * @author: 张爱苹
    * @date: 2024/2/2 14:13
    * @param alarmUserInfoDTOList:
    * @Return: void
    */
    void saveUserInfo(List<AlarmNoticeGroupDetail> alarmUserInfoDTOList) throws Exception;

    /**
    * @Description: 更新用户信息
    * @author: 张爱苹
    * @date: 2024/2/2 15:16
    * @param alarmUserInfo:
    * @Return: void
    */
    AlarmUserInfo getUpdateUserInfo(AlarmUserInfo alarmUserInfo);

    /**
    * @Description: 校验组名是否重复
    * @author: 张爱苹
    * @date: 2024/3/5 16:49
    * @param groupName:
    * @Return: boolean
    */
    boolean checkGroupzName(String groupName);

    /**
    * @Description: 获取手机号
    * @author: 张爱苹
    * @date: 2025/6/26 16:58
    * @param account:
    * @Return: java.lang.String
    */
    List getUserPhone(String account) throws Exception;
}
