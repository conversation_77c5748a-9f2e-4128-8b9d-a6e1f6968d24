package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.DataPrivilegeDimension;
import com.haier.devops.bill.common.mapper.DataPrivilegeDimensionMapper;
import com.haier.devops.bill.common.service.DataPrivilegeDimensionService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据权限维度表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Service
public class DataPrivilegeDimensionServiceImpl extends ServiceImpl<DataPrivilegeDimensionMapper, DataPrivilegeDimension> implements DataPrivilegeDimensionService {

}
