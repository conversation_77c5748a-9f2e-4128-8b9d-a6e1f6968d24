package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.DataPrivilege;
import com.haier.devops.bill.common.vo.UserDataPrivilegeVo;

import java.util.List;

/**
 * <p>
 * 数据权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface DataPrivilegeService extends IService<DataPrivilege> {
    /**
     * 查询用户某一维度下的数据权限
     * @param dimension
     * @param userId
     * @return
     */
    List<UserDataPrivilegeVo> selectUserDataPrivilegeByDimension(String dimension, String userId);
}
