package com.haier.devops.bill.common.distributed_lock;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR>
 */
public class RedisDistributedLock {
    private static final String LOCK_SUCCESS = "OK";
    private static final Long RELEASE_SUCCESS = 1L;

    /**
     * Try to get distributed lock
     *
     * @param lockKey    lock
     * @param requestId  Request ID
     * @param expireTime Overdue time
     * @return Success or not
     */
    public static boolean tryGetDistributedLock(Jedis jedis, String lockKey, String requestId, long expireTime) {
        String result = jedis.set(lockKey, requestId, new SetParams().nx().ex(expireTime));
        return LOCK_SUCCESS.equals(result);

    }

    /**
     * Release distributed lock
     *
     * @param lockKey   lock
     * @return Whether to release successfully
     */
    public static boolean releaseDistributedLock(<PERSON><PERSON> jedis, String lockKey) {
        Long result = jedis.del(lockKey);
        return RELEASE_SUCCESS.equals(result);
    }

}


