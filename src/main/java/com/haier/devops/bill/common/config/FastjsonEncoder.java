package com.haier.devops.bill.common.config;

import com.alibaba.fastjson.JSON;
import feign.RequestTemplate;
import feign.codec.EncodeException;
import feign.codec.Encoder;

import java.lang.reflect.Type;

public class FastjsonEncoder implements Encoder {
    @Override
    public void encode(Object object, Type bodyType, RequestTemplate template) throws EncodeException {
        try {
            String json = JSON.toJSONString(object);
            template.body(json);
            template.header("Content-Type", "application/json");
        } catch (Exception e) {
            throw new EncodeException("Fastjson2 encode error", e);
        }
    }}
