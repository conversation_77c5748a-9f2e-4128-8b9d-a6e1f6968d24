package com.haier.devops.bill.common.enums;

/**
* @ClassName: ReconciliationEnum
* @Description: 调账类型
* @author: 张爱苹
* @date: 2024/3/11 16:29
*/
public enum ReconciliationEnum {
    ALL("all","全部账单"),
    PART("part","部分账单");

    ReconciliationEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
