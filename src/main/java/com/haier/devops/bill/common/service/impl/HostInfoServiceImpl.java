package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.haier.devops.bill.common.entity.HostInfo;
import com.haier.devops.bill.common.mapper.HostInfoMapper;
import com.haier.devops.bill.common.service.HostInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务器信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
public class HostInfoServiceImpl extends ServiceImpl<HostInfoMapper, HostInfo> implements HostInfoService {
    private final HostInfoMapper hostInfoMapper;

    public HostInfoServiceImpl(HostInfoMapper hostInfoMapper) {
        this.hostInfoMapper = hostInfoMapper;
    }

    @Override
    public PageInfo<HostInfo> list(List<String> scodes, int page, int pageSize, String... currency) {
        return PageMethod.startPage(page, pageSize)
                .doSelectPageInfo(() -> hostInfoMapper.selectByScodes(scodes, currency));
    }
}
