package com.haier.devops.bill.common.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: AlarmNoticeObjVo
* @Description:  告警通知对象
* @author: 张爱苹
* @date: 2024/1/17 11:22
*/
@Data
@Builder
public class AlarmNoticeObjVo implements Serializable {


    private static final long serialVersionUID = -3648665221578967031L;
    /**
     * 通知人账号或通知组ID或群id
     */
    private String noticeObjId;

    /**
     * 通知人姓名
     */
    private String noticeObjName;

    /**
     * 通知人手机号
     */
    private String mobile;

    /**
     *  通知人邮箱
     */
    private String email;




}
