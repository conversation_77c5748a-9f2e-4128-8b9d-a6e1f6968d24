package com.haier.devops.bill.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.IdmOrg;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.IdmOrgMapper;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.service.IdmOrgService;
import com.haier.devops.bill.common.vo.DepartIdVo;
import com.haier.devops.bill.util.HeaderUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (IdmOrg)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-23 16:49:32
 */
@Service("idmOrgService")
@DS("pg")
public class IdmOrgServiceImpl extends ServiceImpl<IdmOrgMapper, IdmOrg> implements IdmOrgService {
    @Value("${hcms_admin_orgids}")
    private String hcmsAdminOrgids;
    @Resource
    private IdmOrgMapper idmOrgMapper;
    private AuthorityService authorityService;

    public IdmOrgServiceImpl(AuthorityService authorityService) {
        this.authorityService = authorityService;
    }

    public List<DepartIdVo> getOrgTree(String orgId){
        List<DepartIdVo> res = new ArrayList<>();

        if (Strings.isNotBlank(orgId)){
            List<IdmOrg> list = this.list(new LambdaQueryWrapper<IdmOrg>().ge(IdmOrg::getEndTime, Timestamp.valueOf(LocalDateTime.now())).eq(IdmOrg::getUpId, orgId));
            list.forEach(item -> {
                res.add(new DepartIdVo(item));
            });
        } else {
            // 判断当前用户是否为管理员
            boolean administrators = isAdmin();
            if (administrators){
                List<String> list = new ArrayList<>(Arrays.asList(hcmsAdminOrgids.split(",")));
                List<IdmOrg> orgs = this.list(new LambdaQueryWrapper<IdmOrg>().ge(IdmOrg::getEndTime, Timestamp.valueOf(LocalDateTime.now())).in(IdmOrg::getOrgId, list));

                for (IdmOrg org : orgs) {
                    res.add(new DepartIdVo(org));
                }
                return res;
            }

            String userCode = HeaderUtil.getUserId();
            HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> resultWrapper =
                    authorityService.getUserAuthorities(userCode);
            if (resultWrapper.getCode() == HttpStatus.OK.value()) {
                List<HworkAuthorityApi.Authority> data = resultWrapper.getData();

                Set<String> orgIds = new HashSet<>();
                for (HworkAuthorityApi.Authority datum : data) {
                    orgIds.add(datum.getAuthorityCode());
                }

                String deptId = LoginContextHolder.getCurrentUser().getDeptId();
                orgIds.add(deptId);

                if (CollectionUtils.isNotEmpty(orgIds)) {
                    List<IdmOrg> list = this.list(new LambdaQueryWrapper<IdmOrg>().ge(IdmOrg::getEndTime, Timestamp.valueOf(LocalDateTime.now())).in(IdmOrg::getOrgId, orgIds));

                    for (IdmOrg idmOrg : list) {
                        Optional<IdmOrg> any = list.stream().filter(item -> item.getOrgId().equals(idmOrg.getUpId())).findAny();
                        if (!any.isPresent()){
                            res.add(new DepartIdVo(idmOrg));
                        }
                    }
                }
            }
        }

        return res;
    }

    @Override
    public List<IdmOrg> getAuthedOrgByName(String name) {
        LambdaQueryWrapper<IdmOrg> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isBlank(name)) {
            HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> userAuthorities =
                    authorityService.getUserAuthorities(HeaderUtil.getUserId());
            if (userAuthorities.getCode() != HttpStatus.OK.value()) {
                return new ArrayList<>();
            }
            List<String> deptIds = userAuthorities.getData().stream()
                    .map(HworkAuthorityApi.Authority::getAuthorityCode)
                    .collect(Collectors.toList());
            queryWrapper.ge(IdmOrg::getEndTime, Timestamp.valueOf(LocalDateTime.now()))
                    .in(IdmOrg::getOrgId, deptIds);
        } else {
            queryWrapper.ge(IdmOrg::getEndTime, Timestamp.valueOf(LocalDateTime.now()))
                    .like(IdmOrg::getOrgName, name);
        }

        return this.list(queryWrapper);
    }

    @Override
    public List<IdmOrg> getAllOrgs() {
        return idmOrgMapper.selectList(new LambdaQueryWrapper<IdmOrg>().ge(IdmOrg::getEndTime, new Date()));
    }

    @Override
    public List<IdmOrg> getOrgsByOrgId(List<String> orgIds) {
        return idmOrgMapper.selectList(
                new LambdaQueryWrapper<IdmOrg>()
                        .in(IdmOrg::getOrgId, orgIds)
                        .ge(IdmOrg::getEndTime, new Date())
                        .orderBy(true, true, IdmOrg::getOrgId));
    }

    /**
     * 判断当前用户是否为管理员。
     * @return 当前用户是否为管理员
     */
    public boolean isAdmin(){
        boolean administrators = false;
        User currentUser = LoginContextHolder.getCurrentUser();
        List<HworkAuthorityApi.RolesDTO> roles = currentUser.getRoles();
        if (!org.springframework.util.CollectionUtils.isEmpty(roles)){
            for (HworkAuthorityApi.RolesDTO role : roles) {
                if (role.getRoleCode().equals("HCMS_Admin")){
                    administrators = true;
                }
            }
        }
        return administrators;
    }
}
