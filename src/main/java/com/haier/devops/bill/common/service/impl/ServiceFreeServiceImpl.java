package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.ServiceFreeCreateDTO;
import com.haier.devops.bill.common.dto.ServiceFreeDTO;
import com.haier.devops.bill.common.entity.ServiceFree;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.ServiceFreeMapper;
import com.haier.devops.bill.common.service.ServiceFreeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
* @ClassName: ServiceFreeServiceImpl
* @Description: 服务费汇率 服务实现类
* @author: 张爱苹
* @date: 2024/3/8 13:18
*/
@Service
public class ServiceFreeServiceImpl extends ServiceImpl<ServiceFreeMapper, ServiceFree> implements ServiceFreeService {
    private Logger logger = LoggerFactory.getLogger(ServiceFreeServiceImpl.class);
    @Override
    public PageInfo<ServiceFree> listByPage(ServiceFreeDTO dto) throws Exception{
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->baseMapper.listByPage(dto)
            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, ServiceFreeDTO:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createServiceFree(ServiceFreeCreateDTO dto) throws Exception{
        try{
            if(dto.getType().intValue() == 2 && StringUtils.isEmpty(dto.getCurrency())){
                throw new RuntimeException("币种不能为空");
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            dto.setCreateBy(currentUser.getUserCode());
            saveOrUpdateServiceFree(dto);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("createServiceFree error:",e.getMessage(),e);
            throw new Exception("createServiceFree error:"+e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateServiceFree(Integer id, ServiceFreeCreateDTO dto) throws Exception{
        try{
            if(dto.getType().intValue() == 2 && StringUtils.isEmpty(dto.getCurrency())){
                throw new RuntimeException("币种不能为空");
            }
            if(dto.getStartDate().compareTo(dto.getEndDate()) > 0){
                throw new RuntimeException("开始日期不能大于结束日期");
            }
            dto.setId(id);
            dto.setUpdateTime(new Date());
            User currentUser = LoginContextHolder.getCurrentUser();
            dto.setUpdateBy(currentUser.getUserCode());
            saveOrUpdateServiceFree(dto);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("updateServiceFree error:",e.getMessage(),e);
            throw new Exception("updateServiceFree error:"+e.getMessage());
        }
    }

    private void saveOrUpdateServiceFree(ServiceFreeCreateDTO dto) throws Exception{
        List<ServiceFree> overlapItems = baseMapper.findOverlapItems(dto);
        if (!CollectionUtils.isEmpty(overlapItems)) {
            throw new RuntimeException("设置时间有重叠");
        }
        ServiceFree serviceFree = new ServiceFree();
        BeanUtils.copyProperties(dto, serviceFree);
        saveOrUpdate(serviceFree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteServiceFree(Integer id) throws Exception{
        ServiceFree serviceFree = getServiceFree(id);
        serviceFree.setUpdateTime(new Date());
        serviceFree.setDelFlag("1");
        User currentUser = LoginContextHolder.getCurrentUser();
        serviceFree.setUpdateBy(currentUser.getUserCode());
        updateById(serviceFree);
    }

    private ServiceFree getServiceFree(Integer id) throws Exception{
        ServiceFree serviceFree = baseMapper.selectOne(new LambdaQueryWrapper<ServiceFree>().eq(ServiceFree::getId, id).eq(ServiceFree::getDelFlag, "0"));
        if(serviceFree == null){
            throw new RuntimeException("配置不存在");
        }
        return serviceFree;
    }
}
