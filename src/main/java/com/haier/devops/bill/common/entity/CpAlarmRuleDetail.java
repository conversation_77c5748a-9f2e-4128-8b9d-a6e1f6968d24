package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("bc_cp_alarm_rule_detail")
@Builder
public class CpAlarmRuleDetail implements Serializable {

    private static final long serialVersionUID = 7628443947901315054L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云产品告警级别表id
     */
    private Integer cpAlarmLevelId;

    /**
     * 通知方式
     */
    private String noticeWay;

    /**
     * 通知对象
     */
    private String noticeObj;

    /**
     * 通知对象id
     */
    private String noticeObjId;



}
