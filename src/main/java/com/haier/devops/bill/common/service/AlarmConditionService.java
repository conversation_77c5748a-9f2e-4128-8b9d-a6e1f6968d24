package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.AlarmConditionDTO;
import com.haier.devops.bill.common.entity.AlarmCondition;

import java.util.List;

/**
* @ClassName: AlarmConditionService
* @Description:  告警条件表 服务类
* @author: 张爱苹
* @date: 2024/1/31 10:22
*/
public interface AlarmConditionService extends IService<AlarmCondition> {

    /**
    * @Description:  查询告警条件表列表
    * @author: 张爱苹
    * @date: 2024/1/31 10:28
    * @param dto:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.AlarmCondition>
    */
    List<AlarmCondition> getAlarmConditionList(AlarmConditionDTO dto);
}
