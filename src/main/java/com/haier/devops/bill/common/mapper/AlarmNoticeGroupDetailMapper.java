package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupDetailVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupDetailMapper
* @Description:  告警通知组明细表Mapper
* @author: 张爱苹
* @date: 2024/1/12 17:24
*/
@Repository
public interface AlarmNoticeGroupDetailMapper extends BaseMapper<AlarmNoticeGroupDetail> {

    List<AlarmNoticeGroupDetailVo> getAlarmNoticeGroupDetailListByGroupId(@Param("groupId") Integer id);

    List<String> getAccountListByGroupId(@Param("groupId")Integer id);
}
