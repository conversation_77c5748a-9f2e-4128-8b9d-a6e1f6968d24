package com.haier.devops.bill.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 原始账单表
 *
 * @Author: <EMAIL>
 * @since 2023-05-15
 */
@Data
@TableName("bc_raw_bill")
public class RawBill {
    private static final List<String> FIELDS = getBeanFieldNames();

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 账期
     */
    @TableField("billing_cycle")
    @ExcelProperty("账期")
    @ColumnWidth(10)
    private LocalDateTime billingCycle;

    /**
     * 云厂商
     */
    @TableField("vendor")
    @ExcelProperty("云厂商")
    @ColumnWidth(10)
    private String vendor;

    /**
     * 记录的账号信息-账户名称
     */
    @TableField("account_name")
    @ExcelProperty("所在账号")
    @ColumnWidth(16)
    private String accountName;

    /**
     * S码
     */
    @TableField(exist = false)
    @ExcelProperty("项目S码")
    @ColumnWidth(10)
    private String scode;

    /**
     * 消费类型
     */
    @TableField("subscription_type")
    @ExcelProperty("支付方式")
    @ColumnWidth(10)
    private String subscriptionType;

    /**
     * 实例ID
     */
    @TableField("instance_id")
    @ExcelProperty("实例ID")
    @ColumnWidth(20)
    private String instanceId;

    /**
     * 实例名称
     */
    @TableField(exist = false)
    @ExcelProperty("实例名称")
    private String instanceName;


    /**
     * 地域
     */
    @TableField("region")
    @ExcelProperty("地域")
    @ColumnWidth(12)
    private String region;

    /**
     * 结算单元
     */
    @TableField("cost_unit")
    @ExcelProperty("企业项目")
    @ColumnWidth(16)
    private String costUnit;

    /**
     * 产品名称
     */
    @TableField("product_name")
    @ExcelProperty("产品")
    @ColumnWidth(12)
    private String productName;

    /**
     * 产品明细
     */
    @TableField("product_detail")
    @ExcelProperty("产品明细")
    @ColumnWidth(20)
    private String productDetail;

    /**
     * 费用
     */
    @TableField("cost")
    @ExcelProperty("费用")
    @ColumnWidth(10)
    private String cost;


    @Schema(description = "应付费用")
    private String payableAmount;

    @Schema(description = "代金券费用")
    private String voucherAmount;

    @Schema(description = "现金费用")
    private String cashAmount;

    /**
     * 记录的账号信息-账户ID
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 账单粒度
     */
    @TableField("granularity")
    private String granularity;


    /**
     * 产品编码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 资源组
     */
    @TableField("resource_group")
    private String resourceGroup;


    /**
     * 服务期
     */
    @TableField("service_period")
    private String servicePeriod;

    /**
     * 服务期单位
     */
    @TableField("service_period_unit")
    private String servicePeriodUnit;


    /**
     * 可用区
     */
    @TableField("zone")
    private String zone;

    /**
     * 计费项
     */
    @TableField("billing_item")
    private String billingItem;

    /**
     * 定价
     */
    @TableField("list_price")
    private String listPrice;

    /**
     * 定价单位
     */
    @TableField("list_price_unit")
    private String listPriceUnit;

    /**
     * 使用量
     */
    @TableField("`usage`")
    private String usage;

    /**
     * 使用量单位
     */
    @TableField("usage_unit")
    private String usageUnit;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 实例id的补充（阿里云部分产品的分拆项id）
     */
    @TableField("supplement_id")
    private String supplementId;

    /**
     * 原始账单内容
     */
    @TableField("content")
    private String content;

    @TableField("aggregated_id")
    private String aggregatedId;

    public RawBill(String cost) {
        this.cost = cost;
    }

    public static List<String> getBeanFieldNames() {
        Field[] fields = RawBill.class.getDeclaredFields();
        return Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
    }

    public static List<String> getFields() {
        return FIELDS;
    }

    public static final List<String> EXCLUDED_COLUMNS = Arrays.asList(new String[]{
            "id", "createTime", "updateTime", "accountId", "granularity", "productCode",
            "resourceGroup", "resourceId", "servicePeriod", "servicePeriodUnit", "zone",
            "billingItem", "listPrice", "listPriceUnit", "usage", "usageUnit", "currency",
            "projectName", "projectCode"});

}
