package com.haier.devops.bill.common.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum VendorEnum {
    ALIYUN("aliyun", "AL"),
    ALIYUN_DEDICATED("aliyun_dedicated", "AD"),
    HUAWEI("huawei", "HW"),
    HUAWEI_DEDICATED("huawei_dedicated", "HD"),
    AWS("aws", "AW"),
    AZURE("azure", "AZ"),
    ORACLE("oracle", "OR"),
    GOOGLE("gcloud", "GC"),
    PRIVATE("private", "PV"),
    TENCENT("tencent", "TC"),
    CLOUD_DESKTOP("cloud_desktop", "CD");

    private String vendor;
    private String abbreviation;

    VendorEnum(String vendor, String abbreviation) {
        this.vendor = vendor;
        this.abbreviation = abbreviation;
    }

    // create hashmap containing vendor and abbreviation in static block
    private static final Map<String, String> VENDOR_ABBREVIATION_MAP;
    static {
        VENDOR_ABBREVIATION_MAP = new HashMap<>();
        for (VendorEnum vendorEnum : values()) {
            VENDOR_ABBREVIATION_MAP.put(vendorEnum.getVendor(), vendorEnum.getAbbreviation());
        }
    }

    // and create a method to get abbreviation from vendor
    public static String getAbbreviation(String vendor) {
        return VENDOR_ABBREVIATION_MAP.get(vendor);
    }
    
}
