package com.haier.devops.bill.common.config;

import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.controller.ResponseEnum.REQUEST_PARAM_ERR;
import static com.haier.devops.bill.common.controller.ResponseEnum.REQUEST_PARAM_LOST;

/**
 * 全局错误处理器
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 400错误
     * @param ex
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntityWrapper<String> handleValidationErrors(MethodArgumentNotValidException ex) {
        List<String> errors = ex.getBindingResult().getFieldErrors()
                .stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());

        return new ResponseEntityWrapper<>(REQUEST_PARAM_ERR, String.join(",", errors), null);
    }

    @ExceptionHandler(BindException.class)
    public ResponseEntityWrapper<String> handleBindExcepitonError(BindException ex) {
        List<String> errors = ex.getBindingResult().getFieldErrors()
                .stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());

        return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, String.join(",", errors), null);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntityWrapper<String> handleBindExcepitonError(ConstraintViolationException ex) {
        return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, ex.toString(), null);
    }
}
