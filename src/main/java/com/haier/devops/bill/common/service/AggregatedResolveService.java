package com.haier.devops.bill.common.service;

import com.haier.devops.bill.common.entity.AggregatedBill;

import java.util.List;

/**
* @ClassName: AggregatedBillService
* @Description:  账单明细汇总表 服务类
* @author: 张爱苹
* @date: 2024/1/31 10:22
*/
public interface AggregatedResolveService{
    void triggerAggregationTaskAmongAggregatedId(String vendor,String start, String end, String... aggregatedId) ;
    List<AggregatedBill> getGroupedAggregatedBillsBetweenMonths(String start, String end, String vendor, String... aggregatedId);

    void saveOrUpdateWhenAggregatingInMonth(List<AggregatedBill> list);
    void clearOldAggregatedBill(String vendor, String start, String end);

    /**
     * 按照云厂商、开始时间、结束时间、账号查询账单汇总
     * @param start
     * @param end
     * @param vendor
     * @param account
     * @return
     */
    List<AggregatedBill> getAggregatedBillsByAccount(String start, String end, String vendor, String account);

    /**
     * 按照云厂商、开始时间、结束时间、账号清除旧的汇总账单
     * @param vendor
     * @param start
     * @param end
     * @param account
     */
    void clearOldAggregatedBillByAccount(String vendor, String start, String end, String account);
}
