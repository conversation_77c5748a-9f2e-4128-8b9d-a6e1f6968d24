package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@TableName("data_privilege")
public class DataPrivilege implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 维度编码，关联 data_privilege_dimension.dimension_code
     */
    private String dimensionCode;

    /**
     * 权限编码，例如：DaYunWei, ZhiHuiLouYu, SanYiNiao
     */
    private String privilegeCode;

    /**
     * 权限名称，例如：大运维, 智慧楼宇, 三翼鸟
     */
    private String privilegeName;

    /**
     * 数据范围，（扩展字段，暂未使用）例如：department_id=1, region_code=CN-BJ，可以存储JSON格式
     */
    private String dataRange;

    /**
     * 权限类型，例如：VIEW, EDIT, DELETE
     */
    private String privilegeType;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 0:未删除，1:已删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
