package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.ZabbixMonitorData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* @ClassName: ZabbixMonitorMapper
* @Description: zabbix监控mapper
* @author: 张爱苹
* @date: 2024/4/17 10:38
*/
@Mapper
public interface ZabbixMonitorMapper extends BaseMapper<ZabbixMonitorData> {

    Map getMaxCpuAndMem(@Param("instanceIdList") List<String> instanceIdList);
}

