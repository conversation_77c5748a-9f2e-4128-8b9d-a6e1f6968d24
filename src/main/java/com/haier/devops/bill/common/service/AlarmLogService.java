package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmLogDTO;
import com.haier.devops.bill.common.entity.CpAlarmLog;

/**
* @ClassName: AlarmLogService
* @Description:  告警日志服务
* @author: 张爱苹
* @date: 2024/1/18 13:28
*/
public interface AlarmLogService extends IService<CpAlarmLog> {

    /**
    * @Description: 分页查询告警日志
    * @author: 张爱苹
    * @date: 2024/1/18 14:37
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CpAlarmLog>
    */
    PageInfo<CpAlarmLog> listByPage(AlarmLogDTO dto);
}
