package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.DataPrivilege;
import com.haier.devops.bill.common.mapper.DataPrivilegeMapper;
import com.haier.devops.bill.common.service.DataPrivilegeService;
import com.haier.devops.bill.common.vo.UserDataPrivilegeVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Service
public class DataPrivilegeServiceImpl extends ServiceImpl<DataPrivilegeMapper, DataPrivilege> implements DataPrivilegeService {
    private final DataPrivilegeMapper dataPrivilegeMapper;

    public DataPrivilegeServiceImpl(DataPrivilegeMapper dataPrivilegeMapper) {
        this.dataPrivilegeMapper = dataPrivilegeMapper;
    }

    @Override
    public List<UserDataPrivilegeVo> selectUserDataPrivilegeByDimension(String dimension, String userId) {
        return dataPrivilegeMapper.selectUserDataPrivilegeByDimension(dimension, userId);
    }
}
