package com.haier.devops.bill.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* @ClassName: AlarmNoticeGroup
* @Description:  告警通知组
* @author: 张爱苹
* @date: 2024/1/12 14:01
*/
@Data
public class AlarmNoticeGroupVo implements Serializable {

    private static final long serialVersionUID = -2232001781677260735L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 通知组名称
     */
    private String groupName;

    /**
     * 通知组明细
     */
    private List<AlarmNoticeGroupDetailVo> alarmNoticeGroupDetailList;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

}

