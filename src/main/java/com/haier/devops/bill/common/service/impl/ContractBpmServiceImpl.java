package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ContractBpm;
import com.haier.devops.bill.common.mapper.ContractBpmMapper;
import com.haier.devops.bill.common.service.ContractBpmService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 合同验收bpm审批流id映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class ContractBpmServiceImpl extends ServiceImpl<ContractBpmMapper, ContractBpm> implements ContractBpmService {

}
