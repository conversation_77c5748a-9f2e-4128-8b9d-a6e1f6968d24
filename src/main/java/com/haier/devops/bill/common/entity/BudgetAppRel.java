package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@TableName("bc_budget_app_rel")
public class BudgetAppRel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用S码
     */
    private String scode;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 预算编码
     */
    private String budgetCode;

    /**
     * 账期 yyyy-MM
     */
    private String billingCycle;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 0: no, 1: yes
     */
    private Integer enabled;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getScode() {
        return scode;
    }

    public void setScode(String scode) {
        this.scode = scode;
    }
    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }
    public String getBudgetCode() {
        return budgetCode;
    }

    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }
    public String getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(String billingCycle) {
        this.billingCycle = billingCycle;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return "BudgetAppRel{" +
            "id=" + id +
            ", scode=" + scode +
            ", vendor=" + vendor +
            ", budgetCode=" + budgetCode +
            ", billingCycle=" + billingCycle +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", enabled=" + enabled +
        "}";
    }
}
