package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.CpAlarmRuleDetail;
import com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
* @ClassName: CpAlarmRuleDetailMapper
* @Description:  告警规则明细Mapper
* @author: 张爱苹
* @date: 2024/1/12 14:54
*/
@Repository
public interface CpAlarmRuleDetailMapper extends BaseMapper<CpAlarmRuleDetail> {

    List<CpAlarmRuleDetailVo> getAlarmRuleDetailListByAlarmLevelId(@Param("id") Integer alarmLevelId);

    List<CpAlarmRuleDetailVo> queryAlarmRuleDetailListByAlarmLevelId(@Param("dto") Map map);
}
