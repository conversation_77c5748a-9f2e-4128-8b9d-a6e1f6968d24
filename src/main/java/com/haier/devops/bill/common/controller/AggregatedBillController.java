package com.haier.devops.bill.common.controller;


import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haier.devops.bill.common.dto.AggregatedBillDTO;
import com.haier.devops.bill.common.dto.AggregatedBillEditDTO;
import com.haier.devops.bill.common.entity.ExchangeRate;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.param.DimensionDetailParam;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.BillDetailVo;
import com.haier.devops.bill.common.vo.DayBillAnalysisVo;
import com.haier.devops.bill.common.vo.TechnicalArchitectureBillVo;
import com.haier.devops.bill.substitution.controller.PassParm;
import com.haier.devops.bill.util.AuthUtil;
import com.haier.devops.bill.util.DateUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.enums.CurrencyEnum.CNY;
import static com.haier.devops.bill.common.enums.CurrencyEnum.USD;
import static com.haier.devops.bill.util.ExecutorUtil.analysisTask;

/**
 * @ClassName: AggregatedBillController
 * @Description: 账单明细汇总表 控制器
 * @author: 张爱苹
 * @date: 2024/1/31 10:24
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/aggregated")
@Slf4j
public class AggregatedBillController {
    private AggregatedBillService aggregatedBillService;
    private CloudAccountService cloudAccountService;
    private NodeService nodeService;
    @Autowired
    private ServerUsageService serverUsageService;
    @Autowired
    private ExchangeRateService exchangeRateService;
    /**
     * 当月预测金额
     */
    private final Function<List<String>, BigDecimal> getForecastForTheMonthFunc;
    private final Function<List<String>, BigDecimal> getForecastForTheMonthUsdFunc;

    /**
     * 当年累计
     */
    private final Function<List<String>, BigDecimal> getCurrentYearTotalAmountFunc;
    private final Function<List<String>, BigDecimal> getCurrentYearTotalAmountUsdFunc;

    /**
     * 去年累计
     */
    private final Function<List<String>, BigDecimal> getLastYearTotalAmountFunc;
    private final Function<List<String>, BigDecimal> getLastYearTotalAmountUsdFunc;

    /**
     * 最近n个月消费，n = 1时为上个月的消费
     */
    private final BiFunction<List<String>, Integer, BigDecimal> getLastNthMonthTotalAmountFunc;
    private final BiFunction<List<String>, Integer, BigDecimal> getLastNthMonthTotalAmountUsdFunc;

    public AggregatedBillController(AggregatedBillService aggregatedBillService, CloudAccountService cloudAccountService,NodeService nodeService) {
        this.aggregatedBillService = aggregatedBillService;
        this.cloudAccountService = cloudAccountService;
        this.nodeService = nodeService;
        getForecastForTheMonthFunc = scodes -> {
            BigDecimal amountUntilYesterday =
                    aggregatedBillService.getCurrentMonthTotalAmountUntilYesterday(scodes, CNY.getCurrency());
            BigDecimal amountOfYesterday =
                    aggregatedBillService.getTotalAmountOfYesterday(scodes, CNY.getCurrency());
            BigDecimal estimatedAmount = null == amountOfYesterday ? BigDecimal.ZERO : amountOfYesterday
                    .multiply(BigDecimal.valueOf(DateUtil.getRemainingDaysCountOfCurrentMonth()))
                    .setScale(2, RoundingMode.HALF_UP);

            return (null == amountUntilYesterday ? BigDecimal.ZERO : amountUntilYesterday).add(estimatedAmount);
        };

        getForecastForTheMonthUsdFunc = scodes -> {
            BigDecimal amountUntilYesterday = aggregatedBillService
                    .getCurrentMonthTotalAmountUntilYesterday(scodes, USD.getCurrency());
            BigDecimal amountOfYesterday = aggregatedBillService
                    .getTotalAmountOfYesterday(scodes, USD.getCurrency());
            BigDecimal estimatedAmount = null == amountOfYesterday ? BigDecimal.ZERO : amountOfYesterday
                    .multiply(BigDecimal.valueOf(DateUtil.getRemainingDaysCountOfCurrentMonth()))
                    .setScale(2, RoundingMode.HALF_UP);

            return (null == amountUntilYesterday ? BigDecimal.ZERO : amountUntilYesterday).add(estimatedAmount);
        };

        getCurrentYearTotalAmountFunc = scodes -> {
            BigDecimal amountOfCurrentYear =
                    aggregatedBillService.getCurrentYearTotalAmount(scodes, CNY.getCurrency());
            return null == amountOfCurrentYear ? BigDecimal.ZERO : amountOfCurrentYear;
        };
        getCurrentYearTotalAmountUsdFunc = scodes -> {
            BigDecimal amountOfCurrentYear = aggregatedBillService
                    .getCurrentYearTotalAmount(scodes, USD.getCurrency());
            return null == amountOfCurrentYear ? BigDecimal.ZERO : amountOfCurrentYear;
        };

        getLastNthMonthTotalAmountFunc =
                (scodes1, nth) ->
                        aggregatedBillService.getLastNthMonthTotalAmount(scodes1, nth, CNY.getCurrency());
        getLastNthMonthTotalAmountUsdFunc =
                (scodes1, nth) ->
                        aggregatedBillService.getLastNthMonthTotalAmount(scodes1, nth, USD.getCurrency());

        getLastYearTotalAmountFunc = scodes -> {
            BigDecimal amountOfLastYear =
                    aggregatedBillService.getLastYearTotalAmount(scodes, CNY.getCurrency());
            return (null == amountOfLastYear ? BigDecimal.ZERO : amountOfLastYear);
        };
        getLastYearTotalAmountUsdFunc = scodes -> {
            BigDecimal amountOfLastYear =
                    aggregatedBillService.getLastYearTotalAmount(scodes, USD.getCurrency());
            return (null == amountOfLastYear ? BigDecimal.ZERO : amountOfLastYear);
        };
    }


//    /**
//     * 分页查询所有账单明细汇总
//     *
//     * @return 账单明细汇总列表
//     */
//    @GetMapping("/listByPage")
//    public ResponseEntityWrapper<PageInfo<AggregatedBillVo>> listByPage(AggregatedBillDTO dto) {
//
//        try {
//            PageInfo<AggregatedBillVo> pageInfo = aggregatedBillService.listByPage(dto);
//            return new ResponseEntityWrapper<>(pageInfo);
//        } catch (Exception e) {
//            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
//        }
//    }

    /**
     * @param id:
     * @Description: 更新S码
     * @author: 张爱苹
     * @date: 2024/2/22 17:03
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
     */
    @PostMapping("/updateScode/{id}")
    public ResponseEntityWrapper updateScode(@PathVariable("id") Integer id, @RequestParam("scode") String scode) {
        try {
            aggregatedBillService.updateScode(id, scode);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
     * @param dto:
     * @Description: 批量更新S码
     * @author: 张爱苹
     * @date: 2024/2/22 17:03
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
     */
    @PostMapping("/updateScodeBatch")
    public ResponseEntityWrapper updateScodeBatch(@Validated @RequestBody AggregatedBillEditDTO dto) {
        try {
            aggregatedBillService.updateScodeBatch(dto);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
     * @param vendor:
     * @Description: 获取云账号列表
     * @author: 张爱苹
     * @date: 2024/3/4 16:00
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List < com.haier.devops.bill.common.entity.CloudAccount>>
     */
    @GetMapping("/getCloudAccountList")
    public ResponseEntityWrapper<List<String>> getCloudAccountList(@RequestParam String vendor, @RequestParam(required = false) String accountName) {
        try {
            List<String> accountList = cloudAccountService.getCloudAccountList(vendor, accountName);
            return new ResponseEntityWrapper<>(accountList);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

    /**
     * @param vendor:
     * @param instanceId:
     * @Description: 获取实例ID列表
     * @author: 张爱苹
     * @date: 2024/3/4 16:14
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List < java.lang.String>>
     */
    @GetMapping("/getInstanceIdList")
    public ResponseEntityWrapper<List<String>> getInstanceIdList(@RequestParam String vendor, @RequestParam(required = false) String instanceId) {
        try {
            List<String> accountList = aggregatedBillService.getInstanceIdList(vendor, instanceId);
            return new ResponseEntityWrapper<>(accountList);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 获取账单汇总列表(技术架构)
     *
     * @param dto
     * @return
     */
    @GetMapping("/getBillOfBillingCycle")
    public ResponseEntityWrapper<Page<TechnicalArchitectureBillVo>> getBillOfBillingCycle(AggregatedBillDTO dto) {
        if (dto.getBillingCycle() == null) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_LOST.getCode(), "账期不能为空", null);
        }

        try {
            Page<TechnicalArchitectureBillVo> page = aggregatedBillService.getBillOfBillingCycle(dto);
            return new ResponseEntityWrapper<>(page);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 分析上个月和上上个月的实例维度金额
     * @param
     * @return
     */
    @PostMapping("/instance/getAgentComparison")
    public ResponseEntityWrapper<AnalysisResult> getAgentBillComparison(@RequestBody PassParm passParm, @RequestParam(required = false) String vendor,
                                                                        @RequestParam(required = false) String startDate,
                                                                        @RequestParam(required = false) String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        String endDateStr = endDate.substring(0,7);
        LocalDate now = LocalDate.parse(endDate);
        String startDateStr = formatter.format(now.withDayOfMonth(1).minus(1, ChronoUnit.DAYS));

        try {
            Map map = new HashMap<>();
            StringBuffer sb = new StringBuffer();
            String scodes = "";
            List<String> appCodeList = null;
            if(CollectionUtils.isNotEmpty(passParm.getAppList())){
                appCodeList = passParm.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                //数组转字符串用逗号隔开
                scodes = StringUtils.join(appCodeList, ",");
            }else{
                List<String> domainCodeList = null;
                if(!CollectionUtils.isEmpty(passParm.getDomainList())){
                    domainCodeList = passParm.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                }
                User currentUser = LoginContextHolder.getCurrentUser();
                String userCode = currentUser.getUserCode();
                boolean isAdmin = AuthUtil.isAdmin(currentUser);
                scodes = aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode);
            }
            //获取productCode的近两个月的账单
            DimensionDetailParam dimensionDetailParam = new DimensionDetailParam();
            dimensionDetailParam.setStartCycle(startDate);
            dimensionDetailParam.setEndCycle(endDate);
            dimensionDetailParam.setDataType("bill");
            dimensionDetailParam.setCycleType("1");
            dimensionDetailParam.setDataDimensions(Arrays.asList("productType"));
            dimensionDetailParam.setVendor(vendor);
            dimensionDetailParam.setScode(scodes);
            List<String> productCodeList = null;
            if(CollectionUtils.isNotEmpty(passParm.getProductList())){
                productCodeList = passParm.getProductList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            dimensionDetailParam.setProductList(productCodeList);
            List<Map<String, Object>> list = null;
            List<Map<String, Object>> listUSD = null;
            String unit = "元";
            if("USD".equals(passParm.getCurrency())){
                unit = "美元";
            }
            ExchangeRate exchangeRate = null;
            try {
                String date = DateUtils.format(new Date(), "yyyy-MM-dd");
                exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if("CNY".equals(passParm.getCurrency()) || "USD".equals(passParm.getCurrency())){
                list =nodeService.getDimensionCustDetail(dimensionDetailParam,passParm.getCurrency());
            }else{
                list =nodeService.getDimensionCustDetail(dimensionDetailParam,exchangeRate.getExchangeRate());
                listUSD =nodeService.getDimensionCustDetail(dimensionDetailParam,"USD");
            }
            if(CollectionUtils.isEmpty(list)||list.size()==0){
                sb.append(startDate+"-"+endDate+"未产生账单");
                map.put("desc",sb);
                return new ResponseEntityWrapper(map);
            }
            Map<String, Object> bill = list.get(0);
            Map slice = (Map) bill.get("slice");
            //上个月金额
            Object amount1 = slice.get(endDateStr) == null ? "0" : slice.get(endDateStr);
            //上上个月的金额
            Object amount2 = slice.get(startDateStr) == null ? "0" : slice.get(startDateStr);
            BigDecimal lastMonthProductAmount = new BigDecimal(amount1.toString());
            BigDecimal previousProductAmount = new BigDecimal(amount2.toString());
            BigDecimal lastMonthProductAmountUSD = new BigDecimal(0);
            BigDecimal previousProductAmountUSD = new BigDecimal(0);
            BigDecimal lastMonthProductAmountCNY = new BigDecimal(0);
            BigDecimal previousProductAmountCNY = new BigDecimal(0);

            if(CollectionUtils.isNotEmpty(listUSD)&&!"CNY".equals(passParm.getCurrency()) && !"USD".equals(passParm.getCurrency())){
                Map<String, Object> billUSD = listUSD.get(0);
                Map sliceUSD = (Map) billUSD.get("slice");
                //上个月金额
                Object amount1USD = sliceUSD.get(endDateStr) == null ? "0" : sliceUSD.get(endDateStr);
                //上上个月的金额
                Object amount2USD = sliceUSD.get(startDateStr) == null ? "0" : sliceUSD.get(startDateStr);
                lastMonthProductAmountUSD = new BigDecimal(amount1USD.toString());
                previousProductAmountUSD = new BigDecimal(amount2USD.toString());
                lastMonthProductAmountCNY = lastMonthProductAmountUSD.divide(exchangeRate.getExchangeRate(),2,BigDecimal.ROUND_HALF_UP);
                previousProductAmountCNY = previousProductAmountUSD.divide(exchangeRate.getExchangeRate(),2,BigDecimal.ROUND_HALF_UP);
            }
            BigDecimal amount = lastMonthProductAmount.subtract(previousProductAmount);
            sb.append(endDateStr).append("(").append(lastMonthProductAmount).append(unit);
            if(lastMonthProductAmountUSD.compareTo(BigDecimal.ZERO) > 0){
                sb.append("，其中美元").append(lastMonthProductAmountUSD).append(",折合人民币").append(lastMonthProductAmountCNY).append("元");
            }
            sb.append("）较").append(startDateStr).append("(").append(previousProductAmount).append(unit);
            if(previousProductAmountUSD.compareTo(BigDecimal.ZERO) > 0){
                sb.append("，其中美元").append(previousProductAmountUSD).append(",折合人民币").append(previousProductAmountCNY).append("元");
            }
            sb.append("）金额");
            if(amount.compareTo(BigDecimal.ZERO) == 0){
                sb.append("接近,差异不大。");
            }else{
                String fang = "增加";
                if (amount.compareTo(BigDecimal.ZERO) < 0){
                    fang = "减少";
                    sb.append(fang).append(amount.multiply(new BigDecimal("-1")).setScale(2,BigDecimal.ROUND_HALF_UP));
                }else{
                    sb.append(fang).append(amount.setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                sb.append(unit);
                DetailBillParam request = new DetailBillParam();
                request.setVendor(vendor);
                request.setScode(scodes);
                request.setStartCycle(startDate);
                request.setEndCycle(endDate);
                request.setProductList(productCodeList);
                List<BillDetailVo> billDetailVoList = null;
                if("CNY".equals(passParm.getCurrency()) || "USD".equals(passParm.getCurrency())){
                    billDetailVoList = aggregatedBillService.listDetail(request,passParm.getCurrency());
                }else{
                    billDetailVoList = aggregatedBillService.listDetail(request,exchangeRate.getExchangeRate());
                }
                //遍历billDetailVoList 根据每一项里的aggregatedId分组
                Map<String, List<BillDetailVo>> map1 = billDetailVoList.stream().collect(Collectors.groupingBy(BillDetailVo::getInstanceId));
                //遍历map1
                List<Map<String, Object>> resultList = new ArrayList<>();
                List<String> instanceList = new ArrayList<>();
                for (Map.Entry<String, List<BillDetailVo>> entry : map1.entrySet()) {
                    Map<String, Object> resultMap = new HashMap<>();
                    String instanceId = entry.getKey();
                    resultMap.put("instanceId", instanceId);
                    List<BillDetailVo> billDetailVoList1 = entry.getValue();
                    Map<String, List<BillDetailVo>> map2 = billDetailVoList1.stream().collect(Collectors.groupingBy(BillDetailVo::getBillingCycle));

                    for (Map.Entry<String, List<BillDetailVo>> entry1 : map2.entrySet()) {
                        String billingCycle = entry1.getKey();
                        //遍历entry1 将每一项里的summer加起来 用lamda表达式
                        BigDecimal sum = BigDecimal.ZERO;
                        for (BillDetailVo vo : entry1.getValue()) {
                            if (vo.getSummer() != null) {
                                sum = sum.add(new BigDecimal(vo.getSummer()));
                            }
                        }
                        resultMap.put(billingCycle,sum);
                    }

                    BigDecimal lastMonthProductMoney = new BigDecimal(resultMap.get(endDateStr) == null?"0":resultMap.get(endDateStr).toString());
                    BigDecimal previousProductMoney = new BigDecimal(resultMap.get(startDateStr) == null?"0":resultMap.get(startDateStr).toString());
                    BigDecimal amplitude = lastMonthProductMoney.subtract(previousProductMoney).setScale(2,BigDecimal.ROUND_HALF_UP);
                    resultMap.put("amplitude",amplitude);
                    if(amplitude.compareTo(BigDecimal.ZERO) !=  0){
                        if((lastMonthProductMoney.compareTo(BigDecimal.ZERO) == 0 && previousProductMoney.compareTo(BigDecimal.ZERO) != 0) || (lastMonthProductMoney.compareTo(BigDecimal.ZERO) != 0 && previousProductMoney.compareTo(BigDecimal.ZERO) == 0)){
                            //新增/减少了实例
                            instanceList.add(instanceId);
                        }
                    }
                    String rate = "0";
                    if(previousProductMoney.compareTo(BigDecimal.ZERO) > 0){
                        rate = amplitude.divide(previousProductMoney, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                    }
                    resultMap.put("rate", rate);
                    resultMap.put("amplitude2",amplitude.setScale(2,BigDecimal.ROUND_HALF_UP));
                    resultMap.put(startDateStr, previousProductMoney.setScale(2,BigDecimal.ROUND_HALF_UP));
                    resultMap.put(endDateStr, lastMonthProductMoney.setScale(2,BigDecimal.ROUND_HALF_UP));
                    if(amplitude.setScale(2,BigDecimal.ROUND_HALF_UP).compareTo(BigDecimal.ZERO) != 0){
                        resultList.add(resultMap);
                    }

                }
                if("增加".equals(fang)){
                    //遍历list 按照item里的amplitude排序
                    resultList.sort((o1, o2) -> {
                        double v1 = Double.parseDouble(o1.get("amplitude").toString());
                        double v2 = Double.parseDouble(o2.get("amplitude").toString());
                        //千位分隔符‌ v1
                        return v2 > v1 ? 1 : -1;
                    });
                    sb.append(",其中实例中");
                    for (int f = 0; f < resultList.size(); f++) {
                        if(f < 3){
                            Map en = resultList.get(f);
                            BigDecimal amplitude = new BigDecimal(en.get("amplitude").toString());
                            String rate = en.get("rate").toString();
                            //amplitude * 100 截取两位
                            if(amplitude.compareTo(BigDecimal.ZERO) > 0){
                                sb.append(en.get("instanceId")).append(fang).append(amplitude.setScale(2,BigDecimal.ROUND_HALF_UP)).append(unit);
                                if(!"0".equals(rate)){
                                    sb.append("(").append("↑").append(rate).append("%)");
                                }
                            }
                            if(f == 2){
                                sb.append("等。");
                            }else{
                                sb.append(",");
                            }
                        }else{
                            break;
                        }
                    }
                }else{
                    //遍历list 按照item里的amplitude排序
                    resultList.sort((o1, o2) -> {
                        double v1 = Double.parseDouble(o1.get("amplitude").toString());
                        double v2 = Double.parseDouble(o2.get("amplitude").toString());
                        //千位分隔符‌ v1
                        return v1 > v2 ? 1 : -1;
                    });
                    sb.append(",其中实例中");
                    for (int f = 0; f < resultList.size(); f++) {
                        if(f < 3){
                            Map en = resultList.get(f);
                            BigDecimal amplitude = new BigDecimal(en.get("amplitude").toString());
                            BigDecimal rate = new BigDecimal(en.get("rate").toString()).multiply(new BigDecimal("-1")).setScale(2, BigDecimal.ROUND_HALF_UP);
                            if(amplitude.compareTo(BigDecimal.ZERO) < 0){
                                sb.append(en.get("instanceId")).append(fang).append(amplitude.multiply(new BigDecimal("-1")).setScale(2,BigDecimal.ROUND_HALF_UP)).append(unit);
                                if(!"0".equals(rate)){
                                    sb.append("(").append("↑").append(rate).append("%)");
                                }
                            }
                            if(f == 2){
                                sb.append("等。");
                            }else{
                                sb.append(",");
                            }

                        }else{
                            break;
                        }

                    }
                }

                if(!CollectionUtils.isEmpty(instanceList)){
                    int size = instanceList.size();
                    if(instanceList.size() > 3){
                        instanceList = instanceList.subList(0,3);
                    }
                    String productListStr = StringUtils.join(instanceList,"、");
                    sb.append("并且").append(fang).append("实例").append(productListStr).append("等共").append(size).append("个。");
                }
                if (resultList.size() > 10){
                    resultList = resultList.subList(0, 10);
                }
                map.put("list",resultList);
            }
            map.put("desc",sb);
            return new ResponseEntityWrapper(map);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

        /**
         * 分析上个月和上上个月的云产品维度金额
         * @param
         * @return
         */
    @PostMapping("/product/getAgentComparison")
    public ResponseEntityWrapper<AnalysisResult> getAgentProductComparison(@RequestBody PassParm passParm, @RequestParam(required = false) String vendor,
                                                                              @RequestParam(required = false) String startDate,

                                                                              @RequestParam(required = false) String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        String endDateStr = endDate.substring(0,7);
        LocalDate now = LocalDate.parse(endDate);
        String startDateStr = formatter.format(now.withDayOfMonth(1).minus(1, ChronoUnit.DAYS));

        Map map = new HashMap<>();
        String scodes = "";
        List<String> scodeList = null;
        if(CollectionUtils.isNotEmpty(passParm.getAppList())){
            scodeList = passParm.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            scodes = StringUtils.join(scodeList, ",");
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(passParm.getDomainList())){
                domainCodeList = passParm.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            scodes = aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode);
            if(StringUtils.isEmpty(scodes)){
                return new ResponseEntityWrapper(map);
            }
            scodeList = Arrays.asList(scodes.split(","));
        }
        BigDecimal lastMonthAmount = new BigDecimal(0);
        BigDecimal previousAmount = new BigDecimal(0);
        BigDecimal lastMonthAmountUSD = new BigDecimal(0);
        BigDecimal lastMonthAmountCNY = new BigDecimal(0);
        BigDecimal previousAmountUSD = new BigDecimal(0);
        BigDecimal previousAmountCNY = new BigDecimal(0);
        String unit = "元";
        if("USD".equals(passParm.getCurrency())){
            unit = "美元";
        }
        ExchangeRate exchangeRate = null;
        try {
            String date = DateUtils.format(new Date(), "yyyy-MM-dd");
            exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int monthDiff = DateUtil.calculateMonthDifference(endDate.substring(0,7),DateUtils.format(new Date(), "yyyy-MM"));
        if("CNY".equals(passParm.getCurrency()) || "USD".equals(passParm.getCurrency())){
            // 获取上个月的金额
            lastMonthAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff,passParm.getCurrency());
            // 获取上上个月的金额
            previousAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff+1,passParm.getCurrency());
        }else{
            // 获取上个月的金额
            lastMonthAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff,exchangeRate.getExchangeRate());
            // 获取上上个月的金额
            previousAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff+1,exchangeRate.getExchangeRate());
            // 获取上个月的金额
            lastMonthAmountUSD = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff,"USD");
            lastMonthAmountCNY = lastMonthAmountUSD.divide(exchangeRate.getExchangeRate(),2,BigDecimal.ROUND_HALF_UP);
            // 获取上上个月的金额
            previousAmountUSD = aggregatedBillService.getLastNthMonthTotalAmount(scodeList,vendor,monthDiff+1,"USD");
            previousAmountCNY = previousAmountUSD.divide(exchangeRate.getExchangeRate(),2,BigDecimal.ROUND_HALF_UP);
        }
        //比较上月和上上个月的金额
        BigDecimal amount = lastMonthAmount.subtract(previousAmount);
        StringBuffer sb = new StringBuffer();
        sb.append(endDateStr).append("(").append(lastMonthAmount).append(unit);
        if(lastMonthAmountUSD.compareTo(BigDecimal.ZERO) > 0){
            sb.append("，其中美元"+lastMonthAmountUSD).append(",折合人民币").append(lastMonthAmountCNY).append("元");
        }
        sb.append("）较").append(startDateStr).append("(").append(previousAmount).append(unit);
        if(previousAmountUSD.compareTo(BigDecimal.ZERO) > 0){
            sb.append("，其中美元"+previousAmountUSD).append(",折合人民币").append(previousAmountCNY).append("元");
        }
        sb.append("）金额");
        if(amount.compareTo(BigDecimal.ZERO) == 0){
            sb.append("接近,差异不大。");
        }else{
            String fang = "增加";
            if (amount.compareTo(BigDecimal.ZERO) < 0){
                fang = "减少";
                sb.append(fang).append(amount.multiply(new BigDecimal("-1")).setScale(2,BigDecimal.ROUND_HALF_UP));
            }else{
                sb.append(fang).append(amount.setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            sb.append(unit);
            //查涨幅最高的云产品
            DimensionDetailParam request = new DimensionDetailParam();
            request.setStartCycle(startDate);
            request.setEndCycle(endDate);
            request.setDataType("bill");
            request.setCycleType("1");
            request.setDataDimensions(Arrays.asList("productType"));
            request.setVendor(vendor);
            request.setScode(scodes);
            List<Map<String, Object>> list = null;
            if("CNY".equals(passParm.getCurrency()) || "USD".equals(passParm.getCurrency())){
                list =nodeService.getDimensionCustDetail(request,passParm.getCurrency());
            }else{
                list = nodeService.getDimensionCustDetail(request, exchangeRate.getExchangeRate());
            }
            if(CollectionUtils.isEmpty(list)||list.size()==0){
                sb.append(startDate+"-"+endDate+"未产生账单");
                map.put("content",sb);
                return new ResponseEntityWrapper(map);
            }
            //产品数量
            List<String> productList = new ArrayList<>();

            for (int i = 0; i < list.size(); i++) {
                Map entity = list.get(i);
                Map slice = (Map) entity.get("slice");
                //上个月金额
                Object amount1 = slice.get(endDateStr) == null ? "0" : slice.get(endDateStr);
                //上上个月的金额
                Object amount2 = slice.get(startDateStr) == null ? "0" : slice.get(startDateStr);
                BigDecimal lastMonthProductAmount = new BigDecimal(amount1.toString());
                BigDecimal previousProductAmount = new BigDecimal(amount2.toString());
                BigDecimal amplitude = lastMonthProductAmount.subtract(previousProductAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
                entity.put("amplitude",amplitude);
                if(amplitude.compareTo(BigDecimal.ZERO) !=  0){
                    if((lastMonthProductAmount.compareTo(BigDecimal.ZERO) == 0 && previousProductAmount.compareTo(BigDecimal.ZERO) != 0) || (lastMonthProductAmount.compareTo(BigDecimal.ZERO) != 0 && previousProductAmount.compareTo(BigDecimal.ZERO) == 0)){
                        //新增/减少了云产品
                        productList.add(entity.get("productType").toString());
                    }
                }
                String rate = "0";
                if(previousProductAmount.compareTo(BigDecimal.ZERO) > 0){
                    rate = amplitude.divide(previousProductAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                }
                entity.put("rate", rate);
                entity.put("amplitude2",amplitude.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP));
                slice.put(startDateStr, previousProductAmount.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP));
                slice.put(endDateStr, lastMonthProductAmount.divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP));
            }
            if("增加".equals(fang)){
                //遍历list 按照item里的amplitude排序
                list.sort((o1, o2) -> {
                    double v1 = Double.parseDouble(o1.get("amplitude").toString());
                    double v2 = Double.parseDouble(o2.get("amplitude").toString());
                    return v2 > v1 ? 1 : -1;
                });
                sb.append(",其中云产品中");
                for (int f = 0; f < list.size(); f++) {
                    if(f < 3){
                        Map en = list.get(f);
                        BigDecimal amplitude = new BigDecimal(en.get("amplitude").toString());
                        String rate = en.get("rate").toString();
                        //amplitude * 100 截取两位
                        if(amplitude.compareTo(BigDecimal.ZERO) > 0){
                            sb.append(en.get("productType")).append(fang).append(amplitude.setScale(2,BigDecimal.ROUND_HALF_UP)).append(unit);
                            if(!"0".equals(rate)){
                                sb.append("(").append("↑").append(rate).append("%)");
                            }
                        }
                        if(f == 2){
                            sb.append("等。");
                        }else{
                            sb.append(",");
                        }
                    }else{
                        break;
                    }
                }
            }else{
                //遍历list 按照item里的amplitude排序
                list.sort((o1, o2) -> {
                    double v1 = Double.parseDouble(o1.get("amplitude").toString());
                    double v2 = Double.parseDouble(o2.get("amplitude").toString());
                    return v1 > v2 ? 1 : -1;
                });
                sb.append(",其中云产品中");
                for (int f = 0; f < list.size(); f++) {
                    if(f < 3){
                        Map en = list.get(f);
                        BigDecimal amplitude = new BigDecimal(en.get("amplitude").toString());
                        BigDecimal rate = new BigDecimal(en.get("rate").toString()).multiply(new BigDecimal("-1")).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if(amplitude.compareTo(BigDecimal.ZERO) < 0){
                            sb.append(en.get("productType")).append(fang).append(amplitude.multiply(new BigDecimal("-1")).setScale(2,BigDecimal.ROUND_HALF_UP)).append(unit);
                            if(!"0".equals(rate)){
                                sb.append("(").append("↑").append(rate).append("%)");
                            }
                        }
                        if(f == 2){
                            sb.append("等。");
                        }else{
                            sb.append(",");
                        }

                    }else{
                        break;
                    }

                }
            }

            if(!CollectionUtils.isEmpty(productList)){
                int size = productList.size();
                if(productList.size() > 3){
                    productList = productList.subList(0,3);
                }
                String productListStr = StringUtils.join(productList,"、");
                sb.append("并且").append(fang).append("云产品").append(productListStr).append("等共").append(size).append("个。");
            }
            if (list.size() > 10){
                list = list.subList(0, 10);
            }
            map.put("list",list);
        }
        map.put("desc",sb);
        return new ResponseEntityWrapper(map);
    }

    /**
     * 资源申请/退订情况
     * @param
     * @return
     */
    @PostMapping("/getAgentResourceApplications")
    public ResponseEntityWrapper<AnalysisResult> getAgentResourceApplications(@RequestBody NodeParam nodeParam,@RequestParam(required = false) String vendor,

                                                                              @RequestParam(required = false) String startDate,
                                                                              @RequestParam(required = false) String endDate) {
        Map map = new HashMap<>();
        String scode = "";
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
            appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            scode = StringUtils.join(appCodeList, ",");
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            scode = aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode);
        }
        if(StringUtils.isEmpty(scode)){
            return new ResponseEntityWrapper(map);
        }
        List<Map> resultMap = aggregatedBillService.getAgentResourceApplications(vendor,scode,startDate,endDate);
        List<Map> applyList = resultMap.stream().filter(item -> "1".equals(item.get("type").toString())).collect(Collectors.toList());
        List<Map> exitList = resultMap.stream().filter(item -> "4".equals(item.get("type").toString())).collect(Collectors.toList());
        map.put("applyCount",applyList.size());
        map.put("exitCount",exitList.size());
        if(applyList.size() > 10){
            applyList = applyList.subList(0,10);
        }
        map.put("applyList",applyList);
        if(exitList.size() > 10){
            exitList = exitList.subList(0,10);
        }
        map.put("exitList",exitList);

        return new ResponseEntityWrapper(map);
    }

    /**
     * 资源利用率
     * @param
     * @return
     */
    @PostMapping("/getAgentServerUsage")
    public ResponseEntityWrapper<AnalysisResult> getAgentServerUsage(@RequestBody NodeParam nodeParam,@RequestParam String resourceUsageQualified) {
        Map map = new HashMap<>();
        String scode = "";
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
            appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            scode = StringUtils.join(appCodeList, ",");
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            scode = aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode);
        }
        map.put("notToStandardCount",serverUsageService.getResourceUsageQualified(scode,resourceUsageQualified));
        return new ResponseEntityWrapper(map);
    }

    /**
     * 消费分析
     * @param request
     * @return
     */
    @PostMapping("/getAgentConsumptionAnalysis")
    public ResponseEntityWrapper<AnalysisResult> getAgentConsumptionAnalysis(@RequestBody AnalysisRequest request,@RequestParam String startDate,@RequestParam String endDate) {
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(request.getAppList())){
            appCodeList = request.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            request.setScodes(scodes);
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(request.getDomainList())){
                domainCodeList = request.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            request.setScodes(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }
        if(null == request.getScodes()){
            return new ResponseEntityWrapper(new AnalysisResult());
        }
        //endDate距离当前月份差几个月
        int monthDiff = DateUtil.calculateMonthDifference(endDate.substring(0,7),DateUtils.format(new Date(), "yyyy-MM"));
        AnalysisResult analysisResult = getAIConsumptionAnalysis(request,monthDiff);
        //analysisResult 转map
        Map<String, Object> map = new HashMap<>();
       // NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        String unit = "万元";
        if("USD".equals(request.getCurrency())){
            unit = "万美元";
        }
        map.put("prevMonthExpense", analysisResult.getPrevMonthExpense().divide(new BigDecimal(10000),2,RoundingMode.HALF_UP)+unit);
        map.put("prevMonthIncrease", analysisResult.getPrevMonthIncrease().divide(new BigDecimal(10000),2,RoundingMode.HALF_UP)+unit);
        map.put("prevMonthExpenseByUSD",analysisResult.getPrevMonthExpenseByUSD().divide(new BigDecimal(10000),2,RoundingMode.HALF_UP)+"万美元");
        map.put("prevMonthExpenseByRMB", analysisResult.getPrevMonthExpenseByRMB().divide(new BigDecimal(10000),2,RoundingMode.HALF_UP)+"万元");
        String symbol1 = "";
        if(analysisResult.getPrevMonthRatio().compareTo(BigDecimal.ZERO) < 0){
            analysisResult.setPrevMonthRatio(analysisResult.getPrevMonthRatio().multiply(BigDecimal.valueOf(-1)));
            symbol1 = "↓";
        }else if(analysisResult.getPrevMonthRatio().compareTo(BigDecimal.ZERO) > 0){
            symbol1 = "↑";
        }
        map.put("prevMonthRatio", new StringBuffer(symbol1).append(analysisResult.getPrevMonthRatio().doubleValue()));
        //10月份之前去掉0
        String month = endDate.substring(5,7);
        if(Integer.parseInt(month) < 10){
            month = month.substring(1);
        }
        map.put("month", month);
        return new ResponseEntityWrapper(map);
    }

    private AnalysisResult getAIConsumptionAnalysis(AnalysisRequest request,int monthDiff) {
        List<String> scodes = Arrays.asList(request.getScodes().split(","));
        String currency = "";
        BigDecimal lastMonthAmount = new BigDecimal(0);
        BigDecimal previousAmount = new BigDecimal(0);
        BigDecimal lastMonthAmount2 = new BigDecimal(0);
        BigDecimal convertCNY = new BigDecimal(0);
        String date = DateUtils.format(new Date(), "yyyy-MM-dd");

        ExchangeRate exchangeRate = null;
        try {
            exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if("ALL".equals(request.getCurrency())){
            lastMonthAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodes,request.getVendor(),monthDiff,exchangeRate.getExchangeRate());
            previousAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodes,request.getVendor(),monthDiff+1,exchangeRate.getExchangeRate());
            lastMonthAmount2 = aggregatedBillService.getLastNthMonthTotalAmount(scodes,request.getVendor(),monthDiff,"USD");
            if(lastMonthAmount2.compareTo(BigDecimal.ZERO) > 0){
                convertCNY = lastMonthAmount2.divide(exchangeRate.getExchangeRate(),4,RoundingMode.HALF_UP);
            }
        }else{
            currency = request.getCurrency();
            lastMonthAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodes,request.getVendor(),monthDiff,currency);
            previousAmount = aggregatedBillService.getLastNthMonthTotalAmount(scodes,request.getVendor(),monthDiff+1,currency);
        }
        AnalysisResult analysisResult = new AnalysisResult();
        try {
            BigDecimal prevMonthRatio = BigDecimal.ZERO;
            if (null == previousAmount || BigDecimal.ZERO.compareTo(previousAmount) == 0) {
                previousAmount = BigDecimal.ZERO;
            } else {
                prevMonthRatio = (lastMonthAmount.subtract(previousAmount))
                        .divide(previousAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }
            analysisResult = AnalysisResult
                    .builder()
                    .prevMonthExpense(lastMonthAmount)
                    .prevMonthExpenseByUSD(lastMonthAmount2)
                    .prevMonthExpenseByRMB(convertCNY)
                    .prevMonthIncrease(lastMonthAmount.subtract(previousAmount))
                    .prevMonthRatio(prevMonthRatio)
                    .build();
        } catch (Exception e) {
            log.error("Failed to get analysis result", e);
        }
        return analysisResult;
    }


    /**
     * 消费分析
     * @param request
     * @return
     */
    @PostMapping("/getConsumptionAnalysis")
    public ResponseEntityWrapper<AnalysisResult> getConsumptionAnalysis(@RequestBody @Valid AnalysisRequest request) {
        List<String> scodes = Arrays.asList(request.getScodes().split(","));

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(4);
        // 创建一个CompletionService，用于获取任务执行结果
        CompletionService<BigDecimal> service = new ExecutorCompletionService<>(executor);

        // 创建一个计数器，用于统计完成的线程数
        AtomicInteger count = new AtomicInteger();
        // 创建一个CountDownLatch，用于等待所有线程完成
        CountDownLatch latch = new CountDownLatch(4);

        Future<BigDecimal> estimatedAmountOfCurrentMonthFuture =
                analysisTask(service, () -> getForecastForTheMonthFunc.apply(scodes), count, latch, log);
        Future<BigDecimal> lastMonthAmountFuture =
                analysisTask(service, () -> getLastNthMonthTotalAmountFunc.apply(scodes, 1), count, latch, log);
        Future<BigDecimal> previousMonthAmountFuture =
                analysisTask(service, () -> getLastNthMonthTotalAmountFunc.apply(scodes, 2), count, latch, log);
        Future<BigDecimal> currentYearAmountFuture =
                analysisTask(service, () -> getCurrentYearTotalAmountFunc.apply(scodes), count, latch, log);
        Future<BigDecimal> lastYearAmountFuture =
                analysisTask(service, () -> getLastYearTotalAmountFunc.apply(scodes), count, latch, log);


        // 等待所有任务完成或超时
        try {
            if (!latch.await(5, TimeUnit.SECONDS)) {
                return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
            }
        } catch (InterruptedException e) {
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
        }

        AnalysisResult analysisResult = new AnalysisResult();
        try {
            BigDecimal lastMonthAmount = lastMonthAmountFuture.get();
            BigDecimal previousAmount = previousMonthAmountFuture.get();
            BigDecimal forecastForTheMonth = estimatedAmountOfCurrentMonthFuture.get();


            BigDecimal prevMonthRatio;
            if (BigDecimal.ZERO.compareTo(previousAmount) == 0) {
                prevMonthRatio = BigDecimal.ZERO;
            } else {
                prevMonthRatio = (lastMonthAmount.subtract(previousAmount))
                        .divide(previousAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }


            BigDecimal currentMonthRatio;
            if (BigDecimal.ZERO.compareTo(lastMonthAmount) == 0) {
                currentMonthRatio = BigDecimal.ZERO;
            } else {
                currentMonthRatio = (forecastForTheMonth.subtract(lastMonthAmount))
                        .divide(lastMonthAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }


            BigDecimal currentYearTotal = currentYearAmountFuture.get();
            BigDecimal lastYearTotal = lastYearAmountFuture.get();

            BigDecimal currentYearRatio;
            if (BigDecimal.ZERO.compareTo(currentYearTotal) == 0) {
                currentYearRatio = BigDecimal.ZERO;
            } else {
                currentYearRatio = (currentYearTotal.subtract(lastYearTotal))
                        .divide(currentYearTotal, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }

            analysisResult = AnalysisResult
                    .builder()
                    .currentYearTotal(currentYearTotal)
                    .forecastForTheMonth(forecastForTheMonth)
                    .prevMonthExpense(lastMonthAmount)
                    .prevMonthRatio(prevMonthRatio)
                    .currentMonthRatio(currentMonthRatio)
                    .currentYearRatio(currentYearRatio)
                    .build();
        } catch (Exception e) {
            log.error("Failed to get analysis result", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "Failed to get analysis result", null);
        }

        return new ResponseEntityWrapper<>(analysisResult);
    }


    @PostMapping("/getConsumptionAnalysis/usd")
    public ResponseEntityWrapper<AnalysisResult> getConsumptionAnalysisWithUsd(@RequestBody @Valid AnalysisRequest request) {
        List<String> scodes = Arrays.asList(request.getScodes().split(","));

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(4);
        // 创建一个CompletionService，用于获取任务执行结果
        CompletionService<BigDecimal> service = new ExecutorCompletionService<>(executor);

        // 创建一个计数器，用于统计完成的线程数
        AtomicInteger count = new AtomicInteger();
        // 创建一个CountDownLatch，用于等待所有线程完成
        CountDownLatch latch = new CountDownLatch(4);

        Future<BigDecimal> estimatedAmountOfCurrentMonthFuture =
                analysisTask(service, () -> getForecastForTheMonthUsdFunc.apply(scodes), count, latch, log);
        Future<BigDecimal> lastMonthAmountFuture =
                analysisTask(service, () -> getLastNthMonthTotalAmountUsdFunc.apply(scodes, 1), count, latch, log);
        Future<BigDecimal> previousMonthAmountFuture =
                analysisTask(service, () -> getLastNthMonthTotalAmountUsdFunc.apply(scodes, 2), count, latch, log);
        Future<BigDecimal> currentYearAmountFuture =
                analysisTask(service, () -> getCurrentYearTotalAmountUsdFunc.apply(scodes), count, latch, log);
        Future<BigDecimal> lastYearAmountFuture =
                analysisTask(service, () -> getLastYearTotalAmountUsdFunc.apply(scodes), count, latch, log);


        // 等待所有任务完成或超时
        try {
            if (!latch.await(5, TimeUnit.SECONDS)) {
                return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
            }
        } catch (InterruptedException e) {
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
        }

        AnalysisResult analysisResult = new AnalysisResult();
        try {
            BigDecimal lastMonthAmount = lastMonthAmountFuture.get();
            BigDecimal previousAmount = previousMonthAmountFuture.get();
            BigDecimal forecastForTheMonth = estimatedAmountOfCurrentMonthFuture.get();


            BigDecimal prevMonthRatio;
            if (BigDecimal.ZERO.compareTo(previousAmount) == 0) {
                prevMonthRatio = BigDecimal.ZERO;
            } else {
                prevMonthRatio = (lastMonthAmount.subtract(previousAmount))
                        .divide(previousAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }


            BigDecimal currentMonthRatio;
            if (BigDecimal.ZERO.compareTo(lastMonthAmount) == 0) {
                currentMonthRatio = BigDecimal.ZERO;
            } else {
                currentMonthRatio = (forecastForTheMonth.subtract(lastMonthAmount))
                        .divide(lastMonthAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }


            BigDecimal currentYearTotal = currentYearAmountFuture.get();
            BigDecimal lastYearTotal = lastYearAmountFuture.get();

            BigDecimal currentYearRatio;
            if (BigDecimal.ZERO.compareTo(currentYearTotal) == 0) {
                currentYearRatio = BigDecimal.ZERO;
            } else {
                currentYearRatio = (currentYearTotal.subtract(lastYearTotal))
                        .divide(currentYearTotal, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100));
            }

            analysisResult = AnalysisResult
                    .builder()
                    .currentYearTotal(currentYearTotal)
                    .forecastForTheMonth(forecastForTheMonth)
                    .prevMonthExpense(lastMonthAmount)
                    .prevMonthRatio(prevMonthRatio)
                    .currentMonthRatio(currentMonthRatio)
                    .currentYearRatio(currentYearRatio)
                    .build();
        } catch (Exception e) {
            log.error("Failed to get analysis result", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "Failed to get analysis result", null);
        }

        return new ResponseEntityWrapper<>(analysisResult);
    }

    /**
     * 查询日消费金额分析
     *
     * @param request
     * @return
     */
    @PostMapping("/getAgentDayBillAnalysis")
    public ResponseEntityWrapper<DayAnalysisResult> getAgentDayBillAnalysis(
            @RequestBody @Valid DayAnalysisRequest request) {
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(request.getAppList())){
            appCodeList = request.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            request.setScodes(scodes);
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(request.getDomainList())){
                domainCodeList = request.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            request.setScodes(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }
        //获取当前月份，格式yyyy-MM
        String month = DateUtil.getCurrentMonth();
        request.setMonth(month);
        if(StringUtils.isEmpty(request.getScodes())){
            return new ResponseEntityWrapper<>(new DayAnalysisResult());
        }
        DayAnalysisResult dayAnalysisResult = getBillsPerDayOfMonthAndPreviousMonth(request).getData();
        List<String> dates1 = new ArrayList<>();
        List<String> dates2 = new ArrayList<>();
        List<DayBillAnalysisVo> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dayAnalysisResult.getPreviousMonthBills())){
            Map map = processBills(dayAnalysisResult.getPreviousMonthBills(),DateUtil.getPreviousMonth(month));
            List<DayBillAnalysisVo>  previousMonthBills =(List<DayBillAnalysisVo>) map.get("result");
            dayAnalysisResult.setPreviousMonthBills(previousMonthBills);
            dates2 = (List<String>) map.get("dates");
            resultList.addAll(previousMonthBills);
        }
        if(!CollectionUtils.isEmpty(dayAnalysisResult.getBills())){
            Map map = processBills(dayAnalysisResult.getBills(),month);
            List<DayBillAnalysisVo>  bills = (List<DayBillAnalysisVo>) map.get("result");
            dayAnalysisResult.setBills(bills);
            dates1 = (List<String>) map.get("dates");
            resultList.addAll(bills);
        }
        dayAnalysisResult.setTotalBills(resultList);
        if (dates1.size() > dates2.size()) {
            dayAnalysisResult.setDates(dates1);
        }else{
            dayAnalysisResult.setDates(dates2);
        }
        return new ResponseEntityWrapper<>(dayAnalysisResult);
    }

    private static Map processBills(List<DayBillAnalysisVo> bills,String month) {
        Map map = new HashMap();
        // 阶段1：合并相同日期的金额
        Map<LocalDate, BigDecimal> dateMap = new HashMap<>();
        for (DayBillAnalysisVo bill : bills) {
            LocalDate date = LocalDate.parse(bill.getBillingCycle());
            BigDecimal amount = new BigDecimal(bill.getTotal());
            dateMap.merge(date, amount, BigDecimal::add);
        }

        LocalDate startDate = Collections.min(dateMap.keySet());
        LocalDate endDate = Collections.max(dateMap.keySet());

        // 阶段3：生成完整日期序列
        List<LocalDate> fullDateRange = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            fullDateRange.add(date);
        }

        // 阶段4：构建最终结果（自动补全缺失日期）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("d号");
        List<DayBillAnalysisVo> result = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        for (LocalDate date : fullDateRange) {
            String formattedDate = date.format(formatter);
            BigDecimal total = dateMap.getOrDefault(date, BigDecimal.ZERO)
                    .stripTrailingZeros();
            result.add(new DayBillAnalysisVo(formattedDate, total.toPlainString(),month));
            dates.add(formattedDate);
        }
        map.put("dates",dates);
        map.put("result",result);
        return map;
    }


    /**
     * 查询日消费金额分析
     *
     * @param request
     * @return
     */
    @PostMapping("/getDayBillAnalysis")
    public ResponseEntityWrapper<DayAnalysisResult> getBillsPerDayOfMonthAndPreviousMonth(
            @RequestBody @Valid DayAnalysisRequest request) {
        String month = request.getMonth();
        if (!DateUtil.isValidFormat("yyyy-MM", month)) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR.getCode(), "Invalid month format", null);
        }

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);
        // 创建一个CompletionService，用于获取任务执行结果
        CompletionService<List<DayBillAnalysisVo>> service = new ExecutorCompletionService<>(executor);

        // 创建一个计数器，用于统计完成的线程数
        AtomicInteger count = new AtomicInteger();
        // 创建一个CountDownLatch，用于等待所有线程完成
        CountDownLatch latch = new CountDownLatch(2);

        Future<List<DayBillAnalysisVo>> billsFuture = analysisTask(
                service,
                () -> aggregatedBillService.getMonthAggregatedBill(Arrays.asList(request.getScodes().split(","))
                        , request.getVendor()
                        , month
                        , CNY.getCurrency()
                ),
                count,
                latch,
                log
        );
        Future<List<DayBillAnalysisVo>> previousMonthBillsFuture = analysisTask(
                service,
                () -> aggregatedBillService.getMonthAggregatedBill(Arrays.asList(request.getScodes().split(","))
                        , request.getVendor()
                        , DateUtil.getPreviousMonth(month)
                        , CNY.getCurrency()
                ),
                count,
                latch,
                log
        );

        // 等待所有任务完成或超时
        try {
            if (!latch.await(2, TimeUnit.SECONDS)) {
                return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
            }
        } catch (InterruptedException e) {
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
        }

        DayAnalysisResult dayAnalysisResult = new DayAnalysisResult();

        try {
            var bills = billsFuture.get();
            var previousMonthBills = previousMonthBillsFuture.get();
            dayAnalysisResult = new DayAnalysisResult(bills, previousMonthBills);
        } catch (Exception e) {
            log.error("Failed to get day analysis result", e);
            throw new RuntimeException(e);
        }


        return new ResponseEntityWrapper<>(dayAnalysisResult);
    }


    /**
     * 查询日消费金额分析
     *
     * @param request
     * @return
     */
    @PostMapping("/getDayBillAnalysis/usd")
    public ResponseEntityWrapper<DayAnalysisResult> getBillsPerDayOfMonthAndPreviousMonthWithUsd(
            @RequestBody @Valid DayAnalysisRequest request) {
        String month = request.getMonth();
        if (!DateUtil.isValidFormat("yyyy-MM", month)) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR.getCode(), "Invalid month format", null);
        }

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);
        // 创建一个CompletionService，用于获取任务执行结果
        CompletionService<List<DayBillAnalysisVo>> service = new ExecutorCompletionService<>(executor);

        // 创建一个计数器，用于统计完成的线程数
        AtomicInteger count = new AtomicInteger();
        // 创建一个CountDownLatch，用于等待所有线程完成
        CountDownLatch latch = new CountDownLatch(2);

        Future<List<DayBillAnalysisVo>> billsFuture = analysisTask(
                service,
                () -> aggregatedBillService.getMonthAggregatedBill(Arrays.asList(request.getScodes().split(","))
                        , request.getVendor()
                        , month
                        , USD.getCurrency()
                ),
                count,
                latch,
                log
        );
        Future<List<DayBillAnalysisVo>> previousMonthBillsFuture = analysisTask(
                service,
                () -> aggregatedBillService.getMonthAggregatedBill(Arrays.asList(request.getScodes().split(","))
                        , request.getVendor()
                        , DateUtil.getPreviousMonth(month)
                        , USD.getCurrency()
                ),
                count,
                latch,
                log
        );

        // 等待所有任务完成或超时
        try {
            if (!latch.await(2, TimeUnit.SECONDS)) {
                return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
            }
        } catch (InterruptedException e) {
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR.getCode(), "任务执行超时", null);
        }

        DayAnalysisResult dayAnalysisResult = new DayAnalysisResult();

        try {
            var bills = billsFuture.get();
            var previousMonthBills = previousMonthBillsFuture.get();
            dayAnalysisResult = new DayAnalysisResult(bills, previousMonthBills);
        } catch (Exception e) {
            log.error("Failed to get day analysis result", e);
            throw new RuntimeException(e);
        }


        return new ResponseEntityWrapper<>(dayAnalysisResult);
    }


    /**
     * 日消费金额分析
     */
    @Data
    static class DayAnalysisRequest {
        /**
         * S码列表
         */
        private String scodes;

        /**
         * 供应商
         */
        private String vendor;

        /**
         * 月份
         */
        private String month;

        private List<Map<String,String>> appList;

        private List<Map<String,String>> domainList;
    }

    @Data
    @NoArgsConstructor
    static class DayAnalysisResult {
        private List<DayBillAnalysisVo> bills = new ArrayList<>();
        private List<DayBillAnalysisVo> previousMonthBills = new ArrayList<>();
        private List<String> dates = new ArrayList<>();
        private List<DayBillAnalysisVo> totalBills = new ArrayList<>();

        public DayAnalysisResult(List<DayBillAnalysisVo> bills, List<DayBillAnalysisVo> previousMonthBills) {
            this.bills = bills;
            this.previousMonthBills = previousMonthBills;
        }
    }

    @Data
    static class AnalysisRequest {
        /**
         * S码列表
         */
        @NotNull
        private String scodes;

        private String vendor;

        private List<Map<String, String>> domainList;

        private List<Map<String,String>> appList;

        private String currency;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class AnalysisResult {
        /**
         * 当月预测
         */
        private BigDecimal forecastForTheMonth = BigDecimal.ZERO;

        /**
         * 上月消费(人民币)
         */
        private BigDecimal prevMonthExpense = BigDecimal.ZERO;

        /**
         * 上月消费(美元)
         */
        private BigDecimal prevMonthExpenseByUSD = BigDecimal.ZERO;
        /**
         * 上月消费(美元折合人民币)
         */
        private BigDecimal prevMonthExpenseByRMB = BigDecimal.ZERO;
        /**
         * 上上月消费
         */
        private BigDecimal prevMonthIncrease = BigDecimal.ZERO;

        /**
         * 当年累计消费
         */
        private BigDecimal currentYearTotal = BigDecimal.ZERO;

        /**
         * 上月月环比
         */
        private BigDecimal prevMonthRatio = BigDecimal.ZERO;

        /**
         * 本月月环比
         */
        private BigDecimal currentMonthRatio = BigDecimal.ZERO;

        /**
         * 当年环比
         */
        private BigDecimal currentYearRatio = BigDecimal.ZERO;
    }
}
