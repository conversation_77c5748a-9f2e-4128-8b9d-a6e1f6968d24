package com.haier.devops.bill.common.vo;

import lombok.Data;

/**
 * 领域子产品value object
 */
@Data
public class DomainSubProductVo {
    /**
     * 领域编码
     */
    private String domainCode;

    /**
     * 领域名称
     */
    private String domainName;

    /**
     * 简称
     */
    private String appShortName;

    /**
     * S码
     */
    private String scode;

    /**
     * 子产品编码
     */
    private String subProductId;

    /**
     * 子产品名称
     */
    private String subProductName;

    /**
     * 子产品英文名称
     */
    private String subProductNameEn;
}
