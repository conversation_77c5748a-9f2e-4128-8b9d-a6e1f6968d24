package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haier.devops.bill.common.dto.BillGatheredDTO;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.BillGatheredService;
import com.haier.devops.bill.common.service.RefinedRawBillService;
import com.haier.devops.bill.common.vo.AggregatedBillVo;
import com.haier.devops.bill.common.vo.AlarmLogBillDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
* @ClassName: BillGatheredServiceImpl
* @Description: 每日账单汇总服务
* @author: 张爱苹
* @date: 2024/1/19 18:34
*/
@Service
@Slf4j
public class BillGatheredServiceImpl implements BillGatheredService {
    private Logger logger = LoggerFactory.getLogger(BillGatheredServiceImpl.class);

    @Autowired
    private AggregatedBillService aggregatedBillService;

    @Autowired
    private RefinedRawBillService rawBillService;

    @Override
    public AlarmLogBillDetailVo getAlarmBillDetail(BillGatheredDTO billGatheredDTO) throws Exception{
        billGatheredDTO.setGranularity("day");
        List<AggregatedBillVo> aggregatedBillVoList =
                aggregatedBillService.listByPage(billGatheredDTO).getList();
        if(CollectionUtils.isEmpty(aggregatedBillVoList)){
            throw new RuntimeException("汇总账单不存在");
        }
        //查询明细
        QueryWrapper<RefinedRawBill> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("aggregated_id",billGatheredDTO.getGatheringId());
        queryWrapper.eq("billing_cycle",billGatheredDTO.getBillingCycle());
        List<RefinedRawBill>
                rawBillList = rawBillService.list(queryWrapper);
        if(CollectionUtils.isEmpty(rawBillList)){
            throw new RuntimeException("账单明细不存在");
        }
        AlarmLogBillDetailVo alarmLogBillDetailVo = AlarmLogBillDetailVo.builder().aggregatedBillVo(aggregatedBillVoList.get(0)).rawBillList(rawBillList).build();
        return alarmLogBillDetailVo;
    }
}
