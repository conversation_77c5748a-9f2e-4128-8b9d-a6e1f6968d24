package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.EnterpriseProviders;

import java.util.List;

/**
* @ClassName: EnterpriseProvidersService
* @Description: 企业项目支持的服务
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
public interface EnterpriseProvidersService extends IService<EnterpriseProviders> {

    /**
    * @Description: 新增企业项目支持的服务
    * @author: 张爱苹
    * @date: 2025/2/14 10:38
    * @param hr690n:
    * @Return: void
    */
    void insertEnterpriseProviders(String accountName) throws Exception;

    /**
    * @Description: 获取企业项目支持的服务
    * @Description:
    * @author: 张爱苹
    * @date: 2025/2/14 14:51

    * @Return: java.util.List<com.haier.devops.bill.common.entity.EnterpriseProviders>
    */
    List<EnterpriseProviders> getEnterpriseProviders();
}
