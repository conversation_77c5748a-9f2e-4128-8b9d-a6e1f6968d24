package com.haier.devops.bill.common.config;

import com.haier.devops.bill.common.api.HworkBillApi;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * Hwork账单对接接口feign配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class HworkBillOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private static final String AUTHORIZATION_KEY = "Authorization";
    private final HdsBillCenterTokenConfigProperties billCenterTokenConfigProperties;

    private okhttp3.OkHttpClient hworkBillOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    public HworkBillOkHttpFeignConfiguration(HdsBillCenterTokenConfigProperties billCenterTokenConfigProperties,
                                             OkHttpProperties properties,
                                             OkHttpClientFactory httpClientFactory,
                                             OkHttpClientConnectionPoolFactory connectionPoolFactory) {
        this.billCenterTokenConfigProperties = billCenterTokenConfigProperties;
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @Bean
    public HworkBillApi hworkBillApi() {
        this.hworkBillOkHttp3Client = setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);


        this.hworkBillOkHttp3Client = hworkBillOkHttp3Client.newBuilder()
                .addInterceptor(authenticationInterceptor(AUTHORIZATION_KEY, billCenterTokenConfigProperties.getToken()))
                .addInterceptor(contentTypeInterceptor(ContentType.APPLICATION_JSON))
                .build();


        return Feign.builder()
                .client(new OkHttpClient(hworkBillOkHttp3Client))
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .target(HworkBillApi.class, billCenterTokenConfigProperties.getUrl());
    }



    @PreDestroy
    public void destroy() {
        super.destroy(this.hworkBillOkHttp3Client);
    }

}

