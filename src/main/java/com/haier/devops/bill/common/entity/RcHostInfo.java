package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (RcHostInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-12-12 10:29:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class RcHostInfo implements Serializable {
    private static final long serialVersionUID = 993839157429297482L;
       
    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
     
       
    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;
     
       
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;
     
       
    @Schema(description = "云厂商")
    private String vendor;
     
       
    @Schema(description = "云账号")
    private String accountName;
     
       
    @Schema(description = "主机ID")
    private String instanceId;
     
       
    @Schema(description = "主机名称")
    private String instanceName;
     
       
    @Schema(description = "主机创建时间")
    private LocalDateTime creationTime;
     
       
    @Schema(description = "主机过期时间")
    private LocalDateTime expiredTime;
     
       
    @Schema(description = "是否已删除")
    private String isDeleted;
     
       
    @Schema(description = "内网IP")
    private String privateIp;
     
       
    @Schema(description = "公网IP")
    private String publicIp;
     
       
    @Schema(description = "S码")
    private String scode;
     
       
    @Schema(description = "所属项目")
    private String project;
     
       
    @Schema(description = "所属环境")
    private String env;
     
       
    @Schema(description = "主机资源组")
    private String resourceGroup;
     
       
    @Schema(description = "操作系统类型")
    private String osType;
     
       
    @Schema(description = "操作系统名称")
    private String osName;
     
       
    @Schema(description = "操作系统架构")
    private String osArch;
     
       
    @Schema(description = "系统镜像id")
    private String imageId;
     
       
    @Schema(description = "CPU核数")
    private Integer cpu;
     
       
    @Schema(description = "是否开启numa")
    private String numa;
     
       
    @Schema(description = "内存大小(M)")
    private Long memory;
     
       
    @Schema(description = "硬盘大小(M)")
    private Long diskSize;
     
       
    @Schema(description = "磁盘类型")
    private String diskType;
     
       
    @Schema(description = "主机状态")
    private String hostStatus;
     
       
    @Schema(description = "主机类型，云主机、虚拟机或物理机")
    private String hostType;
     
       
    @Schema(description = "宿主机ID")
    private String hostInsId;
     
       
    @Schema(description = "所在地区")
    private String region;
     
       
    @Schema(description = "所在机房")
    private String zone;
     
       
    @Schema(description = "网络类型")
    private String networkType;
     
       
    @Schema(description = "所属VPC")
    private String vpcId;
     
       
    @Schema(description = "所属子网")
    private String subnetId;
     
       
    @Schema(description = "云上主机规格码")
    private String classCode;
     
       
    @Schema(description = "主机付费类型")
    private String chargeType;
     
       
    @Schema(description = "机架位")
    private String rack;
     
       
    @Schema(description = "供应商名称")
    private String providerName;
     
       
    @Schema(description = "主机品牌")
    private String brandName;
     
       
    @Schema(description = "主机型号")
    private String model;
     
       
    @Schema(description = "主机SN号")
    private String sn;
     
       
    @Schema(description = "主机描述")
    private String description;
     
       
    @Schema(description = "资源唯一ID")
    private String resourceId;
     
       
    private String uniRegionId;
     
       
    private String idracIp;
     
       
    private String domain;
     
       
    private String team;
     
       
    private String ownerId;
     
       
    private String ownerName;
     
       
    @Schema(description = "运维人员")
    private String maintainer;
     

}

