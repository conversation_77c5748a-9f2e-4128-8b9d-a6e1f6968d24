package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 账单汇总vo
 * @Author: A0018437
 * @Date：2024-02-29
 */
@Data
public class BillingSummaryVo {
    /**
     * 应付总额
     */
    private BigDecimal payableSum;

    /**
     * 代金券总额
     */
    private BigDecimal voucherSum;

    /**
     * 现金总额
     */
    private BigDecimal cashSum;

    /**
     * 优惠券总额
     */
    private BigDecimal couponSum;

    /**
     * 原价
     */
    private BigDecimal originalSum;

    /**
     * 账单和
     */
    private BigDecimal summer;
    /**
     * 聚合id
     */
    private String aggregatedId;
    /**
     * 云厂商
     */
    private String vendor;
    /**
     * 云账号
     */
    private String accountName;
    /**
     * 云账户id
     */
    private String accountId;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 区域
     */
    private String region;
    /**
     * 区域
     */
    private String zone;
    /**
     * 结算单元
     */
    private String costUnit;

    /**
     * 币种
     */
    private String currency;

    /**
     * 账期
     */
    private LocalDateTime billingCycle;
    /**
     * 消费类型
     */
    private String subscriptionType;

    /**
     * 分拆项
     */
    private String supplementId;

    /**
     * 未经验证的S码
     */
    private String scodeUnverified;

    /**
     * 匹配S码的规则id，默认为0，即没走规则
     */
    private Integer scodeRuleMatched;

    /**
     * 父级S码是否找到，-1未找到，1找到，0无需寻找
     */
    private Integer parentFound;
}
