package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.CloudAccount;
import com.haier.devops.bill.common.mapper.CloudAccountMapper;
import com.haier.devops.bill.common.redis.Cache;
import com.haier.devops.bill.common.service.CloudAccountService;
import com.haier.devops.bill.util.AesUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 云账号表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Service
public class CloudAccountServiceImpl
        extends ServiceImpl<CloudAccountMapper, CloudAccount>
        implements CloudAccountService {

    private CloudAccountMapper cloudAccountMapper;
    private Cache<String, List<CloudAccount>> cloudAccountCache;

    public CloudAccountServiceImpl(CloudAccountMapper cloudAccountMapper, @Qualifier("cloudAccountCache") Cache<String, List<CloudAccount>> cloudAccountCache) {
        this.cloudAccountMapper = cloudAccountMapper;
        this.cloudAccountCache = cloudAccountCache;
    }

    @Override
    public List<CloudAccount> getEnabledAccounts(String vendor, String accountName) {
        List<CloudAccount> cloudAccountList = cloudAccountCache.get(vendor);

        if (CollectionUtils.isEmpty(cloudAccountList)){
            LambdaQueryWrapper<CloudAccount> wrapper = new LambdaQueryWrapper<CloudAccount>()
                    .eq(CloudAccount::getIsEnabled, 1)
                    .eq(CloudAccount::getVendor, vendor);

            cloudAccountList = cloudAccountMapper.selectList(wrapper);
            cloudAccountCache.put(vendor,cloudAccountList, CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
        }

        if (StringUtils.isNotBlank(accountName)) {
            cloudAccountList = cloudAccountList.stream().filter(cloudAccount -> cloudAccount.getAccountName().equals(accountName)).collect(Collectors.toList());
        }

        return cloudAccountList;
    }

    public Map<String, String> getOpenApiKey(String vendor, String accountName, String... purposeType) {
        String purpose = "MAIN";

        if (null != purposeType && purposeType.length > 0) {
            purpose = purposeType[0];
        }
        // 获取账号信息
        String finalPurpose = purpose;
        List<CloudAccount> enabledAccounts = this.getEnabledAccounts(vendor, accountName)
                .stream().filter(cloudAccount -> cloudAccount.getPurposeType().equals(finalPurpose)).collect(Collectors.toList());
        String accessKey;
        String accessSecret;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(enabledAccounts)) {
            accessKey = enabledAccounts.get(0).getEncrypted().equals("1") ? AesUtil.decryptCBC(enabledAccounts.get(0).getAccessKey()) : enabledAccounts.get(0).getAccessKey();
            accessSecret = enabledAccounts.get(0).getEncrypted().equals("1") ? AesUtil.decryptCBC(enabledAccounts.get(0).getAccessKeySecret()) : enabledAccounts.get(0).getAccessKeySecret();
        } else {
            accessSecret = "";
            accessKey = "";
        }
        Map<String, String> map = new HashMap<>();
        map.put("accessKey", accessKey);
        map.put("accessKeySecret", accessSecret);
        return map;
    }

    @Override
    public List<String> getCloudAccountList(String vendor,String accountName) {
        LambdaQueryWrapper<CloudAccount> queryWrapper =
        new LambdaQueryWrapper<CloudAccount>()
                .eq(CloudAccount::getVendor, vendor)
                .eq(CloudAccount::getIsEnabled, 1);
        if(!StringUtils.isEmpty(accountName)){
            queryWrapper.like(CloudAccount::getAccountName, accountName);
        }
        List<CloudAccount> cloudAccountList = cloudAccountMapper.selectList(queryWrapper);
        return cloudAccountList.stream().map(CloudAccount::getAccountName).distinct().collect(Collectors.toList());
    }

    @Override
    public List<CloudAccount> getAliyunAccountList() {
        LambdaQueryWrapper<CloudAccount> queryWrapper =
                new LambdaQueryWrapper<CloudAccount>();
        queryWrapper.likeRight(CloudAccount::getVendor, "aliyun");
        queryWrapper.eq(CloudAccount::getIsEnabled, 1);
        queryWrapper.eq(CloudAccount::getPurposeType, "MAIN");
        return list(queryWrapper);
    }

    @Override
    public List<CloudAccount> getAliyunDedicatedAccountList() {
        LambdaQueryWrapper<CloudAccount> queryWrapper =
                new LambdaQueryWrapper<CloudAccount>();
        queryWrapper.eq(CloudAccount::getIsEnabled, 1);
        queryWrapper.apply("((vendor = 'aliyun' and purpose_type = 'MAIN') or (vendor = 'aliyun_dedicated' and purpose_type = 'DEDICATED'))");
        return list(queryWrapper);
    }

    @Override
    public List<CloudAccount> getHuaweiAccountList(String purposeType) {
        LambdaQueryWrapper<CloudAccount> queryWrapper =
                new LambdaQueryWrapper<CloudAccount>();
        queryWrapper.likeRight(CloudAccount::getVendor, "huawei");
        queryWrapper.eq(CloudAccount::getIsEnabled, 1);
        if(StringUtils.isNotEmpty(purposeType)){
            queryWrapper.eq(CloudAccount::getPurposeType, purposeType);
        }
        return list(queryWrapper);
    }

    @Override
    public List<String> getAccountListByCurrentCy(String currentCy) {
        LambdaQueryWrapper<CloudAccount> queryWrapper =
                new LambdaQueryWrapper<CloudAccount>();
        queryWrapper.eq(CloudAccount::getIsEnabled, 1);
        queryWrapper.eq(CloudAccount::getCurrency, currentCy);
        List<CloudAccount> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        List<String> accounts = list.stream().map(item ->{
            return item.getVendor()+"-"+item.getAccountName();
        }).distinct().collect(Collectors.toList());
        return accounts;
    }

    @Override
    public List<CloudAccount> getAwsAccountList(String purposeType) {
        LambdaQueryWrapper<CloudAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CloudAccount::getVendor, "aws");
        queryWrapper.eq(CloudAccount::getIsEnabled, "1");
        if (StringUtils.isNotEmpty(purposeType)) {
            queryWrapper.eq(CloudAccount::getPurposeType, purposeType);
        }
        return list(queryWrapper);
    }

    @Override
    public CloudAccount getAccountById(Long id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }

    @Override
    public CloudAccount getAccountByIdentify(String accountIdentify) {
        if (StringUtils.isEmpty(accountIdentify)) {
            return null;
        }
        LambdaQueryWrapper<CloudAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CloudAccount::getAccountIdentify, accountIdentify);
        queryWrapper.eq(CloudAccount::getIsEnabled, "Y");
        queryWrapper.last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public Map<String, String> getDecryptedAccessKeys(CloudAccount cloudAccount) {
        if (cloudAccount == null) {
            return new HashMap<>();
        }

        String accessKey = cloudAccount.getAccessKey();
        String accessKeySecret = cloudAccount.getAccessKeySecret();

        // 如果是加密状态，则先解密
        if ("1".equals(cloudAccount.getEncrypted())) {
            accessKey = AesUtil.decryptCBC(accessKey);
            accessKeySecret = AesUtil.decryptCBC(accessKeySecret);
        }

        Map<String, String> result = new HashMap<>();
        result.put("accessKey", accessKey);
        result.put("accessKeySecret", accessKeySecret);

        return result;
    }
}
