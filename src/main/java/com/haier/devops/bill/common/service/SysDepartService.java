package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.SysDepart;
import com.haier.devops.bill.common.vo.DepartIdVo;

import java.util.List;

/**
 * (SysDepart)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-19 15:36:14
 */
public interface SysDepartService extends IService<SysDepart> {
    List<DepartIdVo> queryDepartIdTreeList();
}
