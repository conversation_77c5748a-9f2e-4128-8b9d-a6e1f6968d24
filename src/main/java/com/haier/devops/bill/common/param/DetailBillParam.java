package com.haier.devops.bill.common.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class DetailBillParam {

    /**
     * S码
     */
    @NotNull(message = "s码不可为空")
    String scode;

    /**
     * 有权限的S码列表
     */
    private List<String> scodeList;

    /**
     * 产品编码
     */
    String productCode;

    List<String> productList;

    @Min(1)
    Integer page = 1;

    @Min(1)
    Integer per_page = 10;

    Integer start_no = 0;

    /**
     * 开始周期
     */
    @NotNull(message = "开始周期不可为空")
    String startCycle;

    /**
     * 结束周期
     */
    @NotNull(message = "结束周期不可为空")
    String endCycle;

    /**
     * 类型 1 月 2 季 3 年
     */
    String type;

    /**
     * 云厂商
     */
    String vendor;

    /**
     * 云账号
     */
    String account;

    /**
     * 提交人
     */
    @NotNull(message = "提交人不能为空")
    private String submitter;

    private String serialNo;

    private List<String> permScodes;

    private String subscriptionType;

    /**
     * 消费金额 1 升序 2 降序
     */
    private Integer sortNo;

    /**
     * 内网ip
     */
    @ExcelProperty("ip/host")
    @ColumnWidth(8)
    private String privateIp;

    /**
     * 资源实例id
     */
    @ExcelProperty("实例ID")
    @ColumnWidth(8)
    private String instanceId;


    /**
     * 分拆项
     */
    private String supplementId;

    private String aggregatedId;

    private String currency;

    private BigDecimal exchangeRate;

}
