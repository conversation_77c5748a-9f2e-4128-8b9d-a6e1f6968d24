package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: CloudVendor
* @Description: 云厂商
* @author: 张爱苹
* @date: 2024/3/4 09:52
*/
@TableName("cloud_vendor")
@Data
@Builder
public class CloudVendor implements Serializable {


    private static final long serialVersionUID = 5956572054653855045L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Date createdTime;

    private Date updatedTime;

    /**
     * 云厂商编码
     *
     */
    private String vendorCode;


    /**
     * 云厂商
     */
    private String vendorDesc;


    private String isDeleted;

    /**
     * 云厂商账号
     */
    private String cloudAdministrator;

}
