package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.Invoice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface InvoiceMapper extends BaseMapper<Invoice> {
    /**
     * 获取发票列表
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<Invoice> getInvoiceList(@Param("vendor") String vendor,
                                 @Param("billingCycle") String billingCycle,
                                 @Param("account") String... account);
}
