package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data

public class CpAlarmRuleDetailVo implements Serializable {

    private static final long serialVersionUID = 2305259385130846959L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 通知方式
     */
    private String noticeWay;

    /**
     * 通知对象
     */
    private String noticeObj;

    /**
     * 通知对象id
     */
    private String noticeObjId;

    /**
     * 通知对象id
     */
    private List<AlarmNoticeObjVo> alarmNoticeObjList;

}
