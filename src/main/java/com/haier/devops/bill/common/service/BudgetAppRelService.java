package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.BudgetAppRel;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface BudgetAppRelService extends IService<BudgetAppRel> {
    /**
     * 根据scode和vendor查询最新的预算配置关系
     * @param scode 应用S码
     * @param vendor 云厂商
     * @return 最新的预算配置关系，如果不存在则返回null
     */
    BudgetAppRel findLatestByCodeAndVendor(String scode, String vendor);


    /**
     * 检查scode和budgetCode的关系是否存在，如果不存在则添加
     * @param scode 应用S码
     * @param vendor 云厂商
     * @param budgetCode 预算编码
     * @param billingCycle 账期
     * @return 是否添加了新记录
     */
    boolean checkAndAddBudgetAppRel(String scode, String vendor, String budgetCode, String billingCycle);

    /**
     * 根据id查询关联的账期
     * @param id
     * @return
     */
    List<String> getBillingCycles(Integer id);
}
