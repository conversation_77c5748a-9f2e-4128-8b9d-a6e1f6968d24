package com.haier.devops.bill.common.config;

import com.google.gson.*;
import com.haier.devops.bill.common.api.HdsOpenApi;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 配置okhttp以及对应的链接池
 *
 * <AUTHOR>
 */
@Configuration
public class HdsOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private static final String AUTHORIZATION_KEY = "Authorization";
    private static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(Date.class, new CustomDateDeserializer())
            .create();
    private okhttp3.OkHttpClient hdsOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    private HdsProjectConfigProperties hdsProjectConfigProperties;

    public HdsOkHttpFeignConfiguration(OkHttpProperties properties,
                                       OkHttpClientFactory httpClientFactory,
                                       OkHttpClientConnectionPoolFactory connectionPoolFactory,
                                       HdsProjectConfigProperties hdsProjectConfigProperties) {
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
        this.hdsProjectConfigProperties = hdsProjectConfigProperties;
    }

    @Bean
    public HdsOpenApi hdsOpenApi() {
        this.hdsOkHttp3Client =
                setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);

        this.hdsOkHttp3Client = hdsOkHttp3Client.newBuilder()
                .addInterceptor(authenticationInterceptor(AUTHORIZATION_KEY, hdsProjectConfigProperties.getToken()))
                .build();

        return Feign.builder()
                .client(new OkHttpClient(hdsOkHttp3Client))
                .encoder(new GsonEncoder(gson))
                .decoder(new GsonDecoder(gson))
                .target(HdsOpenApi.class, hdsProjectConfigProperties.getUrl());
    }

    @PreDestroy
    public void destroy() {
        super.destroy(this.hdsOkHttp3Client);
    }


    /**
     * <AUTHOR>
     */
    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "api.hds")
    public static class HdsProjectConfigProperties {
        private String token;
        private String url;
    }
}

class CustomDateDeserializer implements JsonDeserializer<Date> {
    private static final String INVALID_YEAR_PREFIX = "0000";
    private static final String VALID_YEAR_PREFIX = "0001";
    private static final String DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ssXXX"; // ISO 8601 with timezone

    @Override
    public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        String dateStr = json.getAsString();

        // Replace invalid year prefix
        if (dateStr.startsWith(INVALID_YEAR_PREFIX)) {
            dateStr = VALID_YEAR_PREFIX + dateStr.substring(4);
        }

        try {
            SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
            formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
            return formatter.parse(dateStr);
        } catch (ParseException e) {
            throw new JsonParseException("Failed to parse date: " + dateStr, e);
        }
    }
}
