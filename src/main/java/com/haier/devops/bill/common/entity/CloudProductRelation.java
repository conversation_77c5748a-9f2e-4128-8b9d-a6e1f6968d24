package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* @ClassName: CloudProductRelation
* @Description: 阿里云产品关系
* @author: 张爱苹
* @date: 2024/3/12 13:37
*/
@Data
@TableName("cloud_product_relation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="阿里云产品关系")
public class CloudProductRelation implements Serializable {

    /**主键*/
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private Integer id;

    /**产品编码*/
    private String productCode;

    /**产品明细编码*/
    private String commodityCode;
}
