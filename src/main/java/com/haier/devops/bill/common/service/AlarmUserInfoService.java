package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;

import java.util.List;

/**
* @ClassName: AlarmUserInfoDetailService
* @Description:  告警用户信息表
* @author: 张爱苹
* @date: 2024/1/17 10:30
*/
public interface AlarmUserInfoService extends IService<AlarmUserInfo> {

    /**
    * @Description: 根据账号获取告警用户信息
    * @author: 张爱苹
    * @date: 2024/1/17 10:41
    * @param account:
    * @Return: com.haier.devops.bill.common.entity.AlarmUserInfo
    */
    AlarmUserInfo getAlarmUserInfoByAccount(String account);

    /**
    * @Description: 根据账号获取告警用户信息列表
    * @author: 张爱苹
    * @date: 2024/1/17 13:01
    * @param noticeObjIdArray:
    * @Return: java.util.List<com.haier.devops.bill.common.dto.AlarmNoticeObjDTO>
    */
    List<AlarmNoticeObjVo> getUserListByUserIdArray(String[] noticeObjIdArray);

    /**
    * @Description: 根据组id获取告警用户信息列表
    * @author: 张爱苹
    * @date: 2024/1/17 15:00
    * @param noticeObjIdArray:
    * @Return: java.util.List<com.haier.devops.bill.common.dto.AlarmNoticeObjDTO>
    */
    List<AlarmNoticeObjVo> getUserListByGroupId(String[] noticeObjIdArray);
}
