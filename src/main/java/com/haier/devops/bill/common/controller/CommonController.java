package com.haier.devops.bill.common.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.bookmark.service.BookmarkServiceImpl;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkBillApi;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.entity.RawBillProducts;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.redis.Queue;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.DomainSubProductVo;
import com.haier.devops.bill.common.vo.SysDictItemVo;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import com.haier.devops.bill.export.vo.OmnibearingExportApplicationVo;
import com.haier.devops.bill.privelege.DomainPrivilegeFinder;
import com.haier.devops.bill.privelege.OpenPlatformDomainProvider;
import com.haier.devops.bill.util.AuthUtil;
import com.haier.devops.bill.util.TimeRangeParser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.controller.ResponseEnum.*;

/**
 * 账单下载
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/hcms/bill")
@Slf4j
public class CommonController {
    private ExportLogService exportLogService;
    private Queue<OmnibearingExportApplicationVo> omnibearingExportQueue;
    private Queue<DetailExportApplicationVo> detailExportQueue;
    private AuthorityService authorityService;

    private SysDictItemService sysDictItemService;

    private HdsSubProductsService hdsSubProductsService;

    private RawBillProductsService rawBillProductsService;

    private final DomainPrivilegeFinder domainPrivilegeFinder;

    private AggregatedBillService aggregatedBillService;

    @Autowired
    private BookmarkServiceImpl bookmarkServiceImpl;

    private final OpenPlatformDomainProvider openPlatformDomainProvider;

    public CommonController(@Qualifier("omnibearingExportQueue")
                            Queue<OmnibearingExportApplicationVo> omnibearingExportQueue,
                            @Qualifier("detailExportQueue")
                            Queue<DetailExportApplicationVo> detailExportQueue,
                            ExportLogService exportLogService,
                            AuthorityService authorityService,
                            SysDictItemService sysDictItemService,
                            HdsSubProductsService hdsSubProductsService,
                            RawBillProductsService rawBillProductsService,
                            DomainPrivilegeFinder domainPrivilegeFinder,
                            OpenPlatformDomainProvider openPlatformDomainProvider,
                            AggregatedBillService aggregatedBillService) {
        this.omnibearingExportQueue = omnibearingExportQueue;
        this.detailExportQueue = detailExportQueue;
        this.exportLogService = exportLogService;
        this.authorityService = authorityService;
        this.domainPrivilegeFinder = domainPrivilegeFinder;
        this.sysDictItemService = sysDictItemService;
        this.hdsSubProductsService = hdsSubProductsService;
        this.rawBillProductsService = rawBillProductsService;
        this.openPlatformDomainProvider = openPlatformDomainProvider;
        this.aggregatedBillService = aggregatedBillService;
    }

    /**
     * 下载账单汇总
     *
     * @param request
     * @return
     */
    @PostMapping("/download/general")
    public ResponseEntityWrapper<ExportLog> download(@RequestBody @Valid RequestParameter request) {
        Date submitTime = new Date();
        List<String> scodes = request.getScodes();
        String serialNo = UUID.randomUUID().toString();
        omnibearingExportQueue.add(OmnibearingExportApplicationVo.builder()
                .scodes(scodes)
                .vendor(request.getVendor())
                .accountName(request.getAccountName())
                .startCycle(request.getStartCycle())
                .endCycle(request.getEndCycle())
                .submitTime(submitTime)
                .submitter(request.getSubmitter())
                .serialNo(serialNo)
                .build());
        ExportLog exportLog = ExportLog.builder()
                .serialNo(serialNo)
                .submitTime(submitTime)
                .submitter(request.getSubmitter())
                .startCycle(request.getStartCycle())
                .endCycle(request.getEndCycle())
                .build();
        exportLogService.save(exportLog);

        return new ResponseEntityWrapper<>(exportLog);
    }

    /**
     * 下载账单明细
     *
     * @param request
     * @return
     */
    @PostMapping("/download/detail")
    public ResponseEntityWrapper<ExportLog> download(@RequestBody @Valid RequestParamWrapper request) {
        if (StringUtils.isBlank(request.getSubmitter())) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "提交人不能为空", null);
        }

        Date submitTime = new Date();
        String serialNo = UUID.randomUUID().toString();
        detailExportQueue.add(DetailExportApplicationVo.builder()
                .scode(request.getScode())
                .params(request.getParams())
                .startCycle(request.getStartCycle())
                .endCycle(request.getEndCycle())
                .submitTime(submitTime)
                .submitter(request.getSubmitter())
                .serialNo(serialNo)
                .build());
        ExportLog exportLog = ExportLog.builder()
                .serialNo(serialNo)
                .submitTime(submitTime)
                .submitter(request.getSubmitter())
                .startCycle(request.getStartCycle())
                .endCycle(request.getEndCycle())
                .build();
        exportLogService.save(exportLog);

        return new ResponseEntityWrapper<>(exportLog);
    }

    /**
     * 下载列表
     *
     * @param page
     * @param per_page
     * @paramsubmitter
     * @param serialNo
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/download/history")
    public ResponseEntityWrapper<PageInfo<ExportLog>> list(@Min(1) @RequestParam Integer page,
                                                           @Min(1) @RequestParam Integer per_page,
                                                           String serialNo,
                                                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        LambdaQueryWrapper<ExportLog> queryWrapper = new LambdaQueryWrapper<>();
        User currentUser = LoginContextHolder.getCurrentUser();
        queryWrapper.eq(ExportLog::getSubmitter, currentUser.getUserCode());
        if (StringUtils.isNotBlank(serialNo)) {
            queryWrapper.eq(ExportLog::getSerialNo, serialNo);
        }
        if (null != startTime) {
            queryWrapper.ge(ExportLog::getUpdateTime, startTime);
        }
        if (null != endTime) {
            queryWrapper.le(ExportLog::getUpdateTime, endTime);
        }
        queryWrapper.orderBy(true, false, ExportLog::getSubmitTime);

        return new ResponseEntityWrapper<>(
                PageHelper.startPage(page, per_page).doSelectPageInfo(() -> exportLogService.list(queryWrapper)));
    }

    @PostMapping("/getUserSCodes")
    public ResponseEntityWrapper<List<HdsSubProducts>> getUserSCodes(@RequestBody RequestParameter request){
        List<HdsSubProducts> authorizedSubProducts =
                authorityService.getAuthedSubProducts(request.getDepartIds());
        return new ResponseEntityWrapper<>(authorizedSubProducts);
    }


    @PostMapping("/getItUserAuth")
    public ResponseEntityWrapper<HworkBillApi.AuthAppForItResponse> getItUserAuth(@RequestBody HworkBillApi.AuthAppForItRequest request) {
        return new ResponseEntityWrapper<>(authorityService.getAuthAppForIt(request));

    }

    @PostMapping("/getItUserAuthWithBookmark")
    public ResponseEntityWrapper<HworkBillApi.AuthAppForItResponse> getItUserAuthWithBookmark(@RequestBody HworkBillApi.AuthAppForItRequest request) {
        // 从LoginContextHolder获取当前用户
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }

        try {
            // 使用当前登录用户的userCode，忽略请求中的userCode
            String userCode = currentUser.getUserCode();
            AuthAppForItResponse response = bookmarkServiceImpl.getAuthAppWithBookmark(userCode);
            return new ResponseEntityWrapper<>(response);
        } catch (Exception e) {
            log.error("获取用户授权数据失败: {}", e.getMessage(), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    @GetMapping("/getDomainSubProducts")
    public ResponseEntityWrapper<List<DomainSubProductVo>> getDomainSubProducts() {
        List<DomainSubProductVo> domainPrivileges = new ArrayList<>();
        User currentUser = LoginContextHolder.getCurrentUser();
        if (null == currentUser) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "未获取到登陆信息", domainPrivileges);
        }

        try {
            domainPrivileges = domainPrivilegeFinder.findDomainPrivileges(currentUser.getUserCode(), AuthUtil.isAdmin(currentUser));
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), domainPrivileges);
        }

        return new ResponseEntityWrapper<>(domainPrivileges);
    }

    @PostMapping("/v3/getAgentAppByDesc")
    public ResponseEntityWrapper<Map> getAgentAppByDesc(@RequestParam String text,@RequestParam String type,@RequestBody NodeParam nodeParam) {
        Map map = new HashMap();
        //查有权限的S码
        User currentUser = LoginContextHolder.getCurrentUser();
        String userCode = currentUser.getUserCode();
        boolean isAdmin = AuthUtil.isAdmin(currentUser);
        List<HdsSubProducts> hdsSubProducts = new ArrayList<>();
        String scodes = aggregatedBillService.getDomainSubProducts(null,isAdmin,userCode);
        if("001".equals(type)){
            //模糊匹配
            //是否是子产品
            hdsSubProducts = hdsSubProductsService.getHdsSubProductList(text);
        }else{
            //精确匹配
            hdsSubProducts = hdsSubProductsService.getAgentHdsSubProductList(text);
        }
        List<Map<String,String>> hasPermAppList = new ArrayList<>();

        map.put("scodePerm","");
        if(!CollectionUtils.isEmpty(hdsSubProducts)){
            List<HdsSubProducts> hdsSubProductList = new ArrayList<>();
            //如果有权限的领域不为空，过滤领域
            if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                List<String> hasPermDomainIdList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                hdsSubProductList = hdsSubProducts.stream().filter(item -> hasPermDomainIdList.contains(item.getBusinessDomainIds())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(hdsSubProductList)){
                    String domains = nodeParam.getDomainList().stream().map(item -> item.get("name")).collect(Collectors.joining("、"));
                    map.put("scodePerm",new StringBuffer("您在").append(domains).append("领域没有这个【").append(text).append("】子产品的权限"));
                    map.put("app",hasPermAppList);
                    return new ResponseEntityWrapper<>(map);
                }
            }else{
                //过滤有权限的S码
                hdsSubProductList = hdsSubProducts.stream().filter(item -> scodes.contains(item.getAppScode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(hdsSubProductList)){
                    map.put("scodePerm",new StringBuffer("您没有这个【").append(text).append("】子产品的权限"));
                    map.put("app",hasPermAppList);
                    return new ResponseEntityWrapper<>(map);
                }
            }
            List<String> appNameList = new ArrayList<>();
            hdsSubProductList.stream().forEach(item -> {
                if(!appNameList.contains(item.getAppName())){
                    Map<String,String> appMap = new HashMap<>();
                    appMap.put("code",item.getAppScode());
                    appMap.put("name",item.getAppName());
                    hasPermAppList.add(appMap);
                    appNameList.add(item.getAppName());
                }
            });
        }else{
            map.put("scodePerm",new StringBuffer("【").append(text).append("】这个子产品不存在"));
        }
        map.put("app",hasPermAppList);
        return new ResponseEntityWrapper<>(map);
    }

    @GetMapping("/v3/getAgentDomainList")
    public ResponseEntityWrapper<Map> getAgentDomainList() {
        Map map = new HashMap();
        //查询所有领域
        List<HdsOpenApi.Domain> sourceDomainList = domainPrivilegeFinder.queryDomainList();
        map.put("domainNameList",sourceDomainList.stream().map(item -> item.getName()).collect(Collectors.joining(",")));
        map.put("domainCodeList",sourceDomainList.stream().map(item -> item.getDomainCode()).collect(Collectors.joining(",")));
        return new ResponseEntityWrapper<>(map);
    }


    /**
    * @Description: 领域查询
    * @author: 张爱苹
    * @date: 2025/4/15 18:52
    * @param text:
    * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.Map>
    */
    @GetMapping("/v3/getAgentDomainByDesc")
    public ResponseEntityWrapper<Map> getAgentDomainByDesc(@RequestParam String text,@RequestParam String type) {
        //获取用户有权限的领域范围
        User currentUser = LoginContextHolder.getCurrentUser();
        String userCode = currentUser.getUserCode();
        List<String> domains = getDomainInfo(userCode,AuthUtil.isAdmin(currentUser));
        Map map = new HashMap();
        //查询所有领域
        List<HdsOpenApi.Domain> sourceDomainList = domainPrivilegeFinder.queryDomainList();
        List<Map<String,String>> domainList = new ArrayList<>();
        if("001".equals(type)){
            //模糊匹配
            domainList = sourceDomainList.stream().filter(item -> item.getCode().contains(text) ||(StringUtils.isNotEmpty(item.getName())&&item.getName().contains(text))).map(item -> {
                Map<String,String> domainMap = new HashMap<>();
                domainMap.put("code",item.getCode());
                domainMap.put("name",item.getName());
                return domainMap;
            }).collect(Collectors.toList());
        }else{
            //精确匹配
            domainList = sourceDomainList.stream().filter(item -> item.getCode().equals(text) ||(StringUtils.isNotEmpty(item.getName())&&item.getName().equals(text))).map(item -> {
                Map<String,String> domainMap = new HashMap<>();
                domainMap.put("code",item.getCode());
                domainMap.put("name",item.getName());
                return domainMap;
            }).collect(Collectors.toList());
        }
        map.put("domainPerm","");
        List<Map<String,String>> hasPermDomainList = new ArrayList<>();
        List<String> hasPermDomainIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(domainList)){
            domainList.stream().forEach(item -> {
                if(domains.contains(item.get("code"))){
                    hasPermDomainList.add(item);
                    hasPermDomainIdList.add(item.get("code"));
                }
            });
            if(CollectionUtils.isEmpty(hasPermDomainList)){
                //没有当前领域权限
                map.put("domainPerm",new StringBuffer("您没有这个【").append(text).append("】领域的权限"));
            }
        }else{
            //当前领域不存在
            map.put("domainPerm",new StringBuffer("【").append(text).append("】这个领域不存在"));
        }
        map.put("domain",hasPermDomainList);
        return new ResponseEntityWrapper<>(map);
    }

    @PostMapping("/v3/getAgentParams")
    public ResponseEntityWrapper<Map> getAgentParamsV3(@RequestBody Map xxMap,@RequestParam String startDate,@RequestParam String endDate) {
        User currentUser = LoginContextHolder.getCurrentUser();
        String userCode = currentUser.getUserCode();
        boolean isAdmin = AuthUtil.isAdmin(currentUser);
        String scodes = aggregatedBillService.getDomainSubProducts(null,isAdmin,userCode);
        //如果有权限的s码是空的话，直接返回无权限
        if(StringUtils.isEmpty(scodes)){
            Map noPerm = new HashMap<>();
            noPerm.put("scodePerm", "noPermAll");
            return new ResponseEntityWrapper<>(noPerm);
        }
        //转换params为json
        LinkedHashMap<String, Object> paramMap = (LinkedHashMap<String, Object>) xxMap.get("params");
        if(paramMap == null){
            return new ResponseEntityWrapper<>(REQUEST_PARAM_ERR, "参数错误", null);
        }
        //维度
        String dimension = (String) paramMap.get("dimension");
        //云厂商
        String vendor = (String) paramMap.get("vendor");
        //子产品
        String app = (String) paramMap.get("app");
        //云产品
        String product = (String) paramMap.get("product");
        //领域
        String domain = (String) paramMap.get("domain");

        //咨询分类
        String type = (String) paramMap.get("type");

        //币种
        String currency = (String) paramMap.get("currency");
        if("人民币".equals(currency)){
            currency = "CNY";
        }else if("美元".equals(currency)){
            currency = "USD";
        }else{
            currency = "ALL";
        }
        //处理是时间
        Integer time = 6;
        if("02".equals(type)){
            time = 2;
        }else{
            if(!"".equals(paramMap.get("time"))){
                time = Integer.parseInt(paramMap.get("time").toString());
                if(time == 0 || time > 12){
                    time = 6;
                }
            }
        }
        //获取用户有权限的领域范围
        List<String> domains = getDomainInfo(userCode,isAdmin);

        //根据time生成开始时间和结束时间
        Map map = TimeRangeParser.parseTimeRange(time,startDate,endDate);
        map.put("type",type);
        //默认值设置
        //云厂商
        map.put("vendor","");
        map.put("vendorPerm","");
        //云产品
        map.put("product",new ArrayList<>());
        map.put("productPerm","");
        //子产品
        map.put("app",new ArrayList<>());
        //赋值scode，默认有子产品权限
        map.put("scodePerm","hasPerm");
        //赋值domainPerm
        map.put("domainPerm","");

        //维度，默认子产品
        map.put("dimension","subProduct");
        //领域
        map.put("domain",new ArrayList<>());

        //云厂商逻辑处理
        if(StringUtils.isNotEmpty(vendor)){
            List<SysDictItemVo> vendorList = sysDictItemService.getDictItemList("CLOUD_VENDOR");
            String finalVendor = vendor;
            List<SysDictItemVo> filter = vendorList.stream().filter(sysDictItemVo -> sysDictItemVo.getItemValue().equals(finalVendor)||sysDictItemVo.getItemText().equals(finalVendor)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(filter)){
                map.put("vendor",filter.get(0).getItemValue());
                vendor = filter.get(0).getItemValue();
            }else{
                map.put("vendorPerm",new StringBuffer("【").append(vendor).append("】这个云厂商不存在"));
            }
        }

        //查询所有领域
        List<HdsOpenApi.Domain> sourceDomainList = domainPrivilegeFinder.queryDomainList();

        List<Map<String,String>> hasPermDomainList = new ArrayList<>();
        List<String> hasPermDomainIdList = new ArrayList<>();
        List<Map<String,String>> hasPermAppList = new ArrayList<>();
        List<Map<String,String>> hasPermProductList = new ArrayList<>();
        map.put("appFlag",false);
        map.put("domainFlag",false);
        map.put("ifAppQJ",true);
        map.put("ifDomainQJ",true);
        if(StringUtils.isNotEmpty(domain)){
            app = getDomainPerm(app, domain, domains, map, sourceDomainList, hasPermDomainList, hasPermDomainIdList);
            if(StringUtils.isNotEmpty(app)){
                getAppPerm(scodes, app, domain,map, hasPermDomainIdList, hasPermAppList);
            }
        }
        boolean ifAppQJ = (boolean) map.get("ifAppQJ");
        if(StringUtils.isNotEmpty(app) && ifAppQJ){
            domain = getAppPerm(scodes, app, domain,map, hasPermDomainIdList, hasPermAppList);
            boolean ifDomainQJ = (boolean) map.get("ifDomainQJ");
            if(StringUtils.isNotEmpty(domain)&& ifDomainQJ){
                getDomainPerm(app, domain, domains, map, sourceDomainList, hasPermDomainList, hasPermDomainIdList);
            }
        }
        if(StringUtils.isNotEmpty(product)){
            //是否是云产品
            List<RawBillProducts> rawBillProducts = rawBillProductsService.getProductsByProduct(product,vendor);
            List<String> productNameList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(rawBillProducts)){
                rawBillProducts.stream().forEach(item -> {
                    if(!productNameList.contains(item.getProductName())){
                        Map<String,String> productMap = new HashMap<>();
                        productMap.put("code",item.getProductCode());
                        productMap.put("name",item.getProductName());
                        hasPermProductList.add(productMap);
                        productNameList.add(item.getProductName());
                    }
                });
            }else{
                map.put("productPerm",new StringBuffer("【").append(product).append("】这个云产品不存在"));
            }
        }
        if(!CollectionUtils.isEmpty(hasPermDomainList) && hasPermDomainList.size() > 1){
            String finalDomain = domain;
            List<Map<String,String>> newHasPermDomainList = hasPermDomainList.stream().filter(item -> item.get("name").equals(finalDomain)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(newHasPermDomainList) && newHasPermDomainList.size() == 1){
                hasPermDomainList = newHasPermDomainList;
            }

        }
        map.put("domain",hasPermDomainList);
        if(!CollectionUtils.isEmpty(hasPermAppList) && hasPermAppList.size() > 1){
            String finalApp = app;
            List<Map<String,String>> newHasPermAppList = hasPermAppList.stream().filter(item -> item.get("name").equals(finalApp)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(newHasPermAppList) && newHasPermAppList.size() == 1){
                hasPermAppList = newHasPermAppList;
            }
        }
        map.put("app",hasPermAppList);
        map.put("product",hasPermProductList);
        if(StringUtils.isNotEmpty(vendor) || hasPermAppList.size() > 0){
            map.put("dimension","productType");
        }else{
            if(StringUtils.isNotEmpty(dimension)){
                switch (dimension){
                    case "云厂商":
                        map.put("dimension","vendor");
                        break;
                    case "云产品":
                        map.put("dimension","productType");
                        break;
                    case "子产品":
                        map.put("dimension","subProduct");
                        break;
                    case "应用":
                        map.put("dimension","subProduct");
                        break;
                    default:
                        map.put("dimension","vendor");
                        break;
                }
            }
        }
        map.put("dimensionName",paramMap.get("dimension"));
        map.put("currency",currency);
        return new ResponseEntityWrapper<>(map);
    }

    private String getAppPerm(String scodes, String app, String domain,Map map, List<String> hasPermDomainIdList, List<Map<String, String>> hasPermAppList) {
        //是否是子产品
        List<HdsSubProducts> hdsSubProducts = hdsSubProductsService.getHdsSubProductList(app);
        if(!CollectionUtils.isEmpty(hdsSubProducts)){
            List<HdsSubProducts> hdsSubProductList = new ArrayList<>();
            //如果有权限的领域不为空，过滤领域
            if(!CollectionUtils.isEmpty(hasPermDomainIdList)){
                hdsSubProductList = hdsSubProducts.stream().filter(item -> hasPermDomainIdList.contains(item.getBusinessDomainIds())).collect(Collectors.toList());
            }else{
                //过滤有权限的S码
                hdsSubProductList = hdsSubProducts.stream().filter(item -> scodes.contains(item.getAppScode())).collect(Collectors.toList());
            }
            if(!CollectionUtils.isEmpty(hdsSubProductList)){
                List<String> appNameList = new ArrayList<>();
                hdsSubProductList.stream().forEach(item -> {
                    if(!appNameList.contains(item.getAppName())){
                        Map<String,String> appMap = new HashMap<>();
                        appMap.put("code",item.getAppScode());
                        appMap.put("name",item.getAppName());
                        hasPermAppList.add(appMap);
                        appNameList.add(item.getAppName());
                    }
                });
            }else{
                map.put("scodePerm",new StringBuffer("您没有这个【").append(app).append("】子产品的权限"));
            }

        }else{
            map.put("scodePerm",new StringBuffer("【").append(app).append("】这个子产品不存在"));
            boolean domainFlag = (boolean) map.get("domainFlag");
            if(domainFlag){
                //无需判断领域
                map.put("ifDomainQJ",false);
            }else{
                if(StringUtils.isEmpty(domain)){
                    domain = app;
                    map.put("appFlag",true);
                    map.put("scodePerm","");
                }
            }
        }
        map.put("ifAppQJ",false);
        return domain;
    }

    private String getDomainPerm(String app, String domain, List<String> domains, Map map, List<HdsOpenApi.Domain> sourceDomainList, List<Map<String, String>> hasPermDomainList, List<String> hasPermDomainIdList) {
        //如果有权限的领域不为空的话，判断是否是领域
        if(!CollectionUtils.isEmpty(domains)){
            String finalDomain = domain;
            List<Map<String,String>> domainList = sourceDomainList.stream().filter(item -> item.getCode().contains(finalDomain) ||(StringUtils.isNotEmpty(item.getName())&&item.getName().contains(finalDomain))).map(item -> {
                Map<String,String> domainMap = new HashMap<>();
                domainMap.put("code",item.getCode());
                domainMap.put("name",item.getName());
                return domainMap;
            }).collect(Collectors.toList());

            if(!CollectionUtils.isEmpty(domainList)){
                domainList.stream().forEach(item -> {
                    if(domains.contains(item.get("code"))){
                        hasPermDomainList.add(item);
                        hasPermDomainIdList.add(item.get("code"));
                    }
                });
                if(CollectionUtils.isEmpty(hasPermDomainList)){
                    //没有当前领域权限
                    map.put("domainPerm",new StringBuffer("您没有这个【").append(domain).append("】领域的权限"));
                }
            }else{
                //当前领域不存在
                map.put("domainPerm",new StringBuffer("【").append(domain).append("】这个领域不存在"));
                boolean appFlag = (boolean) map.get("appFlag");
                if(appFlag){
                    //无需继续判断子产品
                    map.put("ifAppQJ",false);
                }else{
                    if(StringUtils.isEmpty(app)){
                        app = domain;
                        map.put("domainFlag",true);
                        map.put("domainPerm","");
                    }
                }
            }
        }else{
            //没有任何领域权限
            map.put("domainPerm",new StringBuffer("您没有这个【").append(domain).append("】领域的权限"));
        }
        map.put("ifDomainQJ",false);
        return app;
    }

    @org.jetbrains.annotations.NotNull
    private List<String> getDomainInfo(String userCode,boolean isAdmin) {
        List<String> domains = new ArrayList<>();
        try {
            if(isAdmin){
                List<HdsOpenApi.Domain> sourceDomainList = domainPrivilegeFinder.queryDomainList();
                domains = sourceDomainList.stream().map(HdsOpenApi.Domain::getCode).collect(Collectors.toList());
            }else{
                domains = domainPrivilegeFinder.getDomainList(userCode,"domainAdmin");
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return domains;
    }
}

@Data
class RequestParameter {
    private List<String> scodes;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 云厂商
     */
    @NotNull(message = "请指定云厂商")
    private String vendor;

    /**
     * 开始周期
     */
    @NotNull(message = "开始周期不能为空")
    private String startCycle;

    /**
     * 结束周期
     */
    @NotNull(message = "结束周期不能为空")
    private String endCycle;

    /**
     * 提交人
     */
    @NotNull(message = "提交人不能为空")
    private String submitter;

    /**
     * 部门id
     */
    private String departIds;
}
