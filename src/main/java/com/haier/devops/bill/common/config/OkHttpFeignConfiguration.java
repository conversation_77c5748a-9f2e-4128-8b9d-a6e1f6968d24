package com.haier.devops.bill.common.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.entity.ContentType;
import org.apache.http.protocol.HTTP;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;

import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class OkHttpFeignConfiguration {
    public Interceptor authenticationInterceptor(String key, String token) {
        return chain -> {
            Request request = chain.request();

            log.info("Hey there, this is my request: {}", request);
            long start = System.currentTimeMillis();
            Request requestWithUserAgent = request.newBuilder()
                    .header(key, token)
                    .build();
            Response response = chain.proceed(requestWithUserAgent);

            long end = System.currentTimeMillis();
            log.info("Hey there, this is my response: {}, costs: {} milliseconds", response, end - start);

            return response;
        };
    }

    public Interceptor contentTypeInterceptor(ContentType contentType) {
        return chain -> {
            Request request = chain.request();

            Request requestWithUserAgent = request.newBuilder()
                    .header(HTTP.CONTENT_TYPE, contentType.toString())
                    .build();
            return chain.proceed(requestWithUserAgent);
        };
    }

    @NotNull
    public okhttp3.OkHttpClient setConnectionProperties(OkHttpProperties properties,
                                                 OkHttpClientFactory httpClientFactory,
                                                 OkHttpClientConnectionPoolFactory connectionPoolFactory) {
        return httpClientFactory.createBuilder(properties.isDisableSslValidation())
                // 链接超时时间
                .connectTimeout(properties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                // 是否禁用重定向
                .followRedirects(properties.isFollowRedirects())
                //设置读超时
                .readTimeout(properties.getReadTimeout(), TimeUnit.MILLISECONDS)
                //设置写超时
                .writeTimeout(properties.getWriteTimeout(), TimeUnit.MILLISECONDS)
                // 链接失败是否重试
                .retryOnConnectionFailure(properties.isRetryOnConnectionFailure())
                //链接池
                .connectionPool(connectionPoolFactory.create(properties.getMaxIdleConnections(),
                        properties.getKeepAliveDuration(),
                        TimeUnit.MILLISECONDS))
                .build();
    }

    public void destroy(OkHttpClient client) {
        client.dispatcher().executorService().shutdown();
        client.connectionPool().evictAll();
    }
}
