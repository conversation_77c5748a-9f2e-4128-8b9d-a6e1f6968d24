/*
 * Haier.com Inc
 * Copyright (c) All Rights Reserved
 */

package com.haier.devops.bill.common.constant;

/**
 * <AUTHOR>
 * @Description 项目通用常量
 * @date 2022/4/24 18:07
 */
public class CommonConstant {

    /**
     * 删除标识 0：未删除，有效
     */
    public static final String DEL_FLAG_FALSE = "0";

    /**
     * 删除标识 1：已删除，无效
     */
    public static final String DEL_FLAG_TRUE = "1";

    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd";

    public static final String DATE_TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final long ONE_DAY_OF_MILLI_SECONDS = 24 * 60 * 60 * 1000L;

    public static final String LOG_STATUS_FINISHED = "finished";
    public static final String LOG_STATUS_ERROR = "error";
    public static final String LOG_STATUS_START = "start";

    public static final String LOG_STAGE_REFINEMENT = "refinement";
    public static final String LOG_STAGE_AGGREGATION = "aggregation";
    public static final String LOG_STAGE_AGGREGATION_IN_MONTH = "aggregation_in_month";

    /**
     * 初始化
     */
    public static final Integer TASK_STATUS_INITIALIZE = 0;
    /**
     * 正式数据
     */
    public static final Integer TASK_STATUS_FORMAL_DATA = 1;
    /**
     * 备份数据
     */
    public static final Integer TASK_STATUS_BACK_UP_DATA = 2;
    /**
     * 失败数据
     */
    public static final Integer TASK_STATUS_FAILURE_DATA = 3;
    /**
     * 历史数据
     */
    public static final Integer TASK_STATUS_HISTORICAL_DATA = 4;

    /**
     * hwork验收初始化状态
     */
    public static final Integer HWORK_SYNCING_STATUS_INITIALIZE = 0;
    /**
     * hwork验收已推送
     */
    public static final Integer HWORK_SYNCING_STATUS_SYNCED = 1;
    /**
     * hwork验收已确认
     */
    public static final Integer HWORK_SYNCING_STATUS_ACCEPTANCE_DONE = 2;
}
