package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AggregateReconciliationDTO;
import com.haier.devops.bill.common.dto.CmdbProductOverviewDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.RuleFieldValueDTO;
import com.haier.devops.bill.common.entity.CloudRuleField;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ReconciliationTask;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.CloudRuleFieldService;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import com.haier.devops.bill.common.service.ResourceInstanceService;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;
import com.haier.devops.bill.common.vo.AggregatedBillInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* @ClassName: CmdbProductOverviewController
* @Description:  cmdb总表 控制器
* @author: 张爱苹
* @date: 2024/1/31 10:24
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/cmdb_product_overview")
public class CmdbProductOverviewController {

	@Autowired
	private CmdbProductOverviewService cmdbProductOverviewService;

	@Autowired
	private AggregatedBillService aggregatedBillService;

	@Autowired
	private CloudRuleFieldService cloudRuleFieldService;

	@Autowired
	private ResourceInstanceService resourceInstanceService;


	/**
	* @Description: 分页查询调账列表
	* @author: 张爱苹
	* @date: 2024/3/8 16:54
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CmdbProductOverview>>
	*/
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<CmdbProductOverview>> listByPage(CmdbProductOverviewDTO dto) {
        try {
			PageInfo<CmdbProductOverview> pageInfo = cmdbProductOverviewService.listByPage(dto);
			return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
	}

	/**
	* @Description: 根据汇总id查询汇总账单信息
	* @author: 张爱苹
	* @date: 2024/3/8 16:57
	* @param aggregatedId:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.haier.devops.bill.common.vo.AggregatedBillInfoVo>
	*/
	@GetMapping("/getInfoByAggregatedId")
	public ResponseEntityWrapper<List<AggregatedBillInfoVo>> getInfoByAggregatedId(@RequestParam("aggregatedId") String aggregatedId) {
		try {
			return new ResponseEntityWrapper<>(aggregatedBillService.getInfoByAggregatedId(aggregatedId));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 按单个实例调账
	* @author: 张爱苹
	* @date: 2024/3/11 09:50
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@PostMapping("/reconciliation")
	public ResponseEntityWrapper reconciliation(@Validated @RequestBody AggregateReconciliationDTO dto) {
		try {
			dto.setReconciliationType("instance");
			aggregatedBillService.reconciliation(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * @Description: 按多个实例批量调账
	 * @author: 张爱苹
	 * @date: 2024/3/11 09:50
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	 */
	@PostMapping("/reconciliationBatch")
	public ResponseEntityWrapper reconciliationBatch(@Validated @RequestBody ReconciliationBatchDTO dto) {
		try {
			dto.setReconciliationType("instance");
			aggregatedBillService.reconciliationBatch(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * @Description: 全局调账
	 * @author: 张爱苹
	 * @date: 2024/3/11 09:50
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	 */
	@PostMapping("/reconciliationGlobal")
	public ResponseEntityWrapper reconciliationGlobal(@Validated @RequestBody ReconciliationBatchDTO dto) {
		try {
			dto.setReconciliationType("instance");
			aggregatedBillService.reconciliationGlobal(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 商品名称
	* @author: 张爱苹
	* @date: 2024/3/12 13:33
	* @param ruleFieldValueDTO:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<java.lang.String>>
	*/
	@GetMapping("/getCommodityCodeList")
	public ResponseEntityWrapper<List<String>> getCommodityCodeList(@RequestParam String accountName) {
		try {
			return new ResponseEntityWrapper<>(resourceInstanceService.getCommodityCodeList(accountName));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 规则字段
	* @author: 张爱苹
	* @date: 2024/3/12 11:00
	* @param ruleType:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<com.haier.devops.bill.common.entity.CloudRuleField>>
	*/
	@GetMapping("/getRuleFieldList")
	public ResponseEntityWrapper<List<CloudRuleField>> getRuleFieldList(@RequestParam("ruleType") String ruleType) {
		try {
			return new ResponseEntityWrapper<>(cloudRuleFieldService.getRuleFieldList(ruleType));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 规则值
	* @author: 张爱苹
	* @date: 2024/3/12 14:31
	* @param ruleFieldValueDTO:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<java.lang.String>>
	*/
	@GetMapping("/getRuleFieldValueList")
	public ResponseEntityWrapper<List<String>> getRuleFieldValueList(@Validated RuleFieldValueDTO ruleFieldValueDTO) {
		try {
			return new ResponseEntityWrapper<>(cloudRuleFieldService.getRuleFieldValueList(ruleFieldValueDTO));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 修改记录查询
	* @author: 张爱苹
	* @date: 2024/3/14 10:57
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<java.lang.String>>
	*/
	@GetMapping("/getAdjustmentRecordList")
	public ResponseEntityWrapper<List<AdjustmentRecordVo>> getAdjustmentRecordList(CmdbProductOverviewDTO dto) {
		try {
			return new ResponseEntityWrapper<>(cmdbProductOverviewService.getAdjustmentRecordList(dto));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	@GetMapping("/getReconciliationTaskList")
	public ResponseEntityWrapper<List<ReconciliationTask>> getReconciliationTaskList(@RequestParam String adjustmentId) {
		try {
			return new ResponseEntityWrapper<>(cmdbProductOverviewService.getReconcliationTaskList(adjustmentId));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}


	/**
	* @ClassName: CmdbProductOverviewController
	* @Description: 分拆项查询
	* @author: 张爱苹
	* @date: 2024/3/14 13:16
	*/
	@GetMapping("/getSupplementIdList")
	public ResponseEntityWrapper<List<String>> getSupplementIdList(@RequestParam String vendor,@RequestParam String productCode) {
		try {
			return new ResponseEntityWrapper<>(cmdbProductOverviewService.getSupplementIdList(vendor,productCode));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}
}
