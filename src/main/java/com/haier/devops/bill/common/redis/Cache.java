package com.haier.devops.bill.common.redis;

import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class Cache<K, V> {
    private final RedisTemplate<K, V> redisTemplate;

    public Cache(RedisTemplate<K, V> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void put(K key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void put(K key, V value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public V get(K key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void remove(K key) {
        redisTemplate.delete(key);
    }
}
