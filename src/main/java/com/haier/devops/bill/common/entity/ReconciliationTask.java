package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
* @ClassName: BcReconciliationTask
* @Description: 调账任务
* @author: 张爱苹
* @date: 2024/3/13 13:37
*/
@Data
@Builder
@TableName("bc_reconciliation_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="调账任务")
public class ReconciliationTask implements Serializable {

    private static final long serialVersionUID = 6333081293086785383L;
    /**id*/
    @TableId(type = IdType.AUTO)
    @Schema(description = "id")
    private java.lang.Integer id;
    /**聚合id*/
    @Schema(description = "聚合id")
    private java.lang.String aggregatedId;
    /**更新类型：tag、resourceGroup、costUnit*/
    @Schema(description = "更新类型：tag、resourceGroup、costUnit")
    private java.lang.String type;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "createTime")
    private java.util.Date createTime;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "updateTime")
    private java.util.Date updateTime;
    /**1 已执行 0未执行*/
    @Schema(description = "1 已执行 0未执行 2失败 3 失效")
    private java.lang.Integer status;
    /**任务执行时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务执行时间")
    private java.util.Date executeTime;

    private java.lang.String scode;

    private String originalScode;

    private String account;

    private Integer errorNum;

    private Integer adjustmentId;

    private String errorMsg;
}
