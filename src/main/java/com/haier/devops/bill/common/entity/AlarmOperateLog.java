package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* @ClassName: AlarmOperateLog
* @Description: 告警配置操作日志
* @author: 张爱苹
* @date: 2025/8/14 13:19
*/
@Data
@TableName("bc_cp_alarm_operate_log")
@Builder
public class AlarmOperateLog {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 内容
     */
    private String content;

    /**
     * 变更内容
     */
    private String changeContent;

    /**
     * 操作人
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private String operateUser;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;

    /**
     * 配置id
     */
    private Integer configId;
}
