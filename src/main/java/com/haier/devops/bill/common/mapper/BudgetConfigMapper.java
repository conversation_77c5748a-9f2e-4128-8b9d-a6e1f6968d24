package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.BudgetConfig;
import com.haier.devops.bill.common.vo.BudgetConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
public interface BudgetConfigMapper extends BaseMapper<BudgetConfig> {

    /**
     * 根据S码查询预算配置
     * @param scodes
     * @return
     */
    List<BudgetConfigVo> queryByScodesAndBillingCycle(@Param("vendor") String vendor,
                                                      @Param("scodes") Set<String> scodes);

    /**
     * 查询预算配置列表
     * @param subProductId
     * @param budgetCode
     * @param userCode
     * @param vendor
     * @return
     */
    List<BudgetConfigVo> queryList(@Param("subProductId") String subProductId,
                                   @Param("budgetCode") String budgetCode,
                                   @Param("userCode") String userCode,
                                   @Param("vendor") String vendor);
}
