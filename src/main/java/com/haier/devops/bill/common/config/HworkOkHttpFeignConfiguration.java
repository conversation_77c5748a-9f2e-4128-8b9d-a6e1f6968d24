package com.haier.devops.bill.common.config;

import com.haier.devops.bill.common.api.HworkApi;
import com.haier.devops.bill.util.HworkSignUtil;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.UUID;

/**
* @ClassName: Hwork2OkHttpFeignConfiguration
* @Description:
* @author: 张爱苹
* @date: 2024/1/24 13:24
*/
@Configuration
@Slf4j
public class HworkOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private final HworkConfigProperties hworkConfigProperties;
    private final String APP_ID = "X-App-Id";
    private final String SIGNATURE = "X-Signature";
    private final String TIMESTAMP = "X-Timestamp";
    private final String NONCE = "X-Nonce";
    private final String VERSION = "X-Version";

    private okhttp3.OkHttpClient hworkOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    public HworkOkHttpFeignConfiguration(HworkConfigProperties hworkConfigProperties,
                                         OkHttpProperties properties,
                                         OkHttpClientFactory httpClientFactory,
                                         OkHttpClientConnectionPoolFactory connectionPoolFactory) {
        this.hworkConfigProperties = hworkConfigProperties;
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @Bean
    public HworkApi hworkApi() {
        this.hworkOkHttp3Client = setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);

        this.hworkOkHttp3Client = hworkOkHttp3Client.newBuilder()
                .addInterceptor(authentication(hworkConfigProperties))
                .build();

        return Feign.builder()
                .client(new OkHttpClient(hworkOkHttp3Client))
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .target(HworkApi.class, hworkConfigProperties.getDomain());
    }

    private Interceptor authentication(HworkConfigProperties authorityConfigProperties) {
        return chain -> {
            Request request = chain.request();
            String body = null;
            String sign = null;
            long time = System.currentTimeMillis();
            String uuid = String.valueOf(UUID.randomUUID());
            if ("GET".equals(request.method())) {
                // todo
            } else if ("POST".equals(request.method()) && null != request.body()) {
                // Get the request body
                Buffer buffer = new Buffer();
                request.body().writeTo(buffer);
                body = buffer.readUtf8();

                sign = HworkSignUtil.generateSign(authorityConfigProperties.getAppId(),authorityConfigProperties.getSecret(),time,uuid);
                log.info("Request Body: {}, Signature: {}", body, sign);

                // re-create the request body since it's a one-time read
                request = request.newBuilder()
                        .post(okhttp3.RequestBody.create(request.body().contentType(), body))
                        .build();
            }

            log.info("Hwork request: {}", request);
            long start = System.currentTimeMillis();
            Request requestWithUserAgent = request.newBuilder()
                    .header(APP_ID, authorityConfigProperties.getAppId())
                    .header(SIGNATURE, sign)
                    .header(TIMESTAMP,time+"")
                    .header(NONCE,uuid)
                    .header(VERSION, "1.0")
                    .header("Content-Type", "application/json; charset=utf-8")
                    .build();
            Response response = chain.proceed(requestWithUserAgent);

            long end = System.currentTimeMillis();
            log.info("Hwork response: {}, costs: {} milliseconds", response, end - start);

            return response;
        };
    }
}
