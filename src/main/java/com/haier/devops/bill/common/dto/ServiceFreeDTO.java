package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: AggregatedBillDTO
* @Description:  账单明细汇总
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class ServiceFreeDTO extends BaseDTO implements Serializable {


    private static final long serialVersionUID = 7481067946863424344L;
    /**
     * 1服务费；2汇率
     */
    @NotEmpty
    private String type;

    /**
     * 云厂商
     */
    private String vendor;


}
