package com.haier.devops.bill.common.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.AggregatedBillMapper;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.service.BillDetailService;
import com.haier.devops.bill.common.service.ExportLogService;
import com.haier.devops.bill.common.service.NodeService;
import com.haier.devops.bill.common.vo.BillDetailVo;
import com.haier.devops.bill.export.util.OssUtil;
import com.haier.devops.bill.export.vo.UploadResult;
import com.haier.devops.bill.util.AuthUtil;
import com.haier.devops.bill.util.DeepCopyUtil;
import com.lark.oapi.core.utils.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.haier.devops.bill.util.EasyExcelUtils.getStyleStrategy;

/**
* @ClassName: BillDetailServiceImpl
* @Description:  账单明细服务实现类
* @author: 张爱苹
* @date: 2024/4/12 10:53
*/
@Service
@Slf4j
public class BillDetailServiceImpl implements BillDetailService {
    private Logger logger = LoggerFactory.getLogger(BillDetailServiceImpl.class);

    @Autowired
    private AggregatedBillMapper aggregatedBillMapper;

    @Autowired
    private NodeService nodeService;

    @Autowired
    private ExportLogService exportLogService;

    @Resource
    private Executor localBootAsyncExecutor;

    @Autowired
    private OssUtil ossUtil;

    private static Integer BATCH_SIZE = 1000;

    @Override
    public PageInfo<BillDetailVo> listByPage(DetailBillParam request) throws Exception{
        try {
            List<String> authedScodes = AuthUtil.extractAuthedScodes(request.getScode());
            request.setScodeList(authedScodes);
            return getBillDetailVoPageInfo(request);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("listByPage error, DetailBillParam:{}", request, e.getMessage(), e.getMessage(), e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    public ExportLog downloadDimensionDetail(DetailBillParam dto,HttpServletRequest request,HttpServletResponse response) {
        List<String> authedScodes = AuthUtil.extractAuthedScodes(dto.getScode());
        if (CollectionUtils.isEmpty(authedScodes)) {
            authedScodes = new ArrayList<>();
            authedScodes.add("没有S码权限");
        }
        dto.setScodeList(authedScodes);
        Date submitTime = new Date();
        String serialNo = UUID.randomUUID().toString();
        User currentUser = LoginContextHolder.getCurrentUser();
        String typeName = getTypeName(dto.getType());
        String fileName = dto.getVendor()+typeName+"账单明细"
                + "(" + dto.getStartCycle() + "-" + dto.getEndCycle() + ")"
                + ".xlsx";
        String currentDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String filePath =  "detail/" + currentDay + "/" + fileName;
        ExportLog exportLog = ExportLog.builder()
                .serialNo(serialNo)
                .submitTime(submitTime)
                .submitter(currentUser.getUserCode())
                .startCycle(dto.getStartCycle())
                .endCycle(dto.getEndCycle())
                .fileName(fileName)
                .build();
        exportLogService.save(exportLog);
        dto.setSerialNo(serialNo);
        new Thread(() -> {
            try {
                UploadResult uploadResult = exportBillExcel(request,dto,response,filePath);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("downloadDimensionDetail is exception {}", JSON.toJSONString(dto),e);
            }
        }).start();
        return exportLog;
    }

    private String getTypeName(String type) {
        switch (type){
            case "1":
                return "月度";
            case "2":
                return "季度";
            case "3":
                return "年度";
            default:
                return "日";
        }
    }

    public UploadResult exportBillExcel(HttpServletRequest request, DetailBillParam dto, HttpServletResponse response,String filePath) throws Exception{
        ExcelWriter excelWriter = null;
        try{
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            excelWriter = EasyExcel.write(outputStream).build();
            WriteSheet writeSheet = null;
            List<Map> timeRangeList = null;
            String start = dto.getStartCycle();
            String end = dto.getEndCycle();
            if(dto.getType().equals("4")){
                timeRangeList = getTimeRangeList(dto.getStartCycle(),dto.getEndCycle());
            }else{
                Map map = new HashMap();
                map.put("startCycle",dto.getStartCycle());
                map.put("endCycle",dto.getEndCycle());
                timeRangeList = new ArrayList<>();
                timeRangeList.add(map);
            }
            for (int m = 0; m < timeRangeList.size(); m++) {
                Map map = timeRangeList.get(m);
                String startCycle = (String) map.get("startCycle");
                String endCycle = (String) map.get("endCycle");
                dto.setStartCycle(startCycle);
                dto.setEndCycle(endCycle);
                List<BillDetailVo> bills = getBillDetailVoList(dto);
                List<List<Object>> resultList = new LinkedList<>();
                List<String> columns = new LinkedList<>();
                columns.add(0,"云厂商");
                columns.add(1,"云账号");
                columns.add(2,"领域");
                columns.add(3,"子产品");
                columns.add(4,"实例ID");
                columns.add(5,"分拆项");
                columns.add(6,"IP地址/域名");
                columns.add(7,"产品类型编码");
                columns.add(8,"云产品名称");
                columns.add(9,"消费类型");
                columns.add(10,"币种");
                columns.add(11,"费用类型");
                final Map[] summerMap = {nodeService.getSummerMap(dto.getStartCycle(),dto.getEndCycle(),columns,dto.getType())};
                if (!CollectionUtils.isEmpty(bills)){
                    resultList = getLists(summerMap[0], bills);
                }
                writeSheet = EasyExcel.writerSheet(dto.getStartCycle()+"_"+dto.getEndCycle()).head(head(columns))
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .registerWriteHandler(getStyleStrategy())
                        .build();
                // 写出sheet数据exportBillExcel
                excelWriter.write(resultList, writeSheet);
            }
            // 关流
            excelWriter.finish();
            dto.setStartCycle(start);
            dto.setEndCycle(end);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            UploadResult upload = ossUtil.upload(filePath, inputStream);
            nodeService.updateLog(dto, upload.getPutResult(), upload.getUrl(), StringUtils.isNotBlank(upload.getUrl()));
            return upload;
        }catch (Exception e){
            nodeService.updateLog(dto, null, null, false, e.getMessage());
            e.printStackTrace();
            log.error("导出失败：{}", e.getMessage(), e);
            throw new RuntimeException("导出失败 "+e.getMessage());
        }finally {

        }

    }

    @NotNull
    private List<List<Object>> getLists(Map summerMap, List<BillDetailVo> list) {
       Map<String,List<BillDetailVo>> reMap = list.stream()
                .collect(Collectors.groupingBy(e ->{
                    return e.getAggregatedId()+"_"+e.getScode();
                },Collectors.mapping(e->e,Collectors.toList())));
        //复制     summerMap
        return getMap(reMap, summerMap);
    }

    class GroupResult {
        List<BillDetailVo> items;
        Double total;

        GroupResult(List<BillDetailVo> items, Double total) {
            this.items = items;
            this.total = total;
        }

        public List<BillDetailVo> getItems() {
            return items;
        }

        public void setItems(List<BillDetailVo> items) {
            this.items = items;
        }

        public Double getTotal() {
            return total;
        }

        public void setTotal(Double total) {
            this.total = total;
        }
    }

    private List<List<Object>> getMap(Map<String,List<BillDetailVo>> reMap,Map<String,Object> summerMap) {
        Map<String,Object> commonMap = DeepCopyUtil.deepCopy(summerMap);
        List<List<Object>> list = new LinkedList<>();
        for (String key: reMap.keySet()) {
            List<Object> subList = new LinkedList<>();
            List<BillDetailVo> itemArr = reMap.get(key);
            Map<String,GroupResult> map = itemArr.stream().collect(Collectors.groupingBy(
                    e -> e.getBillingCycle(),
                    Collectors.collectingAndThen(
                            Collectors.toList(),
                            items -> {
                                // 1. 累加金额（用BigDecimal避免精度丢失）
                                BigDecimal totalBigDecimal = items.stream()
                                        .map(vo -> BigDecimal.valueOf(vo.getSummer() != null ? vo.getSummer() : 0.0))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(4, RoundingMode.FLOOR);
                                return new GroupResult(items,totalBigDecimal.doubleValue());
                            }
                    )
            ));
            // String[] itemArr = reMap.get(key).split(",");
            try{

                for (int i = 0; i < map.keySet().size();i++) {
                  //  String[] arr = itemArr[i].split("&");
                    String subKey = map.keySet().toArray()[i].toString();
                    if(i == 0){
                        List<BillDetailVo> detailVoList = map.get(subKey).items;
                        BillDetailVo e = detailVoList.get(0);
                        subList.add(e.getVendor());
                        subList.add(e.getAccountName());
                        subList.add(e.getBusinessDomains());
                        subList.add(e.getAppName());
                        subList.add(e.getInstanceId());
                        subList.add(e.getSupplementId());
                        subList.add(e.getPrivateIp());
                        subList.add(e.getAliasCode());
                        subList.add(e.getProductName());
                        subList.add(e.getSubscriptionTypeName());
                        subList.add(e.getCurrency());
                        subList.add(e.getExpenseType());
                    }
                    summerMap.put(subKey,map.get(subKey).total);
                }
                subList.addAll(summerMap.values());
                list.add(subList);
            }catch (Exception e){
                e.getMessage();
                logger.error("数据格式错误",itemArr);
            }
            summerMap = DeepCopyUtil.deepCopy(commonMap);
        }
        return list;
    }

    public List<Map> getTimeRangeList(String startDateStr, String endDateStr) {
        List<Map> timeRangeList = new ArrayList<>();
        // 日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 获取按月拆分后的日期段
        List<String[]> dateRanges = getMonthlyDateRanges(startDate, endDate, formatter);

        // 打印结果
        for (String[] range : dateRanges) {
            System.out.println("Start: " + range[0] + ", End: " + range[1]);
            Map map = new HashMap();
            map.put("startCycle",range[0]);
            map.put("endCycle",range[1]);
            timeRangeList.add(map);
        }
        return timeRangeList;
    }

    public static List<String[]> getMonthlyDateRanges(LocalDate startDate, LocalDate endDate, DateTimeFormatter formatter) {
        List<String[]> dateRanges = new ArrayList<>();

        // 确保开始日期在结束日期之前
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        LocalDate currentStart = startDate;

        while (!currentStart.isAfter(endDate)) {
            // 计算当前月的最后一天
            LocalDate endOfMonth = currentStart.withDayOfMonth(currentStart.lengthOfMonth());

            // 确保当前月的结束日期不超过结束日期
            LocalDate endOfRange = endOfMonth.isBefore(endDate) ? endOfMonth : endDate;

            // 处理当前月份的开始和结束日期
            if (currentStart.isBefore(endOfRange) || currentStart.isEqual(endOfRange)) {
                dateRanges.add(new String[]{
                        currentStart.format(formatter),
                        endOfRange.format(formatter)
                });
            }

            // 移动到下一个月的第一天
            currentStart = endOfRange.plusDays(1);

            // 如果当前日期超过了结束日期，则跳出循环
            if (currentStart.isAfter(endDate)) {
                break;
            }
        }

        return dateRanges;
    }


    private List<List<String>> head(List<String> dateList) {
        List<List<String>> headTitles = Lists.newArrayList();
        // 固定title
        String billPeriodHead = "消费明细";
       // List<String> paymentTitles = Lists.newArrayList("上午", "下午");
        for (String localDate : dateList) {
            headTitles.add(Lists.newArrayList(billPeriodHead,localDate));
        }
        return headTitles;
    }

    private PageInfo<BillDetailVo> getBillDetailVoPageInfo(DetailBillParam request) {
        PageInfo<BillDetailVo> pageInfo = PageHelper.startPage(request.getPage(), request.getPer_page()).doSelectPageInfo(
                () -> {
                    getBillDetailVoList(request);
                }
        );
        return pageInfo;
    }

    private List<BillDetailVo> getBillDetailVoList(DetailBillParam request) {
        switch (request.getType()){
            case "2":
                return aggregatedBillMapper.listDetailQByPage(request);
            case "3":
                return aggregatedBillMapper.listDetailYByPage(request);
            default:
                return aggregatedBillMapper.listDetailByPage(request);
        }
    }

}
