package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.AlarmConfigurationDTO;
import com.haier.devops.bill.common.entity.AlarmConfiguration;
import com.haier.devops.bill.common.vo.CpAlarmConfigurationVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AlarmConfigurationMapper
* @Description: 告警配置Mapper接口
* @author: 张爱苹
* @date: 2024/1/11 10:51
*/
@Repository
public interface AlarmConfigurationMapper extends BaseMapper<AlarmConfiguration> {
    List<CpAlarmConfigurationVo> listByPage(@Param("dto") AlarmConfigurationDTO dto);
    @Select("select case when app_scode is not null and app_scode != '' then concat(app_scode ,product_id) else product_id end as product_id from bc_cp_alarm_configuration bcac where bcac.del_flag = 0")
    List<String> getAllAlarmConfigurationList();

    @Update("update bc_cp_alarm_configuration set del_flag = '1' where id = #{id}")
    void deleteAlarmConfiguration(Integer id);

    void updateAlarmConfigurationStatusByPeriod(@Param("isEnableAlarm") int isEnableAlarm);
}
