package com.haier.devops.bill.common.service.impl;

import com.haier.devops.bill.common.dto.HworkQrqmDTO;
import com.haier.devops.bill.common.entity.RcMiddlewareInfo;
import com.haier.devops.bill.common.service.HworkQrqmService;
import com.haier.devops.bill.common.service.RcMiddlewareInfoService;
import com.haier.devops.bill.common.service.ZabbixMonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @ClassName: HworkQrqmServiceImpl
* @Description: TODO
* @author: 张爱苹
* @date: 2024/12/10 10:19
*/
@Service
@Slf4j
public class HworkQrqmServiceImpl implements HworkQrqmService {

    @Autowired
    private ZabbixMonitorDataService zabbixMonitorDataService;

    @Autowired
    private RcMiddlewareInfoService middlewareInfoService;


    @Override
    public Map getDbMonitorData(HworkQrqmDTO dto) throws Exception{
        Map map = new HashMap();
        if(StringUtils.isNotEmpty(dto.getScodes())){
            List<String> scodeList = Arrays.asList(dto.getScodes().split(","));
            //查询中间件数量
            List<RcMiddlewareInfo> middlewareInfoList = getMiddlewareInfoList(scodeList);
            map.put("mqCount",middlewareInfoList.size());
            map.put("tpsMax",0);
            if(CollectionUtils.isNotEmpty(middlewareInfoList)){
                //获取中间件cpu最高使用率和内存最高使用率
                Map pxp = zabbixMonitorDataService.getMaxCpuAndMem(middlewareInfoList.stream().map(middlewareInfo -> middlewareInfo.getInstanceId()).collect(Collectors.toList()));
                map.put("tpsMax",pxp.get("tpsMax"));
            }

        }else{
            log.info("用户{}没有任何s码权限",dto.getUserId());
        }
        return map;
    }

    private List<RcMiddlewareInfo> getMiddlewareInfoList(List<String> scodeList) {
        return middlewareInfoService.getMiddlewareInfoByScodeList(scodeList);
    }


}
