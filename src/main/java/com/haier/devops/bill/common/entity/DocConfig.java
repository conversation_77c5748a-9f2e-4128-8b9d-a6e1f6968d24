package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 账单中心文档配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
@TableName("bc_doc_config")
public class DocConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型：sheet
     */
    private String type;

    /**
     * 业务模块
     */
    private String business;

    /**
     * 账期
     */
    private String billingCycle;

    private String token;

    private Integer isEnabled;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
