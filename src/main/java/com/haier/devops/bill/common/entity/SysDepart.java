package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * (SysDepart)实体类
 *
 * <AUTHOR>
 * @since 2024-01-19 15:36:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "$tableInfo.comment")
public class SysDepart implements Serializable {
    private static final long serialVersionUID = 699958884989336998L;

    @Schema(description = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;


    @Schema(description = "父机构ID")
    private String parentId;


    @Schema(description = "机构/部门名称")
    private String departName;


    @Schema(description = "英文名")
    private String departNameEn;


    @Schema(description = "缩写")
    private String departNameAbbr;


    @Schema(description = "排序")
    private Integer departOrder;


    @Schema(description = "描述")
    private String description;


    @Schema(description = "机构类别 1公司，2组织机构，2岗位")
    private String orgCategory;


    @Schema(description = "机构类型 1一级部门 2子部门")
    private String orgType;


    @Schema(description = "机构编码")
    private String orgCode;


    @Schema(description = "手机号")
    private String mobile;


    @Schema(description = "传真")
    private String fax;


    @Schema(description = "地址")
    private String address;


    @Schema(description = "备注")
    private String memo;


    @Schema(description = "状态（1启用，0不启用）")
    private String status;


    @Schema(description = "删除状态（0，正常，1已删除）")
    private String delFlag;


    @Schema(description = "对接企业微信的ID")
    private String qywxIdentifier;


    @Schema(description = "创建人")
    private String createBy;


    @Schema(description = "创建日期")
    private Date createTime;


    @Schema(description = "更新人")
    private String updateBy;


    @Schema(description = "更新日期")
    private Date updateTime;


//    @Schema(description = "二级部门里是否显示 1是 0否 仅针对一线运维")
//    private String ifShow;


//    @Schema(description = "等级 1 大部门")
//    private Integer level;


}

