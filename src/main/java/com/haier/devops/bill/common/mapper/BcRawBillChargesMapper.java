package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.haier.devops.bill.common.entity.BcRawBillCharges;
import com.haier.devops.bill.common.vo.BillChargesProductsVo;
import com.haier.devops.bill.common.vo.BillChargesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BcRawBillCharges)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-02 11:10:16
 */
public interface BcRawBillChargesMapper extends BaseMapper<BcRawBillCharges> {
    List<BillChargesVo> getBillCharges();


    List<BillChargesProductsVo> getProductChargeInfo(@Param(Constants.WRAPPER) QueryWrapper<BcRawBillCharges> queryWrapper);

}

