package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.BudgetConfig;
import com.haier.devops.bill.common.mapper.BudgetConfigMapper;
import com.haier.devops.bill.common.service.BudgetConfigService;
import com.haier.devops.bill.common.vo.BudgetConfigVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@Service
public class BudgetConfigServiceImpl extends ServiceImpl<BudgetConfigMapper, BudgetConfig> implements BudgetConfigService {
    private final BudgetConfigMapper configMapper;

    public BudgetConfigServiceImpl(BudgetConfigMapper configMapper) {
        this.configMapper = configMapper;
    }

    @Override
    public List<BudgetConfigVo> queryByScodesAndBillingCycle(String vendor, Set<String> scodes) {
        return configMapper.queryByScodesAndBillingCycle(vendor,scodes);
    }

    @Override
    public PageInfo<BudgetConfigVo> queryList(String subProductId, String budgetCode, String userCode, String vendor, int page, int pageSize) {
        return PageHelper
                .startPage(page, pageSize)
                .doSelectPageInfo(() -> configMapper.queryList(subProductId, budgetCode, userCode, vendor));
    }
}
