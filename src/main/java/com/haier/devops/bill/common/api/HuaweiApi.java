package com.haier.devops.bill.common.api;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.CloudAccountService;
import com.huaweicloud.sdk.config.v1.ConfigClient;
import com.huaweicloud.sdk.config.v1.model.ListAllResourcesRequest;
import com.huaweicloud.sdk.config.v1.model.ListAllResourcesResponse;
import com.huaweicloud.sdk.config.v1.region.ConfigRegion;
import com.huaweicloud.sdk.core.auth.GlobalCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.*;
import com.huaweicloud.sdk.eps.v1.region.EpsRegion;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsResponse;
import com.huaweicloud.sdk.iam.v3.region.IamRegion;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
* @ClassName: HuaweiApi
* @Description: 华为云api
* @author: 张爱苹
* @date: 2025/2/13 14:22
*/
@Service
public class HuaweiApi {

    @Autowired
    private CloudAccountService accountService;

    private final LoadingCache<String, EpsClient> epsClientCache = CacheBuilder.newBuilder()
            .build(new CacheLoader<String, EpsClient>() {
                @Override
                public EpsClient load(@NotNull String account) throws ExecutionException {
                    String[] str = account.split("_");
                    return _getEpsClient(str[0],str[1]);
                }
            });

    private final LoadingCache<String, ConfigClient> configClientCache = CacheBuilder.newBuilder()
            .build(new CacheLoader<String, ConfigClient>() {
                @Override
                public ConfigClient load(@NotNull String account) throws ExecutionException {
                    String[] str = account.split("_");
                    return _getConfigClient(str[0],str[1]);
                }
            });

    private final LoadingCache<String, IamClient> iamClientCache = CacheBuilder.newBuilder()
            .build(new CacheLoader<String, IamClient>() {
                @Override
                public IamClient load(@NotNull String account) throws ExecutionException {
                    String[] str = account.split("_");
                    return _getIamClient(str[0],str[1]);
                }
            });

    private IamClient _getIamClient(String account,String region) throws ExecutionException {
        try{
            Map<String,String> keyMap = getAccount(account);
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            ICredential auth = new GlobalCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            if(StringUtils.isEmpty(region)){
                region = "cn-north-4";
            }
            IamClient client = IamClient.newBuilder()
                    .withCredential(auth)
                    .withRegion(IamRegion.valueOf(region))
                    .build();
            return client;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private EpsClient _getEpsClient(String account,String region) throws ExecutionException {
        try{
            Map<String,String> keyMap = getAccount(account);
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            ICredential auth = new GlobalCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            if(StringUtils.isEmpty(region)){
                region = "cn-north-4";
            }
            EpsClient client = EpsClient.newBuilder()
                    .withCredential(auth)
                    .withRegion(EpsRegion.valueOf(region))
                    .build();
            return client;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    private ConfigClient _getConfigClient(String account, String region) throws ExecutionException {
        try{
            Map<String,String> keyMap = getAccount(account);
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            ICredential auth = new GlobalCredentials()
                    .withAk(accessKeyId)
                    .withSk(accessKeySecret);

            if(StringUtils.isEmpty(region)){
                region = "cn-north-4";
            }
            ConfigClient client = ConfigClient.newBuilder()
                    .withCredential(auth)
                    .withRegion(ConfigRegion.valueOf(region))
                    .build();
            return client;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public Map<String,String> getAccount(String account) throws ExecutionException {
        Map<String,String> keyMap = accountService.getOpenApiKey(VendorEnum.HUAWEI.getVendor(),account);
        return keyMap;
    }

    public EpsClient getEpsClient(String account, String region)  throws ExecutionException{
        return epsClientCache.get(account+"_"+region);
    }

    public ConfigClient getConfigClientClient(String account, String region)  throws ExecutionException{
        return configClientCache.get(account+"_"+region);
    }



    public ListEnterpriseProjectResponse listEnterpriseProjects(EpsClient client, Integer offset, Integer limit) {
        ListEnterpriseProjectRequest request = new ListEnterpriseProjectRequest();
        request.withLimit(limit);
        request.withOffset(offset);
        try {
            ListEnterpriseProjectResponse response = client.listEnterpriseProject(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public KeystoneListAuthProjectsResponse keystoneListAuthProjects(IamClient client) {
        KeystoneListAuthProjectsRequest request = new KeystoneListAuthProjectsRequest();
        try {
            KeystoneListAuthProjectsResponse response = client.keystoneListAuthProjects(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public ListProvidersResponse listEnterpriseProviders(EpsClient client,Integer offset,Integer limit) {
        ListProvidersRequest request = new ListProvidersRequest();
        request.withLimit(limit);
        request.withOffset(offset);
        try {
            ListProvidersResponse response = client.listProviders(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public ShowResourceBindEnterpriseProjectResponse showResourceBindEnterpriseProject(EpsClient client,String enterpriseProjectId,List<String> listbodyResourceTypes,List<String> listbodyProjects, Integer offset, Integer limit) {
        ShowResourceBindEnterpriseProjectRequest request = new ShowResourceBindEnterpriseProjectRequest();
        request.withEnterpriseProjectId(enterpriseProjectId);
        ResqEpResouce body = new ResqEpResouce();
        body.withLimit(limit);
        body.withOffset(offset);
        body.withResourceTypes(listbodyResourceTypes);
        body.withProjects(listbodyProjects);
        request.withBody(body);
        try {
            ShowResourceBindEnterpriseProjectResponse response = client.showResourceBindEnterpriseProject(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public ListAllResourcesResponse listAllResourcesResponse(ConfigClient client,String marker,Integer limit) {
        ListAllResourcesRequest request = new ListAllResourcesRequest();
        try {
            if(StringUtils.isNotEmpty(marker)){
                request.setMarker(marker);
            }
            request.setLimit(limit);
            ListAllResourcesResponse response = client.listAllResources(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public String migrateResource(EpsClient client,String projectId,String resourceType,String resourceId,String enterpriseProjectId) {
        String errorMsg = "";
        MigrateResourceRequest request = new MigrateResourceRequest();
        request.withEnterpriseProjectId(enterpriseProjectId);
        MigrateResource body = new MigrateResource();
        body.withResourceType(resourceType);
        body.withProjectId(projectId);
        body.withResourceId(resourceId);
        request.withBody(body);
        try {
            MigrateResourceResponse response = client.migrateResource(request);
        } catch (ConnectionException e) {
            e.printStackTrace();
            errorMsg = JSON.toJSONString(e);
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
            errorMsg = JSON.toJSONString(e);
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            errorMsg = JSON.toJSONString(e);
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return errorMsg;
    }

    public EnableEnterpriseProjectResponse enableEnterpriseProject(EpsClient client,String enterpriseProjectId) {
        EnableEnterpriseProjectRequest request = new EnableEnterpriseProjectRequest();
        request.withEnterpriseProjectId(enterpriseProjectId);
        EnableAction body = new EnableAction();
        body.withAction(EnableAction.ActionEnum.fromValue("enable"));
        request.withBody(body);
        try {
            EnableEnterpriseProjectResponse response = client.enableEnterpriseProject(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public CreateEnterpriseProjectResponse createEnterpriseProject(EpsClient client,String name) {
        CreateEnterpriseProjectRequest request = new CreateEnterpriseProjectRequest();
        EnterpriseProject body = new EnterpriseProject();
        body.withName(name);
        request.withBody(body);
        try {
            CreateEnterpriseProjectResponse response = client.createEnterpriseProject(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }

    public IamClient getIamClient(String account, String region) throws ExecutionException{
        return iamClientCache.get(account+"_"+region);
    }
}
