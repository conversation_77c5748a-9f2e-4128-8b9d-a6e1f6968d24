package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.BillSubstitutionTask;
import com.haier.devops.bill.common.mapper.BillSubstitutionTaskMapper;
import com.haier.devops.bill.common.service.BillSubstitutionTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 账单替换任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Service
public class BillSubstitutionTaskServiceImpl extends ServiceImpl<BillSubstitutionTaskMapper, BillSubstitutionTask> implements BillSubstitutionTaskService {
    private final BillSubstitutionTaskMapper substitutionTaskMapper;

    public BillSubstitutionTaskServiceImpl(BillSubstitutionTaskMapper substitutionTaskMapper) {
        this.substitutionTaskMapper = substitutionTaskMapper;
    }

    @Override
    public List<BillSubstitutionTask> getTaskByTypeAndBillingCycle(String type, String billingCycle) {
        LambdaQueryWrapper<BillSubstitutionTask> queryWrapper = new LambdaQueryWrapper<BillSubstitutionTask>().eq(BillSubstitutionTask::getSubType, type).eq(BillSubstitutionTask::getBillingCycle, billingCycle);
        return substitutionTaskMapper.selectList(queryWrapper);
    }

    @Override
    public List<BillSubstitutionTask> getPendingMigrationTaskByType(String type, String billingCycle) {
        return substitutionTaskMapper.getPendingMigrationTaskByType(type, billingCycle);
    }

    @Override
    public List<BillSubstitutionTask> getPendingCalculationTaskByType(String type, String billingCycle) {
        return substitutionTaskMapper.getPendingCalculationTaskByType(type, billingCycle);
    }

    @Override
    public List<BillSubstitutionTask> getPendingSubstitutionTaskByType(String type, String billingCycle) {
        return substitutionTaskMapper.getPendingSubstitutionTaskByType(type, billingCycle);
    }

    @Override
    public int createTask(BillSubstitutionTask task) {
        return substitutionTaskMapper.insert(task);
    }

    @Override
    public int updateStageAndProcessByTaskId(String subTaskId, String stage, Integer process) {
        return substitutionTaskMapper.updateStageAndProcessByTaskId(subTaskId, stage, process);
    }

    @Override
    public void updateErrorInfo(String subTaskId, String errorInfo) {
        substitutionTaskMapper.updateErrorInfo(subTaskId, errorInfo);
    }

    @Override
    public List<BillSubstitutionTask> getPendingCleaningTaskByType(String type, String billingCycle) {
        return substitutionTaskMapper.getPendingCleaningTaskByType(type, billingCycle);
    }
}
