package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.RuleFieldValueDTO;
import com.haier.devops.bill.common.entity.CloudCostunitMoveRule;
import com.haier.devops.bill.common.entity.CloudRuleField;
import com.haier.devops.bill.common.entity.ResourceInstance;
import com.haier.devops.bill.common.enums.RuleFieldEnum;
import com.haier.devops.bill.common.enums.RuleTypeEnum;
import com.haier.devops.bill.common.mapper.CloudCostunitMoveRuleMapper;
import com.haier.devops.bill.common.mapper.CloudRuleFieldMapper;
import com.haier.devops.bill.common.mapper.ResourceInstanceMapper;
import com.haier.devops.bill.common.service.CloudRuleFieldService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 规则字段详情
 * @Author: jeecg-boot
 * @Date:   2024-03-12
 * @Version: V1.0
 */
@Service
public class CloudRuleFieldServiceImpl extends ServiceImpl<CloudRuleFieldMapper, CloudRuleField> implements CloudRuleFieldService {

    @Autowired
    private ResourceInstanceMapper resourceInstanceMapper;

    @Autowired
    private CloudCostunitMoveRuleMapper cloudCostunitMoveRuleMapper;

    @Override
    public List<CloudRuleField> getRuleFieldList(String ruleType) {
        return baseMapper.selectList(new LambdaQueryWrapper<CloudRuleField>()
                .eq(CloudRuleField::getRuleType, ruleType));
    }

    @Override
    public List<String> getRuleFieldValueList(RuleFieldValueDTO ruleFieldValueDTO) throws Exception{
        List<String> resultList = new ArrayList<>();
        String ruleType = ruleFieldValueDTO.getRuleType();
        if(StringUtils.isEmpty(ruleType)){
            throw new RuntimeException("ruleType不能为空");
        }
        if(ruleType.equals(RuleTypeEnum.RULE.getKey())&&ruleFieldValueDTO.getRuleField().equals(RuleFieldEnum.RESOURCEID.getKey())){
            return cloudCostunitMoveRuleMapper.selectList(new LambdaQueryWrapper<CloudCostunitMoveRule>()
                    .eq(CloudCostunitMoveRule::getAccount, ruleFieldValueDTO.getAccountName()).eq(CloudCostunitMoveRule::getCommoditycode, ruleFieldValueDTO.getCommodityCode()))
                    .stream().map(CloudCostunitMoveRule::getResourceid).collect(Collectors.toList());
        }

        List<ResourceInstance> list = resourceInstanceMapper.selectList(new LambdaQueryWrapper<ResourceInstance>().eq(ResourceInstance::getResourceUserName, ruleFieldValueDTO.getAccountName()).eq(ResourceInstance::getCommodityCode, ruleFieldValueDTO.getCommodityCode()));
        switch (ruleFieldValueDTO.getRuleField()){
            case "resource_tag":
                if(ruleType.equals(RuleTypeEnum.RULE.getKey())){
                    resultList = list.stream().filter(resourceInstance -> !resourceInstance.getResourceTag().contains("appscode")).map(ResourceInstance::getResourceTag).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                }else {
                    resultList = list.stream().filter(resourceInstance -> resourceInstance.getResourceTag().contains("appscode")).map(ResourceInstance::getResourceTag).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                }
                break;
            case "resource_nick":
                resultList = list.stream().map(ResourceInstance::getResourceNick).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                break;
            case "resource_group":
                resultList = list.stream().map(ResourceInstance::getResourceGroup).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                break;
            default:
                break;

        }
        return resultList;
    }

}
