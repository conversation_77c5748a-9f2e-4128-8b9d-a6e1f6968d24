package com.haier.devops.bill.common.distributed_lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.net.InetAddress;
import java.util.Date;
import java.util.UUID;
import java.util.function.Consumer;

@Component
@Slf4j
public class DistributedLockCompetitor {
    private final JedisPool jedisPool;

    public DistributedLockCompetitor(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }

    public void compete(Runnable runnable, String lockKey, long expirePeriod,
                        Consumer<LockObj> competeCallback, Consumer<LockObj> cancelCallback) {
        Jedis jedis = null;
        String requestId = UUID.randomUUID().toString();
        LockObj lockObj = null;
        try {
            jedis = jedisPool.getResource();

            // Get distributed lock
            boolean success = RedisDistributedLock
                    .tryGetDistributedLock(jedis, lockKey, requestId, expirePeriod);

            InetAddress ip = InetAddress.getLocalHost();
            String hostName = ip.getHostName();
            String hostAddress = ip.getHostAddress();
            lockObj = new LockObj(hostAddress, lockKey, new Date(), expirePeriod);
            log.info("DistributedLockExecutor ==> {} - {} Get timed task distributed lock: {}", hostName, hostAddress, success);
            if (success) {
                log.info("DistributedLockExecutor ==> successfully got the distributed lock!");

                competeCallback.accept(lockObj);
                runnable.run();

                // Release distributed lock
                RedisDistributedLock.releaseDistributedLock(jedis, lockKey);
                cancelCallback.accept(lockObj);
            } else {
                log.info("DistributedLockExecutor ==> failed to get the distributed lock!");
            }
        } catch (Exception e) {
            if (null != jedis) {
                // Release distributed lock
                RedisDistributedLock.releaseDistributedLock(jedis, lockKey);
                if (null != lockObj) {
                    cancelCallback.accept(lockObj);
                }
            }
            log.error("DistributedLockExecutor ==> Error occurs when syncing data", e);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}
