package com.haier.devops.bill.common.service;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.vo.BillDetailVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* @ClassName: BillDetailService
* @Description:  账单明细服务
* @author: 张爱苹
* @date: 2024/4/12 10:52
*/
public interface BillDetailService {

    /**
    * @Description: 分页查询账单明细数据
    * @author: 张爱苹
    * @date: 2024/4/12 10:54
    * @param request:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.vo.BillDetailVo>
    */
    PageInfo<BillDetailVo> listByPage(DetailBillParam request) throws Exception;

    /**
    * @Description:  导出账单明细数据
    * @author: 张爱苹
    * @date: 2024/4/12 16:20
    * @param request:
    * @Return: com.haier.devops.bill.common.entity.ExportLog
    */
    ExportLog downloadDimensionDetail(DetailBillParam dto,HttpServletRequest request, HttpServletResponse response);

    /**
    * @Description: 账单明细导出
    * @author: 张爱苹
    * @date: 2024/8/14 09:27
    * @param request:
    * @param dto:
    * @param billDetailVoClass:
    * @param 账单明细:
    * @Return: org.springframework.web.servlet.ModelAndView
    */
   // UploadResult exportBillExcel(HttpServletRequest request, DetailBillParam dto, HttpServletResponse response) throws Exception;
}
