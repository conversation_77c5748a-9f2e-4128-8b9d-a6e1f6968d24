package com.haier.devops.bill.common.param;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description: 多维度消费分析参数
 * @Author: A0058187
 * @Date: 2024/1/3 15:10
 */
@Data
public class DimensionDetailParam {

    /**
     * 开始周期
     */
    String startCycle;

    /**
     * 结束周期
     */
    String endCycle;

    /**
     * 数据项，money，count
     */
    private String dataType;

    /**
     * 统计周期  1 month、2 quarter、 3 year
     */
    private String cycleType;

    String scode;


    /**
     * 数据维度：vendor，productCode，subProductName，accountName，subscriptionType,productName
     */
    private List<String> dataDimensions;

    private String productCode;

    private List<String> productList;

    /**
     * 是否导出全部领域
     */
    private Boolean exportAll = false;

    private String vendor;

    private List<Map<String,String>> appList;

    private List<Map<String,String>> domainList;

    private String domains;

    private String currency;

}
