package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.OrphanParentReplacingLog;
import com.haier.devops.bill.common.mapper.OrphanParentReplacingLogMapper;
import com.haier.devops.bill.common.service.OrphanParentReplacingLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 父子资源替换日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-08
 */
@Service
public class OrphanParentReplacingLogServiceImpl
        extends ServiceImpl<OrphanParentReplacingLogMapper, OrphanParentReplacingLog>
        implements OrphanParentReplacingLogService {

}
