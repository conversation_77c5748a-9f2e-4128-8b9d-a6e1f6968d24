package com.haier.devops.bill.common.enums;

/**
 * <AUTHOR>
 */

public enum NoticeWayEnum {
    SMS("sms","短信"),
    EMAIL("email","邮箱"),
    HWORK("hworkim","CLOUD_PRODUCT_HWORK_GROUP"),
    IHAIER("ihaier2","CLOUD_PRODUCT_IHAIER_GROUP");


    NoticeWayEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public static String getValue(String key) {
        for (NoticeWayEnum noticeWayEnum : NoticeWayEnum.values()) {
            if (noticeWayEnum.getKey().equals(key)) {
                return noticeWayEnum.getValue();
            }
        }
        return null;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
