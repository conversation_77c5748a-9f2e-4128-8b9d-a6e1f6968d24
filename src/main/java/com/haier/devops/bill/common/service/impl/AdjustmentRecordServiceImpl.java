package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.AdjustmentRecord;
import com.haier.devops.bill.common.enums.ReconciliationStatusEnum;
import com.haier.devops.bill.common.mapper.AdjustmentRecordMapper;
import com.haier.devops.bill.common.service.AdjustmentRecordService;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;
import com.haier.devops.bill.util.ListSplitter;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 调账记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Service
public class AdjustmentRecordServiceImpl
        extends ServiceImpl<AdjustmentRecordMapper, AdjustmentRecord> implements AdjustmentRecordService {


    @Override
    public Long getNotCompleteReconciliationCount(List<String> aggregatedIdList) {
        List<List<String>> list = ListSplitter.splitList(aggregatedIdList,500);
        Long count = 0L;
        for (int i = 0; i < list.size(); i++) {
            List<String> subList = list.get(i);
            count = count + baseMapper.selectCount(new QueryWrapper<AdjustmentRecord>().in("aggregated_id", subList).eq("reconciliation_status", ReconciliationStatusEnum.INPROGRESS.getKey()));
            // 执行查询操作
        }
        return count;
    }

    @Override
    public List<AdjustmentRecordVo> getAdjustmentRecordList(QueryWrapper<AdjustmentRecord> queryWrapper) {
        return baseMapper.getAdjustmentRecordList(queryWrapper);
    }


}
