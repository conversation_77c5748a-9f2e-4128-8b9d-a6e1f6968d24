package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.ZabbixMonitorData;

import java.util.List;
import java.util.Map;

/**
* @ClassName: ZabbixMonitorDataService
* @Description: zabbix监控数据服务
* @author: 张爱苹
* @date: 2024/4/17 10:41
*/
public interface ZabbixMonitorDataService extends IService<ZabbixMonitorData> {

    /**
    * @Description: zabbix监控数据保存
    * @author: 张爱苹
    * @date: 2024/4/17 10:42
    * @param param:
    * @Return: void
    */
    void saveMonitorDataBatch(List<ZabbixMonitorData> param);

    /**
    * @Description: 获取最大cpu和内存
    * @Description:
    * @author: 张爱苹
    * @date: 2024/12/26 16:29
    * @param collect:
    * @Return: java.util.Map
    */
    Map getMaxCpuAndMem(List<String> collect);
}
