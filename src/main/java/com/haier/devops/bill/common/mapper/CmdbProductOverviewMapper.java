package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.CmdbProductOverviewDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: CmdbProductOverviewMapper
* @Description: cmdb总表Mapper
* @author: 张爱苹
* @date: 2024/1/31 10:21
*/
@Repository
public interface CmdbProductOverviewMapper extends BaseMapper<CmdbProductOverview> {

    @Update("update bc_cmdb_product_overview set scode = #{scode},update_time = now() where aggregated_id = #{aggregatedId}")
    void updateCmdbAggregatedBill(@Param("scode") String scode,@Param("aggregatedId") String aggregatedId);

    List<CmdbProductOverview> listByPage(@Param("dto") CmdbProductOverviewDTO dto);

    @Select("select id, aggregated_id, vendor, account_id, account_name, product_code, product_name, instance_id, supplement_id,scode, project_code, project_name, subscription_type, creation_time, releasing_time, create_time, update_time,'' as cost_unit, '' as reconciliation_status from bc_cmdb_product_overview where aggregated_id = #{aggregatedId}")
    CmdbProductOverview getOneByAggregatedId(@Param("aggregatedId")String aggregatedId);

    void updateCmdbAggregatedBillBatch(@Param("dto") ReconciliationBatchDTO dto);

    List<CmdbProductOverview> getListByAggregateIdList(@Param("dto") ReconciliationBatchDTO dto);

    @Select("select distinct supplement_id from bc_cmdb_product_overview bcpo where vendor = #{vendor} and product_code = #{productCode} and supplement_id is not null")
    List<String> getSupplementIdList(@Param("vendor") String vendor, @Param("productCode") String productCode);

    /**
     * 查找父级未找到的资源（不包括云盘）
     * @return
     */
    List<CmdbProductOverview> listParentNotFoundItems();

    /**
     * 根据实例id查询父级资源
     * @param instanceId
     * @return
     */
    CmdbProductOverview getParentInstanceByInstanceId(String instanceId);

    /**
     * 删除找到了父资源的子资源
     * @param aggregatedId
     * @return
     */
    int deleteOrphanData(String aggregatedId);

    /**
     * 根据s码更新项目信息
     * @param scode
     * @param projectId
     * @param projectName
     *
     * @return 修改的数量
     */
    int correctProjectInfoByScode(@Param("scode") String scode,
                                   @Param("projectId") String projectId,
                                   @Param("projectName") String projectName);

    @Select("select * from bc_cmdb_product_overview where aggregated_id = #{aggregatedId}")
    List<CmdbProductOverview> listByAggregatedId(String aggregatedId);
}
