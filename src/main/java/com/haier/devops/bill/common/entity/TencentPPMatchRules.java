package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: TencentPPMatchRules
* @Description: 腾讯云产品项目匹配规则
* @author: 张爱苹
* @date: 2024/1/31 15:10
*/
@Data
@TableName("bc_tencent_pp_match_rules")
public class TencentPPMatchRules implements Serializable {

    private static final long serialVersionUID = -7400175046058126716L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账号
     */
    private String accountId;

    /**
     * S码
     */
    private String appScode;


    /**
     * 项目名称
     */
    private String businessCodeName;

    /**
     * 标签
     */
    private String tag;


}

