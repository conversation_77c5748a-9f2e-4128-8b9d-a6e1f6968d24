package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.HostInfo;

import java.util.List;

/**
 * <p>
 * 服务器信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface HostInfoService extends IService<HostInfo> {
    PageInfo<HostInfo> list(List<String> scodes, int page, int pageSize, String... currency);
}
