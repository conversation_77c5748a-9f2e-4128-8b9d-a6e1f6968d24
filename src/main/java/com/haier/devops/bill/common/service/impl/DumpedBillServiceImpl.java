package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.DumpedBill;
import com.haier.devops.bill.common.mapper.DumpedBillMapper;
import com.haier.devops.bill.common.service.DumpedBillService;
import com.haier.devops.bill.common.vo.DumpedDetailBillVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class DumpedBillServiceImpl extends ServiceImpl<DumpedBillMapper, DumpedBill> implements DumpedBillService {
    private final DumpedBillMapper dumpedBillMapper;

    public DumpedBillServiceImpl(DumpedBillMapper dumpedBillMapper) {
        this.dumpedBillMapper = dumpedBillMapper;
    }

    @Override
    public List<DumpedBill> queryBillGroupByScode(String vendor, String billingCycle, String... account) {
        return dumpedBillMapper.queryBillGroupByScode(vendor, billingCycle, account);
    }

    @Override
    public List<DumpedDetailBillVo> queryDetailBill(String vendor, String billingCycle, String... account) {
        return dumpedBillMapper.queryDetailBill(vendor, billingCycle, account);
    }
}
