package com.haier.devops.bill.common.api.entity;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: SubProduct
* @Description: Hds子产品
* @author: 张爱苹
* @date: 2024/1/11 16:24
*/
@Data
public class SubProduct implements Serializable {
    private static final long serialVersionUID = -6110929574708516147L;
    /**
     * 应用S码
     */
    private String alm_scode;

    /**
     * 应用简称
     */
    private String alm_code;

    /**
     * 应用名称
     */
    private String alm_name;

    /**
     * 所属领域，多个用逗号隔开
     */
    private String business_domain_ids;

    /**
     * 领域信息数组
     */
    private String business_domains;

    /**
     * 产品描述信息
     */
    private String description;

    /**
     * IT产品经理
     */
    private String it_manager_name;

    /**
     * IT产品经理（工号）
     */
    private String it_manager;

    /**
     * IT运维经理
     */
    private String it_ops_manager;

    /**
     * IT运维经理
     */
    private String it_ops_manager_name;

    /**
     * 业务负责人（工号）
     */
    private String owner;

    /**
     * 业务产品经理
     */
    private String owner_name;

    /**
     * 所属部门
     */
    private String org_id;

    /**
     * 产品id
     */
    private String product_id;
    /**
     * 产品名称
     */
    private String  product_name;
    /**
     * 子产品id
     */
    private String puid;
    /**
     * 子产品名称
     */
    private String name;
    /**
     * 子产品英文名称
     */
    private String name_en;
}
