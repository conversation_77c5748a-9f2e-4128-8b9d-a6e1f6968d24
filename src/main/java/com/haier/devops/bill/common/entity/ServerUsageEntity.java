package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 服务器使用效能实体类
 */
@Data
@TableName("server_usage")
public class ServerUsageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 记录日期
     */
    @TableField("day")
    private LocalDate day;

    /**
     * 领域
     */
    @TableField("domain")
    private String domain;

    /**
     * 领域负责人
     */
    @TableField("domain_owner")
    private String domainOwner;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 子产品名称
     */
    @TableField("sub_product_name")
    private String subProductName;

    /**
     * 子产品负责人
     */
    @TableField("sub_product_owner")
    private String subProductOwner;

    /**
     * S码
     */
    @TableField("s_code")
    private String sCode;

    /**
     * 云厂商
     */
    @TableField("cloud_vendor")
    private String cloudVendor;

    /**
     * 服务器实例ID
     */
    @TableField("instance_id")
    private String instanceId;

    /**
     * 所属区域
     */
    @TableField("region")
    private String region;

    /**
     * 服务器实例IP
     */
    @TableField("instance_ip")
    private String instanceIp;

    /**
     * 资源利用率是否达标
     */
    @TableField("resource_usage_qualified")
    private String resourceUsageQualified;

    /**
     * CPU利用率达标
     */
    @TableField("cpu_usage_qualified")
    private String cpuUsageQualified;

    /**
     * 近30日CPU峰值利用率
     */
    @TableField("cpu_peak_usage_30days")
    private BigDecimal cpuPeakUsage30days;

    /**
     * CPU数量
     */
    @TableField("cpu_count")
    private String cpuCount;

    /**
     * 内存利用率达标
     */
    @TableField("memory_usage_qualified")
    private String memoryUsageQualified;

    /**
     * 近30日内存峰值利用率
     */
    @TableField("memory_peak_usage_30days")
    private BigDecimal memoryPeakUsage30days;

    /**
     * 内存数量
     */
    @TableField("memory_size")
    private String memorySize;

    /**
     * 是否备案
     */
    @TableField("is_registered")
    private String isRegistered;

    /**
     * 备案原因
     */
    @TableField("registration_reason")
    private String registrationReason;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
