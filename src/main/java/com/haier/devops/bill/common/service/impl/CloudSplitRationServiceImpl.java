package com.haier.devops.bill.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.CloudSplitRatio;
import com.haier.devops.bill.common.mapper.CloudSplitRatioMapper;
import com.haier.devops.bill.common.service.CloudSplitRatioService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@DS("clouddb")
public class CloudSplitRationServiceImpl
        extends ServiceImpl<CloudSplitRatioMapper, CloudSplitRatio>
        implements CloudSplitRatioService {
    private CloudSplitRatioMapper cloudSplitRatioMapper;

    public CloudSplitRationServiceImpl(CloudSplitRatioMapper cloudSplitRatioMapper) {
        this.cloudSplitRatioMapper = cloudSplitRatioMapper;
    }

    @Override
    public List<CloudSplitRatio> selectByAccountAndCostUnit(String account, String costUnit) {
        return cloudSplitRatioMapper.selectList(new LambdaQueryWrapper<CloudSplitRatio>()
                .eq(CloudSplitRatio::getAccount, account)
                .eq(CloudSplitRatio::getCostUnit, costUnit));
    }
}
