package com.haier.devops.bill.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DayBillAnalysisVo {
    private String vendor;
    private String billingCycle;
    private String total;
    private String month;

    public DayBillAnalysisVo(String formattedDate, String toPlainString,String month) {
        this.billingCycle = formattedDate;
        this.total = toPlainString;
        this.month = month;
    }
}
