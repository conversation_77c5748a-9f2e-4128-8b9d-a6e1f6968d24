package com.haier.devops.bill.common.mapper;

import com.haier.devops.bill.adjustment.vo.AdjustmentRange;
import com.haier.devops.bill.common.entity.BillAcceptanceResource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账单确认验收资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface BillAcceptanceResourceMapper extends BaseMapper<BillAcceptanceResource> {
    /**
     * 查询指定id、指定时间范围内的非存疑的验收单
     * @param aggregatedIds
     * @param range
     * @return
     */
    List<BillAcceptanceResource> selectBillsNotReservedAmongIdBetweenDate(@Param("aggregatedIds") List<String> aggregatedIds,
                                                                          @Param("range") AdjustmentRange... range);
}
