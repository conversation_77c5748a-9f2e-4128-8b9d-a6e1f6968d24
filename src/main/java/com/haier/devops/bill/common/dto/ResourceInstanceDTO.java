package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: ResourceInstanceDTO
* @Description:  资源实例
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class ResourceInstanceDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 3805454647376554852L;

    /**
     * 云厂商
     */
    @NotEmpty
    private String vendor;

    /**
     * 规则类型
     */
    @NotEmpty
    private String ruleType;


    /**
     * 云账户
     */
    @NotEmpty
    private String accountName;

    /**
     * 商品名称
     */
    @NotEmpty
    private String commodityCode;

    /**
     * 规则字段
     */
    @NotEmpty
    private String ruleField;


    /**
     * 资源实例id
     */
    @NotEmpty
    private String ruleFieldValue;

    /**
     * 调账类型
     */
    @NotEmpty
    private String reconciliationType;

    private String scode;

}
