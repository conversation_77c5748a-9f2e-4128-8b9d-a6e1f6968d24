package com.haier.devops.bill.common.config;

import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 静态的 ApplicationContext 持有者
 * <AUTHOR>
 */
@Configuration
public class ContextHolderConfig {
    private static ApplicationContext context;

    @Bean
    public static ApplicationContextHolder applicationContextHolder() {
        return new ApplicationContextHolder();
    }

    public static class ApplicationContextHolder {
        public void setApplicationContext(ApplicationContext applicationContext) {
            context = applicationContext;
        }

        public static ApplicationContext getContext() {
            return context;
        }
    }
}
