package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.AggregatedDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.ReconciliationDTO;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.enums.*;
import com.haier.devops.bill.common.mapper.AggregatedBillMapper;
import com.haier.devops.bill.common.mapper.CmdbProductOverviewMapper;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.util.ListSplitter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
* @ClassName: AggregatedBillServiceImpl
* @Description:  账单明细汇总表 服务实现类
* @author: 张爱苹
* @date: 2024/1/31 10:23
*/
@Service
@Slf4j
public class AggregatedServiceImpl extends ServiceImpl<AggregatedBillMapper, AggregatedBill> implements AggregatedService {
    private Logger logger = LoggerFactory.getLogger(AggregatedServiceImpl.class);

    @Autowired
    private AdjustmentRecordService adjustmentRecordService;

    @Autowired
    private CmdbProductOverviewMapper cmdbProductOverviewMapper;
    @Autowired
    private ReconciliationTaskService reconciliationTaskService;

    @Autowired
    private CmdbProductOverviewService cmdbProductOverviewService;

    @Autowired
    private CloudCostunitMoveRuleService cloudCostunitMoveRuleService;

    @Autowired
    private AggregatedResolveService aggregatedResolveService;

    @Async("localBootAsyncExecutor")
    public void executeReconciliation(AggregatedDTO dto,ResourceInstanceDTO resourceInstanceDTO,String vendor) {
        String reconciliationStatus = ReconciliationStatusEnum.DONE.getKey();
        List<ReconciliationTask> taskList = new ArrayList<>();
        try{
            String appScode = dto.getAppScode();
            String aggregatedId = dto.getAggregatedId();
            //调账
            List<ReconciliationDTO> list = dto.getList();
            baseMapper.updateBatch(list);
            String startBillingCycle = list.get(0).getStartDate();
            String endBillingCycle = list.get(list.size() - 1).getEndDate();
            Integer adjustmentId = dto.getRecordList().get(0).getId();
            //月度重新汇总
            aggregatedResolveService.triggerAggregationTaskAmongAggregatedId(vendor,startBillingCycle.substring(0,7),endBillingCycle.substring(0,7),aggregatedId);
            //如果最新日期的S码发生变化，则更新cmdb的S码
            if (!dto.getScode().equals(appScode)) {
                cmdbProductOverviewMapper.updateCmdbAggregatedBill(appScode, aggregatedId);
                if(dto.getVendor().equals(VendorEnum.ALIYUN.getVendor())||dto.getVendor().equals(VendorEnum.ALIYUN_DEDICATED.getVendor())){
                    taskList.add(getReconciliationTask(adjustmentId,aggregatedId,appScode,ReconciliationTaskTypeEnum.COSTUNIT.getKey()));
                }else if(dto.getVendor().equals(VendorEnum.HUAWEI.getVendor())){
                   taskList.add(getReconciliationTask(adjustmentId,aggregatedId,appScode,ReconciliationTaskTypeEnum.ENTERPRISEPROJECT.getKey()));
                }
            }
            saveTask(taskList, resourceInstanceDTO);
        }catch (Exception e){
            reconciliationStatus = ReconciliationStatusEnum.FAIL.getKey();
            logger.error("调账失败",e);
        }
        String finalReconciliationStatus = reconciliationStatus;
        dto.getRecordList().stream().forEach(item ->item.setReconciliationStatus(finalReconciliationStatus));
        adjustmentRecordService.updateBatchById(dto.getRecordList());
    }

    @Async("localBootAsyncExecutor")
    public void executeReconciliation(ReconciliationBatchDTO dto,AggregatedDTO aggregatedDTO,String vendor) {
        List<String> newAggregatedIdList = aggregatedDTO.getAggregatedIdList();
        String reconciliationStatus = ReconciliationStatusEnum.DONE.getKey();
        try{
            List<ReconciliationTask> taskList = new ArrayList<>();
            //调账
            updateReconciliationBatch(dto,vendor);
            //更新cmdb
            if(!CollectionUtils.isEmpty(newAggregatedIdList)){
                dto.setAggregateIdList(newAggregatedIdList);
                //查cmdb中S码发生变化的
                taskList = getListByAggregateIdList(dto,aggregatedDTO);
            }
            ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
            if(resourceInstanceDTO != null){
                //规则类型
                String ruleType = resourceInstanceDTO.getRuleType();
                //调账类型
                String rType = resourceInstanceDTO.getReconciliationType();
                //规则类型是rule并且调账类型是全部 且调账范围包含全部账期 修改规则表
                if(ruleType.equals(RuleTypeEnum.RULE.getKey()) && ReconciliationEnum.ALL.getKey().equals(rType) && dto.isUpdate()){
                    //查规则表调账前S码
                    CloudCostunitMoveRule rule = cloudCostunitMoveRuleService.getMoveRule(dto);
                    if(!rule.getAppscode().equals(dto.getScode())){
                        //改规则表
                        rule.setAppscode(dto.getScode());
                        cloudCostunitMoveRuleService.updateById(rule);
                    }
                }
            }
            saveTask(taskList, resourceInstanceDTO);
        }catch (Exception e){
            reconciliationStatus = ReconciliationStatusEnum.FAIL.getKey();
            logger.error("执行调账失败!",e);
        }
        updateAdjustmentRecord(aggregatedDTO.getRecordList(), reconciliationStatus);
    }

    private void updateAdjustmentRecord(List<AdjustmentRecord> recordList, String finalReconciliationStatus) {
        try {
            List<List<AdjustmentRecord>> list = ListSplitter.splitList(recordList, 500);
            for (int i = 0; i < list.size(); i++) {
                List<AdjustmentRecord> subList = list.get(i);
                subList.stream().forEach(item ->item.setReconciliationStatus(finalReconciliationStatus));
                adjustmentRecordService.updateBatchById(subList);
            }
        }catch (Exception e){
            logger.error("更新调账记录失败",e);
            throw new RuntimeException("执行调账失败！");
        }

    }

    private List<ReconciliationTask> getListByAggregateIdList(ReconciliationBatchDTO dto,AggregatedDTO aggregatedDTO) {
        List<ReconciliationTask> taskList = new ArrayList<>();
        List<String> aggregatedIdList = dto.getAggregateIdList();
        List<AdjustmentRecord> recordList = aggregatedDTO.getRecordList();
        Map<String,AdjustmentRecord> adjustmentRecordMap = recordList.stream().collect(Collectors.toMap(AdjustmentRecord::getAggregatedId, item -> item));
        List<List<String>> list = ListSplitter.splitList(aggregatedIdList, 500);
        ExecutorService executorService = Executors.newFixedThreadPool(list.size());
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        AtomicInteger successCount = new AtomicInteger(0);
        for (int i = 0; i < list.size(); i++) {
            List<String> subList = list.get(i);
            executorService.submit(() ->{
                try{
                    dto.setAggregateIdList(subList);
                    List<CmdbProductOverview> arrayList = cmdbProductOverviewMapper.getListByAggregateIdList(dto);
                    if(!CollectionUtils.isEmpty(arrayList)){
                        arrayList.stream().forEach(item ->{
                            item.setScode(dto.getScode());
                            if(item.getVendor().equals(VendorEnum.ALIYUN.getVendor()) || item.getVendor().equals(VendorEnum.ALIYUN_DEDICATED.getVendor())){
                                AdjustmentRecord record = adjustmentRecordMap.get(item.getAggregatedId());
                                if( record != null){
                                    taskList.add(getReconciliationTask(record.getId(),item.getAggregatedId(),dto.getScode(),ReconciliationTaskTypeEnum.COSTUNIT.getKey()));
                                }
                            }else if(item.getVendor().equals(VendorEnum.HUAWEI.getVendor())){
                                AdjustmentRecord record = adjustmentRecordMap.get(item.getAggregatedId());
                                if( record != null){
                                    taskList.add(getReconciliationTask(record.getId(),item.getAggregatedId(),dto.getScode(),ReconciliationTaskTypeEnum.ENTERPRISEPROJECT.getKey()));
                                }
                            }
                        });
                        cmdbProductOverviewService.updateBatchById(arrayList);
                    }
                    successCount.getAndAdd(1);
                }catch (Exception e){
                    e.printStackTrace();
                }finally {
                    countDownLatch.countDown();
                }

            });

        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException("异常");
        }
        executorService.shutdown();
        if(successCount.get() != list.size()){
            throw new RuntimeException("异常");
        }
        return taskList;
    }

    private ReconciliationTask getReconciliationTask(Integer recordId,String aggregatedId,String scode,String type) {
        ReconciliationTask task = ReconciliationTask.builder().adjustmentId(recordId).aggregatedId(aggregatedId).type(type).scode(scode).build();
        return task;
    }

    private void updateReconciliationBatch(ReconciliationBatchDTO dto,String vendor) throws Exception{
        List<String> aggregatedIdList = dto.getAggregateIdList();
        List<List<String>> list = ListSplitter.splitList(aggregatedIdList, 500);
        ExecutorService executorService = Executors.newFixedThreadPool(list.size());
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        AtomicInteger  successCount = new AtomicInteger();
        for (int i = 0; i < list.size(); i++) {
            List<String> subList = list.get(i);
            executorService.submit(()->{
                try{
                    dto.setAggregateIdList(subList);
                    baseMapper.updateReconciliationBatch(dto);
                    //月度重新汇总
                    String startDate = null;
                    if(StringUtils.isNotEmpty(dto.getStartDate())){
                        startDate = dto.getStartDate().substring(0,7);
                    }
                    String endDate = null;
                    if(StringUtils.isNotEmpty(dto.getEndDate())){
                        endDate = dto.getEndDate().substring(0,7);
                    }
                    aggregatedResolveService.triggerAggregationTaskAmongAggregatedId(vendor,startDate,endDate,subList.toArray(new String[0]));
                    successCount.getAndAdd(1);
                }catch (Exception e){
                    e.printStackTrace();
                }finally {
                    countDownLatch.countDown();
                }

            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException("异常");
        }
        executorService.shutdown();
        if(successCount.get() != list.size()){
            throw new RuntimeException("异常");
        }
    }

    private void saveTask(List<ReconciliationTask> taskList, ResourceInstanceDTO resourceInstanceDTO) {
        if(taskList.size() > 0){
            List<ReconciliationTask> allList = new ArrayList<>();
            //更新财务单元
            allList.addAll(gennerTask(null, taskList));
            if (resourceInstanceDTO != null) {
                //如果最新日期S码发生变化，且规则类型为原生字段，且规则字段为标签，则需要更新标签
                if (RuleTypeEnum.NATIVEFIELD.getKey().equals(resourceInstanceDTO.getRuleType())) {
                    String type = "";
                    if (RuleFieldEnum.RESOURCETAG.getKey().equals(resourceInstanceDTO.getRuleField())) {
                        type = ReconciliationTaskTypeEnum.TAG.getKey();
                        allList.addAll(gennerTask(type, taskList));
                    } else if (RuleFieldEnum.RESOURCE_GROUP.getKey().equals(resourceInstanceDTO.getRuleField())) {
                        allList.addAll(gennerGroupTask(resourceInstanceDTO.getRuleFieldValue(),allList.get(0).getScode(),resourceInstanceDTO.getAccountName()));
                    }
                }
            }
            if (!CollectionUtils.isEmpty(allList)) {
                reconciliationTaskService.saveOrUpdateBatch(allList);
            }
        }
    }

    private List<ReconciliationTask> gennerGroupTask(String ruleFieldValue, String scode,String accountName) {
        List<ReconciliationTask> allList = new ArrayList<>();
        String originalScode = "默认资源组";
        if(!"默认资源组".equals(ruleFieldValue)){
            String[] parts = ruleFieldValue.split("-");
            originalScode = parts[parts.length-1];
        }
        ReconciliationTask task = ReconciliationTask.builder().type(ReconciliationTaskTypeEnum.RESOURCEGROUP.getKey())
                .originalScode(originalScode)
                .account(accountName)
                .status(0)
                .build();
        ReconciliationTask oldTask = reconciliationTaskService.getOne(new QueryWrapper<>(task));
        task.setScode(scode);
        if (oldTask != null) {
            if (!oldTask.getScode().equals(scode)) {
                task.setId(oldTask.getId());
                task.setUpdateTime(new Date());
                allList.add(task);
            }
        }else{
            allList.add(task);
        }
        return allList;
    }

    private List<ReconciliationTask> gennerTask(String finalType, List<ReconciliationTask> taskList) {
        List<ReconciliationTask> allList = new ArrayList<>();
        taskList.stream().forEach(reconciliationTask -> {
            ReconciliationTask task = ReconciliationTask.builder().aggregatedId(reconciliationTask.getAggregatedId()).type(finalType).status(0).build();
            if (finalType == null) {
                task.setType(reconciliationTask.getType());
            }else{
                reconciliationTask.setType(finalType);
            }
            ReconciliationTask oldTask = reconciliationTaskService.getOne(new QueryWrapper<>(task));
            if (oldTask != null) {
                oldTask.setUpdateTime(new Date());
                oldTask.setStatus(3);
                allList.add(oldTask);
            }
            allList.add(reconciliationTask);
        });
        return allList;
    }


}
