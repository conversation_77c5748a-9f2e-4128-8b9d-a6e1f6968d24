package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import com.haier.devops.bill.common.vo.NodeDetailBillsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (RefinedRawBill)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-29 10:54:40
 */
public interface RefinedRawBillMapper extends BaseMapper<RefinedRawBill>{
    List<BillingSummaryVo> getBillingSummaryData(@Param("vendor")String vendor, @Param("accountName")String accountName, @Param("billingCycle")String billingCycle);

    Integer getBillingSummaryCount(@Param("vendor")String vendor, @Param("accountName")String accountName, @Param("billingCycle")String billingCycle);

    IPage<NodeDetailBillsVo> getDetailBillByType(IPage<NodeDetailBillsVo> page, @Param(Constants.WRAPPER) QueryWrapper<NodeDetailBillsVo> queryWrapper);


    /**
     * 获取待聚合的账单ID
     * @param vendor
     * @param accountName
     * @param billingCycle
     * @return
     */
    List<String> getPendingAggregatedId(@Param("vendor")String vendor,
                                        @Param("accountName")String accountName,
                                        @Param("billingCycle")String billingCycle);

    /**
     * 获取聚合后的账单数据
     * @param vendor
     * @param accountName
     * @param billingCycle
     * @param aggregatedIds
     * @return
     */
    List<BillingSummaryVo> getSummaryDataWithAggregatedIds(@Param("vendor")String vendor,
                                                           @Param("accountName")String accountName,
                                                           @Param("billingCycle")String billingCycle,
                                                           @Param("aggregatedIds")List<String> aggregatedIds);

    /**
     * 根据汇总id查询明细
     * @param aggregatedId
     * @return
     */
    List<RefinedRawBill> getByAggregatedId(String aggregatedId);

    /**
     * 批量更新
     * @param bills
     * @return
     */
    int batchUpdateById(@Param("bills") List<RefinedRawBill> bills);


    /**
     * 按日汇总
     * @param aggregatedId
     * @return
     */
    List<AggregatedBill> getAggregatedInDay(String aggregatedId);

    /**
     * 按月汇总
     * @param aggregatedId
     * @return
     */
    List<AggregatedBill> getAggregatedInMonth(String aggregatedId);
}

