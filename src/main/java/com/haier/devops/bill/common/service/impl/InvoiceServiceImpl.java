package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.Invoice;
import com.haier.devops.bill.common.mapper.InvoiceMapper;
import com.haier.devops.bill.common.service.InvoiceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
public class InvoiceServiceImpl extends ServiceImpl<InvoiceMapper, Invoice> implements InvoiceService {
    private final InvoiceMapper invoiceMapper;

    public InvoiceServiceImpl(InvoiceMapper invoiceMapper) {
        this.invoiceMapper = invoiceMapper;
    }

    @Override
    public List<Invoice> getInvoiceList(String vendor, String billingCycle, String... account) {
        return invoiceMapper.getInvoiceList(vendor, billingCycle, account);
    }
}
