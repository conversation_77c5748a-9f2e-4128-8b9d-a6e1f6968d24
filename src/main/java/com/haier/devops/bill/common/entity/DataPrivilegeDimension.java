package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据权限维度表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@TableName("data_privilege_dimension")
public class DataPrivilegeDimension implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 维度编码，例如：domain
     */
    private String dimensionCode;

    /**
     * 维度名称，例如：领域
     */
    private String dimensionName;

    /**
     * 维度描述
     */
    private String description;

    /**
     * 0:未删除，1:已删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
