package com.haier.devops.bill.common.enums;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 产品区域
 * @Author: A0018437
 * @Date：2023-12-28
 */
public enum RegionEnum {
    qingdao("华北1（青岛）", Arrays.asList("cn-qingdao",".cn-qingdao.aliyuncs.com")),
    beijing("华北2（北京）", Arrays.asList("cn-beijing",".cn-beijing.aliyuncs.com")),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("华北3（张家口）", Arrays.asList("cn-zhangjiakou",".cn-zhangjiakou.aliyuncs.com")),
    zhengzhou("郑州（联通合营）", Arrays.asList("cn-zhengzhou-jva",".cn-zhengzhou-jva.aliyuncs.com")),
    huh<PERSON><PERSON><PERSON>("华北5（呼和浩特）", Arrays.asList("cn-huhehaote",".cn-huhehaote.aliyuncs.com")),
    w<PERSON><PERSON><PERSON><PERSON>("华北6（乌兰察布）", Arrays.asList("cn-wulanchabu",".cn-wulanchabu.aliyuncs.com")),
    hangzhou("华东1（杭州）", Arrays.asList("cn-hangzhou",".aliyuncs.com")),
    shanghai("华东2（上海）", Arrays.asList("cn-shanghai",".cn-shanghai.aliyuncs.com")),
    nanjing("华东5（南京-本地地域）", Arrays.asList("cn-nanjing","-smarthosting.cn-hangzhou-cloudstone.aliyuncs.com")),
    fuzhou("华东6（福州-本地地域）", Arrays.asList("cn-fuzhou",".cn-fuzhou.aliyuncs.com")),
    shenzhen("华南1（深圳）", Arrays.asList("cn-shenzhen",".cn-shenzhen.aliyuncs.com")),
    heyuan("华南2（河源）", Arrays.asList("cn-heyuan",".cn-heyuan.aliyuncs.com")),
    guangzhou("华南3（广州）", Arrays.asList("cn-guangzhou",".cn-guangzhou.aliyuncs.com")),
    chengdu("西南1（成都）", Arrays.asList("cn-chengdu",".cn-chengdu.aliyuncs.com")),
    hongkong("中国香港", Arrays.asList("cn-hongkong",".cn-hongkong.aliyuncs.com")),
    tokyo("日本（东京）", Arrays.asList("ap-northeast-1",".ap-northeast-1.aliyuncs.com")),
    seoul("韩国（首尔）", Arrays.asList("ap-northeast-2",".ap-northeast-2.aliyuncs.com")),
    ingapore("新加坡", Arrays.asList("ap-southeast-1",".aliyuncs.com")),
    ydney("澳大利亚（悉尼）", Arrays.asList("ap-southeast-2",".ap-southeast-2.aliyuncs.com")),
    kualaLumpur("马来西亚（吉隆坡）", Arrays.asList("ap-southeast-3",".ap-southeast-3.aliyuncs.com")),
    jakarta("印度尼西亚（雅加达）", Arrays.asList("ap-southeast-5",".ap-southeast-5.aliyuncs.com")),
    manila("菲律宾（马尼拉）", Arrays.asList("ap-southeast-6",".ap-southeast-6.aliyuncs.com")),
    wuhan("华中1（武汉-本地地域）", Arrays.asList("cn-wuhan-lr",".cn-wuhan-lr.aliyuncs.com")),
    bangkok("泰国（曼谷）", Arrays.asList("ap-southeast-7",".ap-southeast-7.aliyuncs.com"));

    private final String name;

    private final List<String> region;

    RegionEnum(String name, List<String> region){
        this.name = name;
        this.region = region;
    }

    // 根据键获取值的静态方法
    public static List<String> getRegion(String key) {
        if (Strings.isNotBlank(key)){
            for (RegionEnum enumValue : RegionEnum.values()) {
                if (enumValue.name.equals(key)) {
                    return enumValue.getValue();
                }
            }
        }
        return Arrays.asList("cn-qingdao",".cn-qingdao.aliyuncs.com"); // 如果未找到匹配的键，则返回null或者其他适当的值
    }

    private List<String> getValue(){
        return region;
    }
}
