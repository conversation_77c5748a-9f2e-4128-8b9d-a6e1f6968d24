package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.haier.devops.bill.common.entity.RawBillProducts;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
* @ClassName: RawBillProductsMapper
* @Description: 云产品 mapper
* @author: 张爱苹
* @date: 2024/1/15 16:05
*/
@Repository
public interface RawBillProductsMapper extends BaseMapper<RawBillProducts> {

    List<Map> queryList(@Param(Constants.WRAPPER) LambdaQueryWrapper<RawBillProducts> queryWrapper);
}
