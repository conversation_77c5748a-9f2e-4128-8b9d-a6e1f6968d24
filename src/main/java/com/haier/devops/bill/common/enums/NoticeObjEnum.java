package com.haier.devops.bill.common.enums;

/**
 * <AUTHOR>
 */

public enum NoticeObjEnum {
    GROUP("group","组"),//组
    INDIVIDUAL("individual","用户、群");//用户、群


    NoticeObjEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
