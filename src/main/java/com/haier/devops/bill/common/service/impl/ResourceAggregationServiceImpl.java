package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import com.haier.devops.bill.common.mapper.ResourceAggregationMapper;
import com.haier.devops.bill.common.service.ResourceAggregationService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 到资源的汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Service
public class ResourceAggregationServiceImpl extends ServiceImpl<ResourceAggregationMapper, ResourceAggregation> implements ResourceAggregationService {
    private final ResourceAggregationMapper aggregationMapper;

    private static final Logger log = LoggerFactory.getLogger(ResourceAggregationServiceImpl.class);

    public ResourceAggregationServiceImpl(ResourceAggregationMapper aggregationMapper) {
        this.aggregationMapper = aggregationMapper;
    }

    @Override
    public List<ResourceAggregation> queryBillByResource(String vendor, String billingCycle, String... account) {
        return aggregationMapper.queryBillByResource(vendor, billingCycle, account);
    }

    @Override
    public List<ResourceAggregation> selectByApplication(ApplicationAggregation aggregation) {
        LambdaQueryWrapper<ResourceAggregation> queryWrapper = new LambdaQueryWrapper<ResourceAggregation>()
                .eq(ResourceAggregation::getPayDate, aggregation.getPayDate())
                .eq(ResourceAggregation::getSysCode, aggregation.getSysCode());

        if (StringUtils.isNotBlank(aggregation.getFactory())) {
            queryWrapper.eq(ResourceAggregation::getFactory, aggregation.getFactory());
        } else {
            queryWrapper.isNull(ResourceAggregation::getFactory);
        }

        if (StringUtils.isNotBlank(aggregation.getAccount())) {
            queryWrapper.eq(ResourceAggregation::getAccount, aggregation.getAccount());
        } else {
            queryWrapper.isNull(ResourceAggregation::getAccount);
        }

        return aggregationMapper.selectList(queryWrapper);
    }

    @Override
    public List<ResourceAggregation> selectByApplications(List<ApplicationAggregation> aggregations) {
        return aggregationMapper.selectByApplications(aggregations);
    }

    @Override
    public void deleteByVendorAndBillingCycleAndStageAndAccount(String vendor, String billingCycle, String... account) {
        aggregationMapper.deleteByVendorAndBillingCycleAndStageAndAccount(vendor, billingCycle, account);
        log.info("Deleted ResourceAggregations for vendor: {}, billingCycle: {}, stage: {}, accounts: {}", vendor, billingCycle, Arrays.toString(account));
    }

    @Override
    public List<ResourceAggregation> selectPendingSyncingItems(String vendor, String billingCycle, String... account) {
        return aggregationMapper.selectPendingSyncingItems(vendor, billingCycle, account);
    }
}
