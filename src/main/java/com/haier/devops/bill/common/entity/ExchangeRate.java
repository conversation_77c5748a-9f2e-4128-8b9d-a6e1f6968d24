package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: ExchangeRate
 * @Description: 汇率实体类
 * @author: System
 * @date: 2025/06/30
 */
@Data
@TableName("bc_exchange_rate")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="汇率信息")
public class ExchangeRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**基础货币代码*/
    @Schema(description = "基础货币代码")
    private String baseCurrency;

    /**目标货币代码*/
    @Schema(description = "目标货币代码")
    private String targetCurrency;

    /**汇率值*/
    @Schema(description = "汇率值（1基础货币=多少目标货币）")
    private BigDecimal exchangeRate;

    /**汇率日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "汇率日期")
    private Date rateDate;

    /**API最后更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "API最后更新时间")
    private Date apiUpdateTime;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**状态*/
    @Schema(description = "状态：1-有效；0-无效")
    private Integer status;
}
