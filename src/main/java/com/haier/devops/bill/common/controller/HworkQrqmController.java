
package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.api.entity.CustomResponse;
import com.haier.devops.bill.common.dto.HworkQrqmDTO;
import com.haier.devops.bill.common.service.HworkQrqmService;
import com.haier.devops.bill.util.HworkAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequestMapping(value = "/api/v1/hcms/bill/qrqm")
@RestController
@Slf4j
public class HworkQrqmController  {

    @Autowired
    private HworkQrqmService hworkQrqmService;

    @PostMapping("/getMqMonitorData")
    public CustomResponse getDbMonitorData(@RequestBody HworkQrqmDTO dto){
        log.info("获取中间件汇总数据开始");
        String userId = HworkAuthUtil.getUserId();
        if(StringUtils.isEmpty(userId)){
            log.error("用户为空！");
            return CustomResponse.fail(500, "获取中间件汇总数据失败");
        }
        dto.setUserId(userId);
        try{
            Map map = hworkQrqmService.getDbMonitorData(dto);
            log.info("获取中间件汇总数据结束");
            return CustomResponse.ok(map);
        }catch (Exception e){
            e.printStackTrace();
            return CustomResponse.fail(500, "获取中间件汇总数据失败");
        }
    }
}
