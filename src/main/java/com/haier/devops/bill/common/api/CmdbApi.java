package com.haier.devops.bill.common.api;

import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.common.api.interceptor.FeignInterceptor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Description: cmdbapi
 * @Author: A0018437
 * @Date：2024-01-05
 */
@FeignClient(name = "cmdbApi", url = "${api.hds.user.url}", path = "/console/cmdb-new/api/v2/projects", configuration = FeignInterceptor.class)
public interface CmdbApi {
    @GetMapping
    JSONObject getUserInfo();
}