package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupCreateDTO;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupDTO;
import com.haier.devops.bill.common.entity.AlarmNoticeGroup;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.entity.CpAlarmRuleDetail;
import com.haier.devops.bill.common.enums.NoticeObjEnum;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.AlarmNoticeGroupMapper;
import com.haier.devops.bill.common.mapper.CpAlarmRuleDetailMapper;
import com.haier.devops.bill.common.service.AlarmNoticeGroupDetailService;
import com.haier.devops.bill.common.service.AlarmNoticeGroupService;
import com.haier.devops.bill.common.service.AlarmUserInfoService;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupDetailVo;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupVo;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import com.haier.devops.bill.util.AesUtil;
import com.haier.devops.bill.util.MobileUtil;
import com.haier.devops.bill.util.RegularUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* @ClassName: AlarmNoticeGroupServiceImpl
* @Description:  告警通知组 服务实现类
* @author: 张爱苹
* @date: 2024/1/12 17:23
*/
@Service
@Slf4j
public class AlarmNoticeGroupServiceImpl extends ServiceImpl<AlarmNoticeGroupMapper, AlarmNoticeGroup> implements AlarmNoticeGroupService {
    private Logger logger = LoggerFactory.getLogger(AlarmNoticeGroupServiceImpl.class);
    @Autowired
    private AlarmNoticeGroupMapper alarmNoticeGroupMapper;
    @Autowired
    private AlarmNoticeGroupDetailService alarmNoticeGroupDetailService;
    @Autowired
    private CpAlarmRuleDetailMapper cpAlarmRuleDetailMapper;
    @Autowired
    private AlarmUserInfoService alarmUserInfoService;

    @Autowired
    private AuthorityService authorityService;

    @Override
    public List getUserPhone(String account) throws Exception{
        List phoneList = new ArrayList<>();
        String[] accountArray = account.split(",");
        for (String obj : accountArray) {
            String phone = authorityService.getUserPhoneByUserCode(obj);
            phoneList.add(phone);
        }
        return phoneList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAlarmNoticeGroup(Integer id) throws Exception{
        AlarmNoticeGroup alarmNoticeGroup = getAlarmNoticeGroup(id);
        //查询该告警组是否已经关联云产品告警配置，存在则不能删除
        int count = cpAlarmRuleDetailMapper.selectCount(new LambdaQueryWrapper<CpAlarmRuleDetail>()
                .eq(CpAlarmRuleDetail::getNoticeObjId, id).eq(CpAlarmRuleDetail::getNoticeObj, NoticeObjEnum.GROUP.getKey())).intValue();
        if (count > 0) {
            throw new Exception("该告警组已关联云产品告警配置，不能删除");
        }
        alarmNoticeGroup.setUpdateTime(new Date());
        alarmNoticeGroup.setDelFlag("1");
        User currentUser = LoginContextHolder.getCurrentUser();
        alarmNoticeGroup.setUpdateBy(currentUser.getUserCode());
        updateById(alarmNoticeGroup);
        //删除告警组明细表
        alarmNoticeGroupDetailService.remove(new LambdaQueryWrapper<AlarmNoticeGroupDetail>().eq(AlarmNoticeGroupDetail::getAlarmNoticeGroupId, id));
    }

    private AlarmNoticeGroup getAlarmNoticeGroup(Integer id) throws Exception{
        AlarmNoticeGroup alarmNoticeGroup = alarmNoticeGroupMapper.selectOne(new LambdaQueryWrapper<AlarmNoticeGroup>().eq(AlarmNoticeGroup::getId, id).eq(AlarmNoticeGroup::getDelFlag, "0"));
        if(alarmNoticeGroup == null){
            throw new RuntimeException("告警通知组不存在");
        }
        return alarmNoticeGroup;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAlarmNoticeGroup(Integer id, AlarmNoticeGroupCreateDTO alarmNoticeGroupCreateDTO) throws Exception{
        try{
            alarmNoticeGroupCreateDTO.setId(id);
            //清空告警组明细表
            alarmNoticeGroupDetailService.deleteAlarmNoticeGroupDetailByGroupId(id);
            alarmNoticeGroupCreateDTO.setUpdateTime(new Date());
            User currentUser = LoginContextHolder.getCurrentUser();
            alarmNoticeGroupCreateDTO.setUpdateBy(currentUser.getUserCode());
            saveOrUpdateAlarmNoticeGroupDetail(alarmNoticeGroupCreateDTO);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("updateAlarmNoticeGroup error:",e.getMessage(),e);
            throw new Exception("updateAlarmNoticeGroup error:"+e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAlarmNoticeGroup(AlarmNoticeGroupCreateDTO alarmNoticeGroupCreateDTO) throws Exception{
        try{
            User currentUser = LoginContextHolder.getCurrentUser();
            alarmNoticeGroupCreateDTO.setCreateBy(currentUser.getUserCode());
            saveOrUpdateAlarmNoticeGroupDetail(alarmNoticeGroupCreateDTO);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("createAlarmNoticeGroup error:",e.getMessage(),e);
            throw new Exception("createAlarmNoticeGroup error:"+e.getMessage());
        }

    }

    private void saveOrUpdateAlarmNoticeGroupDetail(AlarmNoticeGroupCreateDTO alarmNoticeGroupCreateDTO) throws Exception{
        try{
            AlarmNoticeGroup alarmNoticeGroup = new AlarmNoticeGroup();
            BeanUtils.copyProperties(alarmNoticeGroupCreateDTO, alarmNoticeGroup);
            saveOrUpdate(alarmNoticeGroup);
            //组id
            Integer groupId = alarmNoticeGroup.getId();
            //组员
            List<AlarmNoticeGroupDetail> alarmNoticeGroupDetailList = alarmNoticeGroupCreateDTO.getAlarmNoticeGroupDetailList();
            List<AlarmUserInfo> alarmUserInfoDetailList = new ArrayList<>();
            for(AlarmNoticeGroupDetail alarmNoticeGroupDetail : alarmNoticeGroupDetailList){
                alarmNoticeGroupDetail.setAlarmNoticeGroupId(groupId);
                getAlarmUserInfoDetailList(alarmNoticeGroupDetail, alarmUserInfoDetailList);
            }
            if(!CollectionUtils.isEmpty(alarmUserInfoDetailList)){
                //保存人员信息
                alarmUserInfoService.saveOrUpdateBatch(alarmUserInfoDetailList);
            }
            //保存组详情
            alarmNoticeGroupDetailService.saveOrUpdateBatch(alarmNoticeGroupDetailList);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("saveOrUpdateAlarmNoticeGroupDetail error:",e.getMessage(),e);
            throw new Exception(e.getMessage());
        }
    }

    private List<AlarmUserInfo> getAlarmUserInfoDetailList(AlarmNoticeGroupDetail alarmNoticeGroupDetail, List<AlarmUserInfo> alarmUserInfoDetailList) throws Exception{
        if(StringUtils.isEmpty(alarmNoticeGroupDetail.getMobile())
                || StringUtils.isEmpty(alarmNoticeGroupDetail.getEmail())
                || StringUtils.isEmpty(alarmNoticeGroupDetail.getName())){
            throw new RuntimeException(alarmNoticeGroupDetail.getAccount()+"人员信息不完整");
        }
        if(!RegularUtil.isEmail(alarmNoticeGroupDetail.getEmail())){
            throw new RuntimeException(alarmNoticeGroupDetail.getAccount()+"人员信息不合法,请检查邮箱");
        }

        AlarmUserInfo oldAlarmUserInfo = alarmUserInfoService.getAlarmUserInfoByAccount(alarmNoticeGroupDetail.getAccount());
        if(oldAlarmUserInfo != null && !StringUtils.isEmpty(oldAlarmUserInfo.getMobile()) && !StringUtils.isEmpty(oldAlarmUserInfo.getEmail())){
            //不处理
            return alarmUserInfoDetailList;
        }
        if(oldAlarmUserInfo != null && RegularUtil.isTmMobile(alarmNoticeGroupDetail.getMobile())){
            alarmNoticeGroupDetail.setMobile(oldAlarmUserInfo.getMobile());
        }else {
            //校验手机号是否合法
            if((!RegularUtil.isMobile(alarmNoticeGroupDetail.getMobile()))){
                throw new RuntimeException(alarmNoticeGroupDetail.getAccount()+"人员信息不合法,请检查手机号");
            }
            alarmNoticeGroupDetail.setMobile(AesUtil.encryptCBC(alarmNoticeGroupDetail.getMobile()));
        }
        AlarmUserInfo alarmUserInfoDetail = AlarmUserInfo.builder().build();
        BeanUtils
                .copyProperties(alarmNoticeGroupDetail, alarmUserInfoDetail);
        if(oldAlarmUserInfo != null){
            alarmUserInfoDetail.setId(oldAlarmUserInfo.getId());
        }
        alarmUserInfoDetailList.add(alarmUserInfoDetail);
        return alarmUserInfoDetailList;
    }

    private AlarmUserInfo getUserInfo(String account) {
        User user = authorityService.getUserByUserCode(account);
        if(user == null){
            return AlarmUserInfo.builder().account(account).build();
        }else{
            String phone = authorityService.getUserPhoneByUserCode(account);
            if(!StringUtils.isEmpty(phone)){
                phone = AesUtil.encryptCBC(phone);
            }
            return AlarmUserInfo.builder().account(account).name(user.getUserName()).email(user.getEmail()).mobile(phone).build();
        }
    }


    @Override
    public AlarmNoticeGroupVo getAlarmNoticeGroupById(Integer id) throws Exception{
        AlarmNoticeGroup alarmNoticeGroup = getAlarmNoticeGroup(id);
        List<AlarmNoticeGroupDetailVo>
                alarmNoticeGroupDetailList = alarmNoticeGroupDetailService.getAlarmNoticeGroupDetailListByGroupId(id);
        //遍历alarmNoticeGroupDetailList 对mobile重新赋值

        alarmNoticeGroupDetailList.stream().forEach(alarmNoticeGroupDetailVo ->alarmNoticeGroupDetailVo.setMobile(MobileUtil.blurPhoneNum(AesUtil.decryptCBC(alarmNoticeGroupDetailVo.getMobile()))));
        AlarmNoticeGroupVo alarmNoticeGroupVo = new AlarmNoticeGroupVo();
        BeanUtils.copyProperties(alarmNoticeGroup,alarmNoticeGroupVo);
        alarmNoticeGroupVo.setAlarmNoticeGroupDetailList(alarmNoticeGroupDetailList);
        return alarmNoticeGroupVo;
    }

    @Override
    public PageInfo<AlarmNoticeGroupVo> listByPage(AlarmNoticeGroupDTO dto) throws Exception{
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->{
                        List<AlarmNoticeGroupVo> list = alarmNoticeGroupMapper.listByPage(dto);
                        list.stream().forEach(alarmNoticeGroupVo ->{
                            List<AlarmNoticeGroupDetailVo>
                                    alarmNoticeGroupDetailList = alarmNoticeGroupDetailService.getAlarmNoticeGroupDetailListByGroupId(alarmNoticeGroupVo.getId());
                            //遍历alarmNoticeGroupDetailList 对mobile重新赋值
                            alarmNoticeGroupDetailList.stream().forEach(alarmNoticeGroupDetailVo ->alarmNoticeGroupDetailVo.setMobile(MobileUtil.blurPhoneNum(AesUtil.decryptCBC(alarmNoticeGroupDetailVo.getMobile()))));
                            alarmNoticeGroupVo.setAlarmNoticeGroupDetailList(alarmNoticeGroupDetailList);
                        });
                    }

            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, AlarmNoticeGroupDTO:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    public List<AlarmNoticeObjVo> getAlarmNoticeGroupByIdList(String[] noticeObjIdArray) {
        return baseMapper.getAlarmNoticeGroupByIdList(noticeObjIdArray);
    }

    @Override
    public AlarmUserInfo getAlarmNoticeObj(String account) {

        AlarmUserInfo oldAlarmUserInfo = alarmUserInfoService.getAlarmUserInfoByAccount(account);
        if(oldAlarmUserInfo == null || StringUtils.isEmpty(oldAlarmUserInfo.getMobile())
                || StringUtils.isEmpty(oldAlarmUserInfo.getEmail())){
            AlarmUserInfo alarmUserInfo = getUserInfo(account);
            if(oldAlarmUserInfo != null){
                alarmUserInfo.setId(oldAlarmUserInfo.getId());
            }
            alarmUserInfoService.saveOrUpdate(alarmUserInfo);
            oldAlarmUserInfo = AlarmUserInfo.builder().build();
            BeanUtils.copyProperties(alarmUserInfo, oldAlarmUserInfo);
        }
        String mobile = oldAlarmUserInfo.getMobile();
        if(!StringUtils.isEmpty(mobile)){
            oldAlarmUserInfo.setMobile(MobileUtil.blurPhoneNum(AesUtil.decryptCBC(mobile)));
        }
        return oldAlarmUserInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserInfo(List<AlarmNoticeGroupDetail> alarmUserInfoDTOList) throws Exception{
        List<AlarmUserInfo> alarmUserInfoDetailList = new ArrayList<>();
        for (AlarmNoticeGroupDetail alarmNoticeGroupDetail : alarmUserInfoDTOList) {
            getAlarmUserInfoDetailList(alarmNoticeGroupDetail, alarmUserInfoDetailList);
        }
        if(!CollectionUtils.isEmpty(alarmUserInfoDetailList)){
            alarmUserInfoService.saveOrUpdateBatch(alarmUserInfoDetailList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AlarmUserInfo getUpdateUserInfo(AlarmUserInfo alarmUserInfo) {
        AlarmUserInfo newAlarmUserInfo = getUserInfo(alarmUserInfo.getAccount());
        try{
            if (alarmUserInfo.getMobile().equals(newAlarmUserInfo.getMobile()) && alarmUserInfo.getEmail().equals(newAlarmUserInfo.getEmail())) {
                return null;
            }
            if(!StringUtils.isEmpty(newAlarmUserInfo.getEmail())||!StringUtils.isEmpty(newAlarmUserInfo.getMobile())){
                if(!StringUtils.isEmpty(newAlarmUserInfo.getEmail())){
                    alarmUserInfo.setEmail(newAlarmUserInfo.getEmail());
                }
                if(!StringUtils.isEmpty(newAlarmUserInfo.getMobile())){
                    alarmUserInfo.setMobile(newAlarmUserInfo.getMobile());
                }
                return alarmUserInfo;
            }
        }catch (Exception e){
            logger
                    .error("getUpdateUserInfo error,alarmUserInfo:{}",alarmUserInfo,e);
        }
        return null;
    }

    @Override
    public boolean checkGroupzName(String groupName) {
        List<AlarmNoticeGroup> alarmNoticeGroupList = list(new LambdaQueryWrapper<AlarmNoticeGroup>().eq(AlarmNoticeGroup::getGroupName, groupName).eq(AlarmNoticeGroup::getDelFlag, "0"));
        if(CollectionUtils.isEmpty(alarmNoticeGroupList)){
            return true;
        }
        return false;
    }
}
