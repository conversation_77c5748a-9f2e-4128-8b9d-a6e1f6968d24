package com.haier.devops.bill.common.controller;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.ServiceFreeCreateDTO;
import com.haier.devops.bill.common.dto.ServiceFreeDTO;
import com.haier.devops.bill.common.entity.ServiceFree;
import com.haier.devops.bill.common.service.ServiceFreeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* @ClassName: ServiceFreeController
* @Description: 服务费汇率 控制器
* @author: 张爱苹
* @date: 2024/3/8 13:19
*/
@Tag(name="服务费汇率")
@RestController
@RequestMapping("/api/v1/hcms/bill/serviceFree")
@Slf4j
public class ServiceFreeController{
    @Autowired
    private ServiceFreeService ServiceFreeService;

    /**
     * 分页查询所有服务费&汇率
     *
     * @return 服务费汇率列表
     */
    @GetMapping("/listByPage")
    public ResponseEntityWrapper<PageInfo<ServiceFree>> listByPage(@Validated ServiceFreeDTO dto)  {
        try{
            PageInfo<ServiceFree> pageInfo =
                    ServiceFreeService.listByPage(dto);
            return new ResponseEntityWrapper<>(pageInfo);
        }catch (Exception e){
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
    }

    /**
    * @Description: 创建服务费汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:29
    * @param dto:
    * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
    */
    @PostMapping("/create")
    public ResponseEntityWrapper createServiceFree(@RequestBody @Validated ServiceFreeCreateDTO dto){
        try {
            ServiceFreeService.createServiceFree(dto);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
    * @Description: 更新服务费汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:29
    * @param id:
    * @param dto:
    * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
    */
    @PutMapping("/update/{id}")
    public ResponseEntityWrapper updateServiceFree(@PathVariable("id") Integer id, @RequestBody @Validated ServiceFreeCreateDTO dto) {
        try {
            ServiceFreeService.updateServiceFree(id, dto);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }


   /**
   * @Description:  删除服务费汇率
   * @author: 张爱苹
   * @date: 2024/3/8 13:30
   * @param id:
   * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
   */
    @DeleteMapping("/delete/{id}")
    public ResponseEntityWrapper deleteServiceFree(@PathVariable("id") Integer id) {
        try {
            ServiceFreeService.deleteServiceFree(id);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }

}
