package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.BillTask;
import com.haier.devops.bill.common.mapper.BillTaskMapper;
import com.haier.devops.bill.common.service.BillTaskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (BillTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-28 13:17:21
 */
@Service("bcBillTaskService")
public class BillTaskServiceImpl extends ServiceImpl<BillTaskMapper, BillTask> implements BillTaskService {
    @Resource
    private BillTaskMapper billTaskMapper;

    
}
