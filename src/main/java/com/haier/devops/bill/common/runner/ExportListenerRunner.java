package com.haier.devops.bill.common.runner;

import com.haier.devops.bill.common.task.ExportLogListener;
import com.haier.devops.bill.common.task.ExportTask;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExportListenerRunner implements CommandLineRunner {
    private static Logger logger = LoggerFactory.getLogger(ExportListenerRunner.class);
    private final ExportTask exportTask;
    public ExportListenerRunner(ExportTask exportTask) {
        this.exportTask = exportTask;
    }

    @Autowired
    private ExportLogListener exportLogListener;

    @Override
    public void run(String... args) {
        exportTask.execute();
        Thread listenerThread = new Thread(new ExportLogListenRunner());
        listenerThread.start();
    }

    class ExportLogListenRunner implements Runnable {
        @Override
        public void run() {
            //try catch
            logger.info("*************[ExportLogListenRunner-监听器启动]**********");
            try{
                exportLogListener.listen();
                //   TimeUnit.MILLISECONDS.sleep(500);
            } catch (Exception e) {
                logger.error("*************[ExportLogListenRunner-监听器报错]**********");
                logger.error(e.getMessage());
            }
            logger.info("*************[ExportLogListenRunner-监听器结束]**********");
        }
    }
}
