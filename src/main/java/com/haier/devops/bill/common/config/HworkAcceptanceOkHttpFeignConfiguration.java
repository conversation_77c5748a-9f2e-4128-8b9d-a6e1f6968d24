package com.haier.devops.bill.common.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.haier.devops.bill.common.api.HworkAcceptanceApi;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * Hwork账单验收对接接口feign配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class HworkAcceptanceOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private static final String AUTHORIZATION_KEY = "Authorization";
    private final HdsBillCenterTokenConfigProperties billCenterTokenConfigProperties;

    private okhttp3.OkHttpClient hworkAcceptanceOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    public HworkAcceptanceOkHttpFeignConfiguration(HdsBillCenterTokenConfigProperties billCenterTokenConfigProperties,
                                                   OkHttpProperties properties,
                                                   OkHttpClientFactory httpClientFactory,
                                                   OkHttpClientConnectionPoolFactory connectionPoolFactory) {
        this.billCenterTokenConfigProperties = billCenterTokenConfigProperties;
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @Bean
    public HworkAcceptanceApi hworkAcceptanceApi() {
        this.hworkAcceptanceOkHttp3Client = setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);
        // 添加日志拦截器
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(message -> log.info(message));
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        this.hworkAcceptanceOkHttp3Client = hworkAcceptanceOkHttp3Client.newBuilder()
                .addInterceptor(authenticationInterceptor(AUTHORIZATION_KEY, billCenterTokenConfigProperties.getToken()))
                .addInterceptor(loggingInterceptor)
                .build();

        Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();

        return Feign.builder()
                .client(new feign.okhttp.OkHttpClient(hworkAcceptanceOkHttp3Client))
                .encoder(new GsonEncoder(gson))
                .decoder(new GsonDecoder(gson))
                .logger(new feign.Logger.JavaLogger().appendToFile("feign.log")) // 添加Feign的日志记录
                .logLevel(feign.Logger.Level.FULL) // 设置日志级别为FULL
                .target(HworkAcceptanceApi.class, billCenterTokenConfigProperties.getUrl());
    }

    @PreDestroy
    public void destroy() {
        super.destroy(this.hworkAcceptanceOkHttp3Client);
    }

}
