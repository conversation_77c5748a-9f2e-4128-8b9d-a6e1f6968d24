package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.haier.devops.bill.common.vo.BillChargesVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (BcRawBillProducts)实体类
 *
 * <AUTHOR>
 * @since 2024-01-10 13:35:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "$tableInfo.comment")
public class BcRawBillProducts implements Serializable {
    private static final long serialVersionUID = 890107151829747111L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "云厂商")
    private String vendor;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "通用产品编码")
    private String aliasCode;

    @Schema(description = "通用产品名称")
    private String aliasName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人账号")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人账号")
    private String updateBy;

    @Schema(description = "删除标识 0：有效 1：无效")
    private String delFlag;

    public BcRawBillProducts(BillChargesVo billChargesVo) {
        this.vendor = billChargesVo.getVendor();
        this.productCode = billChargesVo.getProductCode();
        this.productName = billChargesVo.getProductName();
        this.aliasCode = billChargesVo.getProductCode();
        this.aliasName = billChargesVo.getProductName();
    }

    public BcRawBillProducts() {
    }
}

