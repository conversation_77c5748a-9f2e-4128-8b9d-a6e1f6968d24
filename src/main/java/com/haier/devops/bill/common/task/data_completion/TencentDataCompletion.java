package com.haier.devops.bill.common.task.data_completion;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* @ClassName: TencentDataCompletion
* @Description: 腾讯云账单数据补全
* @author: 张爱苹
* @date: 2024/1/30 17:21
*/
@Service
@Slf4j
public class TencentDataCompletion {
//
//    @Resource
//    private RcHostInfoService rcHostInfoService;
//    @Resource
//    private RcDatabaseInfoService rcDatabaseInfoService;
//    @Resource
//    private RcMiddlewareInfoService rcMiddlewareInfoService;
//    @Resource
//    private RawBillService rawBillService;
//    @Resource
//    Executor localBootAsyncExecutor;
//    @Resource
//    private CommonUtil commonUtil;
//    @Autowired
//    static TencentPPMatchRulesService tencentPPMatchRulesService;
//
//    //子账号列表
//    static List<String> subAccountList = new ArrayList<>();
//    //主账号-产品列表
//    static List<String> rules = new ArrayList<>();
////    static {
////        //获取腾讯云产品项目匹配规则
////        List<TencentPPMatchRules>
////            tencentPPMatchRulesList = tencentPPMatchRulesService.list();
////        subAccountList = tencentPPMatchRulesList.stream().filter(entity -> StringUtils.isEmpty(entity.getBusinessCodeName())
////        ).map(entity -> {
////                    return new StringBuffer(entity.getAccountId()).append("-").append(entity.getAppScode()).toString();
////        }).collect(Collectors.toList());
////
////        rules = tencentPPMatchRulesList.stream().filter(entity -> !StringUtils.isEmpty(entity.getBusinessCodeName())).map(entity -> {
////             return new StringBuffer(entity.getAccountId()).append("-").append(entity.getBusinessCodeName()).append("-").append(entity.getAppScode()).toString();
////        }).collect(Collectors.toList());
////    }
//
//    /**
//    * @Description: 主机数据补全
//    * @author: 张爱苹
//    * @date: 2024/1/30 17:36
//    * @param rawBillList:
//    * @Return: void
//    */
//    public void basicDataByHost(List<RawBill> rawBillList) {
//        // 对每种产品的数据进行拆分
//        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
//        // 结果集
//        List<RawBill> res = new ArrayList<>();
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            Set<String> instanceIds = new HashSet<>();
//            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
//            instanceIds.remove(null);
//            // 数据补全
//            return hostInfo(item, instanceIds);
//        }, localBootAsyncExecutor)).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
//        rawBillService.updateRawBills(res);
//    }
//
//    /**
//    * @Description: 获取主机信息
//    * @author: 张爱苹
//    * @date: 2024/1/30 17:38
//     * @param item        要获取主机信息的原始订单列表
//     * @param instanceIds 要获取主机信息的实例ID集合
//    * @Return: java.util.List<com.haier.devops.bill.common.entity.RawBill>
//    */
//    public List<RawBill> hostInfo(List<RawBill> item, Set<String> instanceIds) {
//        if (instanceIds.size() > 0) {
//            // 获取rc_host_info的数据
//            List<RcHostInfo> hostInfos = rcHostInfoService.list(new LambdaQueryWrapper<RcHostInfo>().in(RcHostInfo::getInstanceId, instanceIds));
//            return item.stream().map(rawBill -> {
//                Optional<RcHostInfo> optional = hostInfos.stream().filter(rcHostInfo -> rcHostInfo.getInstanceId().equals(rawBill.getResourceId())).findFirst();
//
//                if (optional.isPresent()) {
//                    RcHostInfo hostInfo = optional.get();
//                    rawBill.setIp(hostInfo.getPrivateIp());
//                    rawBill.setInstanceId(hostInfo.getInstanceId());
//                    rawBill.setInstanceName(hostInfo.getInstanceName());
//                }
//                //赋值账单项目和S码信息
//                getRawBillProjectInfo(rawBill);
//                return rawBill;
//            }).collect(Collectors.toList());
//        } else {
//            return projectInfo(item);
//        }
//    }
//
//    public List<RawBill> projectInfo(List<RawBill> item){
//        return item.stream().map(rawBill -> {
//            getRawBillProjectInfo(rawBill);
//            return rawBill;
//        }).collect(Collectors.toList());
//    }
//
//    private RawBill getRawBillProjectInfo(RawBill rawBill) {
//        rawBill.setScode(getScode(rawBill));
//        HdsOpenApi.Project projectInfo = commonUtil.getProjectInfo(rawBill.getScode());
//        if(projectInfo != null){
//            rawBill.setProjectCode(projectInfo.getId());
//            rawBill.setProjectName(projectInfo.getName());
//        }
//        return rawBill;
//    }
//
//    private String getScode(RawBill rawBill) {
//        String accountId =  rawBill.getAccountId();
//        String appScode = "";
//        //原始账单内容
//        JSONObject content = JSONObject.parseObject(rawBill.getContent());
//        //项目名称
//        String projectName = content.getString("projectName");
//        //拼接信息
//        String rule = new StringBuffer(accountId).append("-").append(projectName).append("-").toString();
//        //Tag
//        JSONArray tags = content.getJSONArray("Tags");
//        String tag = "";
//        if(CollectionUtils.isNotEmpty(tags)){
//            tag = tags.getJSONObject(0).getString("TagValue");
//        }
//        //1.如果tag=zabbix 则返回基础设施
//        //2.如果是子账号产生的账号，则返回对应的scode
//        //3.如果匹配规则，则返回对应的scode
//        //4.如果以上都不是，截取projectName里的S码
//        if(StringUtils.equals(tag,"zabbix")){
//            appScode = "基础设施";
//        }else if(subAccountList.contains(accountId)){
//            appScode = subAccountList.stream().filter(a -> StringUtils.contains(a,accountId)).findFirst().get().split("-")[1];
//        }else if(rules.contains(rule)){
//            appScode = rules.stream().filter(a -> StringUtils.contains(a,rule)).findFirst().get().split("-")[2];
//        }else{
//            try{
//                appScode = projectName.substring(0,projectName.indexOf("_"));
//            }catch (Exception e){
//                //以上规则都不是，则不处理；
//                log.error("appScode 补全失败，billId:{}",rawBill.getId());
//            }
//        }
//        return appScode;
//    }

}
