package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;

/**
 * <p>
 * 待替换账单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface PendingSubstitutionBillMapper extends BaseMapper<PendingSubstitutionBill> {
    /**
     * 获取替换任务的总金额
     * @param subTaskId
     * @return
     */
    String getSumBySubTaskId(String subTaskId);
}
