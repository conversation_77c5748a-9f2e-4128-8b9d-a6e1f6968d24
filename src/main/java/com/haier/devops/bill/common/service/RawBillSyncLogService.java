package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.RawBillSyncLog;

import java.util.List;

/**
 * <p>
 *  用于记录已经同步的账单信息  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface RawBillSyncLogService extends IService<RawBillSyncLog> {
    /**
     * 获取待汇总的账单记录
     * @param vendor
     * @return
     */
    List<RawBillSyncLog> getPendingAggregatedBillSyncLog(String vendor);
}
