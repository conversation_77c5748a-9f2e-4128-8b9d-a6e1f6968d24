package com.haier.devops.bill.common.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.YearMonth;
import java.time.format.DateTimeParseException;

public class YearMonthFormatValidator implements ConstraintValidator<YearMonthFormat, String> {
    @Override
    public void initialize(YearMonthFormat constraintAnnotation) {
        // No initialization needed
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(value)) {
            return false; // Allow null or empty value
        }

        try {
            YearMonth.parse(value); // Try parsing the value as YearMonth
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
