package com.haier.devops.bill.common.api;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.JsonObject;
import com.haier.devops.bill.common.dto.SendNotifyParam;
import com.haier.devops.bill.common.dto.SubProductsPageDto;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Date;
import java.util.List;

public interface HdsOpenApi {
    /**
     * 获取项目列表信息
     */
    @RequestLine("GET /console/cmdb-new/api/v2/projects")
    ResultWrapper queryProjectsInfo();

    /**
     * 获取项目列表信息
     */
    @RequestLine("GET /console/cmdb-new/api/v2/sub-products?page={page}&page_size={pageSize}")
    Result querSubProducts(@Param("page") Integer page, @Param("pageSize") Integer pageSize);

    /**
     * 获取项目信息
     */
    @RequestLine("GET /console/cmdbx/api/v4/project/getProjectDetail?almSCode={almSCode}")
    AlmProjectWrapper queryAlmProject(@Param("almSCode") String almSCode);


    @RequestLine(value="POST /api/nc/v1/send?almScode={almScode}")
    ResultWrapper sendNotify(@RequestBody List<SendNotifyParam> array, @Param("almScode") String almScode);

    /**
     * 获取子产品信息
     */
    @RequestLine("POST hwork/server-console-manager/gw/platform/platform/v1/application/pageOut")
    @Headers("Content-Type: application/json")
    ResultSubProduct getSubProducts(@RequestBody SubProductsPageDto subProductsPageDto);


    /**
     * 获取子产品信息
     */
    @RequestLine("POST hwork/server-console-manager/gw/platform/platform/v1/application/pageOut")
    @Headers("Content-Type: application/json")
    Object getSubProductsNew(@RequestBody SubProductsPageDto subProductsPageDto);

    /**
     * 获取领域信息
     */
    @RequestLine("GET hwork/server-console-manager/gw/platform/platform/v1/application/burying/domainList")
    DomainWrapper getDomainList();

    /**
     * @Description: 获取团队成员
     * @Author: ws
     * @Date: 2025/6/6 18:27
     * @Param: [str, authorization]
     * @Return: java.lang.String
     */
    @RequestLine("POST hw/server-console-manager/gw/platform/platform/v1/application/user/pageOpen")
    @Headers("Content-Type: application/json")
    JSONObject getTeamMember(@RequestBody JsonObject str);


    @Data
    class DomainWrapper {
        private int code;
        private String message;
        private List<Domain> data;
    }

    /**
     * 项目Pojo
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class Domain {

        private String code;

        private String name;

        private String domainCode;

        private String domainName;

    }

    @Data
    class ResultWrapper {
        private int code;
       private String msg;
        private List<Project> data;
    }

    @Data
    class AlmProjectWrapper {
        private int code;
        private String msg;
        private AlmProject data;
    }

    @Data
    class Result {
        private int code;
        private String msg;
        private JSONObject data;
    }

    @Data
    class ResultSubProduct {
        private int code;
        private String msg;
        private SubResult data;
    }
    /**
     * 项目Pojo
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class Project {
        /**
         * 应用编码
         */
        private String id;

        /**
         * 应用名称
         */
        private String name;

        /**
         * 应用描述
         */
        private String desc;

        /**
         * 领域
         */
        private String domain;

        /**
         * ALM子代码
         */
        private String almSCode;

        /**
         * 产品子代码
         */
        private String productSCode;

        /**
         * 产品ID
         */
        private String productId;

        /**
         * 产品名称
         */
        private String productName;

        /**
         * Git地址
         */
        private String gitAddr;

        /**
         * 组织ID
         */
        private String orgId;

        /**
         * 组织名称
         */
        private String orgName;

        /**
         * 类型
         */
        private String type;

        /**
         * 状态
         */
        private String status;

        /**
         * 开发语言
         */
        private String devLanguage;

        /**
         * 小微组织ID
         */
        private String smallMicroOrgId;

        /**
         * 所有者
         */
        private String owner;

        /**
         * 业务所有者
         */
        private String businessOwner;

        /**
         * 用途
         */
        private String purpose;

        /**
         * 源类型
         */
        private String srcType;

        /**
         * 登录地址
         */
        private String loginAddr;

        /**
         * 创建时间
         */
        private String created_at;

        /**
         * 更新时间
         */
        private String updated_at;

        /**
         * 是否已删除
         */
        @JSONField(name = "is_deleted")
        private int isDeleted;

        /**
         * 所有者姓名
         */
        private String ownerName;

        /**
         * 所有者电子邮件
         */
        private String ownerEmail;
    }


    @Data
    class SubResult {
        private Double total;
        private List<SubProduct> data;
        private Double totalPage;
        private Double pageSize;
        private Double page;
    }

    /**
     * 子产品Pojo
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class SubProduct {
        // 应用S码
        private String scode;
        // 应用简称
        private String applicationNameShort;
        // 子产品编码
        private String applicationCode;
        // 子产品名称
        private String applicationName;
        // 领域编码
        private String moduleDomainCode;
        // 领域名称
        private String moduleDomainName;
        // 应用描述
        private String applicationDesc;
        // 人员集合
        private List<User> users;
        // 产品编码
        private String productPuid;
        // 产品名称
        private String productName;
        // 是否在线
        private Boolean ifOnline;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class User {
        private String id;
        private String userCode;
        private String userName;
        private String groupRoleCode;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    class AlmProject {
        private String id;
        private String name;
        private String desc;
        private String icon;
        private String almCode;
        private String almSCode;
        private String productSCode;
        private String productId;
        private String productName;
        private String gitAddr;
        private String orgId;
        private String orgName;
        private String category;
        private String cluster;
        private String k8sTenant;
        private String analysisIdSite;
        private String analysisUrl;
        private String analysisCookieName;
        private String structure;
        private List<String> registry;
        private String type;
        private String domain;
        private Date goliveTime;
        private String status;
        private int wsStatus;
        private String bia;
        private String devLanguage;
        private String smallMicroOrgId;
        private String implMode;
        private String owner;
        private String businessOwner;
        private String ownerName;
        private String businessOwnerName;
        private String responsibleUnit;
        private String platformOrgId;
        private String purpose;
        private String groupId;
        private int jiraId;
        private int product;
        private String srcType;
        private String loginAddr;
        private int xPaasCount;
        private int compassCount;
        private Date createdAt;
        private Date updatedAt;
        private int isDeleted;

    }
}
