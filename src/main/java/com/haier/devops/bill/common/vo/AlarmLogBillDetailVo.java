package com.haier.devops.bill.common.vo;

import com.haier.devops.bill.common.entity.RefinedRawBill;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* @ClassName: AlarmLogBillDetailVo
* @Description:  告警日志账单详情
* @author: 张爱苹
* @date: 2024/1/19 18:39
*/
@Data
@Builder
public class AlarmLogBillDetailVo implements Serializable {


    private static final long serialVersionUID = 2807993969181306772L;

    private AggregatedBillVo aggregatedBillVo;

    private List<RefinedRawBill> rawBillList;
}
