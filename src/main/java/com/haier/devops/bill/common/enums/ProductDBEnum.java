package com.haier.devops.bill.common.enums;

/**
 * @Description: 产品信息补全对应数据表枚举
 * @Author: A0018437
 * @Date：2023-12-12
 */
public enum ProductDBEnum {

    ECS("ecs","host"),
    SNAPSHOT("snapshot","host"),
    HWEC2("hws.service.type.ec2","host"),
    HWBAREMETAL("hws.service.type.baremetal","host"),
    HWBIGDATA("hws.service.type.bigdata","host"),
    HWCDM("hws.service.type.cdm","host"),
    HWCPTS("hws.service.type.cpts","host"),
    HWDCAAS("hws.service.type.dcaas","host"),
    HWDCS("hws.service.type.dcs","host"),
    HWESS("hws.service.type.ess","host"),
    HWGES("hws.service.type.ges","host"),
    HW0001CKLCC("hws.service.type.mkp.isv.0001cklcc","host"),
    HW0001DH0G5("hws.service.type.mkp.isv.0001dh0g5","host"),
    HW0001EH69R("hws.service.type.mkp.isv.0001eh69r","host"),
    HWNLP("hws.service.type.nlp","host"),
    HWOBS("hws.service.type.obs","host"),
    HWVPC("hws.service.type.vpc","host"),
    TCVM("p_cvm","host"),
    REDISA("redisa","database"),
    RES("rds","database"),
    ELASTICSEARCH("elasticsearch","database"),
    KVSTORE("kvstore","database"),
    MONGODB("mongodb","database"),
    POLARDB("polardb","database"),
    DDS("dds","database"),
    OCEANBASE("oceanbase","database"),
    DDSSHARDING("ddssharding","database"),
    TCBD("p_cdb","database"),
    TPOSTGRESQL("p_postgresql","database"),
    TREDIS("p_redis","database"),
    ALIKAFKA("alikafka","middleware"),
    MNS("mns","middleware"),
    ONS("ons","middleware"),
    ONSMQTT("onsMqtt","middleware");
    private final String product;

    private final String database;

    ProductDBEnum(String product, String database){
        this.product = product;
        this.database = database;
    }

    // 根据键获取值的静态方法
    public static String getDB(String key) {
        for (ProductDBEnum enumValue : ProductDBEnum.values()) {
            if (enumValue.product.equals(key)) {
                return enumValue.getValue();
            }
        }
        return null; // 如果未找到匹配的键，则返回null或者其他适当的值
    }

    private String getValue(){
        return database;
    }
}
