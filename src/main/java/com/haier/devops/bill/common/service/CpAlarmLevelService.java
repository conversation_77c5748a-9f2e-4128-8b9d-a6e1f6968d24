package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.AlarmConfigurationCreateDTO;
import com.haier.devops.bill.common.entity.AlarmConfiguration;
import com.haier.devops.bill.common.entity.CpAlarmLevel;
import com.haier.devops.bill.common.vo.CpAlarmLevelVo;

import java.util.List;

/**
* @ClassName: CpAlarmLevelService
* @Description:  告警级别
* @author: 张爱苹
* @date: 2024/1/12 14:39
*/
public interface CpAlarmLevelService extends IService<CpAlarmLevel> {

    /**
    * @Description: 根据配置id删除告警级别
    * @author: 张爱苹
    * @date: 2024/1/12 16:46
    * @param id:
    * @Return: void
    */
    void deleteByCpAlarmConfigurationId(Integer id);

    /**
    * @Description: 根据配置id查询告警级别
    * @author: 张爱苹
    * @date: 2024/1/15 10:25
    * @param id:
    * @Return: java.util.List<com.haier.devops.bill.common.vo.CpAlarmLevelVo>
    */
    List<CpAlarmLevelVo> getAlarmLevelListByAlarmConfigurationId(Integer id);

    /**
    * @Description: 保存告警级别和规则详情
    * @author: 张爱苹
    * @date: 2024/1/23 18:57
    * @param alarmConfiguration:
    * @param alarmConfigurationCreateDTO:
    * @Return: void
    */
    void saveOrUpdateAlarmLevelAndRuleDetail(AlarmConfiguration alarmConfiguration, AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception;
}
