package com.haier.devops.bill.common.redis;

/**
 * <AUTHOR>
 */
public class QueueConstants {
    /**
     * 图运算队列
     */
    public static final String GRAPH_BUILDING_QUEUE = "graph_building_queue";

    /**
     * 特别账单聚合替换队列
     */
    public static final String BILLING_AGGREGATION_SUBSTITUTION_QUEUE = "billing_aggregation_substitution_queue";

    /**
     * 分布式锁队列，图运算任务获取锁后会将锁信息（例如lockKey）放入该队列
     */
    public static final String DISTRIBUTED_LOCK_QUEUE = "distributed_lock_queue";

    /**
     * 多个S码多维度账单导出队列
     */
    public static final String OMNIBEARING_EXPORT_QUEUE = "omnibearing_export_queue";

    /**
     * 账单详情导出队列
     */
    public static final String DETAIL_EXPORT_QUEUE = "detail_export_queue";


    /**
     * 探测两个导出任务是否完成的队列
     */
    public static final String UPLOADING_PROBE_QUEUE = "uploading_probe_queue";
}
