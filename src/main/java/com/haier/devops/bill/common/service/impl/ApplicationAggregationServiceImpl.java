package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.mapper.ApplicationAggregationMapper;
import com.haier.devops.bill.common.service.ApplicationAggregationService;
import com.haier.devops.bill.common.vo.HworkPendingSyncBillVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 到S码汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Service
public class ApplicationAggregationServiceImpl extends ServiceImpl<ApplicationAggregationMapper, ApplicationAggregation> implements ApplicationAggregationService {
    private final ApplicationAggregationMapper aggregationMapper;

    public ApplicationAggregationServiceImpl(ApplicationAggregationMapper aggregationMapper) {
        this.aggregationMapper = aggregationMapper;
    }

    @Override
    public List<ApplicationAggregation> queryBillGroupByScode(String vendor, String billingCycle, String stage, String... account) {
        return aggregationMapper.queryBillGroupByScode(vendor, billingCycle, stage, account);
    }

    @Override
    public int batchUpdateStatus(List<ApplicationAggregation> applications, int status) {
        return aggregationMapper.batchUpdateStatus(applications, status);
    }

    @Override
    public PageInfo<HworkPendingSyncBillVo> selectPendingSyncBill(HworkPendingSyncBillVo hworkPendingSyncBillVo, int pageNum, int pageSize, String startDate, String endDate) {
        return PageHelper
                .startPage(pageNum, pageSize)
                .doSelectPageInfo(() -> aggregationMapper.selectPendingSyncBill(hworkPendingSyncBillVo, startDate, endDate));
    }

    @Override
    public List<Map<String, Object>> getYearlyTotalByBudgetCodes(List<String> budgetCodes, String year) {
        return baseMapper.getYearlyTotalByBudgetCodes(budgetCodes, year);
    }

    @Override
    @Transactional
    public boolean saveOrUpdateBatchByOrderId(List<ApplicationAggregation> aggregations) {
        if (CollectionUtils.isEmpty(aggregations)) {
            return true;
        }
        baseMapper.upsertBatchByOrderId(aggregations);
        return true;
    }

    @Override
    public int batchRejectByOrderId(List<String> orderIds) {
        return baseMapper.batchRejectByOrderId(orderIds);
    }
}
