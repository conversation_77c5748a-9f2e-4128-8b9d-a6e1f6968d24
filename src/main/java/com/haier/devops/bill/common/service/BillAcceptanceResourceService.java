package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.adjustment.vo.AdjustmentRange;
import com.haier.devops.bill.common.entity.BillAcceptanceResource;
import java.util.List;

/**
 * <p>
 * 账单确认验收资源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface BillAcceptanceResourceService extends IService<BillAcceptanceResource> {
    /**
     * 查询指定id、指定时间范围内的非存疑的验收单
     * @param aggregatedIds
     * @param range
     * @return
     */
    List<BillAcceptanceResource> selectBillsNotReservedAmongIdBetweenDate(List<String> aggregatedIds, AdjustmentRange... range);
}
