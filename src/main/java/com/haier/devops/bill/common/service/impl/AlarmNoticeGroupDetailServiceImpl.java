package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.mapper.AlarmNoticeGroupDetailMapper;
import com.haier.devops.bill.common.service.AlarmNoticeGroupDetailService;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupDetailServiceImpl
* @Description:  告警通知组明细表 服务实现类
* @author: 张爱苹
* @date: 2024/1/15 09:17
*/
@Service
@Slf4j
public class AlarmNoticeGroupDetailServiceImpl extends ServiceImpl<AlarmNoticeGroupDetailMapper, AlarmNoticeGroupDetail> implements AlarmNoticeGroupDetailService {
    private Logger logger = LoggerFactory.getLogger(AlarmNoticeGroupDetailServiceImpl.class);

    @Override
    public void deleteAlarmNoticeGroupDetailByGroupId(Integer id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("alarm_notice_group_id", id);
        remove(queryWrapper);
    }

    @Override
    public List<AlarmNoticeGroupDetailVo> getAlarmNoticeGroupDetailListByGroupId(Integer id) {
        return baseMapper.getAlarmNoticeGroupDetailListByGroupId(id);
    }

    @Override
    public List<String> getAccountListByGroupId(Integer id) {
        return baseMapper.getAccountListByGroupId(id);
    }
}
