package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.CloudProductRelation;
import com.haier.devops.bill.common.mapper.CloudProductRelationMapper;
import com.haier.devops.bill.common.service.CloudProductRelationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
* @ClassName: CloudProductRelationServiceImpl
* @Description: 阿里云产品关系
* @author: 张爱苹
* @date: 2024/3/20 10:07
*/
@Service
public class CloudProductRelationServiceImpl
        extends ServiceImpl<CloudProductRelationMapper, CloudProductRelation> implements CloudProductRelationService {


    @Override
    public CloudProductRelation getEntity(String productCode, String commodityCode) {
        LambdaQueryWrapper<CloudProductRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CloudProductRelation::getProductCode,productCode);
        queryWrapper.eq(CloudProductRelation::getCommodityCode,commodityCode);
        List<CloudProductRelation> list = list(queryWrapper);
        if(!CollectionUtils.isEmpty(list)){
            return list.get(0);
        }
        return null;
    }
}
