package com.haier.devops.bill.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
* @ClassName: AdjustmentRecord
* @Description: 调账记录
* @author: 张爱苹
* @date: 2024/2/22 14:31
*/
@Data
public class AdjustmentRecordVo implements Serializable {

    private static final long serialVersionUID = -840343231532081051L;
    private Integer id;

    /**
     * 聚合id
     *
     */
    private String aggregatedId;


    /**
     * 调账时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 调账状态
     */
    private String  reconciliationStatus;

    /**
     * 调账状态
     */
    private String  createBy;

    /**
     * 调账类型
     */
    private String  reconciliationType;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账户id
     */
    private String accountId;

    /**
     * 云账号
     */
    private String accountName;

    /**
     * 产品编码
     *
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源实例id
     */
    private String instanceId;


    /**
     * 分拆项
     */
    private String supplementId;


    @Schema(description = "私网ip")
    private String privateIp;

    /**
     * 项目编码
     */
    private String projectCode;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 消费类型
     */
    private String subscriptionType;



    /**
     * 资源创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    /**
     * 资源释放时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releasingTime;

    @Schema(description = "校准前的S码")
    private String scodeUnverified;

    private String originalContent;

    private String newContent;

    private String type;


}
