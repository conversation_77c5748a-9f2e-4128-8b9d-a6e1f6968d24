package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HuaweiApi;
import com.haier.devops.bill.common.entity.EnterpriseProjects;
import com.haier.devops.bill.common.entity.EnterpriseProviders;
import com.haier.devops.bill.common.entity.EnterpriseResources;
import com.haier.devops.bill.common.entity.IamProjects;
import com.haier.devops.bill.common.mapper.EnterpriseResourcesMapper;
import com.haier.devops.bill.common.service.EnterpriseProjectsService;
import com.haier.devops.bill.common.service.EnterpriseProvidersService;
import com.haier.devops.bill.common.service.EnterpriseResourcesService;
import com.haier.devops.bill.common.service.IamProjectsService;
import com.huaweicloud.sdk.config.v1.ConfigClient;
import com.huaweicloud.sdk.config.v1.model.ListAllResourcesResponse;
import com.huaweicloud.sdk.config.v1.model.ResourceEntity;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.Resources;
import com.huaweicloud.sdk.eps.v1.model.ShowResourceBindEnterpriseProjectResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* @ClassName: EnterpriseResourcesServiceImpl
* @Description: 企业项目资源
* @author: 张爱苹
* @date: 2025/2/13 14:58
*/
@Service
public class EnterpriseResourcesServiceImpl
        extends ServiceImpl<EnterpriseResourcesMapper, EnterpriseResources> implements EnterpriseResourcesService {
    private static Logger logger = LoggerFactory.getLogger(EnterpriseResourcesServiceImpl.class);

    @Autowired
    private HuaweiApi huaweiApi;

    @Autowired
    private EnterpriseProjectsService enterpriseProjectsService;

    @Autowired
    private IamProjectsService iamProjectsService;

    @Autowired
    private EnterpriseProvidersService enterpriseProvidersService;

    @Override
    public void insertEnterpriseResourcesJobHandler(String accountName) throws Exception {
        Integer offset = 0;
        Integer limit = 100;
        List<EnterpriseProjects> list = enterpriseProjectsService.getEnterpriseProjects(accountName);
        if(CollectionUtils.isNotEmpty(list)){
            EpsClient epsClient = huaweiApi.getEpsClient(accountName,"cn-north-4");
            List<EnterpriseProviders> enterpriseProvidersList = enterpriseProvidersService.getEnterpriseProviders();
            if(CollectionUtils.isEmpty(enterpriseProvidersList)){
                throw new RuntimeException("资源类型为空");
            }
            List<String> providerList = enterpriseProvidersList.stream().map(item->item.getResourceType()).collect(Collectors.toList());
            List<IamProjects> iamProjectsList = iamProjectsService.getIamProjects(accountName);
           if(CollectionUtils.isEmpty(iamProjectsList)){
               logger.info("项目位空,{}",accountName);
               return;
           }
            List<String> projectList = iamProjectsList.stream().map(item->item.getId()).collect(Collectors.toList());
            List<List<String>> subLists = splitList(providerList,80);
            list.stream().forEach(item->{
                //保存资源
                try {
                    List<String> idList = new ArrayList<>();
                    for(List<String> subList : subLists){
                        idList.addAll(saveEnterpriseResources(item,subList,projectList,accountName,epsClient,offset,limit));
                    }
                    //删除不在idList的企业项目资源
                    deleteBatchNoIds(idList,item.getId());
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("获取企业项目资源失败，{},{}",item.getId(),e.getMessage(),e);
                }
            });
        }
    }

    @Override
    public EnterpriseResources getOneByResourceId(String resourceId) {
        EnterpriseResources old = getByResourceId(resourceId);
        return old;
    }

    @Override
    public String migrateResource(EnterpriseResources enterpriseResources, String id)throws Exception {
        EpsClient epsClient = huaweiApi.getEpsClient(enterpriseResources.getAccountName(),"cn-north-4");
        String errorMsg = huaweiApi.migrateResource(epsClient,enterpriseResources.getProjectId(),enterpriseResources.getResourceType(),enterpriseResources.getResourceId(),id);
        return errorMsg;
    }

    @Override
    public void insertEnterpriseResources(String accountName) throws Exception {
        Integer limit = 200;
        ConfigClient configClient = huaweiApi.getConfigClientClient(accountName,"cn-north-4");
        ListAllResourcesResponse response = huaweiApi.listAllResourcesResponse(configClient,null,limit);
        if(response == null){
            throw new RuntimeException("获取资源失败");
        }
        List<ResourceEntity> resourceEntityList = response.getResources();
        Integer currentCount = response.getPageInfo().getCurrentCount();
        String marker =response.getPageInfo().getNextMarker();
        while (currentCount >= limit && StringUtils.isNotEmpty(marker)){
            ListAllResourcesResponse response1 = huaweiApi.listAllResourcesResponse(configClient,marker,limit);
            if(response1 == null){
                logger.error("获取资源失败，{},{}",marker,accountName);
                break;
            }
            currentCount = response1.getPageInfo().getCurrentCount();
            marker =response1.getPageInfo().getNextMarker();
            resourceEntityList.addAll(response1.getResources());
        }
        List<String> idList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(resourceEntityList)){
            List<EnterpriseResources> addList = new ArrayList<>();
            List<EnterpriseResources> updateList = new ArrayList<>();
            resourceEntityList.stream().forEach(entity->{
                EnterpriseResources resources = EnterpriseResources.builder()
                        .enterpriseProjectId(entity.getEpId())
                        .projectId(entity.getProjectId())
                        .projectName(entity.getProjectName())
                        .resourceId(entity.getId())
                        .resourceName(entity.getName())
                        .resourceType(entity.getType())
                        .delFlag("0")
                        .accountName(accountName)
                        .build();
                EnterpriseResources old = getByResourceId(entity.getId());
                if(old == null){
                    addList.add(resources);
                }else{
                    resources.setId(old.getId());
                    updateList.add(resources);
                }
                idList.add(entity.getId());

            });
            if(CollectionUtils.isNotEmpty(addList)){
                saveBatch(addList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                updateBatchById(updateList);
            }
            //删除不在idList的企业项目资源
            deleteBatchNoIds(idList,accountName);
        }
    }


    public static <T> List<List<T>> splitList(List<T> list,int batchSize) {
        List<List<T>> subLists = new ArrayList<>();
        int listSize = list.size();
        for (int i = 0; i < listSize; i += batchSize) {
            subLists.add(new ArrayList<>(list.subList(i, Math.min(i + batchSize, listSize))));
        }
        return subLists;
    }


    private List<String> saveEnterpriseResources(EnterpriseProjects item, List<String> providerList, List<String> projectList, String accountName,EpsClient epsClient,Integer offset,Integer limit) throws Exception{
        ShowResourceBindEnterpriseProjectResponse response = huaweiApi.showResourceBindEnterpriseProject(epsClient,item.getId(),providerList,projectList,offset,limit);
        if(response == null){
            throw new RuntimeException("获取企业项目资源失败");
        }
        int total = response.getTotalCount();
        List<String> idList = new ArrayList<>();
        if(total > 0){
            List<Resources> list = response.getResources();
            int totalPage = total % limit == 0 ? total / limit : total / limit + 1;
            for(int i = 2;i < totalPage;i++){
                offset = (i - 1) * limit;
                response = huaweiApi.showResourceBindEnterpriseProject(epsClient,item.getId(),providerList,projectList,offset,limit);
                if(response == null){
                    logger.error("获取企业项目资源失败,{},{},{}",accountName,item.getId(),offset,limit);
                    continue;
                }
                list.addAll(response.getResources());
            }
            List<EnterpriseResources> addList = new ArrayList<>();
            List<EnterpriseResources> updateList = new ArrayList<>();

            list.stream().forEach(entity->{
                EnterpriseResources resources = EnterpriseResources.builder()
                        .enterpriseProjectId(entity.getEnterpriseProjectId())
                        .projectId(entity.getProjectId())
                        .projectName(entity.getProjectName())
                        .resourceId(entity.getResourceId())
                        .resourceName(entity.getResourceName())
                        .resourceType(entity.getResourceType())
                        .delFlag("0")
                        .accountName(accountName)
                        .build();
                EnterpriseResources old = getByResourceId(entity.getResourceId());
                if(old == null){
                    addList.add(resources);
                }else{
                    resources.setId(old.getId());
                    updateList.add(resources);
                }
                idList.add(entity.getResourceId());

            });
            if(CollectionUtils.isNotEmpty(addList)){
                saveBatch(addList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                updateBatchById(updateList);
            }
        }
        return idList;
    }

    private EnterpriseResources getByResourceId(String resourceId) {
        LambdaQueryWrapper<EnterpriseResources> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseResources::getDelFlag,"0");
        queryWrapper.eq(EnterpriseResources::getResourceId,resourceId);
        return getOne(queryWrapper);
    }

    private void deleteBatchNoIds(List<String> idList, String accountName) {
        UpdateWrapper<EnterpriseResources> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("account_name",accountName);
        updateWrapper.eq("del_flag","0");
        if(CollectionUtils.isNotEmpty(idList)){
            updateWrapper.notIn("resource_id",idList);
        }
        updateWrapper.set("del_flag","1");
        update(updateWrapper);
    }
}
