package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.service.BillDetailService;
import com.haier.devops.bill.common.vo.BillDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* @ClassName: BillDetailController
* @Description: 账单明细汇总控制器
* @author: 张爱苹
* @date: 2024/4/12 10:25
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/detail")
public class BillDetailController {

	@Autowired
	private BillDetailService billDetailService;

	/**
	 * 分页查询所有账单明细汇总
	 *
	 * @return 账单明细汇总列表
	 */
	@PostMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<BillDetailVo>> listByPage(@RequestBody DetailBillParam request) {
        try {
			PageInfo<BillDetailVo> pageInfo = billDetailService.listByPage(request);
			return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
	}

	/**
	 * 分页查询所有账单明细汇总
	 *
	 * @return 账单明细汇总列表
	 */
	@PostMapping("/queryAggregatedBillDetail")
	public ResponseEntityWrapper<PageInfo<BillDetailVo>> queryAggregatedBillDetail(@RequestBody DetailBillParam request) {
		try {
			request.setType("4");
			PageInfo<BillDetailVo> pageInfo = billDetailService.listByPage(request);
			return new ResponseEntityWrapper<>(pageInfo);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description:  导出消费明细数据
	* @author: 张爱苹
	* @date: 2024/4/12 16:20
	* @param request:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.haier.devops.bill.common.entity.ExportLog>
	*/
	@PostMapping("/download")
	public ResponseEntityWrapper<ExportLog> downloadDimensionDetail(@RequestBody DetailBillParam dto,HttpServletRequest request,HttpServletResponse response) {
		try {
			ExportLog exportLog = billDetailService.downloadDimensionDetail(dto,request,response);
			return new ResponseEntityWrapper<>(exportLog);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * @Description:  导出消费明细数据
	 * @author: 张爱苹
	 * @date: 2024/4/12 16:20
	 * @param request:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.haier.devops.bill.common.entity.ExportLog>
	 */
	@RequestMapping("/exportBillExcel")
	public void exportBillExcel(@RequestBody DetailBillParam dto, HttpServletRequest request, HttpServletResponse response)throws Exception {
		//billDetailService.exportBillExcel(request, dto,response);
	}


}
