package com.haier.devops.bill.common.service.impl;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.api.HworkBillApi;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItRequest;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.HdsSubProductsMapper;
import com.haier.devops.bill.common.redis.Cache;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.util.AuthUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.haier.devops.bill.export.Exporter.log;

/**
 * <AUTHOR>
 */
@Service
public class AuthorityServiceImpl implements AuthorityService {
    @Value("${cache.personal-info.expire-time}")
    private Integer expireTime;

    @Value("${hcms_admin_orgids}")
    private String hcmsAdminOrgids;

    @Value("${domain.code}")
    private String domainCode;

    private Cache<String, List<String>> scodesCache;
    private Cache<String, List<HdsSubProducts>> hdsSubProductsCache;
    private HworkAuthorityApi hworkAuthorityApi;
    private HworkBillApi hworkBillApi;
    private HdsSubProductsMapper hdsSubProductsMapper;

    public AuthorityServiceImpl(@Qualifier("authorityScodesCache") Cache<String, List<String>> scodesCache,
                                @Qualifier("hdsSubProductsCache") Cache<String, List<HdsSubProducts>> hdsSubProductsCache,
                                HworkAuthorityApi hworkAuthorityApi,
                                HworkBillApi hworkBillApi,
                                HdsSubProductsMapper hdsSubProductsMapper) {
        this.scodesCache = scodesCache;
        this.hdsSubProductsCache = hdsSubProductsCache;
        this.hworkAuthorityApi = hworkAuthorityApi;
        this.hworkBillApi = hworkBillApi;
        this.hdsSubProductsMapper = hdsSubProductsMapper;
    }

    @Override
    public List<String> getAuthorizedScodes(String userCode) {
        List<String> scodes = scodesCache.get(userCode);
        if (!CollectionUtils.isEmpty(scodes)) {
            return scodes;
        }

        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> resultWrapper =
                this.getUserAuthorities(userCode);
        if (resultWrapper.getCode() != HttpStatus.OK.value()) {
            return null;
        }
        Set<String> authorizedScodes = new HashSet<>();
        for (HworkAuthorityApi.Authority authority : resultWrapper.getData()) {
            authorizedScodes.addAll(hdsSubProductsMapper.getAuthorizedScodes(authority.getAuthorityCode()));
        }

        scodes = new ArrayList<>(authorizedScodes);
        scodesCache.put(AUTH_CACHE_KEY + userCode, scodes, expireTime, TimeUnit.MINUTES);

        return scodes;
    }

    @Override
    public User getUserByUserCode(String userCode) {
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> resultWrapper =
                hworkAuthorityApi.getUserDetail(userCode, true);
        if (resultWrapper.getCode() == HttpServletResponse.SC_OK) {
            HworkAuthorityApi.User huser = resultWrapper.getData();
            User user = User.builder()
                    .userCode(huser.getUserCode())
                    .userName(huser.getUserName())
                    .employeeStatus(huser.getEmployeeStatus())
                    .email(huser.getEmail())
                    .deptId(huser.getDeptId())
                    .deptName(huser.getDeptName())
                    .roles(huser.getRoles())
                    .build();
            return user;
        } else {
            return null;
        }
    }

    @Override
    public String getUserPhoneByUserCode(String account) {
        HworkAuthorityApi.ResultWrapper<String> resultWrapper =
                hworkAuthorityApi.getUserPhone(account);
        if (resultWrapper.getCode() == HttpServletResponse.SC_OK) {
            String phone = resultWrapper.getData();
            return phone;
        } else {
            return "";
        }
    }

    @Override
    public List<HdsSubProducts> getAuthorizedSubProducts(String userCode) {
        List<HdsSubProducts> hdsSubProducts = hdsSubProductsCache.get(userCode);
        if (!CollectionUtils.isEmpty(hdsSubProducts)) {
            return hdsSubProducts;
        }

        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> resultWrapper =
                this.getUserAuthorities(userCode);
        if (resultWrapper.getCode() != HttpStatus.OK.value()) {
            return null;
        }

        Set<HdsSubProducts> authorizedSubProducts = new HashSet<>();
        for (HworkAuthorityApi.Authority authority : resultWrapper.getData()) {
            authorizedSubProducts.addAll(hdsSubProductsMapper.getAuthorizedSubProducts(authority.getAuthorityCode()));
        }
        hdsSubProducts = new ArrayList<>(authorizedSubProducts);
        hdsSubProductsCache.put(SUB_PRODUCT_CACHE_KEY + userCode, hdsSubProducts, expireTime, TimeUnit.MINUTES);

        return hdsSubProducts;
    }

    /**
     * 获取经过授权的子产品SCodes。
     * @param departIds 部门ID列表
     * @return 经过授权的子产品SCodes列表
     */
    public List<HdsSubProducts> getAuthedSubProducts(String departIds) {
        List<String> deptIdList = new ArrayList<>();

        User user = AuthUtil.getCurrentUser();
        boolean isAdmin = AuthUtil.isAdmin(user);

        if (StringUtils.isBlank(departIds)) {
            if (isAdmin) {
                return hdsSubProductsMapper.getDeptFilteredSubProducts(deptIdList);
            } else {
                return this.getAuthorizedSubProducts(user.getUserCode());
            }
        }

        deptIdList = Arrays.asList(departIds.split(","));
        List<HdsSubProducts> filteredSubProducts = hdsSubProductsMapper.getDeptFilteredSubProducts(deptIdList);

        if (isAdmin) {
            return filteredSubProducts;
        }

        List<HdsSubProducts> authedSubProducts = this.getAuthorizedSubProducts(user.getUserCode());
        if (CollectionUtils.isEmpty(authedSubProducts)){
            return new ArrayList<>();
        }

        return filteredSubProducts.stream().filter(authedSubProducts::contains).collect(Collectors.toList());
    }

    @Override
    public HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> getUserAuthorities(String userCode) {
        // 只获取配置了数据权限的部门
        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> userAuthoritiesWrapper =
                hworkAuthorityApi.getUserAuthorities(userCode, domainCode);
        if (userAuthoritiesWrapper.getCode() != HttpStatus.OK.value()) {
            return userAuthoritiesWrapper;
        }

        // 由于Hwork接口未上线，暂时注释掉
        // 已经上线，开启
        if (CollectionUtils.isEmpty(userAuthoritiesWrapper.getData())) {
            userAuthoritiesWrapper = hworkAuthorityApi.getUserGroupDataPrivilege(userCode, domainCode);
        }
        return userAuthoritiesWrapper;
    }


    @Override
    public AuthAppForItResponse getAuthAppForIt(AuthAppForItRequest request) {
        ResultWrapper<AuthAppForItResponse> authAppForIt = hworkBillApi.getAuthAppForIt(request);
        if (authAppForIt.getCode() == HttpStatus.OK.value()) {
            return authAppForIt.getData();
        }
        
        log.error("getAuthAppForIt error: {}", authAppForIt.getMessage());
        return new AuthAppForItResponse();
    }

}
