package com.haier.devops.bill.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: AlarmLogDTO
* @Description:  告警日志
* @author: 张爱苹
* @date: 2024/1/18 14:36
*/
@Data
public class AlarmLogDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 5699224334477503119L;

    /**
     * 告警配置id
     */
    @NotNull
    private Integer cpAlarmConfigurationId;

    /**
     * 告警等级
     */
    private String level;

    /**
     * 告警开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 告警等级
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;


}
