package com.haier.devops.bill.common.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import feign.Param;
import feign.RequestLine;
import lombok.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Hiwork 权限中心 api
 *
 * <AUTHOR>
 */
public interface HworkAuthorityApi {
    /**
     * 查询用户（领域内+领域外）指定领域下的数据权限（不分页）
     * 注意：实际上，领域外不生效，需要使用用户群组授权的方式，
     * 即：com.haier.devops.bill.common.api.HworkAuthorityApi#getUserGroupDataPrivilege
     * 使用时，调用com.haier.devops.bill.common.service.AuthorityService#getUserAuthorities(java.lang.String)
     *
     * @param userCode
     * @param domainCode
     * @return
     */
    @RequestLine("POST /metaAuth/api/user/authority/getUserAuthorityAll")
    ResultWrapper<List<Authority>> getUserAuthorities(@Param("userCode") String userCode, @Param("domainCode") String domainCode);

    /**
     * 查询用户指定领域下用户群组数据权限（不分页）
     * @param userCode
     * @param domainCode
     */
    @RequestLine("POST /metaAuth/api/user/auth/group/auth")
    ResultWrapper<List<Authority>> getUserGroupDataPrivilege(@Param("userCode") String userCode, @Param("domainCode") String domainCode);

    /**
     * 查询用户基本信息
     *
     * @param userCode
     * @param needRole
     * @return
     */
    @RequestLine("POST /domain/open/userInfo/detail")
    ResultWrapper<User> getUserDetail(@Param("userCode") String userCode, @Param("needRole") Boolean needRole);

    /**
     * 获取人员的组织信息
     * @param userCode
     * @return
     */
    @RequestLine("POST /domain/open/userOrg/info")
    ResultWrapper<OrgInfoDTO> getUserOrgInfo(@Param("userCode") String userCode);

    /**
     * 查询用户基本信息
     *
     * @param userCode
     * @return
     */
    @RequestLine("GET /domain/open/userInfo/getPhone?userCode={userCode}")
    ResultWrapper<String> getUserPhone(@Param("userCode") String userCode);


    /**
     * 3.5 查询数据维度大类下的维度值
     * @param request
     * @return
     */
    @RequestLine("POST /authOpenApi/api/domain/dimension/getByDomainAndDimensionCode")
    ResultWrapper<Pager> getByDomainAndDimensionCode(@RequestBody GetByDomainAndDimensionCodeRequestWrapper request);

    /**
     * 3.14 根据数据权限+用户组编码查询用户（分页）
     * @param request
     * @return
     */
    @RequestLine("POST /authOpenApi/api/user/privilege/qualifiedUsersByDataPermissionPage")
    Object qualifiedUsersByDataPermissionPage(@RequestBody QualifiedUsersByDataPermissionPageRequestWrapper request);

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Pager {
        private Integer total;
        private Integer totalPage;
        private Integer page;
        private Integer pageSize;
        private List<Dimension> data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Dimension {
        private String code;
        private String name;
        private String parentCode;
        private String parentName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class QualifiedUsersByDataPermissionPageRequestWrapper {
        private String domain;
        private List<DimensionObject> dimensionObjectList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class DimensionObject {
        private String dimensionCode;
        private List<String> authorityCodeList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class GetByDomainAndDimensionCodeRequestWrapper {
        private String domainCode;
        private String dimensionCode;
        private Integer pageNum;
        private Integer pageSize;
    }

    @Setter
    @Getter
    class ResultWrapper<T> {
        private int code;
        private String message;
        private String msg;
        private T data;
    }

    @Setter
    @Getter
    class Authority {
        @JsonProperty("userId")
        private String userId;
        @JsonProperty("authorityCode")
        private String authorityCode;
        @JsonProperty("authorityName")
        private String authorityName;
        @JsonProperty("type")
        private Integer type;
        @JsonProperty("dimensionCode")
        private String dimensionCode;
        @JsonProperty("domainCode")
        private String domainCode;
        @JsonProperty("parentAuthorityCode")
        private String parentAuthorityCode;
        @JsonProperty("parentAuthorityName")
        private String parentAuthorityName;
    }

    @NoArgsConstructor
    @Data
    class User {
        @JsonProperty("userCode")
        private String userCode;
        @JsonProperty("email")
        private String email;
        @JsonProperty("userName")
        private String userName;
        @JsonProperty("employeeStatus")
        private String employeeStatus;
        @JsonProperty("deptId")
        private String deptId;
        @JsonProperty("deptName")
        private String deptName;
        @JsonProperty("gwName")
        private String gwName;
        @JsonProperty("roles")
        private List<RolesDTO> roles;
        @JsonProperty("firstLineId")
        private String firstLineId;
        @JsonProperty("gwId")
        private String gwId;
        @JsonProperty("firstLineName")
        private String firstLineName;
        @JsonProperty("deptPath")
        private String deptPath;
        @JsonProperty("deptPathName")
        private String deptPathName;
        @JsonProperty("userPhoto")
        private String userPhoto;


    }

    @NoArgsConstructor
    @Data
    class RolesDTO {
        @JsonProperty("roleCode")
        private String roleCode;
        @JsonProperty("roleName")
        private String roleName;
        @JsonProperty("classifyCode")
        private String classifyCode;
        @JsonProperty("classifyName")
        private String classifyName;
        @JsonProperty("roleTypeCode")
        private String roleTypeCode;
        @JsonProperty("roleTypeName")
        private String roleTypeName;
    }

    @NoArgsConstructor
    @Data
    class OrgInfoDTO {
        @JsonProperty("departmentCode")
        private String departmentCode;
        @JsonProperty("departmentName")
        private String departmentName;
        @JsonProperty("empCode")
        private String empCode;
        @JsonProperty("empName")
        private String empName;
        @JsonProperty("line1DepartmentCode")
        private String line1DepartmentCode;
        @JsonProperty("line1DepartmentName")
        private String line1DepartmentName;
        @JsonProperty("line1ManagerCode")
        private String line1ManagerCode;
        @JsonProperty("line1ManagerName")
        private String line1ManagerName;
        @JsonProperty("positionCode")
        private String positionCode;
        @JsonProperty("positionName")
        private String positionName;
        @JsonProperty("ptCode")
        private String ptCode;
        @JsonProperty("ptMaster")
        private String ptMaster;
        @JsonProperty("ptMasterDepartmentCode")
        private String ptMasterDepartmentCode;
        @JsonProperty("ptMasterDepartmentName")
        private String ptMasterDepartmentName;
        @JsonProperty("ptMasterName")
        private String ptMasterName;
        @JsonProperty("ptName")
        private String ptName;
        @JsonProperty("xwCode")
        private String xwCode;
        @JsonProperty("xwMaster")
        private String xwMaster;
        @JsonProperty("xwMasterDepartmentCode")
        private String xwMasterDepartmentCode;
        @JsonProperty("xwMasterDepartmentName")
        private String xwMasterDepartmentName;
        @JsonProperty("xwMasterName")
        private String xwMasterName;
        @JsonProperty("xwName")
        private String xwName;
        @JsonProperty("deptPath")
        private String deptPath;
        @JsonProperty("deptPathName")
        private String deptPathName;
        @JsonProperty("userPhoto")
        private String userPhoto;

    }
}
