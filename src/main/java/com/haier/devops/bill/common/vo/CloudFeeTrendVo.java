package com.haier.devops.bill.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CloudFeeTrendVo {
    /**
     * 当前月份/年份以及上一月份/年份的费用
     */
    private List<CloudFeeVo> cloudFeeVos;

    /**
     * 当前年份/月份
     */
    @Getter
    @Setter
    private CloudFeeVo current;

    /**
     * 趋势
     */
    @Getter
    @Setter
    private BigDecimal trend;

    public CloudFeeTrendVo() {
    }

    public CloudFeeTrendVo(List<CloudFeeVo> cloudFeeVos) {
        this.cloudFeeVos = cloudFeeVos;
        this.trend = getTrend();
    }

    /**
     * 获取增长趋势
     *
     * @return
     */
    public BigDecimal getTrend() {
        if (cloudFeeVos.size() < 2) {
            if (cloudFeeVos.size() == 1) {
                this.current = cloudFeeVos.get(0);
            }
            return BigDecimal.ZERO;
        }
        CloudFeeVo last = cloudFeeVos.get(cloudFeeVos.size() - 1);
        CloudFeeVo secondLast = cloudFeeVos.get(cloudFeeVos.size() - 2);
        CloudFeeVo later, earlier = null;
        if (last.getTimeUnit().compareTo(secondLast.getTimeUnit()) < 0) {
            later = secondLast;
            earlier = last;
        } else {
            later = last;
            earlier = secondLast;
        }
        this.current = later;

        if (null == earlier.getTotalSum() || earlier.getTotalSum().compareTo(BigDecimal.ZERO) == 0) {
            if (later.getTotalSum().compareTo(BigDecimal.ZERO) > 0) {
                return new BigDecimal(100);
            } else {
                return BigDecimal.ZERO;
            }
        } else {
            return getGrowthRate(later, earlier).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
        }
    }

    private BigDecimal getGrowthRate(CloudFeeVo later, CloudFeeVo earlier) {
        return later.getTotalSum().subtract(earlier.getTotalSum()).divide(earlier.getTotalSum(), 4, RoundingMode.HALF_UP);
    }

    public static void main(String[] args) {
        System.out.println("2023-04".compareTo("2023-05"));
        System.out.println("2023-01".compareTo("2020-12"));
        System.out.println("2023".compareTo("2020"));

        CloudFeeVo fee1 = new CloudFeeVo();
        fee1.setTimeUnit("2023-03");
        fee1.setTotalSum(new BigDecimal(107.81));
        CloudFeeVo fee2 = new CloudFeeVo();
        fee2.setTimeUnit("2023-04");
        fee2.setTotalSum(new BigDecimal(100.01));
        List<CloudFeeVo> cloudFeeVos = new ArrayList<>();
        cloudFeeVos.add(fee1);
        cloudFeeVos.add(fee2);

        CloudFeeTrendVo trendVo = new CloudFeeTrendVo(cloudFeeVos);
        System.out.println(trendVo.getTrend());
    }
}
