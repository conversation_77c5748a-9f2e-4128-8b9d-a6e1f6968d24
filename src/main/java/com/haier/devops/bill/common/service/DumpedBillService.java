package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.DumpedBill;
import com.haier.devops.bill.common.vo.DumpedDetailBillVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface DumpedBillService extends IService<DumpedBill> {
    /**
     * 按照S码分组查询月度账单
     * 按照S码、账期、账号汇总，并取2位小数
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<DumpedBill> queryBillGroupByScode(String vendor, String billingCycle, String... account);

    /**
     * 按照资源查询月度账单明细
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<DumpedDetailBillVo> queryDetailBill(String vendor, String billingCycle, String... account);


}
