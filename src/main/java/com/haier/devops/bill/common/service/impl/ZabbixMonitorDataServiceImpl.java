package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ZabbixMonitorData;
import com.haier.devops.bill.common.mapper.ZabbixMonitorMapper;
import com.haier.devops.bill.common.service.ZabbixMonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @ClassName: ZabbixMonitorDataServiceImpl
* @Description: zabbix监控数据 服务实现类
* @author: 张爱苹
* @date: 2024/4/17 10:41
*/
@Service
@Slf4j
public class ZabbixMonitorDataServiceImpl extends ServiceImpl<ZabbixMonitorMapper, ZabbixMonitorData> implements ZabbixMonitorDataService {


    @Override
    public void saveMonitorDataBatch(List<ZabbixMonitorData> param) {
        List<ZabbixMonitorData> addList = new ArrayList<>();
        List<String> instanceIdList = param.stream().map(ZabbixMonitorData::getInstanceId).collect(Collectors.toList());
        List<ZabbixMonitorData> oldList = getOldListByInstanceIdList(instanceIdList,"zabbix");
        //oldList转map<String,ZabbixMonitorData>
        Map<String,ZabbixMonitorData> oldMap = oldList.stream().collect(Collectors.toMap(ZabbixMonitorData::getInstanceId, item->item));
        param.stream().forEach(item -> {
//            if(item.getInstanceId().startsWith("ZJ_")){
//                String ip = item.getInstanceId().substring(0,item.getInstanceId().lastIndexOf("_")).replace("ZJ_","").replace("_",".");
//                String port = item.getInstanceId().substring(item.getInstanceId().lastIndexOf("_")+1);
//                item.setInstanceId(ip+":"+port);
//            }
            ZabbixMonitorData oldData = oldMap.get(item.getInstanceId());
            if(oldData == null){
                item.setCreateTime(new Date());
            }else{
                item.setId(oldData.getId());
            }
            item.setUpdateTime(new Date());
            item.setSourceType("zabbix");
            addList.add(item);
        });
        if(CollectionUtils.isNotEmpty(addList)){
            saveOrUpdateBatch(addList);
        }
    }

    @Override
    public Map getMaxCpuAndMem(List<String> collect) {
        return baseMapper.getMaxCpuAndMem(collect);
    }

    public List<ZabbixMonitorData> getOldListByInstanceIdList(List<String> instanceIdList,String sourceType) {
        return list(new QueryWrapper<ZabbixMonitorData>().in("instance_id",instanceIdList).eq("source_type",sourceType));
    }

}
