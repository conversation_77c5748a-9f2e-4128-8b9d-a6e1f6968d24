package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.mapper.ExportLogMapper;
import com.haier.devops.bill.common.service.ExportLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账单导出记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class ExportLogServiceImpl extends ServiceImpl<ExportLogMapper, ExportLog> implements ExportLogService {

}
