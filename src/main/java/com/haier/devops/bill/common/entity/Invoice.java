package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@TableName("bc_invoice")
@Data
public class Invoice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账号名称
     */
    private String accountName;

    /**
     * 云账号id
     */
    private String accoundId;

    /**
     * 账期
     */
    private String billingCycle;

    /**
     * 可开票金额
     */
    private String availableInvoiceAmount;

    /**
     * 已开票金额
     */
    private String invoicedAmount;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
