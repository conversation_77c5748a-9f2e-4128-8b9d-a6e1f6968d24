package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.CmdbProductOverviewDTO;
import com.haier.devops.bill.common.entity.AdjustmentRecord;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ReconciliationTask;
import com.haier.devops.bill.common.mapper.CmdbProductOverviewMapper;
import com.haier.devops.bill.common.service.AdjustmentRecordService;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import com.haier.devops.bill.common.service.ReconciliationTaskService;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: CmdbProductOverviewServiceImpl
 * @Description: cmdb总表
 * @author: 张爱苹
 * @date: 2024/3/8 16:12
 */
@Service
@Slf4j
public class CmdbProductOverviewServiceImpl extends ServiceImpl<CmdbProductOverviewMapper, CmdbProductOverview> implements CmdbProductOverviewService {
    private Logger logger = LoggerFactory.getLogger(CmdbProductOverviewServiceImpl.class);
    @Resource
    private CmdbProductOverviewMapper cmdbProductOverviewMapper;

    @Autowired
    private AdjustmentRecordService adjustmentRecordService;

    @Autowired
    private ReconciliationTaskService reconciliationTaskService;

    public boolean saveOrUpdateBatchByParams(List<CmdbProductOverview> list) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<CmdbProductOverview> queryWrapper = Wrappers.<CmdbProductOverview>lambdaQuery()
                    .eq(CmdbProductOverview::getAggregatedId, entity.getAggregatedId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<CmdbProductOverview> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CmdbProductOverview::getAggregatedId, entity.getAggregatedId());

            // String fields - update if not null and not empty
            if (entity.getVendor() != null && !entity.getVendor().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getVendor, entity.getVendor());
            }
            if (entity.getAccountId() != null && !entity.getAccountId().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getAccountId, entity.getAccountId());
            }
            if (entity.getAccountName() != null && !entity.getAccountName().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getAccountName, entity.getAccountName());
            }
            if (entity.getProductCode() != null && !entity.getProductCode().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getProductCode, entity.getProductCode());
            }
            if (entity.getProductName() != null && !entity.getProductName().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getProductName, entity.getProductName());
            }
            if (entity.getInstanceId() != null && !entity.getInstanceId().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getInstanceId, entity.getInstanceId());
            }
            if (entity.getSupplementId() != null && !entity.getSupplementId().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getSupplementId, entity.getSupplementId());
            }
            if (entity.getPrivateIp() != null && !entity.getPrivateIp().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getPrivateIp, entity.getPrivateIp());
            }
            if (entity.getScode() != null && !entity.getScode().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getScode, entity.getScode());
            }
            if (entity.getProjectCode() != null && !entity.getProjectCode().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getProjectCode, entity.getProjectCode());
            }
            if (entity.getProjectName() != null && !entity.getProjectName().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getProjectName, entity.getProjectName());
            }
            if (entity.getSubscriptionType() != null && !entity.getSubscriptionType().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getSubscriptionType, entity.getSubscriptionType());
            }
            if (entity.getScodeUnverified() != null && !entity.getScodeUnverified().isEmpty()) {
                updateWrapper.set(CmdbProductOverview::getScodeUnverified, entity.getScodeUnverified());
            }

            // LocalDateTime fields - update if not null
            if (entity.getCreationTime() != null) {
                updateWrapper.set(CmdbProductOverview::getCreationTime, entity.getCreationTime());
            }
            if (entity.getReleasingTime() != null) {
                updateWrapper.set(CmdbProductOverview::getReleasingTime, entity.getReleasingTime());
            }

            // Integer fields - update if not null
            if (entity.getScodeRuleMatched() != null) {
                updateWrapper.set(CmdbProductOverview::getScodeRuleMatched, entity.getScodeRuleMatched());
            }
            if (entity.getParentFound() != null) {
                updateWrapper.set(CmdbProductOverview::getParentFound, entity.getParentFound());
            }

            updateWrapper.set(CmdbProductOverview::getUpdateTime, java.time.LocalDateTime.now());

            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(1); // Expect 1 as we only put WRAPPER
            // param.put(Constants.ENTITY, entity); // REMOVED - We are controlling fields via updateWrapper
            param.put(Constants.WRAPPER, updateWrapper);
            
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }


    @Override
    public PageInfo<CmdbProductOverview> listByPage(CmdbProductOverviewDTO dto) {
        try {
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () -> baseMapper.listByPage(dto)
            );
        } catch (Exception e) {
            logger.error("listByPage error, dto:{}", dto, e.getMessage(), e.getMessage(), e);
            throw new RuntimeException("listByPage error");
        }
    }



    @Override
    public List<AdjustmentRecordVo> getAdjustmentRecordList(CmdbProductOverviewDTO dto) {
        QueryWrapper<AdjustmentRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("bar.create_time");
        if(StringUtils.isNotEmpty(dto.getVendor())){
            queryWrapper.eq("bcpo.vendor", dto.getVendor());
        }
       if (StringUtils.isNotEmpty(dto.getAccountName())){
           queryWrapper.eq("bcpo.account_name", dto.getAccountName());
       }
       if (StringUtils.isNotEmpty(dto.getScode())){
            queryWrapper.eq("bcpo.scode", dto.getScode());
        }
        if (StringUtils.isNotEmpty(dto.getScodeUnverified())){
            queryWrapper.like("bcpo.scode_unverified", dto.getScodeUnverified());
        }

        if (StringUtils.isNotEmpty(dto.getProductCode())){
            queryWrapper.eq("brbp.alias_code", dto.getProductCode());
        }

        if (StringUtils.isNotEmpty(dto.getSupplementId())){
            queryWrapper.like("bcpo.supplement_id", dto.getSupplementId());
        }

        if (StringUtils.isNotEmpty(dto.getInstanceId())){
            queryWrapper.like("bcpo.instance_id", dto.getInstanceId());
        }

        queryWrapper.eq("bar.reconciliation_type",dto.getReconciliationType());
        if (StringUtils.isNotEmpty(dto.getCreateBy())){
            queryWrapper.eq("bar.create_by",dto.getCreateBy());
        }

        if (StringUtils.isNotEmpty(dto.getStartTime())){
           queryWrapper.apply("bar.create_time >= '"+dto.getStartTime()+"'");
        }
        if (StringUtils.isNotEmpty(dto.getEndTime())){
            queryWrapper.apply("bar.create_time <= '"+dto.getEndTime()+"'");
        }
        if (StringUtils.isNotEmpty(dto.getReconciliationStatus())){
            queryWrapper.eq("bar.reconciliation_status",dto.getReconciliationStatus());
        }
        queryWrapper.eq("brbp.del_flag ","0");
      //  queryWrapper.apply("( (bar.scode != bar.original_scode) or (bar.original_scode is null))");
        return adjustmentRecordService.getAdjustmentRecordList(queryWrapper);
    }

    @Override
    public List<String> getSupplementIdList(String vendor, String productCode) {
        return baseMapper.getSupplementIdList(vendor, productCode);
    }

    @Override
    public PageInfo<CmdbProductOverview> listParentNotFoundItems(int page, int perPage) {
        return PageHelper.startPage(page, perPage).doSelectPageInfo(
                () -> cmdbProductOverviewMapper.listParentNotFoundItems()
        );
    }

    @Override
    public CmdbProductOverview getParentInstanceByInstanceId(String instanceId) {
        return cmdbProductOverviewMapper.getParentInstanceByInstanceId(instanceId);
    }

    @Override
    public List<CmdbProductOverview> queryCmdbProductOverviewList(List<String> aggregatedIdList) {
        LambdaQueryWrapper<CmdbProductOverview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CmdbProductOverview::getAggregatedId,aggregatedIdList);
        return list(queryWrapper);
    }

    @Override
    public List<ReconciliationTask> getReconcliationTaskList(String adjustmentId) {
        return reconciliationTaskService.list(new LambdaQueryWrapper<ReconciliationTask>().eq(ReconciliationTask::getAdjustmentId,adjustmentId));
    }

    @Override
    public int correctProjectInfoByScode(String scode, String projectId, String projectName) {
        return cmdbProductOverviewMapper.correctProjectInfoByScode(scode, projectId, projectName);
    }

    @Override
    public List<CmdbProductOverview> listByAggregatedId(SqlSession sqlSession, String aggregatedId) {
        CmdbProductOverviewMapper mapper = sqlSession.getMapper(CmdbProductOverviewMapper.class);
        return mapper.listByAggregatedId(aggregatedId);
    }

    @Override
    public CmdbProductOverview getOneByAggregatedId(String aggregatedId) {
        LambdaQueryWrapper<CmdbProductOverview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmdbProductOverview::getAggregatedId,aggregatedId);
        return getOne(queryWrapper);
    }

}
