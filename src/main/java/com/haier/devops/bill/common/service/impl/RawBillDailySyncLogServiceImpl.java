package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.RawBillDailySyncLog;
import com.haier.devops.bill.common.mapper.RawBillDailySyncLogMapper;
import com.haier.devops.bill.common.service.RawBillDailySyncLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (RawBillDailySyncLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-31 16:51:06
 */
@Service("awBillDailySyncLogService")
public class RawBillDailySyncLogServiceImpl extends ServiceImpl<RawBillDailySyncLogMapper, RawBillDailySyncLog> implements RawBillDailySyncLogService {
    @Resource
    private RawBillDailySyncLogMapper rawBillDailySyncLogMapper;

    
}
