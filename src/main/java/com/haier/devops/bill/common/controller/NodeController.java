package com.haier.devops.bill.common.controller;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ExchangeRate;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.entity.RawBill;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.param.DimensionDetailParam;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.ColumnValueVo;
import com.haier.devops.bill.common.vo.NodeDetailBillsVo;
import com.haier.devops.bill.export.vo.UploadResult;
import com.haier.devops.bill.util.AuthUtil;
import com.haier.devops.bill.util.DateUtil;
import com.haier.devops.bill.util.HeaderUtil;
import com.haier.devops.bill.util.QueryWrapperUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.controller.ResponseEnum.*;
import static com.haier.devops.bill.common.enums.CurrencyEnum.CNY;
import static com.haier.devops.bill.common.enums.CurrencyEnum.USD;

/**
 * 节点信息查询
 *
 * <AUTHOR>
 */
@RestController
//@Controller
@RequestMapping("/api/v1/hcms/bill/nodes")
@Slf4j
@Validated
public class NodeController {
    private NodeService nodeService;
    private RawBillService rawBillService;
    private AdjustRecordService recordService;

    private ExportLogService exportLogService;

    private AggregatedBillService aggregatedBillService;

    @Autowired
    private ExchangeRateService exchangeRateService;

    public NodeController(NodeService nodeService,
                          RawBillService rawBillService,
                          AdjustRecordService recordService,
                          ExportLogService exportLogService,AggregatedBillService aggregatedBillService) {
        this.nodeService = nodeService;
        this.rawBillService = rawBillService;
        this.recordService = recordService;
        this.exportLogService = exportLogService;
        this.aggregatedBillService = aggregatedBillService;
    }



    /**
     * 查询账单明细
     *
     * @param request
     * @return
     */
    @PostMapping("/scode-detail")
    public ResponseEntityWrapper<PageInfo<RawBill>> selectDetailItems(@RequestBody @Valid RequestParamWrapper request) {
        PageInfo<RawBill> pager = new PageInfo<>();

        if (StringUtils.isBlank(request.getScode())) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "S码不可为空", pager);
        }

        QueryWrapper<RawBill> queryWrapper = QueryWrapperUtil.compose(request.getParams());
        queryWrapper.eq("scode", request.getScode());
        queryWrapper.apply("SUBSTR(billing_cycle, 1, 7) between {0} and {1}",
                request.getStartCycle(), request.getEndCycle());

        try {
            pager = rawBillService.selectPage(queryWrapper, request.getPage(), request.getPer_page());
        } catch (Exception e) {
            log.error("查询明细失败{}", JSON.toJSONString(request.getParams()), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询细失败", pager);
        }
        return new ResponseEntityWrapper<>(pager);
    }

    /**
     * 明细过滤器查询某一列的值以及个数
     *
     * @param request
     * @return
     */
    @PostMapping("/column-values")
    public ResponseEntityWrapper<List<ColumnValueVo>> queryColumnValues(@Valid @RequestBody RequestParamWrapper request) {
        String targetColumn = request.getTargetColumn();
        if (StringUtils.isBlank(targetColumn)) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "目标列不能为空", null);
        }

        if (!RawBill.getFields().contains(targetColumn)) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_ERR, "目标列不存在", null);
        }

        List<ColumnValueVo> columnValueVos = new ArrayList<>();
        try {
            QueryWrapper<RawBill> queryWrapper = QueryWrapperUtil.compose(request.getParams());
            queryWrapper.eq("scode", request.getScode());
            queryWrapper.apply("SUBSTR(billing_cycle, 1, 7) between {0} and {1}",
                    request.getStartCycle(), request.getEndCycle());
            columnValueVos = rawBillService.selectColumnValueCountByQueryWrapper(queryWrapper,
                    QueryWrapperUtil.camelToUnderscore(targetColumn));
        } catch (Exception e) {
            log.error("查询列值失败{}", JSON.toJSONString(request.getParams()), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询列值失败", columnValueVos);
        }
        return new ResponseEntityWrapper<>(columnValueVos);
    }


    /**
     * 获取最新的账单月份
     *
     * @return
     */
    @GetMapping("/latest-month")
    public ResponseEntityWrapper<String> getLatestMonth() {
        return new ResponseEntityWrapper<>(DateUtil.getLatestBillingMonth());
    }

    /**
     * 获取消费分析
     *
     * @return
     */
    @PostMapping("/getConsumptionAnalysis")
    public ResponseEntityWrapper<Map<String, Object>> getConsumptionAnalysis(@RequestBody @Valid NodeParam nodeParam) {
        try {

            return new ResponseEntityWrapper<>(nodeService.getConsumptionAnalysis(nodeParam.getScode()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取主要资产
     * @return
     */
    @PostMapping("/getAgentMainAssets")
    public ResponseEntityWrapper<Map<String, Integer>> getAgentMainAssets(@RequestBody NodeParam nodeParam,@RequestParam(required = false) String vendor) {
        try {
            List<String> appCodeList = null;
            if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
                appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                //数组转字符串用逗号隔开
                String scodes = StringUtils.join(appCodeList, ",");
                nodeParam.setScode(scodes);
            }else{
                List<String> domainCodeList = null;
                if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                    domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                }
                User currentUser = LoginContextHolder.getCurrentUser();
                String userCode = currentUser.getUserCode();
                boolean isAdmin = AuthUtil.isAdmin(currentUser);
                nodeParam.setScode(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
            }
            if(StringUtils.isNotEmpty(vendor)){
                nodeParam.setVendors(Arrays.asList(vendor));
            }
            if(StringUtils.isEmpty(nodeParam.getScode())){
                return new ResponseEntityWrapper<>(new HashMap<>());
            }
            return getMainAssetsAll(nodeParam);
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取主要资产详情
     * @return
     */
    @PostMapping("/getAgentMainAssetsDetail")
    public ResponseEntityWrapper<Map<String, Integer>> getAgentMainAssetsDetail(@RequestBody NodeParam nodeParam,@RequestParam(required = false) String vendor,@RequestParam String type) {
        try {
            List<String> appCodeList = null;
            if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
                appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                //数组转字符串用逗号隔开
                String scodes = StringUtils.join(appCodeList, ",");
                nodeParam.setScode(scodes);
            }else{
                List<String> domainCodeList = null;
                if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                    domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
                }
                User currentUser = LoginContextHolder.getCurrentUser();
                String userCode = currentUser.getUserCode();
                boolean isAdmin = AuthUtil.isAdmin(currentUser);
                nodeParam.setScode(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
            }
            if(StringUtils.isNotEmpty(vendor)){
                nodeParam.setVendors(Arrays.asList(vendor));
            }
            if(StringUtils.isEmpty(nodeParam.getScode())){
                return new ResponseEntityWrapper<>(new HashMap<>());
            }
            return getMainAssetsDetail(nodeParam,type);
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    private ResponseEntityWrapper<Map<String, Integer>> getMainAssetsDetail(NodeParam nodeParam, String type) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getMainAssetsDetail(nodeParam.getScode(),
                            nodeParam.getVendors(),nodeParam.getStartDate(),nodeParam.getEndDate(),type));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    private ResponseEntityWrapper<Map<String, Integer>> getMainAssetsAll(NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getMainAssets(nodeParam.getScode(),
                            nodeParam.getVendors()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取主要资产
     * @return
     */
    @PostMapping("/getMainAssets")
    public ResponseEntityWrapper<Map<String, Integer>> getMainAssets(@RequestBody NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getMainAssets(nodeParam.getScode(),
                            nodeParam.getVendors(),
                            CNY.getCurrency()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }


    /**
     * 获取主要资产
     * @return
     */
    @PostMapping("/getMainAssets/usd")
    public ResponseEntityWrapper<Map<String, Integer>> getMainAssetsWithUsd(@RequestBody NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getMainAssets(nodeParam.getScode(),
                            nodeParam.getVendors(),
                            USD.getCurrency()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取消费趋势分析
     *
     * @return
     */
    @PostMapping("/getAgentConsumerTrends")
    public ResponseEntityWrapper<List<Map<String, Object>>> getAgentConsumerTrends(@RequestBody NodeParam nodeParam,

                                                                                   @RequestParam(required = false) String vendor) {
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
            appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            nodeParam.setScode(scodes);
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            nodeParam.setScode(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }
        if(StringUtils.isNotEmpty(vendor)){
            nodeParam.setVendors(Arrays.asList(vendor));
        }
        LocalDate startDate = nodeParam.getStartDate();
        //根据startDate获取上个月的1号
        startDate = startDate.minus(1, ChronoUnit.MONTHS).withDayOfMonth(1);
        nodeParam.setStartDate(startDate);
        NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        List<Map<String, Object>> list = new ArrayList<>();
        if(StringUtils.isEmpty(nodeParam.getScode())){
            return new ResponseEntityWrapper<>(list);
        }
        if("CNY".equals(nodeParam.getCurrency())){
            list = getConsumerTrends(nodeParam).getData();
        }else if("USD".equals(nodeParam.getCurrency())){
            list = getConsumerTrendsWithUsd(nodeParam).getData();
        }else{
            list = getConsumerTrendsAll(nodeParam).getData();
        }

        if(CollectionUtils.isEmpty(list)){
            return new ResponseEntityWrapper<>(list);
        }
        BigDecimal firstAmount = new BigDecimal(0);
        for (int i = 0; i < list.size(); i++) {
            Map<String,Object> map = list.get(i);
            if(i == 0){
                map.put("changeRate","--");
            }else{
                BigDecimal amountOfChangeInBigdecimal = new BigDecimal(map.get("amountOfChange").toString());
                String symbol = "";
                if(amountOfChangeInBigdecimal.compareTo(BigDecimal.ZERO) < 0){
                    amountOfChangeInBigdecimal = amountOfChangeInBigdecimal.multiply(BigDecimal.valueOf(-1));
                    symbol = "↓";
                }else if(amountOfChangeInBigdecimal.compareTo(BigDecimal.ZERO) > 0){
                    symbol = "↑";
                }
                map.put("changeRate",new StringBuffer(symbol).append(BigDecimal.ZERO.compareTo(firstAmount) == 0?"--":amountOfChangeInBigdecimal.multiply(BigDecimal.valueOf(100)).divide(firstAmount, 2, RoundingMode.HALF_UP)).append("%"));
            }
            firstAmount = new BigDecimal(map.get("value").toString());
            map.put("amountOfChange", nf.format(Double.parseDouble(map.get("amountOfChange").toString())));
            map.put("value", new BigDecimal(map.get("value").toString()).divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP));
        }
        //list移除第一项
        list.remove(0);
        return new ResponseEntityWrapper<>(list);
    }

    private ResponseEntityWrapper<List<Map<String, Object>>> getConsumerTrendsAll(NodeParam nodeParam) {
        try {
            ExchangeRate exchangeRate = null;
            try {
                String date = DateUtils.format(new Date(), "yyyy-MM-dd");
                exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumerTrends(nodeParam.getVendors(),
                            nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),exchangeRate.getExchangeRate()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }


    /**
     * 获取消费趋势分析
     *
     * @return
     */
    @PostMapping("/getConsumerTrends")
    public ResponseEntityWrapper<List<Map<String, Object>>> getConsumerTrends(@RequestBody NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumerTrends(nodeParam.getVendors(),
                            nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),
                            CNY.getCurrency()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取消费趋势分析
     *
     * @return
     */
    @PostMapping("/getConsumerTrends/usd")
    public ResponseEntityWrapper<List<Map<String, Object>>> getConsumerTrendsWithUsd(@RequestBody NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumerTrends(
                            nodeParam.getVendors(),
                            nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),
                            USD.getCurrency()));
        } catch (Exception e){
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取消费分步分析
     * @return
     */
    @PostMapping("/getAgentConsumptionStepByStep")
    public ResponseEntityWrapper<List<Map<String, Object>>> getAgentConsumptionStepByStep(@RequestBody NodeParam nodeParam,@RequestParam(required = false) String vendor) {
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(nodeParam.getAppList())){
            appCodeList = nodeParam.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            nodeParam.setScode(scodes);
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(nodeParam.getDomainList())){
                domainCodeList = nodeParam.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            nodeParam.setScode(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }

        if("subProduct".equals(nodeParam.getFilter())){
            nodeParam.setFilter("scode");
        }
        if(StringUtils.isNotEmpty(vendor)){
            nodeParam.setVendors(Arrays.asList(vendor));
        }
        List<Map<String, Object>> list = new ArrayList<>();
        if(StringUtils.isEmpty(nodeParam.getScode())){
            return new ResponseEntityWrapper<>(list);
        }
        if("CNY".equals(nodeParam.getCurrency())){
            list =getConsumptionStepByStep(nodeParam).getData();
        }else if("USD".equals(nodeParam.getCurrency())){
            list =getConsumptionStepByStepWithUsd(nodeParam).getData();
        }else{
            list =getConsumptionStepByStepAll(nodeParam).getData();
        }
        if(CollectionUtils.isEmpty(list)){
            return new ResponseEntityWrapper<>(list);
        }
        NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        //比较list 中每个map的value值，按value 排序
        list.sort((o1, o2) -> {
            double v1 = Double.parseDouble(o1.get("value").toString());
            double v2 = Double.parseDouble(o2.get("value").toString());
            //千位分隔符‌ v1
            return v2 > v1 ? 1 : -1;
        });
        List<Map<String, Object>> firstList = list;
        // 取前10个
        if (list.size() > 10) {
            firstList = list.subList(0, 10);
            //将剩下的取和
            Map lastMap = new HashMap();
            switch (nodeParam.getFilter()){
                case "scode":
                    lastMap.put("subProductName", "其他");
                    break;
                case "vendor":
                    lastMap.put("vendor", "其他");
                    break;
                case "productType":
                    lastMap.put("productName", "其他");
                    break;
                default:
                    lastMap.put("subProductName", "其他");
                    break;
            }
            List<Map<String, Object>> lastList = list.subList(10, list.size());
            lastMap.put("value", lastList.stream().mapToDouble(map -> Double.parseDouble(map.get("value").toString())).sum());
            lastMap.put("date",list.get(0).get("date"));
            firstList.add(lastMap);
        }

//        for (Map<String, Object> map : list) {
//            map.put("value", nf.format(Double.parseDouble(map.get("value").toString())));
//            map.put("amountOfChange",nf.format(Double.parseDouble(map.get("amountOfChange").toString())));
//            map.put("changeRate",new StringBuffer(map.get("changeRate").toString()).append("%"));
//        }
        return new ResponseEntityWrapper<>(firstList);
    }

    private ResponseEntityWrapper<List<Map<String, Object>>> getConsumptionStepByStepAll(NodeParam nodeParam) {
        try {
            String date = DateUtils.format(new Date(), "yyyy-MM-dd");

            ExchangeRate exchangeRate = null;
            try {
                exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumptionStepByStep(nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),
                            nodeParam.getFilter(),
                            nodeParam.getVendors(),
                            exchangeRate.getExchangeRate()));
        } catch (Exception e){
            log.error("getConsumptionStepByStep is error", e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取消费分步分析
     * @return
     */
    @PostMapping("/getConsumptionStepByStep")
    public ResponseEntityWrapper<List<Map<String, Object>>> getConsumptionStepByStep(@RequestBody NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumptionStepByStep(nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),
                            nodeParam.getFilter(),
                            nodeParam.getVendors(),
                            CNY.getCurrency()));
        } catch (Exception e){
            log.error("getConsumptionStepByStep is error", e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }

    /**
     * 获取消费分步分析
     * @return
     */
    @PostMapping("/getConsumptionStepByStep/usd")
    public ResponseEntityWrapper<List<Map<String, Object>>> getConsumptionStepByStepWithUsd(@RequestBody
                                                                                            NodeParam nodeParam) {
        try {
            return new ResponseEntityWrapper<>(
                    nodeService.getConsumptionStepByStep(
                            nodeParam.getStartDate(),
                            nodeParam.getEndDate(),
                            nodeParam.getType(),
                            nodeParam.getScode(),
                            nodeParam.getFilter(),
                            nodeParam.getVendors(),
                            USD.getCurrency())
            );
        } catch (Exception e){
            log.error("getConsumptionStepByStep is error", e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, e.getMessage(), null);
        }
    }
    /**
     * 查询各维度的账单明细
     *
     * @param request
     * @return
     */
    @PostMapping("/get/dimension/detail")
    public ResponseEntityWrapper<IPage<NodeDetailBillsVo>> getDimensionDetailItems(@RequestBody DetailBillParam request) {
        if (StringUtils.isAnyBlank(request.getScode(), request.getType(), request.getStartCycle(), request.getEndCycle())) {
            log.error("getDimensionDetailItems is error param is miss,scode {} or type {} or start {} or end {} is null", request.getScode(), request.getType(), request.getStartCycle(), request.getEndCycle());
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "param is miss,scode or type or start or end is null", null);
        }
        try {
            return new ResponseEntityWrapper<>(nodeService.getDetailBillsByPage(request));
        } catch (Exception e) {
            log.error("查询多维度明细失败{}", JSON.toJSONString(request), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询多维度细失败"+e.getMessage(), null);
        }
    }

    /**
     * @Description: 多维度消费分析
     * @Author: 王帅
     * @Date: 2024/1/5 11:29
     * @Param: [request]
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.baomidou.mybatisplus.core.metadata.IPage<com.haier.devops.bill.common.vo.NodeDetailBillsVo>>
     */
    @PostMapping("/get/dimension/getAgentConsumption")
    public ResponseEntityWrapper<List<Map<String, Object>>> getAgentConsumption(@RequestBody DimensionDetailParam request,@RequestParam(required = false) String dimension
    ,@RequestParam(required = false) String vendor) {
        List<String> appCodeList = null;
        if(!CollectionUtils.isEmpty(request.getAppList())){
            appCodeList = request.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            request.setScode(scodes);
            request.setDataDimensions(Arrays.asList("productType"));
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(request.getDomainList())){
                domainCodeList = request.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            request.setDataDimensions(Arrays.asList(dimension));
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            request.setScode(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }
        if(StringUtils.isNotEmpty(vendor)){
            request.setVendor(vendor);
            request.setDataDimensions(Arrays.asList("productType"));
        }
        List<Map<String, Object>> list = new ArrayList<>();
        if(StringUtils.isEmpty(request.getScode())){
            return new ResponseEntityWrapper<>(list);
        }
        if("CNY".equals(request.getCurrency())){
            list = getDimensionConsumption(request).getData();
        }else if("USD".equals(request.getCurrency())){
            list = getDimensionConsumptionWithUsd(request).getData();
        }else{
            list = getDimensionConsumptionAll(request).getData();
        }

        if(CollectionUtils.isEmpty(list)){
            return new ResponseEntityWrapper<>(list);
        }
        //获取上个月的月份
        String startDateStr = DateUtil.getLastMonth();
        list.sort((o1, o2) -> {
            Map map1 = (Map) o1.get("slice");
            Map map2 = (Map) o2.get("slice");
            double v1 = Double.parseDouble(ObjectUtils.isEmpty(map1.get(startDateStr)) ? "0" : map1.get(startDateStr).toString());
            double v2 = Double.parseDouble(ObjectUtils.isEmpty(map2.get(startDateStr)) ? "0" : map2.get(startDateStr).toString());
            return v2 > v1 ? 1 : -1;
        });
        if (list.size() > 10){
            list = list.subList(0, 10);
        }
       // NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        for (Map<String, Object> objectMap : list){
            Map<String, Object> map = (Map) objectMap.get("slice");
            for (String key : map.keySet()) {
                map.put(key, new BigDecimal(map.get(key).toString()).divide(new BigDecimal("10000"), 2, BigDecimal.ROUND_HALF_UP));
            }
        }
        return new ResponseEntityWrapper<>(list);
    }

    private ResponseEntityWrapper<List<Map<String, Object>>> getDimensionConsumptionAll(DimensionDetailParam request) {
        if (StringUtils.isAnyBlank(request.getDataType(),request.getCycleType(),  request.getStartCycle(), request.getEndCycle())) {
            log.error("getDimensionConsumption is error param is miss,scode {} or dataType {}  or cycleTpe {} or start {} or end {} is null", request.getScode(), request.getDataType(),request.getCycleType(), request.getStartCycle(), request.getEndCycle());
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "param is miss,scode or dataType or cycleTpe or start or end is null", null);
        }
        try {
            String date = DateUtils.format(new Date(), "yyyy-MM-dd");

            ExchangeRate exchangeRate = null;
            try {
                exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return new ResponseEntityWrapper<>(nodeService.getDimensionCustDetail(request, exchangeRate.getExchangeRate()));
        } catch (Exception e) {
            log.error("查询多维度消费分析失败{}", JSON.toJSONString(request), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询多维度消费分析失败"+e.getMessage(), null);
        }
    }


    /**
     * @Description: 多维度消费分析
     * @Author: 王帅
     * @Date: 2024/1/5 11:29
     * @Param: [request]
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.baomidou.mybatisplus.core.metadata.IPage<com.haier.devops.bill.common.vo.NodeDetailBillsVo>>
     */
    @PostMapping("/get/dimension/consumption")
    public ResponseEntityWrapper<List<Map<String, Object>>> getDimensionConsumption(@RequestBody DimensionDetailParam request) {
        if (StringUtils.isAnyBlank(request.getDataType(),request.getCycleType(),  request.getStartCycle(), request.getEndCycle())) {
            log.error("getDimensionConsumption is error param is miss,scode {} or dataType {}  or cycleTpe {} or start {} or end {} is null", request.getScode(), request.getDataType(),request.getCycleType(), request.getStartCycle(), request.getEndCycle());
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "param is miss,scode or dataType or cycleTpe or start or end is null", null);
        }
        try {
            return new ResponseEntityWrapper<>(nodeService.getDimensionCustDetail(request, CNY.getCurrency()));
        } catch (Exception e) {
            log.error("查询多维度消费分析失败{}", JSON.toJSONString(request), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询多维度消费分析失败"+e.getMessage(), null);
        }
    }


    /**
     * 多维度消费分析usd
     * @param request
     * @return
     */
    @PostMapping("/get/dimension/consumption/usd")
    public ResponseEntityWrapper<List<Map<String, Object>>> getDimensionConsumptionWithUsd(@RequestBody DimensionDetailParam request) {
        if (StringUtils.isAnyBlank(request.getDataType(),request.getCycleType(),  request.getStartCycle(), request.getEndCycle())) {
            log.error("getDimensionConsumption is error param is miss,scode {} or dataType {}  or cycleTpe {} or start {} or end {} is null", request.getScode(), request.getDataType(),request.getCycleType(), request.getStartCycle(), request.getEndCycle());
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "param is miss,scode or dataType or cycleTpe or start or end is null", null);
        }
        try {
            return new ResponseEntityWrapper<>(nodeService.getDimensionCustDetail(request, USD.getCurrency()));
        } catch (Exception e) {
            log.error("查询多维度消费分析失败{}", JSON.toJSONString(request), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "查询多维度消费分析失败"+e.getMessage(), null);
        }
    }

    /**
    * @Description: 根据币种获取账号
    * @author: 张爱苹
    * @date: 2025/5/29 14:07
    * @param currentCy:
    * @Return: void
    */
    @GetMapping("/getAccountListByCurrentCy")
    public ResponseEntityWrapper<List<String>> getAccountListByCurrentCy(@RequestParam String currentCy) {
        return new ResponseEntityWrapper<>(nodeService.getAccountListByCurrentCy(currentCy));
    }

    /**
     * @Description:  导出多维度消费分析
     * @author: 张爱苹
     * @date: 2024/4/12 16:20
     * @param request:
     * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.haier.devops.bill.common.entity.ExportLog>
     */
    @PostMapping("/download")
    public void downloadDimensionDetail(@RequestBody DimensionDetailParam dto, HttpServletRequest request, HttpServletResponse response) {
        nodeService.exportBillExcel(dto,request,response);
    }

    /**
     * 查询各维度的账单明细
     *
     * @param request
     * @return
     */
    @PostMapping("/download/dimension/detail")
    public ResponseEntityWrapper<ExportLog> downloadDimensionDetail(@RequestBody DetailBillParam request) {
        if (StringUtils.isAnyBlank(request.getScode(), request.getType(), request.getStartCycle(), request.getEndCycle(),request.getSubmitter())) {
            log.error("getDimensionDetailItems is error param is miss,scode {} or type {} or start {} or end {}  or submitter {} is null", request.getScode(), request.getType(), request.getStartCycle(), request.getEndCycle(),request.getSubmitter());
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "param is miss,scode or type or start or end is null", null);
        }
        try {
            List<String> permScodes = nodeService.getPermScodes(request.getScode(), "downloadDimensionDetail");
            if (org.springframework.util.CollectionUtils.isEmpty(permScodes)) {
                log.error("the user {} not this scodes permission {}", HeaderUtil.getUserId(),request.getScode());
                throw new RuntimeException("此用户没有这些子产品权限");
            }
            request.setPermScodes(permScodes);
            Date submitTime = new Date();
            String serialNo = UUID.randomUUID().toString();
            ExportLog exportLog = ExportLog.builder()
                    .serialNo(serialNo)
                    .submitTime(submitTime)
                    .submitter(request.getSubmitter())
                    .startCycle(request.getStartCycle())
                    .endCycle(request.getEndCycle())
                    .build();
            exportLogService.save(exportLog);
            request.setSerialNo(serialNo);
            new Thread(() -> {
                try {
                    UploadResult uploadResult = nodeService.downloadDetailBills(request);
                } catch (Exception e) {
                    log.error("downloadDimensionDetail is exception {}",JSON.toJSONString(request),e);
                }
            }).start();
            return new ResponseEntityWrapper<>(exportLog);
        } catch (Exception e) {
            log.error("下载多维度明细失败{}", JSON.toJSONString(request), e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "下载多维度明细失败"+e.getMessage(), null);
        }
    }
}



@Getter
@Setter
class RequestParamWrapper {
    /**
     * 过滤器
     */
    List<QueryWrapperUtil.Filter> params;

    /**
     * S码
     */
    @NotNull(message = "s码不可为空")
    String scode;

    @Min(1)
    Integer page = 1;
    @Min(1)
    Integer per_page = 10;

    /**
     * 开始周期
     */
    @NotNull(message = "开始周期不可为空")
    String startCycle;

    /**
     * 结束周期
     */
    @NotNull(message = "结束周期不可为空")
    String endCycle;

    /**
     * 目标列
     */
    private String targetColumn;

    /**
     * 提交人
     */
    private String submitter;
}

@Data
class NodeParam {

    /**
     * 代码编号
     **/
    @NotNull(message = "s码不可为空")
    private String scode;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 类型
     */
    private String type;

    /**
     * 过滤条件
     */
    private String filter;

    /**
     * 云厂商（可多个）
     */
    private List<String> vendors;

    private List<Map<String, String>> appList;

    private List<Map<String,String>> domainList;

    private String currency;


}
