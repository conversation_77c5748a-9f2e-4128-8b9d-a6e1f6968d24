package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.Contract;
import com.haier.devops.bill.common.mapper.ContractMapper;
import com.haier.devops.bill.common.service.ContractService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 合同验收 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements ContractService {

}
