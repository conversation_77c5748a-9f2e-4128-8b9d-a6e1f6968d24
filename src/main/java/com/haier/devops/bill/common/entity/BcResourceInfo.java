package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (BcResourceInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-12-12 14:27:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class BcResourceInfo implements Serializable {
    private static final long serialVersionUID = 771685458900294897L;
       
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;
     
       
    @Schema(description = "云服务ID")
    private String service;
     
       
    @Schema(description = "资源类型")
    private String resourceType;
     
       
    @Schema(description = "资源组ID")
    private String resourceGroupId;
     
       
    @Schema(description = "资源ID")
    private String resourceId;
     
       
    @Schema(description = "资源创建时间（UTC时间）")
    private LocalDateTime createDate;
     
       
    @Schema(description = "地域ID")
    private String regionId;
     
       
    @Schema(description = "创建日期")
    private LocalDateTime createTime;
     
       
    @Schema(description = "更新日期")
    private LocalDateTime updateTime;
     

}

