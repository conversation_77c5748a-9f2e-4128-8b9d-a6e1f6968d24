package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.AlarmOperateLog;
import com.haier.devops.bill.common.mapper.AlarmOperateLogMapper;
import com.haier.devops.bill.common.service.AlarmOperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* @ClassName: AlarmConfigurationServiceImpl
* @Description: 告警配置Service实现类
* @author: 张爱苹
* @date: 2024/1/11 10:53
*/
@Service
@Slf4j
public class AlarmOperateLogServiceImpl extends ServiceImpl<AlarmOperateLogMapper, AlarmOperateLog> implements AlarmOperateLogService {

}
