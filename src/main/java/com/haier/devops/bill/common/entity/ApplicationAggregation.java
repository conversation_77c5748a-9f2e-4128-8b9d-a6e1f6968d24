package com.haier.devops.bill.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.Expose;
import com.haier.devops.bill.acceptance.converter.CloudServiceTypeConverter;
import com.lark.oapi.service.attendance.v1.model.ApprovalInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 到S码汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@TableName("bc_application_aggregation")
@Data
@Schema(description = "到S码汇总表")
public class ApplicationAggregation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    @Expose(serialize = false)
    private Integer id;

    @Schema(description = "订单ID")
    @Expose
    private String orderId;

    @Schema(description = "厂商")
    @Expose
    private String factory;

    @Schema(description = "账号")
    @Expose
    private String account;

    /**
     * 1:云资源，2:云桌面
     */
    @Schema(description = "服务类型,1:云资源，2:云桌面")
    @Expose
    @ExcelProperty(value = "云服务", converter = CloudServiceTypeConverter.class)
    private Integer type;

    /**
     * 所属系统编码（云资源：S码，云桌面：产业编码）
     */
    @Schema(description = "所属系统编码（云资源：S码，云桌面：产业编码）")
    @Expose
    @ExcelProperty("产业编码")
    private String sysCode;

    /**
     * 所属系统名称（云资源：系统名，云桌面：产业名称）
     */
    @Schema(description = "所属系统名称（云资源：系统名，云桌面：产业名称）")
    @Expose
    @ExcelProperty("产业名称")
    private String sysName;

    /**
     * 账期
     */
    @Schema(description = "账期")
    @Expose
    @ExcelProperty("账期")
    private String payDate;

    /**
     * 预估金额
     */
    @Schema(description = "预估金额")
    @Expose
    private BigDecimal estimateAmount;

    /**
     * 实际金额
     */
    @Schema(description = "实际金额")
    @Expose
    @ExcelProperty("实际金额(元)")
    private BigDecimal actualAmount;

    /**
     * 预算编码
     */
    @Schema(description = "预算编码")
    @Expose
    @ExcelProperty("预算编码")
    private String budgetCode;

    /**
     * 负责人工号
     */
    @Schema(description = "负责人工号")
    @Expose
    @ExcelProperty("验收负责人")
    private String userCode;

    /**
     * 负责人姓名
     */
    @Schema(description = "负责人姓名")
    @Expose
    private String userName;

    /**
     * alm里程碑id
     */
    @Schema(description = "alm里程碑id")
    @Expose
    @ExcelProperty("里程碑编码")
    private String almMilestoneId;

    /**
     * 里程碑名称
     */
    @Schema(description = "里程碑名称")
    @Expose
    @ExcelProperty("里程碑名称")
    private String milestoneName;

    /**
     * 所属项目编码
     */
    @Schema(description = "所属项目编码")
    @Expose
    private String almProjectCode;

    /**
     * 所属项目名称
     */
    @Schema(description = "所属项目名称")
    @Expose
    private String almProjectName;

    /**
     * 所属合同编码
     */
    @Schema(description = "所属合同编码")
    @Expose
    private String contractCode;

    /**
     * 所属合同名称
     */
    @Schema(description = "所属合同名称")
    @Expose
    private String contractName;

    /**
     * 供应商V码
     */
    @Schema(description = "供应商V码")
    @Expose
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @Expose
    private String supplierName;

    /**
     * 账单生成时间
     */
    @Schema(description = "账单生成时间")
    @Expose
    private String generationTime;

    @Schema(description = "创建时间")
    @Expose(serialize = false)
    @JsonIgnore
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @Expose(serialize = false)
    @JsonIgnore
    private LocalDateTime updateTime;

    /**
     * 订单状态：0：待推送，1：已推送未验收，2：已验收
     */
    @Schema(description = "订单状态：0：待推送，1：已推送未验收，2：已验收")
    @Expose(serialize = false)
    private Integer status;

    /**
     * 确认单流水号
     */
    @Schema(description = "确认单流水号")
    @Expose
    private String confirmationSerialNo;

    /**
     * 系统确认人
     */
    @Schema(description = "系统确认人")
    @Expose(serialize = false)
    private String systemConfirmer;

    /**
     * 系统确认人姓名
     */
    @Schema(description = "系统确认人姓名")
    @Expose
    private String systemConfirmerName;

    /**
     * 领域确认人
     */
    @Schema(description = "领域确认人")
    @Expose(serialize = false)
    private String domainConfirmer;

    /**
     * 领域确认人姓名
     */
    @Schema(description = "领域确认人姓名")
    @Expose
    private String domainConfirmerName;
}
