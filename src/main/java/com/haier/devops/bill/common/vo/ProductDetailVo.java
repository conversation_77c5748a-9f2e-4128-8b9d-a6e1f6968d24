package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: BillGathered
* @Description: 每日账单汇总
* @author: 张爱苹
* @date: 2024/1/16 09:18
*/
@Data
public class ProductDetailVo implements Serializable {


    private static final long serialVersionUID = -7362088286757220241L;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 记录的账号信息-账户名称
     */
    private String accountName;

    /**
     * 记录的账号信息-账户ID
     */
    private String accountId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源id
     */
    private String instanceId;

    /**
     * S码
     */
    private String scode;

    /**
     * 项目简称
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    private String supplementId;


}
