package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 到资源的汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface ResourceAggregationMapper extends BaseMapper<ResourceAggregation> {
    /**
     * 按照资源查询月度账单明细
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ResourceAggregation> queryBillByResource(@Param("vendor") String vendor,
                                                  @Param("billingCycle") String billingCycle,
                                                  @Param("account") String... account);

    /**
     * 根据多个汇总账单查明细账单
     * @param aggregations
     * @return
     */
    List<ResourceAggregation> selectByApplications(@Param("aggregations") List<ApplicationAggregation> aggregations);

    void deleteByVendorAndBillingCycleAndStageAndAccount(@Param("vendor") String vendor,
                                                       @Param("billingCycle") String billingCycle,
                                                       @Param("account") String... account);

    /**
     * 查找待推送的项目
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ResourceAggregation> selectPendingSyncingItems(@Param("vendor") String vendor,
                                                        @Param("billingCycle") String billingCycle,
                                                        @Param("account") String... account);
}
