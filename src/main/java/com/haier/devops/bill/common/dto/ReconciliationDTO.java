package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: ReconciliationDTO
* @Description: ReconciliationDTO
* @author: 张爱苹
* @date: 2024/3/8 17:36
*/
@Data
public class ReconciliationDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 8972029648977388461L;
    /**
     * S码
     */
    @NotEmpty
    private String scode;

    @NotEmpty
    private String originalScode;

    @NotEmpty
    private String startDate;

    @NotEmpty
    private String endDate;

    private String aggregatedId;


}
