package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.BcRawBillProducts;
import com.haier.devops.bill.common.mapper.BcRawBillProductsMapper;
import com.haier.devops.bill.common.service.BcRawBillProductsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (BcRawBillProducts)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-10 13:42:43
 */
@Service("bcRawBillProductsService")
public class BcRawBillProductsServiceImpl extends ServiceImpl<BcRawBillProductsMapper, BcRawBillProducts> implements BcRawBillProductsService {
    @Resource
    private BcRawBillProductsMapper bcRawBillProductsDao;

    
}
