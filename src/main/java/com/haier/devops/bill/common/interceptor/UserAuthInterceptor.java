package com.haier.devops.bill.common.interceptor;


import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.enums.NamespaceEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class UserAuthInterceptor implements HandlerInterceptor {
    private static final String[] X_USER_KEY = {"X-User", "X-USER", "x-user", "x-User"};
    private static final String SCOPE = "Scope";
    private static final String ACCEPTANCE_URI = "/api/v1/hcms/bill/acceptance";
    private static final String SWAGGER_URI = "/swagger";
    private static final String ERROR = "/error";

    private final HworkAuthorityApi hworkAuthorityApi;

    public UserAuthInterceptor(HworkAuthorityApi hworkAuthorityApi) {
        this.hworkAuthorityApi = hworkAuthorityApi;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if ("127.0.0.1".equals(request.getRemoteAddr()) || "localhost".equals(request.getRemoteAddr())) {
            return true;
        }
        if (request.getRequestURI().startsWith(ACCEPTANCE_URI)
                || request.getRequestURI().startsWith(SWAGGER_URI)
                || request.getRequestURI().startsWith(ERROR)) {
            return true;
        }

        Optional<String> userCodeOpt = Arrays.stream(X_USER_KEY)
                .filter(k -> StringUtils.isNotBlank(request.getHeader(k)))
                .findFirst();

        if (userCodeOpt.isPresent()) {
            String userCode = request.getHeader(userCodeOpt.get());
            HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> resultWrapper =
                    hworkAuthorityApi.getUserDetail(userCode, true);
            if (resultWrapper.getCode() == HttpServletResponse.SC_OK) {
                HworkAuthorityApi.User huser = resultWrapper.getData();
                User user = User.builder()
                        .userCode(huser.getUserCode())
                        .userName(huser.getUserName())
                        .employeeStatus(huser.getEmployeeStatus())
                        .email(huser.getEmail())
                        .deptId(huser.getDeptId())
                        .deptName(huser.getDeptName())
                        .roles(huser.getRoles())
                        .build();

                String scope = request.getHeader(SCOPE);
                if (NamespaceEnum.getNamespaceMap().containsKey(scope)) {
                    LoginContextHolder.setNamespaceScope(scope);
                } 
                LoginContextHolder.setCurrentUser(user);
                return true;
            } else {
                response.sendError(resultWrapper.getCode(), resultWrapper.getMessage());
                return false;
            }

        } else {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Access Denied");
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理用户信息，确保不会在其他请求中保留
        LoginContextHolder.clearContext();
    }
}
