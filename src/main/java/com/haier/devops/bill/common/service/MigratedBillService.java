package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.MigratedBill;

/**
 * <p>
 * 账单明细汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface MigratedBillService extends IService<MigratedBill> {

    /**
     * 清理脏数据
     * @param subTaskId
     * @return
     */
    int cleanDirtyData(String subTaskId);

    /**
     * 获取替换任务的总金额
     * @param subTaskId
     * @return
     */
    String getSumBySubTaskId(String subTaskId);
}
