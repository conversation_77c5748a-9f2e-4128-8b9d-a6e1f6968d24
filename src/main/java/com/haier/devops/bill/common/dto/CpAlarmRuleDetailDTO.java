package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* @ClassName: CpAlarmRuleDetailDTO
* @Description: 云产品告警规则详情
* @author: 张爱苹
* @date: 2024/1/12 13:52
*/
@Data
public class CpAlarmRuleDetailDTO implements Serializable {

    private static final long serialVersionUID = 5211279623714826856L;

    /**
     * 通知方式
     */
    private String noticeWay;

    /**
     * 通知对象
     */
    private String noticeObj;

    /**
     * 通知对象
     */
    private List<AlarmNoticeObjDTO> alarmNoticeObjList;

}
