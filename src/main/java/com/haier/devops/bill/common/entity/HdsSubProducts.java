package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.task.data_completion.CommonUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 产品实体类
 */
@Data
@TableName("bc_hds_sub_products")
@Component
public class HdsSubProducts {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String appScode;

    private String appShortName;

    private String appName;

    private String businessDomainIds;

    private String businessDomains;

    private String description;

    private String itManagerName;

    private String itManager;

    private String itOpsManager;

    private String itOpsManagerName;

    private String owner;

    private String ownerName;

    private String orgId;

    private String orgName;

    private String productId;

    private String  productName;

    private String subProductId;

    private String subProductName;

    private String subProductNameEn;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

    // 用于注入CommonUtil的静态实例
    private static CommonUtil commonUtil;

    @Autowired
    public void setCommonUtil(CommonUtil commonUtil) {
        HdsSubProducts.commonUtil = commonUtil;
    }

    public HdsSubProducts() {
    }

    public HdsSubProducts(HdsOpenApi.SubProduct subProduct) {
        this.appScode = subProduct.getScode();
        this.appName = subProduct.getApplicationName();
        this.appShortName = subProduct.getApplicationNameShort();
        this.description = subProduct.getApplicationDesc();
        this.subProductId = subProduct.getApplicationCode();
        this.subProductName = subProduct.getApplicationName();
        this.businessDomainIds = subProduct.getModuleDomainCode();
        this.businessDomains = subProduct.getModuleDomainName();
        this.productId = subProduct.getProductPuid();
        this.productName = subProduct.getProductName();
        this.delFlag = subProduct.getIfOnline() ? "0" : "1";
        
        // 从com.haier.devops.bill.common.api.HdsOpenApi#queryAlmProject接口获取owner信息
        if (this.appScode != null && !this.appScode.isEmpty() && commonUtil != null) {
            HdsOpenApi.AlmProject almProject = commonUtil.getAlmProjectInfo(this.appScode);
            if (almProject != null) {
                this.owner = almProject.getOwner();
                this.ownerName = almProject.getOwnerName();
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HdsSubProducts that = (HdsSubProducts) o;
        return Objects.equals(appScode, that.appScode) && Objects.equals(appShortName, that.appShortName) && Objects.equals(orgId, that.orgId) && Objects.equals(productId, that.productId) && Objects.equals(subProductId, that.subProductId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appScode, appShortName, orgId, productId, subProductId);
    }
}
