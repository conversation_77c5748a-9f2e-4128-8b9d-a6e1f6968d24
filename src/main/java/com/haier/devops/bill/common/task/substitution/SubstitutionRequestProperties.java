package com.haier.devops.bill.common.task.substitution;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "substitution.request")
@Data
public class SubstitutionRequestProperties {
    private List<SubstitutionRequest> substitutionRequests;
}
