package com.haier.devops.bill.common.redis;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class Queue<T> {
    private final RedisTemplate<String, T> redisTemplate;
    private final String queue;

    public Queue(RedisTemplate<String, T> redisTemplate, String queue) {
        this.redisTemplate = redisTemplate;
        this.queue = queue;
    }

    /**
     * 向队列头部添加一个元素
     * @param t
     */
    public void add(T t) {
        redisTemplate.opsForList().leftPush(queue, t);
    }

    /**
     * 查看队列尾部一个元素
     * @return
     */
    public T get() {
        List<T> list = redisTemplate.opsForList().range(queue, -1, -1);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 从队列尾部取出一个元素
     * @return
     */
    public T pop() {
        return redisTemplate.opsForList().rightPop(queue);
    }

    /**
     * 获取队列长度
     * @return
     */
    public long size() {
        Long size = redisTemplate.opsForList().size(queue);
        return null == size ? 0 : size;
    }

    /**
     * 获取队列所有元素
     * @return
     */
    public List<T> getAll() {
        return redisTemplate.opsForList().range(queue, 0, -1);
    }

    /**
     * 删除队列中的元素
     * @param t
     */
    public void remove(T t) {
        redisTemplate.opsForList().remove(queue, 0, t);
    }
}
