package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.TencentPPMatchRules;
import com.haier.devops.bill.common.mapper.TencentPPMatchRulesMapper;
import com.haier.devops.bill.common.service.TencentPPMatchRulesService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
* @ClassName: AlarmConditionServiceImpl
* @Description:  腾讯云产品项目匹配规则 服务实现类
* @author: 张爱苹
* @date: 2024/1/31 10:23
*/
@Service
@Slf4j
public class TencentPPMatchRulesServiceImpl extends ServiceImpl<TencentPPMatchRulesMapper, TencentPPMatchRules> implements TencentPPMatchRulesService {
    private Logger logger = LoggerFactory.getLogger(TencentPPMatchRulesServiceImpl.class);

}
