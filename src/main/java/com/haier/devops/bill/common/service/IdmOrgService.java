package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.IdmOrg;
import com.haier.devops.bill.common.vo.DepartIdVo;

import java.util.List;

/**
 * (IdmOrg)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-23 16:49:32
 */
public interface IdmOrgService extends IService<IdmOrg>{
    List<DepartIdVo> getOrgTree(String orgId);

    List<IdmOrg> getAuthedOrgByName(String name);

    List<IdmOrg> getAllOrgs();

    List<IdmOrg> getOrgsByOrgId(List<String> orgIds);
}
