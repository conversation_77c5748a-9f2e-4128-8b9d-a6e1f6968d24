package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupCreateDTO;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupDTO;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.service.AlarmNoticeGroupService;
import com.haier.devops.bill.common.service.HdsSubProductsService;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
* @ClassName: AlarmNoticeGroupController
* @Description:  告警通知组
* @author: 张爱苹
* @date: 2024/1/12 17:19
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/alarm/notice-group")
public class AlarmNoticeGroupController {

	@Autowired
	private AlarmNoticeGroupService alarmNoticeGroupService;

	@Autowired
	private HdsSubProductsService hdsSubProductsService;

	/**
	 * 分页查询所有告警通知组
	 *
	 * @return 告警通知组列表
	 */
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<AlarmNoticeGroupVo>> listByPage(AlarmNoticeGroupDTO dto) {
        try {
			PageInfo<AlarmNoticeGroupVo> pageInfo = alarmNoticeGroupService.listByPage(dto);
			return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
	}


	/**
	 * 根据ID获取告警通知组
	 *
	 * @param id 告警通知组ID
	 * @return 告警通知组
	 */
	@GetMapping("/{id}")
	public ResponseEntityWrapper<AlarmNoticeGroupVo> getAlarmNoticeGroupById(@PathVariable("id") Integer id) {
		try {
			return new ResponseEntityWrapper<>(alarmNoticeGroupService.getAlarmNoticeGroupById(id));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * 创建告警通知组
	 *
	 * @param AlarmNoticeGroupCreateDTO 告警通知组
	 * @return 创建成功的告警通知组
	 */
	@PostMapping("/create")
	public ResponseEntityWrapper createAlarmNoticeGroup(@RequestBody @Validated AlarmNoticeGroupCreateDTO AlarmNoticeGroupCreateDTO) {
		try {
			alarmNoticeGroupService.createAlarmNoticeGroup(AlarmNoticeGroupCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 更新告警通知组
	 *
	 * @param id                 告警通知组ID
	 * @param AlarmNoticeGroupCreateDTO 更新的告警通知组
	 * @return 更新后的告警通知组
	 */
	@PutMapping("/update/{id}")
	public ResponseEntityWrapper updateAlarmNoticeGroup(@PathVariable("id") Integer id, @RequestBody @Validated AlarmNoticeGroupCreateDTO AlarmNoticeGroupCreateDTO) {
		try {
			alarmNoticeGroupService.updateAlarmNoticeGroup(id, AlarmNoticeGroupCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 删除告警通知组
	 *
	 * @param id 告警通知组ID
	 */
	@DeleteMapping("/delete/{id}")
	public ResponseEntityWrapper deleteAlarmNoticeGroup(@PathVariable("id") Integer id) {
		try {
			alarmNoticeGroupService.deleteAlarmNoticeGroup(id);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 根据ID获取告警通知组
	 *
	 * @param account 告警通知组ID
	 * @return 告警通知组
	 */
	@GetMapping("/getUserInfo/{account}")
	public ResponseEntityWrapper<AlarmUserInfo> getAlarmNoticeObj(@PathVariable("account") String account) {
		try {
			return new ResponseEntityWrapper<>(alarmNoticeGroupService.getAlarmNoticeObj(account));
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 人员信息保存
	* @author: 张爱苹
	* @date: 2024/2/2 14:12
	* @param alarmUserInfoDTOList:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@PostMapping("/saveUserInfo")
	public ResponseEntityWrapper saveUserInfo(@Validated @RequestBody List<AlarmNoticeGroupDetail> alarmUserInfoDTOList) {
		try {
			alarmNoticeGroupService.saveUserInfo(alarmUserInfoDTOList);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	* @Description: 校验组名是否重复
	* @author: 张爱苹
	* @date: 2024/3/5 16:48
	* @param groupName:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@PostMapping("/checkGroupName")
	public ResponseEntityWrapper checkGroupName(@RequestParam String groupName) {
		try {
			boolean flag = alarmNoticeGroupService.checkGroupzName(groupName);
			return new ResponseEntityWrapper<>(flag);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 获取手机号
	* @author: 张爱苹
	* @date: 2025/6/26 17:00
	* @param account:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@GetMapping("/getUserPhone")
	public ResponseEntityWrapper getUserPhone(@RequestParam String account) {
		try {
			List phone = alarmNoticeGroupService.getUserPhone(account);
			return new ResponseEntityWrapper<>(phone);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	* @Description: 获取应用负责人的工号
	* @author: 张爱苹
	* @date: 2025/6/30 15:18
	* @param appScode:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@GetMapping("/getAccountByScode")
	public ResponseEntityWrapper getAccountByScode(@RequestParam String appScode) {
		try {
			List<Map> account = hdsSubProductsService.getAccountByScode(appScode);
			return new ResponseEntityWrapper<>(account);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

}
