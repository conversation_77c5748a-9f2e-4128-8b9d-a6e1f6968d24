package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DimensionDetailVo {
    /**
     * s码
     */
    private String scode;

    /**
     * 产品类型
     */
    private String productCode;

    /**
     * 云账号
     */
    private String accountName;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 支付方式
     */
    private String subscriptionType;

    /**
     * 维度值
     */
    private String dimensionalValue;

    /**
     * 总额
     */
    private BigDecimal totalSum;

    /**
     * 实例数量
     */
    private long insCount;

    /**
     * 产品
     */
    private String productName;

    /**
     * 子产品
     */
    private String subProductName;

    private String fappName;

    private Map<String,Object> timeToResult = new HashMap<>();


}
