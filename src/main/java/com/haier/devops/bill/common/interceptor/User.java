package com.haier.devops.bill.common.interceptor;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class User {
    private String userCode;
    private String userName;
    private String employeeStatus;
    private String email;
    private String deptId;
    private String deptName;

    private List<HworkAuthorityApi.RolesDTO> roles;
}
