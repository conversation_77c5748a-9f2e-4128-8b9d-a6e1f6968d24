package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: AlarmUserInfoDetail
* @Description:  告警用户信息表
* @author: 张爱苹
* @date: 2024/1/17 10:24
*/
@Data
@TableName("bc_alarm_user_info")
@Builder
public class AlarmUserInfo implements Serializable {


    private static final long serialVersionUID = 8193321546480505032L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 人员工号
     */
    private String account;

    /**
     * 姓名
     */
    private String name;

    /**
     *  邮箱
     */
    private String email;

    /**
     *  手机号
     */
    private String mobile;

}

