package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AggregateReconciliationDTO;
import com.haier.devops.bill.common.dto.ReconciliationBatchDTO;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.CloudRuleFieldService;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import com.haier.devops.bill.common.service.ResourceInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* @ClassName: CmdbProductOverviewController
* @Description:  cmdb总表 控制器
* @author: 张爱苹
* @date: 2024/1/31 10:24
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/resouce-instance")
public class ResourceInstanceController {

	@Autowired
	private CmdbProductOverviewService cmdbProductOverviewService;

	@Autowired
	private AggregatedBillService aggregatedBillService;

	@Autowired
	private CloudRuleFieldService cloudRuleFieldService;

	@Autowired
	private ResourceInstanceService resourceInstanceService;


	/**
	* @Description: 分页查询调账列表
	* @author: 张爱苹
	* @date: 2024/3/8 16:54
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CmdbProductOverview>>
	*/
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<CmdbProductOverview>> listByPage(@Validated ResourceInstanceDTO dto) {
        try {
			PageInfo<CmdbProductOverview> pageInfo = resourceInstanceService.listByPage(dto);
			return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
	}

	/**
	 * @Description: 按全部资源实例调账
	 * @author: 张爱苹
	 * @date: 2024/3/11 09:50
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	 */
	@PostMapping("/reconciliationAll")
	public ResponseEntityWrapper reconciliationAll(@Validated @RequestBody ReconciliationBatchDTO dto) {
		try {
			dto.setReconciliationType("rule");
			aggregatedBillService.reconciliationAll(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * @Description: 按部分资源实例调账
	 * @author: 张爱苹
	 * @date: 2024/3/11 09:50
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	 */
	@PostMapping("/reconciliationPart")
	public ResponseEntityWrapper reconciliationPart(@Validated @RequestBody ReconciliationBatchDTO dto) {
		try {
			dto.setReconciliationType("rule");
			aggregatedBillService.reconciliationPart(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}

	/**
	 * @Description: 按单个实例调账
	 * @author: 张爱苹
	 * @date: 2024/3/11 09:50
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	 */
	@PostMapping("/reconciliation")
	public ResponseEntityWrapper reconciliation(@Validated @RequestBody AggregateReconciliationDTO dto) {
		try {
			dto.setReconciliationType("rule");
			aggregatedBillService.reconciliationSingle(dto);
			return new ResponseEntityWrapper<>();
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}


}
