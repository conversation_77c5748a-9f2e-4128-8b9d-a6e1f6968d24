package com.haier.devops.bill.common.api;

import com.haier.devops.bill.common.api.entity.ResultWrapper;
import feign.RequestLine;
import lombok.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;

/**
 * Hwork账单对接
 */
public interface HworkBillApi {
    /**
     * 查询用户授权
     * @param request
     * @return
     */
    @RequestLine("POST /nlfbpt/op-server-console-new/gw/platform/platform/v1/application/getAuthAppForIt")
    ResultWrapper<AuthAppForItResponse> getAuthAppForIt(@RequestBody AuthAppForItRequest request);

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class AuthAppForItRequest {
        private String domain;
        private List<String> productIds;
        private String userCode;
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class AuthAppForItResponse {
        private List<Domain> domains = new ArrayList<>();
        private List<Product> products = new ArrayList<>();
        private List<SubProduct> subProducts = new ArrayList<>();
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class Domain {
        private String domainCode;
        private String domainName;
        private boolean defaultChecked;
        private boolean bookmarked;

        public Domain(String domainCode, String domainName, boolean defaultChecked) {
            this.domainCode = domainCode;
            this.domainName = domainName;
            this.defaultChecked = defaultChecked;
            this.bookmarked = false;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class Product {
        private String id;
        private String productName;
        private String productNameShort;
        private boolean bookmarked;
        
        public Product(String id, String productName, String productNameShort) {
            this.id = id;
            this.productName = productName;
            this.productNameShort = productNameShort;
            this.bookmarked = false;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class SubProduct {
        private String appScode;
        private String appShortName;
        private String appName;
        private boolean bookmarked;
        private boolean checked;
        
        public SubProduct(String appScode, String appShortName, String appName) {
            this.appScode = appScode;
            this.appShortName = appShortName;
            this.appName = appName;
            this.bookmarked = false;
            this.checked = false;
        }
    }
}
