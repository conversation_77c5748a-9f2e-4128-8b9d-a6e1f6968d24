package com.haier.devops.bill.common.service.impl;

import com.haier.devops.bill.adjustment.vo.AdjustmentRange;
import com.haier.devops.bill.common.entity.BillAcceptanceResource;
import com.haier.devops.bill.common.mapper.BillAcceptanceResourceMapper;
import com.haier.devops.bill.common.service.BillAcceptanceResourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 账单确认验收资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class BillAcceptanceResourceServiceImpl extends ServiceImpl<BillAcceptanceResourceMapper, BillAcceptanceResource> implements BillAcceptanceResourceService {
    private final BillAcceptanceResourceMapper acceptanceResourceMapper;

    public BillAcceptanceResourceServiceImpl(BillAcceptanceResourceMapper acceptanceResourceMapper) {
        this.acceptanceResourceMapper = acceptanceResourceMapper;
    }

    @Override
    public List<BillAcceptanceResource> selectBillsNotReservedAmongIdBetweenDate(List<String> aggregatedIds, AdjustmentRange... range) {
        return acceptanceResourceMapper.selectBillsNotReservedAmongIdBetweenDate(aggregatedIds, range);
    }
}
