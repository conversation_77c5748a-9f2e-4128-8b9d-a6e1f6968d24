package com.haier.devops.bill.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* @ClassName: AlarmConfigurationDTO
* @Description: 告警配置 dto
* @author: 张爱苹
* @date: 2024/1/11 11:14
*/
@Data
public class CpAlarmConfigurationVo implements Serializable {

    private static final long serialVersionUID = 6415017122484432766L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * S码
     */
    private String appScode;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用简称
     */
    private String appShortName;


    /**
     * 产品id
     */
    private Integer productId;


    /**
     * 等级
     */
    @NotEmpty
    private List<CpAlarmLevelVo> bcCpAlarmLevelList;

    /**
    * 云厂商
    */
    String vendor;

    /**
     * 产品编码
     */
    String productCode;

    /**
     * 产品名称
     */
    String productName;

    /**
     * 是否开启告警：0否；1是
     */
    private Integer isEnableAlarm;

    /**T
     * 是否开启告警：0否；1是
     */
    private String isEnableAlarmName;

    /**
     * 有效期开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startValidPeriodTime;

    /**
     * 有效期结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endValidPeriodTime;

    /**
     * 告警类型：单日账单费用突增；（字典获取）
     */
    private String alarmType;

    /**
     * 告警类型：单日账单费用突增；（字典获取）
     */
    private String alarmTypeName;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createByName;


    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 更新人姓名
     */
    private String updateByName;


    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

    /**
     * 告警次数
     */
    private Integer alarmNum;

    /**
     *  告警金额
     */
    private String warnAmount;
}
