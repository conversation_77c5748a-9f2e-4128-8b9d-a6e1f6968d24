package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.BillSubstitutionTask;

import java.util.List;

/**
 * <p>
 * 账单替换任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface BillSubstitutionTaskService extends IService<BillSubstitutionTask> {
    /**
     * 根据类型和阶段获取任务
     * @param type
     * @param billingCycle
     * @return
     */
    List<BillSubstitutionTask> getTaskByTypeAndBillingCycle(String type, String billingCycle);

    /**
     * 查询待迁移任务
     * @param type
     * @param billingCycle
     * @return
     */
    List<BillSubstitutionTask> getPendingMigrationTaskByType(String type, String billingCycle);

    /**
     * 查询待计算任务
     * @param type
     * @return
     */
    List<BillSubstitutionTask> getPendingCalculationTaskByType(String type, String billingCycle);

    /**
     * 查询待替换任务
     * @param type
     * @return
     */
    List<BillSubstitutionTask> getPendingSubstitutionTaskByType(String type, String billingCycle);

    /**
     * 创建替换任务
     * @param task
     * @return
     */
    int createTask(BillSubstitutionTask task);

    /**
     * 更新任务表阶段和进度
     * @param subTaskId
     * @param stage
     * @param process
     * @return
     */
    int updateStageAndProcessByTaskId(String subTaskId, String stage, Integer process);

    /**
     * 更新错误信息
     * @param subTaskId
     * @param errorInfo
     */
    void updateErrorInfo(String subTaskId, String errorInfo);

    /**
     * 查询待清理任务
     * @param type
     * @param billingCycle
     * @return
     */
    List<BillSubstitutionTask> getPendingCleaningTaskByType(String type, String billingCycle);
}
