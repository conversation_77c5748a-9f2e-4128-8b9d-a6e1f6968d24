package com.haier.devops.bill.common.task;

import com.haier.devops.bill.common.distributed_lock.LockObj;
import com.haier.devops.bill.common.distributed_lock.RedisDistributedLock;
import com.haier.devops.bill.common.redis.Queue;
import com.haier.devops.bill.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 找到无效的分布式锁，解锁
 */
@Component
@Slf4j
public class InvalidDistributedLockSweepingTask extends AbstractTask {
    private Queue<LockObj> distributedLockQueue;
    private JedisPool jedisPool;

    public InvalidDistributedLockSweepingTask(@Qualifier("distributedLockQueue")
                                              Queue<LockObj> distributedLockQueue,
                                              JedisPool jedisPool) {
        this.distributedLockQueue = distributedLockQueue;
        this.jedisPool = jedisPool;
    }

    @Override
    @SuppressWarnings("InfiniteLoopStatement")
    public void doExecute() {
        Calendar calendar = Calendar.getInstance();
        final Jedis jedis = jedisPool.getResource();
        while (true) {
            List<LockObj> locks = distributedLockQueue.getAll();
            for (LockObj lock : locks) {
                calendar.setTime(lock.getLockTime());
                calendar.add(Calendar.SECOND, lock.getExpirationInSeconds().intValue());
                Date expirationTime = calendar.getTime();

                log.info("Distributed lock {} expiration time: {}", lock, expirationTime);

                // 锁超时了，锁会自动释放
                if (new Date().after(expirationTime)) {
                    // remove from queue
                    distributedLockQueue.remove(lock);
                    continue;
                }

                // 锁没超时，节点不可用
                if (!IpUtil.isIpReachable(lock.getIp())) {
                    // release and remove from queue
                    RedisDistributedLock.releaseDistributedLock(jedis, lock.getLockKey());
                    distributedLockQueue.remove(lock);
                }
            }

            sleep(5);
        }
    }
}
