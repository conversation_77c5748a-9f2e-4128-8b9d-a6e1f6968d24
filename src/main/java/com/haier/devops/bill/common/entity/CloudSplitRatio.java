package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 分摊比例设置
 * <AUTHOR>
 */
@Data
@TableName("cloud_split_cost")
public class CloudSplitRatio {
    /**
     * 账户
     */
    @TableField("account")
    private String account;

    /**
     * 财务单元
     */
    @TableField("cost_unit")
    private String costUnit;

    /**
     * S码
     */
    @TableField("appscode")
    private String scode;

    /**
     * 分摊比例
     */
    @TableField("percent")
    private String percent;
}
