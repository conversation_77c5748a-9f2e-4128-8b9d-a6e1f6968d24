package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: AlarmConfigurationDTO
* @Description: 告警配置 dto
* @author: 张爱苹
* @date: 2024/1/11 11:14
*/
@Data
public class AlarmConfigurationDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1044095738362670055L;

    private Integer id;
    /**
     * S码
     */
    private String appScode;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 是否开启告警：0否；1是
     */
    private Integer isEnableAlarm;

    /**
     * 告警类型：单日账单费用突增；（字典获取）
     */
    private String alarmType;


    /**
     * 云厂商
     */
    private String vendor;


    /**
     * 创建人
     */
    private String createBy;


}
