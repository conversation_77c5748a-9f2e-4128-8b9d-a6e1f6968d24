package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
* @ClassName: CloudRuleField
* @Description: TODO
* @author: 张爱苹
* @date: 2024/3/12 10:59
*/
@Data
@TableName("cloud_rule_field")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="规则字段详情")
public class CloudRuleField implements Serializable {


    private static final long serialVersionUID = -3662383108061376737L;
    /**主键*/
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private java.lang.Integer id;
    /**规则类型*/
    @Schema(description = "规则类型")
    private java.lang.String ruleType;
    /**规则字段*/
    @Schema(description = "规则字段")
    private java.lang.String ruleField;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private java.util.Date createTime;
    /**云厂商*/
    @Schema(description = "云厂商")
    private java.lang.String vendor;
    /**规则字段名称*/
    @Schema(description = "规则字段名称")
    private java.lang.String ruleFileldName;
}
