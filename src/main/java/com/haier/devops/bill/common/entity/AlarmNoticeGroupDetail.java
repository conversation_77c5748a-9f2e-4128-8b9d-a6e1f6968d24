package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: AlarmNoticeGroupDetail
* @Description:  告警通知组明细
* @author: 张爱苹
* @date: 2024/1/12 14:01
*/
@Data
@TableName("bc_alarm_notice_group_detail")
public class AlarmNoticeGroupDetail implements Serializable {

    private static final long serialVersionUID = -2384842221031862891L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 通知组id
     */
    private Integer alarmNoticeGroupId;

    /**
     * 人员工号
     */
    private String account;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String name;


    /**
     *  邮箱
     */
    @TableField(exist = false)
    private String email;

    /**
     *  手机号
     */
    @TableField(exist = false)
    private String mobile;



}

