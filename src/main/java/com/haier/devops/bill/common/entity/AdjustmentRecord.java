package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: AdjustmentRecord
* @Description: 调账记录
* @author: 张爱苹
* @date: 2024/2/22 14:31
*/
@TableName("bc_adjustment_record")
@Data
@Builder
public class AdjustmentRecord implements Serializable {

    private static final long serialVersionUID = -6569077587583301234L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 聚合id
     *
     */
    private String aggregatedId;
    /**
     * 调账时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 调账状态
     */
    private String  reconciliationStatus;

    /**
     * 调账状态
     */
    private String  createBy;

    /**
     * 调账类型
     */
    private String  reconciliationType;


    /**
     * 调账前列表
     */
    private String originalContent;


    /**
     * 调账后列表
     */
    private String newContent;

    /**
     * 单个实例调账；部份调账；全部调账
     */
    private String type;

}
