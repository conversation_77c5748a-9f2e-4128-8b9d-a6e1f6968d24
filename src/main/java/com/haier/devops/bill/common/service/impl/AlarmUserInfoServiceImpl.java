package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.mapper.AlarmUserInfoMapper;
import com.haier.devops.bill.common.service.AlarmUserInfoService;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* @ClassName: AlarmUserInfoServiceImpl
* @Description:  告警用户信息 服务实现类
* @author: 张爱苹
* @date: 2024/1/17 10:31
*/
@Service
@Slf4j
public class AlarmUserInfoServiceImpl extends ServiceImpl<AlarmUserInfoMapper, AlarmUserInfo> implements AlarmUserInfoService {
    private Logger logger = LoggerFactory.getLogger(AlarmUserInfoServiceImpl.class);

    @Override
    public AlarmUserInfo getAlarmUserInfoByAccount(String account) {
        return getOne(new LambdaQueryWrapper<AlarmUserInfo>().eq(AlarmUserInfo::getAccount, account));
    }

    @Override
    public List<AlarmNoticeObjVo> getUserListByUserIdArray(String[] noticeObjIdArray) {
        return baseMapper.getUserListByUserIdArray(noticeObjIdArray);
    }

    @Override
    public List<AlarmNoticeObjVo> getUserListByGroupId(String[] noticeObjIdArray) {
        return baseMapper.getUserListByGroupId(noticeObjIdArray);
    }
}
