package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmLogDTO;
import com.haier.devops.bill.common.entity.CpAlarmLog;
import com.haier.devops.bill.common.mapper.AlarmLogMapper;
import com.haier.devops.bill.common.service.AlarmLogService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
* @ClassName: AlarmLogServiceImpl
* @Description:  告警日志服务实现类
* @author: 张爱苹
* @date: 2024/1/18 13:29
*/
@Service
@Slf4j
public class AlarmLogServiceImpl extends ServiceImpl<AlarmLogMapper, CpAlarmLog> implements AlarmLogService {
    private Logger logger = LoggerFactory.getLogger(AlarmLogServiceImpl.class);

    @Override
    public PageInfo<CpAlarmLog> listByPage(AlarmLogDTO dto) {
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->  baseMapper.listByPage(dto)
            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, AlarmLogDTO:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }
}
