package com.haier.devops.bill.common.distributed_lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisUtil {
    private JedisPool jedisPool;

    public RedisUtil(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }

    public <T> T execute(RedisCallbackFunction<T, Jedis> fun) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return (T)fun.callback(jedis);
        }catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }

}
