package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.RawBillProductsDTO;
import com.haier.devops.bill.common.dto.RawBillProductsWithVendorsDTO;
import com.haier.devops.bill.common.entity.RawBillProducts;
import com.haier.devops.bill.common.mapper.RawBillProductsMapper;
import com.haier.devops.bill.common.service.RawBillProductsService;
import com.haier.devops.bill.common.vo.RawBillProductTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @ClassName: RawBillProductsServiceImpl
* @Description: 云产品 服务是嫌累
* @author: 张爱苹
* @date: 2024/1/15 16:06
*/
@Service
@Slf4j
public class RawBillProductsServiceImpl extends ServiceImpl<RawBillProductsMapper, RawBillProducts> implements RawBillProductsService {
    private Logger logger = LoggerFactory.getLogger(RawBillProductsServiceImpl.class);

    @Override
    public List<RawBillProductTreeVo> getRawBillProductTreeList(String searchContent,String appScode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("del_flag","0");
        if (!StringUtils.isEmpty(searchContent)) {
            queryWrapper.apply("(product_code like '%" + searchContent + "%' or product_name like '%" + searchContent + "%')");
        }
        if(!StringUtils.isEmpty(appScode)){
            queryWrapper.apply("id not in (select distinct bcac2.product_id from bc_cp_alarm_configuration bcac2 where bcac2.del_flag = '0' and bcac2.app_scode = '"+appScode+"')");
        }
        List<RawBillProducts> list = list(queryWrapper);
        Map<String, List<RawBillProducts>> resultMap = list.stream().collect(Collectors.groupingBy(RawBillProducts::getVendor));
        List<RawBillProductTreeVo> resultList = new ArrayList<>();
        for (Map.Entry<String, List<RawBillProducts>> entry : resultMap.entrySet()) {
            RawBillProductTreeVo treeVo = new RawBillProductTreeVo();
            treeVo.setVendor(entry.getKey());
            treeVo.setList(entry.getValue());
            resultList.add(treeVo);
        }
        return resultList;
    }

    @Override
    public List<Map> getAliasProducts(RawBillProductsDTO rawBillProductsDTO) {
        LambdaQueryWrapper<RawBillProducts> queryWrapper = new LambdaQueryWrapper<>();
        if(!StringUtils.isEmpty(rawBillProductsDTO.getVendor())){
            queryWrapper.eq(RawBillProducts::getVendor,rawBillProductsDTO.getVendor());
        }
        if(!StringUtils.isEmpty(rawBillProductsDTO.getProductCode())){
            queryWrapper.eq(RawBillProducts::getProductCode,rawBillProductsDTO.getProductCode());
        }
        if(!StringUtils.isEmpty(rawBillProductsDTO.getProductName())){
            queryWrapper.eq(RawBillProducts::getProductName,rawBillProductsDTO.getProductName());
        }
        queryWrapper.eq(RawBillProducts::getDelFlag,"0");
        queryWrapper.apply("alias_code is not null and  alias_code != '' and alias_name is not null and  alias_name != ''");
        return baseMapper.queryList(queryWrapper);
    }


    @Override
    public List<Map> getAliasProductsWithVendors(RawBillProductsWithVendorsDTO dto) {
        LambdaQueryWrapper<RawBillProducts> queryWrapper = new LambdaQueryWrapper<>();
        if(!CollectionUtils.isEmpty(dto.getVendors())){
            queryWrapper.in(RawBillProducts::getVendor,dto.getVendors());
        }
        if(!StringUtils.isEmpty(dto.getProductCode())){
            queryWrapper.eq(RawBillProducts::getProductCode,dto.getProductCode());
        }
        if(!StringUtils.isEmpty(dto.getProductName())){
            queryWrapper.eq(RawBillProducts::getProductName,dto.getProductName());
        }
        queryWrapper.eq(RawBillProducts::getDelFlag,"0");
        queryWrapper.apply("alias_code is not null and  alias_code != '' and alias_name is not null and  alias_name != ''");
        return baseMapper.queryList(queryWrapper);
    }

    @Override
    public List<RawBillProducts> getProductsByProduct(String product,String vendor) {
        LambdaQueryWrapper<RawBillProducts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(RawBillProducts::getProductCode,RawBillProducts::getProductName);
        queryWrapper.eq(RawBillProducts::getDelFlag,"0");
        queryWrapper.apply("(product_code = '"+product+"' or product_name = '"+product+"')");
        if(StringUtils.isNotEmpty(vendor)){
            queryWrapper.eq(RawBillProducts::getVendor,vendor);
        }
        return list(queryWrapper);
    }

    @Override
    public List<RawBillProducts> getRawBillProductList(RawBillProductsDTO rawBillProductsDTO) {
        LambdaQueryWrapper<RawBillProducts> queryWrapper = new LambdaQueryWrapper<>();
        if(!StringUtils.isEmpty(rawBillProductsDTO.getVendor())){
            queryWrapper.eq(RawBillProducts::getVendor,rawBillProductsDTO.getVendor());
        }
        if(!StringUtils.isEmpty(rawBillProductsDTO.getProductCode())){
            queryWrapper.eq(RawBillProducts::getProductCode,rawBillProductsDTO.getProductCode());
        }
        if(!StringUtils.isEmpty(rawBillProductsDTO.getProductName())){
            queryWrapper.eq(RawBillProducts::getProductName,rawBillProductsDTO.getProductName());
        }
        queryWrapper.eq(RawBillProducts::getDelFlag,"0");
        queryWrapper.apply("alias_code is not null and  alias_code != '' and alias_name is not null and  alias_name != ''");
        return list(queryWrapper);
    }
}
