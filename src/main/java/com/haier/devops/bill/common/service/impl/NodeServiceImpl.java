package com.haier.devops.bill.common.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.haier.devops.bill.common.api.CmdbApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.entity.RcDatabaseInfo;
import com.haier.devops.bill.common.entity.RcHostInfo;
import com.haier.devops.bill.common.entity.RcMiddlewareInfo;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.AggregatedBillMapper;
import com.haier.devops.bill.common.mapper.NodeMapper;
import com.haier.devops.bill.common.mapper.RefinedRawBillMapper;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.param.DimensionDetailParam;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.DimensionDetailVo;
import com.haier.devops.bill.common.vo.NodeDetailBillsVo;
import com.haier.devops.bill.export.util.OssUtil;
import com.haier.devops.bill.export.vo.UploadResult;
import com.haier.devops.bill.util.*;
import com.lark.oapi.core.utils.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.haier.devops.bill.export.Exporter.FAILED;
import static com.haier.devops.bill.export.Exporter.SUCCESS;
import static com.haier.devops.bill.util.EasyExcelUtils.getStyleStrategy;

/**
 * <p>
 * 节点 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
@Service
@Slf4j
public class NodeServiceImpl implements NodeService {
    private static final String MONTH = "1";
    private static final String QUARTER = "2";
    private static final String YEAR = "3";

    private static final String VENDOR = "vendor";
    private static final String PRODUCT_TYPE = "productType";
    private static final String SCODE = "scode";
    private static final String PRODUCT = "product";

    private NodeMapper nodeMapper;
    private RcHostInfoService rcHostInfoService;
    private RcDatabaseInfoService rcDatabaseInfoService;
    private RcMiddlewareInfoService rcMiddlewareService;
    private RedisUtils redisUtils;
    private CmdbApi cmdbApi;
    private AuthorityService authorityService;

    private OssUtil ossUtil;

    private static final String DIR_NAME = "detail";

    private ExportLogService exportLogService;

    private RefinedRawBillMapper refinedRawBillMapper;

    private AggregatedBillMapper aggregatedBillMapper;

    @Autowired
    private CloudAccountService cloudAccountService;

    @Resource
    private Executor localBootAsyncExecutor;

    private static Integer BATCH_SIZE = 200;

    public NodeServiceImpl(NodeMapper nodeMapper, RcHostInfoService rcHostInfoService,
                           RcDatabaseInfoService rcDatabaseInfoService, RcMiddlewareInfoService rcMiddlewareService, RedisUtils redisUtils, CmdbApi cmdbApi, AuthorityService authorityService,
                           OssUtil ossUtil, ExportLogService exportLogService, RefinedRawBillMapper refinedRawBillMapper, AggregatedBillMapper aggregatedBillMapper) {
        this.nodeMapper = nodeMapper;
        this.rcHostInfoService = rcHostInfoService;
        this.rcDatabaseInfoService = rcDatabaseInfoService;
        this.rcMiddlewareService = rcMiddlewareService;
        this.redisUtils = redisUtils;
        this.cmdbApi = cmdbApi;
        this.authorityService = authorityService;
        this.ossUtil = ossUtil;
        this.exportLogService = exportLogService;
        this.refinedRawBillMapper = refinedRawBillMapper;
        this.aggregatedBillMapper = aggregatedBillMapper;
    }


    /**
     * 当前消费分析
     *
     * @return
     */
    public Map<String, Object> getConsumptionAnalysis(String scode) {
        List<String> authedScodes = AuthUtil.extractAuthedScodes(scode);

        Map<String, Object> res = new HashMap<>();  // 创建一个String和Object类型的映射关系
        String currentMonth = DateUtil.getCurrentMonth();  // 获取当前月份
        String lastMonthOf = DateUtil.getPreviousMonth(currentMonth);  // 获取当前月份的上一个月
        String lastTwoMonth = DateUtil.getPreviousMonth(lastMonthOf);  // 获取上上个月

        // 获取上个月的消费情况
        double lastMonthAmount = nodeMapper.getConsumptionAnalysis(lastMonthOf, authedScodes);
        // 获取上上个月的消费情况
        double monthBeforeLastMonthAmount = nodeMapper.getConsumptionAnalysis(lastTwoMonth, authedScodes);
        // 获取当月预测
        double forecastForTheMonth = nodeMapper.forecastForTheMonth(authedScodes);
        // 获取去年总消费
        double lastYearTotalAmount = nodeMapper.getLastYearTotalAmount(authedScodes);


        double prevMonthRatio = 0;
        if (monthBeforeLastMonthAmount != 0) {
            prevMonthRatio = ((lastMonthAmount - monthBeforeLastMonthAmount) / monthBeforeLastMonthAmount) * 100;
        }

        double currentMonthRatio = 0;
        if (lastMonthAmount != 0) {
            currentMonthRatio = ((forecastForTheMonth - lastMonthAmount) / lastMonthAmount) * 100;
        }

        double currentYearRatio = 0;
        if (lastYearTotalAmount != 0) {
            currentYearRatio = ((forecastForTheMonth - lastYearTotalAmount) / lastYearTotalAmount) * 100;
        }

        NumberFormat nf = NumberFormat.getNumberInstance();
        // digits 显示的数字位数为格式化对象设定小数点后的显示的最多位,显示的最后位是舍入的
        nf.setMaximumFractionDigits(2);

        // 将上个月的消费情况放入结果集中
        res.put("prevMonthExpense", lastMonthAmount);
        // 将消费增长比例放入结果集中
        res.put("prevMonthRatio", nf.format(prevMonthRatio));
        // 将当月预测放入结果集中
        res.put("forecastForTheMonth", forecastForTheMonth);
        // 将当月预测放入结果集中
        res.put("currentMonthRatio", currentMonthRatio);
        res.put("currentYearRatio", currentYearRatio);
        return res;

    }

    /**
     * 获取主要资产
     *
     * @param scode
     * @return
     */
    public Map<String, Integer> getMainAssets(String scode,List<String> vendors, String... currency) {
        List<String> authedScodes = AuthUtil.extractAuthedScodes(scode);
        if (CollectionUtils.isEmpty(authedScodes)) {
            return new HashMap<String, Integer>() {{
                put("ecsCount", 0);
                put("dbCount", 0);
                put("mwCount", 0);
            }};
        }

        return nodeMapper.getMainAssets(authedScodes,vendors, currency);
    }

    @Override
    public Map<String, Integer> getMainAssets(String scode, List<String> vendors) {
        List<String> authedScodes = AuthUtil.extractAuthedScodes(scode);
        if (CollectionUtils.isEmpty(authedScodes)) {
            return new HashMap<String, Integer>() {{
                put("ecsCount", 0);
                put("dbCount", 0);
                put("mwCount", 0);
            }};
        }

        return nodeMapper.getMainAssetsAll(authedScodes,vendors);
    }

    /**
     * 获取消费趋势
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      类型 1:按月 2:按季度 3：按年 4：按天
     * @param scode     S码
     */
    public List<Map<String, Object>> getConsumerTrends(List<String> vendors,
                                                       LocalDate startDate,
                                                       LocalDate endDate,
                                                       String type,
                                                       String scode,
                                                       String... currency) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");  // 定义日期格式
        NumberFormat nf = NumberFormat.getNumberInstance();
        // digits 显示的数字位数为格式化对象设定小数点后的显示的最多位,显示的最后位是舍入的
        nf.setMaximumFractionDigits(2);

        String start = startDate.format(formatter);
        String end = endDate.format(formatter);


        if (type.equals(MONTH)) {
            start = DateUtil.getPreviousMonth(start);
        } else if (type.equals(QUARTER)) {
            start = DateUtil.getFirstMonthOfPreviousQuarter(start);
        } else if (type.equals(YEAR)) {
            start = DateUtil.getFirstMonthOfPreviousYear(start);
        }

        // 获取消费者趋势列表
        List<Map<String, Object>> consumerTrends = nodeMapper.getConsumerTrends(
                vendors,
                start,
                end,
                validScodes,
                type, currency);

        if (CollectionUtils.isEmpty(consumerTrends)) {
            return consumerTrends;
        }

        List<Map<String, Object>> finalConsumerTrends = new ArrayList<>();

        // 初始上一个金额
        Double prevValue = consumerTrends.get(0).get("value") != null ? ((Number) consumerTrends.get(0).get("value")).doubleValue() : null;

        // 从第二个元素开始遍历
        for (int i = 1; i < consumerTrends.size(); i++) {
            Map<String, Object> currMap = consumerTrends.get(i);
            Double currValue = currMap.get("value") != null ? ((Number) currMap.get("value")).doubleValue() : null;

            currMap.put("value", formatDouble(currValue));
            currMap.put("amountOfChange", formatDouble(getDiff(currValue, prevValue)));

            finalConsumerTrends.add(currMap);
            // 更新上一个金额为当前金额
            prevValue = currValue;
        }

        return finalConsumerTrends;
    }

    @Override
    public List<Map<String, Object>> getConsumerTrends(List<String> vendors, LocalDate startDate, LocalDate endDate, String type, String scode, BigDecimal exchangeRate) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");  // 定义日期格式
        NumberFormat nf = NumberFormat.getNumberInstance();
        // digits 显示的数字位数为格式化对象设定小数点后的显示的最多位,显示的最后位是舍入的
        nf.setMaximumFractionDigits(2);

        String start = startDate.format(formatter);
        String end = endDate.format(formatter);


        if (type.equals(MONTH)) {
            start = DateUtil.getPreviousMonth(start);
        } else if (type.equals(QUARTER)) {
            start = DateUtil.getFirstMonthOfPreviousQuarter(start);
        } else if (type.equals(YEAR)) {
            start = DateUtil.getFirstMonthOfPreviousYear(start);
        }

        // 获取消费者趋势列表
        List<Map<String, Object>> consumerTrends = nodeMapper.getConsumerTrendsAll(
                vendors,
                start,
                end,
                validScodes,
                type, exchangeRate);

        if (CollectionUtils.isEmpty(consumerTrends)) {
            return consumerTrends;
        }

        List<Map<String, Object>> finalConsumerTrends = new ArrayList<>();

        // 初始上一个金额
        Double prevValue = consumerTrends.get(0).get("value") != null ? ((Number) consumerTrends.get(0).get("value")).doubleValue() : null;

        // 从第二个元素开始遍历
        for (int i = 1; i < consumerTrends.size(); i++) {
            Map<String, Object> currMap = consumerTrends.get(i);
            Double currValue = currMap.get("value") != null ? ((Number) currMap.get("value")).doubleValue() : null;

            currMap.put("value", formatDouble(currValue));
            currMap.put("amountOfChange", formatDouble(getDiff(currValue, prevValue)));

            finalConsumerTrends.add(currMap);
            // 更新上一个金额为当前金额
            prevValue = currValue;
        }

        return finalConsumerTrends;
    }

    private Double getDiff(Double cur, Double prev) {
        if (null == cur) {
            cur = 0.0;
        }
        if (null == prev) {
            prev = 0.0;
        }

        return cur - prev;
    }

    public static String formatBigDecimal(BigDecimal val) {
        if (null == val || BigDecimal.ZERO.equals(val)) {
            return "0";
        }

        return val.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    public static String formatDouble(Double val) {
        if (null == val) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("0.##");
        return df.format(val);
    }

    /**
     * 获取消费分步分析
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param type      类型 1:按月 2:按季度 3：按年
     * @param scode     S码
     */
    public List<Map<String, Object>> getConsumptionStepByStep(LocalDate startDate,
                                                              LocalDate endDate,
                                                              String type,
                                                              String scode,
                                                              String filter,
                                                              List<String> vendors, String... currency) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        String start = startDate.format(formatter);
        String end = endDate.format(formatter);

        String minDate;

        if (type.equals(MONTH)) {
            start = DateUtil.getPreviousMonth(start);
            minDate = start;
        } else if (type.equals(QUARTER)) {
            start = DateUtil.getFirstMonthOfPreviousQuarter(start);
            minDate = DateUtil.getQuarter(start);
        } else if (type.equals(YEAR)) {
            start = DateUtil.getFirstMonthOfPreviousYear(start);
            minDate = DateUtil.getYear(start);
        } else {
            minDate = null;
        }

        // 获取消费者趋势列表
        List<Map<String, Object>> consumerTrends =
                nodeMapper.getConsumptionStepByStep(start, end, validScodes, filter, type, vendors, currency);
        if (CollectionUtils.isEmpty(consumerTrends)) {
            return consumerTrends;
        }

        Map<String, String> filterMap = new HashMap<>();
        filterMap.put(VENDOR, "vendor");
        filterMap.put(PRODUCT_TYPE, "productCode");
        filterMap.put(SCODE, "scode");
        filterMap.put(PRODUCT, "productId");

        final String finalFilterName = filterMap.get(filter);
        Function<Map<String, Object>, String> groupingByFunc = trend -> trend.get(finalFilterName).toString();
        Predicate<Map<String, Object>> canBefilterPredicate = trend -> trend.get(finalFilterName) != null;

        String maxDate;
        Set<String> dateSet = consumerTrends.stream().map(t -> t.get("date").toString()).collect(Collectors.toSet());
        Optional<String> maxDateOpt = dateSet.stream().max(String::compareTo);
        maxDate = maxDateOpt.orElse(null);

        return consumerTrends.stream()
                .filter(canBefilterPredicate)
                .collect(Collectors.groupingBy(groupingByFunc))
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    List<Map<String, Object>> sortedList = entry.getValue().stream()
                            .sorted(Comparator.comparing(trend -> trend.get("date").toString()))
                            .collect(Collectors.toList());

                    if (sortedList.size() == 1) {
                        String theOnlyDate = String.valueOf(sortedList.get(0).get("date"));
                        if (theOnlyDate.equals(maxDate) && theOnlyDate.compareTo(minDate) > 0) {
                            Map<String, Object> singleItem = sortedList.get(0);
                            singleItem.put("amountOfChange", 0);
                            singleItem.put("changeRate", "--");
                            return sortedList.stream();
                        } else {
                            return null;
                        }
                    } else {
                        Map<String, Object> first = sortedList.get(0);
                        Map<String, Object> second = sortedList.get(1);

                        BigDecimal firstValue = (BigDecimal) first.get("value");
                        BigDecimal secondValue = (BigDecimal) second.get("value");
                        BigDecimal amountOfChangeInBigdecimal = secondValue.subtract(firstValue, new MathContext(2, RoundingMode.HALF_UP));

                        String amountOfChange = formatBigDecimal(amountOfChangeInBigdecimal);
                        String changeRate = null == firstValue || BigDecimal.ZERO.compareTo(firstValue) == 0 ? "--" : formatBigDecimal(amountOfChangeInBigdecimal.multiply(BigDecimal.valueOf(100)).divide(firstValue, 2, RoundingMode.HALF_UP));

                        second.put("amountOfChange", amountOfChange);
                        second.put("changeRate", changeRate);

                        // 只保留第二项
                        return sortedList.stream().skip(1);
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getConsumptionStepByStep(LocalDate startDate, LocalDate endDate, String type, String scode, String filter, List<String> vendors, BigDecimal exchangeRate) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        String start = startDate.format(formatter);
        String end = endDate.format(formatter);

        String minDate;

        if (type.equals(MONTH)) {
            start = DateUtil.getPreviousMonth(start);
            minDate = start;
        } else if (type.equals(QUARTER)) {
            start = DateUtil.getFirstMonthOfPreviousQuarter(start);
            minDate = DateUtil.getQuarter(start);
        } else if (type.equals(YEAR)) {
            start = DateUtil.getFirstMonthOfPreviousYear(start);
            minDate = DateUtil.getYear(start);
        } else {
            minDate = null;
        }

        // 获取消费者趋势列表
        List<Map<String, Object>> consumerTrends =
                nodeMapper.getConsumptionStepByStepAll(start, end, validScodes, filter, type, vendors, exchangeRate);
        if (CollectionUtils.isEmpty(consumerTrends)) {
            return consumerTrends;
        }

        Map<String, String> filterMap = new HashMap<>();
        filterMap.put(VENDOR, "vendor");
        filterMap.put(PRODUCT_TYPE, "productCode");
        filterMap.put(SCODE, "scode");
        filterMap.put(PRODUCT, "productId");

        final String finalFilterName = filterMap.get(filter);
        Function<Map<String, Object>, String> groupingByFunc = trend -> trend.get(finalFilterName).toString();
        Predicate<Map<String, Object>> canBefilterPredicate = trend -> trend.get(finalFilterName) != null;

        String maxDate;
        Set<String> dateSet = consumerTrends.stream().map(t -> t.get("date").toString()).collect(Collectors.toSet());
        Optional<String> maxDateOpt = dateSet.stream().max(String::compareTo);
        maxDate = maxDateOpt.orElse(null);

        return consumerTrends.stream()
                .filter(canBefilterPredicate)
                .collect(Collectors.groupingBy(groupingByFunc))
                .entrySet()
                .stream()
                .flatMap(entry -> {
                    List<Map<String, Object>> sortedList = entry.getValue().stream()
                            .sorted(Comparator.comparing(trend -> trend.get("date").toString()))
                            .collect(Collectors.toList());

                    if (sortedList.size() == 1) {
                        String theOnlyDate = String.valueOf(sortedList.get(0).get("date"));
                        if (theOnlyDate.equals(maxDate) && theOnlyDate.compareTo(minDate) > 0) {
                            Map<String, Object> singleItem = sortedList.get(0);
                            singleItem.put("amountOfChange", 0);
                            singleItem.put("changeRate", "--");
                            return sortedList.stream();
                        } else {
                            return null;
                        }
                    } else {
                        Map<String, Object> first = sortedList.get(0);
                        Map<String, Object> second = sortedList.get(1);

                        BigDecimal firstValue = (BigDecimal) first.get("value");
                        BigDecimal secondValue = (BigDecimal) second.get("value");
                        BigDecimal amountOfChangeInBigdecimal = secondValue.subtract(firstValue, new MathContext(2, RoundingMode.HALF_UP));

                        String amountOfChange = formatBigDecimal(amountOfChangeInBigdecimal);
                        String changeRate = null == firstValue || BigDecimal.ZERO.compareTo(firstValue) == 0 ? "--" : formatBigDecimal(amountOfChangeInBigdecimal.multiply(BigDecimal.valueOf(100)).divide(firstValue, 2, RoundingMode.HALF_UP));

                        second.put("amountOfChange", amountOfChange);
                        second.put("changeRate", changeRate);

                        // 只保留第二项
                        return sortedList.stream().skip(1);
                    }
                })
                .collect(Collectors.toList());
    }

    public String getEnv(String instanceId) {
        String env = (String) redisUtils.get("env" + instanceId);
        if (StringUtils.isEmpty(env)) {
            RcHostInfo host = rcHostInfoService.getOne(new LambdaQueryWrapper<RcHostInfo>().eq(RcHostInfo::getInstanceId, instanceId));
            if (host == null) {
                RcDatabaseInfo database = rcDatabaseInfoService.getOne(new LambdaQueryWrapper<RcDatabaseInfo>().eq(RcDatabaseInfo::getInstanceId, instanceId));
                if (database == null) {
                    RcMiddlewareInfo middleware = rcMiddlewareService.getOne(new LambdaQueryWrapper<RcMiddlewareInfo>().eq(RcMiddlewareInfo::getInstanceId, instanceId));
                    if (middleware != null) {
                        env = middleware.getEnv();
                    }
                } else {
                    env = database.getEnv();
                }
            } else {
                env = host.getEnv();
            }
        }

        if (!StringUtils.isEmpty(env)) {
            redisUtils.set("env" + instanceId, env);
        }
        return env;
    }


    /**
     * @Description: 分页查看账单明细
     * @Author: 王帅
     * @Date: 2024/1/4 15:29
     * @Param: [request]
     * @Return: com.baomidou.mybatisplus.core.metadata.IPage<com.haier.devops.bill.common.vo.NodeDetailBillsVo>
     */
    public IPage<NodeDetailBillsVo> getDetailBillsByPage(DetailBillParam request) {
        String scode = request.getScode();
        List<String> scodes = getPermScodes(scode, "getDetailBillsByPage");
        if (CollectionUtils.isEmpty(scodes)) {
            log.error("the user {} not this scodes permission {}", HeaderUtil.getUserId(), request.getScode());
            throw new RuntimeException("此用户没有这些子产品权限");
        }
        String type = request.getType();
        String startCycle = request.getStartCycle().replaceAll("/", "-");
        String endCycle = request.getEndCycle().replaceAll("/", "-");
        QueryWrapper<NodeDetailBillsVo> queryWrapper = getWrapper(request, scodes, type, startCycle, endCycle);

        IPage<NodeDetailBillsVo> page = new Page<>();
        page.setCurrent(request.getPage());
        page.setSize(request.getPer_page());
        return refinedRawBillMapper.getDetailBillByType(page, queryWrapper);
    }

    @NotNull
    private QueryWrapper<NodeDetailBillsVo> getWrapper(DetailBillParam request, List<String> scodes, String type, String startCycle, String endCycle) {
        List<String> productCodes = null;
        if (StringUtils.isNotBlank(request.getProductCode())) {
            productCodes = Arrays.stream(request.getProductCode().split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.toList());
        }
        QueryWrapper<NodeDetailBillsVo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("w.scode", scodes);
        if (!CollectionUtils.isEmpty(productCodes)) {
            queryWrapper.in("t.product_code", productCodes);

        }
        if (StringUtils.isNotBlank(request.getVendor())) {
            queryWrapper.eq("t.vendor", request.getVendor());
        }
        if (StringUtils.isNotBlank(request.getAccount())) {
            queryWrapper.eq("t.account_name", request.getAccount());

        }
        queryWrapper.groupBy("t.billing_cycle");
        queryWrapper.between("t.billing_cycle", startCycle, endCycle);
        queryWrapper.orderByDesc("t.billing_cycle");

        queryWrapper.eq("k.status", "1");
        queryWrapper.eq("k.stage", "refinement");
        queryWrapper.groupBy("w.project_code,w.project_name,t.product_name,w.scode,t.vendor,t.subscription_type,t.product_code,t.product_detail");
        return queryWrapper;
    }

    /**
     * @Description: 下载账单明细
     * @Author: 王帅
     * @Date: 2024/1/23 17:59
     * @Param: [request]
     * @Return: com.haier.devops.bill.export.vo.UploadResult
     */
    @XxlJob("downloadDetailBills")
    public UploadResult downloadDetailBills(DetailBillParam request) {
        List<String> scodes = request.getPermScodes();
        request.setPage(1);
        String type = request.getType();
        String startCycle = request.getStartCycle().replaceAll("/", "-");
        String endCycle = request.getEndCycle().replaceAll("/", "-");

        QueryWrapper<NodeDetailBillsVo> queryWrapper = getWrapper(request, scodes, type, startCycle, endCycle);

        IPage<NodeDetailBillsVo> pageResult = getNodeDetailBills(request, queryWrapper);
        List<NodeDetailBillsVo> allDetailBills = new ArrayList<>(pageResult.getRecords());
        long pages = pageResult.getPages();
        log.info("downloadDetailBills pages:{}", pages);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 2; i < pages; i++) {
            int finalPageNum = i;
            // 使用异步执行器处理账单数据
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                request.setPage(finalPageNum);
                IPage<NodeDetailBillsVo> page = getNodeDetailBills(request, queryWrapper);
                if (!CollectionUtils.isEmpty(page.getRecords())) {
                    allDetailBills.addAll(page.getRecords());
                }
                log.info("getNodeDetailBills is success ,ser:{},page:{}", request.getSerialNo(), finalPageNum);
            }, localBootAsyncExecutor);
            futures.add(voidCompletableFuture);
        }
        // 等待所有子线程的任务执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        UploadResult upload = null;
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).build()) {
            WriteSheet sheet = EasyExcel
                    .writerSheet(0, "账单明细")
                    .head(NodeDetailBillsVo.class).build();
            excelWriter.write(allDetailBills, sheet);
            excelWriter.finish();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            upload = ossUtil.upload(getFilePath(request), inputStream);
            updateLog(request, upload.getPutResult(), upload.getUrl(), StringUtils.isNotBlank(upload.getUrl()));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出失败：{}", JSON.toJSONString(request), e);
            updateLog(request, null, null, false, e.getMessage());
            throw new RuntimeException("导出失败 " + e.getMessage());
        }
        return upload;
    }

    public void updateLog(DetailBillParam request, PutObjectResult result, String url,
                          boolean isUploadingSuccess, String... remark) {
        LambdaUpdateWrapper<ExportLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExportLog::getSerialNo, request.getSerialNo());
        String remakrStr = "";
        if (remark.length > 0) {
            remakrStr = remark[0];
        }
        ExportLog entity = ExportLog.builder()
                .putResult(JSON.toJSONString(result))
                .url(url)
                .remark(remakrStr)
                .isReady(isUploadingSuccess ? SUCCESS : FAILED)
                .build();

        exportLogService.update(entity, updateWrapper);
    }

    public String getFileName(DetailBillParam unit) {
        return unit.getVendor()+"账单明细"
                + "（" + unit.getStartCycle() + "-" + unit.getEndCycle() + "）"
                + "-"
                + System.currentTimeMillis()
                + ".xlsx";
    }

    public String getFilePath(DetailBillParam unit) {
        String currentDay = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return DIR_NAME + "/" + currentDay + "/" + getFileName(unit);
    }

    @Override
    public Map getSummerMap(String startDateStr,String endDateStr, List<String> columns,String type){
        Map map = null;
        switch (type){
            case "1":
                map = getMonthMap(startDateStr,endDateStr,columns);
                break;
            case "2":
                map = getQMap(startDateStr,endDateStr,columns);
                break;
            case "3":
                map = getYMap(startDateStr,endDateStr,columns);
                break;
            default:
                map = getDayMap(startDateStr,endDateStr,columns);
                break;
        }

        return map;
    }

    @Override
    public List<String> getAccountListByCurrentCy(String currentCy) {
        return cloudAccountService.getAccountListByCurrentCy(currentCy);
    }

    @Override
    public Map getMainAssetsDetail(String scode, List<String> vendors,LocalDate startDate, LocalDate endDate,String type) {
        List<String> authedScodes = AuthUtil.extractAuthedScodes(scode);
        if (CollectionUtils.isEmpty(authedScodes)) {
            return new HashMap<String, Integer>() {{
                put("count", 0);
            }};
        }
        String start = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
        String end = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59"));
        Map map = new HashMap<String, Integer>();
        List<Map> list = new ArrayList<>();
        switch (type){
            case "ecs":
                list = nodeMapper.getEcsDetail(authedScodes,vendors,start,end);
                break;
            case "db":
                list = nodeMapper.getDbDetail(authedScodes,vendors,start,end);
                break;
            case "mw":
                list = nodeMapper.getMwDetail(authedScodes,vendors,start,end);
                break;
            default:
                list = nodeMapper.getEcsDetail(authedScodes,vendors,start,end);
                break;
        }
        map.put("count",list.size());
        if(list.size() > 10){
            list = list.subList(0, 10);
        }
        map.put("list",list);
        return map;
    }

    private Map getYMap(String startDateStr, String endDateStr, List<String> columns) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        // 日期格式化模式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(startDateStr, dateFormatter);
        LocalDate endDate = LocalDate.parse(endDateStr, dateFormatter);

        // 从开始日期的年份开始循环，直到结束日期的年份
        int startYear = startDate.getYear();
        int endYear = endDate.getYear();

        for (int year = startYear; year <= endYear; year++) {
            String currentDateStr = String.format("%d", year);
            map.put(currentDateStr,0);
            columns.add(currentDateStr);
        }

        // 将集合转换为列表并返回
        return map;
    }

    private Map getQMap(String startDateStr, String endDateStr, List<String> columns) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        // 日期格式化模式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(startDateStr, dateFormatter);
        LocalDate endDate = LocalDate.parse(endDateStr, dateFormatter);

        // 获取开始日期所在的季度的第一天
        LocalDate tempDate = startDate.withDayOfMonth(1).withMonth((startDate.getMonthValue() - 1) / 3 * 3 + 1);
        // 获取结束日期所在季度的最后一天
        LocalDate endQuarter = endDate.withDayOfMonth(1).withMonth((endDate.getMonthValue() - 1) / 3 * 3 + 3).withDayOfMonth(
                endDate.withDayOfMonth(1).plusMonths(1).minusDays(1).getDayOfMonth()
        );

        while (tempDate.isBefore(endQuarter) || tempDate.isEqual(endQuarter)) {
            String currentDateStr = formatQuarter(tempDate);
            map.put(currentDateStr,0);
            columns.add(currentDateStr);
            tempDate = tempDate.plusMonths(3);
        }
        return map;
    }

    private static String formatQuarter(LocalDate date) {
        int month = date.getMonthValue();
        int quarter = (month - 1) / 3 + 1;
        return String.format("%d-Q%d", date.getYear(), quarter);
    }

    private Map getMonthMap(String startDateStr, String endDateStr, List<String> columns) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        // 日期格式化模式
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 输出格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(startDateStr, dateFormatter);
        LocalDate endDate = LocalDate.parse(endDateStr, dateFormatter);

        // 从开始日期的当月开始循环
        LocalDate tempDate = startDate.withDayOfMonth(1);
        // 结束日期的当月的最后一天
        LocalDate endMonth = endDate.withDayOfMonth(endDate.lengthOfMonth());

        while (tempDate.isBefore(endMonth) || tempDate.isEqual(endMonth)) {
            String currentDateStr = tempDate.format(outputFormatter);
            map.put(currentDateStr,0);
            columns.add(currentDateStr);
            tempDate = tempDate.plusMonths(1);
        }

        return map;
    }

    private Map getDayMap(String startDateStr, String endDateStr,List<String> columns) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        // 日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析字符串为 LocalDate 对象
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 从开始日期循环到结束日期
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // 输出当前日期
            String currentDateStr = currentDate.format(formatter);
            map.put(currentDateStr,0);
            columns.add(currentDateStr);
            // 移动到下一个日期
            currentDate = currentDate.plusDays(1);

        }
        return map;
    }

    @Override
    public void exportBillExcel(DimensionDetailParam dto, HttpServletRequest request, HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try{
            String fileName="多维度消费分析（"+dto.getStartCycle()+"-"+dto.getEndCycle()+")";
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            WriteSheet writeSheet = null;
            List<Map<String, Object>> list = getDimensionCustDetail(dto,dto.getCurrency());
            List<List<Object>> resultList = new LinkedList<>();
            List<String> columns = getHeadColumns(dto);
            Map<String, Object> summerMap = getSummerMap(dto.getStartCycle(),dto.getEndCycle(),columns,dto.getCycleType());
            if (!CollectionUtils.isEmpty(list)){
                Map<String,Object> headMap = getHeadMap(dto);
                Map<String,Object> commonMap = DeepCopyUtil.deepCopy(summerMap);
                Map<String,Object> commonHeaderMap = DeepCopyUtil.deepCopy(headMap);
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = list.get(i);
                    List<Object> subList = new LinkedList<>();
                    for (String key:map.keySet()) {
                        if(key.equals("slice")){
                            Map<String,Object> slice = (Map) map.get("slice");
                            for(String dateKey:slice.keySet()){
                                summerMap.put(dateKey,slice.get(dateKey));
                            }
                        }else{
                            headMap.put(key,map.get(key));
                        }
                    }
                    headMap.remove("businessDomainIds");
                    headMap.remove("productCode");
                    headMap.remove("subProductId");
                    headMap.remove("productId");
                    subList.addAll(headMap.values());
                    subList.addAll(summerMap.values());
                    resultList.add(subList);
                    summerMap = DeepCopyUtil.deepCopy(commonMap);
                    headMap = DeepCopyUtil.deepCopy(commonHeaderMap);
                }
            }
            writeSheet = EasyExcel.writerSheet(dto.getStartCycle()+"_"+dto.getEndCycle()).head(head(columns))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(getStyleStrategy())
                    .build();
            // 写出sheet数据
            excelWriter.write(resultList, writeSheet);
            // 关流
            excelWriter.finish();
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出失败：{}", JSON.toJSONString(request), e);
            throw new RuntimeException("导出失败 "+e.getMessage());
        }finally {

        }
    }

    private Map<String, Object> getHeadMap(DimensionDetailParam dto) {
        Map<String, Object> headMap = new LinkedHashMap<String, Object>();
        if(dto.getExportAll()){
            headMap.put("businessDomain","");
        }
        if(dto.getDataDimensions().contains("subProduct")){
            headMap.put("subProductName","");
        }
        if(dto.getDataDimensions().contains("vendor")){
            headMap.put("vendor","");
        }
        if(dto.getDataDimensions().contains("product")){
            headMap.put("productName","");
        }
        if(dto.getDataDimensions().contains("productType")){
            headMap.put("productType","");
        }
        if(dto.getDataDimensions().contains("accountName")){
            headMap.put("accountName","");
        }
        return headMap;
    }

    private List<String> getHeadColumns(DimensionDetailParam dto) {
        List<String> columns = new LinkedList<>();
        if(dto.getExportAll()){
            columns.add("领域");
        }
        if(dto.getDataDimensions().contains("subProduct")){
            columns.add("子产品");
        }
        if(dto.getDataDimensions().contains("vendor")){
            columns.add("云厂商");
        }
        if(dto.getDataDimensions().contains("product")){
            columns.add("产品");
        }
        if(dto.getDataDimensions().contains("productType")){
            columns.add("云产品类型");
        }
        if(dto.getDataDimensions().contains("accountName")){
            columns.add("云账号");
        }

        return columns;


    }

    private List<List<String>> head(List<String> dateList) {
        List<List<String>> headTitles = Lists.newArrayList();
        // 固定title
        String billPeriodHead = "多维度消费分析";
        // List<String> paymentTitles = Lists.newArrayList("上午", "下午");
        for (String localDate : dateList) {
            headTitles.add(Lists.newArrayList(billPeriodHead,localDate));
        }
        return headTitles;
    }

    private IPage<NodeDetailBillsVo> getNodeDetailBills(DetailBillParam request, QueryWrapper<NodeDetailBillsVo> queryWrapper) {
        IPage<NodeDetailBillsVo> page = new Page<>();
        page.setCurrent(request.getPage());
        page.setSize(BATCH_SIZE);
        return refinedRawBillMapper.getDetailBillByType(page, queryWrapper);
    }

    public List<String> getPermScodes(String scode, String className) {
        List<String> scodes = Arrays.stream(scode.split(","))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
        log.info(className + " start scode:{}", scodes);
        boolean administrators = getAdmin();
        // 不是管理员的话需要进行权限判断，去除掉没有权限的scode
        if (!administrators) {
            List<String> noPermScodes = new ArrayList<>();
            List<String> permissionScodes = authorityService.getAuthorizedScodes(HeaderUtil.getUserId());
            if (CollectionUtils.isEmpty(permissionScodes)) {
                log.error(className + "user {} not permission", HeaderUtil.getUserId());
                // throw new RuntimeException("此用户没有子产品权限");
                return new ArrayList<>();
            }
            for (String code : scodes) {
                if (!permissionScodes.contains(code)) {
                    noPermScodes.add(code);
                }
            }
            scodes.removeAll(noPermScodes);
        }
        log.info(className + " filter scode:{}", scodes);
        return scodes;
    }

    /**
     * 分页查看多维度分析信息
     * @param dimensionDetailParam
     * @return
     */
    public List<Map<String, Object>> getDimensionCustDetail(DimensionDetailParam dimensionDetailParam, String... currency) {
        List<String> authedScodes = null;
        List<String> domains = null;

        List<String> dataDimensions = dimensionDetailParam.getDataDimensions();
        if(!dimensionDetailParam.getExportAll()){
            authedScodes = AuthUtil.extractAuthedScodes(dimensionDetailParam.getScode());
        }else{
            domains = Arrays.asList(dimensionDetailParam.getDomains().split(","));
            dataDimensions.add(0,"domain");
        }
        String startCycle = dimensionDetailParam.getStartCycle();
        String endCycle = dimensionDetailParam.getEndCycle();
        String vendor = dimensionDetailParam.getVendor();
        String productCode = dimensionDetailParam.getProductCode();
        List<String> productList = dimensionDetailParam.getProductList();
        QueryWrapper<DimensionDetailVo> queryWrapper = new QueryWrapper<>();
        List<String> conditions = new ArrayList<>();
        List<String> groupConditions = new ArrayList<>();
        List<String> sortedKeys = new ArrayList<>();

        if (!CollectionUtils.isEmpty(dataDimensions)) {
            for (String dataDimension : dataDimensions) {
                if("domain".equals(dataDimension)){
                    conditions.add("a.business_domain_ids as businessDomainIds");
                    conditions.add("a.business_domains as businessDomain");
                    groupConditions.add("a.business_domain_ids");
                    sortedKeys.add("businessDomainIds");
                }else if ("subProduct".equals(dataDimension)) {
                    // 子产品
                    conditions.add("a.sub_product_id as subProductId");
                    conditions.add("a.sub_product_name as subProductName");
                    groupConditions.add("a.sub_product_id");
                    sortedKeys.add("subProductId");
                } else if ("product".equals(dataDimension)) {
                    // 产品
                    conditions.add("a.product_id as productId");
                    conditions.add("a.product_name as productName");
                    groupConditions.add("a.product_id");
                    sortedKeys.add("productId");
                } else if ("vendor".equals(dataDimension)) {
                    // 云厂商
                    conditions.add("a." + dataDimension);
                    groupConditions.add("a." + dataDimension);
                    sortedKeys.add("vendor");
                } else if ("accountName".equals(dataDimension)) {
                    // 云账号
                    conditions.add("a.account_name as accountName");
                    groupConditions.add("a.account_name");
                } else if ("productType".equals(dataDimension)) {
                    // 云产品类型
                    conditions.add("a.product_code as productCode");
                    conditions.add("a.product_type as productType");
                    groupConditions.add("a.product_code");
                    sortedKeys.add("productCode");
                }
            }
        }
        queryWrapper.eq("d.date_type", "month");
        queryWrapper.between("d.date_value", startCycle, endCycle);
        groupConditions.add("dimensionalValue");
        queryWrapper.groupBy(groupConditions);
        queryWrapper.orderByAsc(groupConditions);
        List<Map<String, Object>> records = aggregatedBillMapper.DimensionDetails(
        queryWrapper, vendor,productCode,productList,dimensionDetailParam.getDataType(), conditions, dimensionDetailParam.getCycleType(), authedScodes,domains,
        dimensionDetailParam.getExportAll(), currency);
        return groupAndOrderDynamicAttributes(records, sortedKeys);
    }

    @Override
    public List<Map<String, Object>> getDimensionCustDetail(DimensionDetailParam dimensionDetailParam, BigDecimal exchangeRate) {
        List<String> authedScodes = null;
        List<String> domains = null;

        List<String> dataDimensions = dimensionDetailParam.getDataDimensions();
        if(!dimensionDetailParam.getExportAll()){
            authedScodes = AuthUtil.extractAuthedScodes(dimensionDetailParam.getScode());
        }else{
            domains = Arrays.asList(dimensionDetailParam.getDomains().split(","));
            dataDimensions.add(0,"domain");
        }
        String startCycle = dimensionDetailParam.getStartCycle();
        String endCycle = dimensionDetailParam.getEndCycle();
        String vendor = dimensionDetailParam.getVendor();
        String productCode = dimensionDetailParam.getProductCode();
        List<String> productList = dimensionDetailParam.getProductList();
        QueryWrapper<DimensionDetailVo> queryWrapper = new QueryWrapper<>();
        List<String> conditions = new ArrayList<>();
        List<String> groupConditions = new ArrayList<>();
        List<String> sortedKeys = new ArrayList<>();

        if (!CollectionUtils.isEmpty(dataDimensions)) {
            for (String dataDimension : dataDimensions) {
                if("domain".equals(dataDimension)){
                    conditions.add("a.business_domain_ids as businessDomainIds");
                    conditions.add("a.business_domains as businessDomain");
                    groupConditions.add("a.business_domain_ids");
                    sortedKeys.add("businessDomainIds");
                }else if ("subProduct".equals(dataDimension)) {
                    // 子产品
                    conditions.add("a.sub_product_id as subProductId");
                    conditions.add("a.sub_product_name as subProductName");
                    groupConditions.add("a.sub_product_id");
                    sortedKeys.add("subProductId");
                } else if ("product".equals(dataDimension)) {
                    // 产品
                    conditions.add("a.product_id as productId");
                    conditions.add("a.product_name as productName");
                    groupConditions.add("a.product_id");
                    sortedKeys.add("productId");
                } else if ("vendor".equals(dataDimension)) {
                    // 云厂商
                    conditions.add("a." + dataDimension);
                    groupConditions.add("a." + dataDimension);
                    sortedKeys.add("vendor");
                } else if ("accountName".equals(dataDimension)) {
                    // 云账号
                    conditions.add("a.account_name as accountName");
                    groupConditions.add("a.account_name");
                } else if ("productType".equals(dataDimension)) {
                    // 云产品类型
                    conditions.add("a.product_code as productCode");
                    conditions.add("a.product_type as productType");
                    groupConditions.add("a.product_code");
                    sortedKeys.add("productCode");
                }
            }
        }
        queryWrapper.eq("d.date_type", "month");
        queryWrapper.between("d.date_value", startCycle, endCycle);
        groupConditions.add("dimensionalValue");
        queryWrapper.groupBy(groupConditions);
        queryWrapper.orderByAsc(groupConditions);
        List<Map<String, Object>> records = aggregatedBillMapper.DimensionDetailsAll(
                queryWrapper, vendor,productCode,productList,dimensionDetailParam.getDataType(), conditions, dimensionDetailParam.getCycleType(), authedScodes,domains,
                dimensionDetailParam.getExportAll(), exchangeRate);
        return groupAndOrderDynamicAttributes(records, sortedKeys);
    }


    private static List<Map<String, Object>> groupAndOrderDynamicAttributes(List<Map<String, Object>> result, List<String> sortedKeys) {
        return result.stream()
                .collect(Collectors.groupingBy(map -> {
                    Map<String, Object> keyMap = new HashMap<>();
                    map.forEach((key, value) -> {
                        if (!key.equals("dimensionalValue") && !key.equals("value")) {
                            keyMap.put(key, value);
                        }
                    });
                    return keyMap;
                }))
                .entrySet().stream()
                .map(entry -> {
                    Map<String, Object> groupedMap = new HashMap<>(entry.getKey());

                    Map<String, Object> sliceMap = new HashMap<>();
                    entry.getValue().forEach(m -> {
                        String dimensionalValue = Optional.ofNullable(m.get("dimensionalValue"))
                                .map(Object::toString)
                                .orElse("");
                        BigDecimal value;
                        try {
                            Object rawValue = m.get("value");
                            if (rawValue == null) {
                                value = BigDecimal.ZERO;
                            } else if (rawValue instanceof BigDecimal) {
                                value = (BigDecimal) rawValue;
                            } else if (rawValue instanceof Number) {
                                value = new BigDecimal(rawValue.toString());
                            } else {
                                value = new BigDecimal(rawValue.toString());
                            }
                        } catch (Exception e) {
                            value = BigDecimal.ZERO;
                        }
                        sliceMap.put(dimensionalValue, formatBigDecimal(value));
                    });

                    groupedMap.put("slice", sliceMap);
                    return groupedMap;
                })
                // Sort the grouped maps by the specified keys
                .sorted((map1, map2) -> {
                    for (String key : sortedKeys) {
                        Comparable value1 = (Comparable) map1.get(key);
                        Comparable value2 = (Comparable) map2.get(key);

                        // Handle null values
                        if (null == value1 && null == value2) {
                            return 0;
                        }
                        if (null == value1) {
                            return -1;
                        }
                        if (null == value2) {
                            return 1;
                        }

                        int comparison = value1.compareTo(value2);
                        if (comparison != 0) {
                            return comparison;
                        }
                    }
                    return 0;
                })
                .collect(Collectors.toList());
    }

    public Set<String> getScodes() {
        JSONObject userInfo = cmdbApi.getUserInfo();
        JSONArray data = userInfo.getJSONArray("data");
        Set<String> scode = new HashSet<>();
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(map -> {
                String sCode = String.valueOf(((LinkedHashMap) map).get("almSCode"));
                scode.add(sCode);
            });
        }
        return scode;
    }

    public boolean getAdmin() {
        boolean administrators = false;
        User currentUser = LoginContextHolder.getCurrentUser();
        List<HworkAuthorityApi.RolesDTO> roles = currentUser.getRoles();
        if (!CollectionUtils.isEmpty(roles)) {
            for (HworkAuthorityApi.RolesDTO role : roles) {
                if (role.getRoleCode().equals("HCMS_Admin")) {
                    administrators = true;
                }
            }
        }
        return administrators;
    }
}
