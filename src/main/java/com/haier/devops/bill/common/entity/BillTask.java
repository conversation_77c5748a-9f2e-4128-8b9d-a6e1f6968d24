package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.haier.devops.bill.common.constant.CommonConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * (BillTask)实体类
 *
 * <AUTHOR>
 * @since 2024-02-28 13:17:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
@TableName("bc_bill_task")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillTask implements Serializable {
    private static final long serialVersionUID = -10462942970199365L;
       
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
     
       
    @Schema(description = "云厂商")
    private String vendor;

    @Schema(description = "云账户id")
    private String accountId;


    @Schema(description = "云账号")
    private String accountName;
       
    @Schema(description = "账期")
    private String billingCycle;

    private String stage;
       
    private String taskId;
     
       
    @Schema(description = "0: 初始化，1: 正式数据，2: 备份数据，3: 失败数据，4: 历史数据")
    private Integer status;
     

    private LocalDateTime createTime;
     
       
    private LocalDateTime updateTime;


    public BillTask(RawBillSyncLog rawBillSyncLog){
        this.vendor = rawBillSyncLog.getVendor();
        this.billingCycle = rawBillSyncLog.getBillingCycle();
        this.taskId = String.valueOf(UUID.randomUUID());
        this.accountId = rawBillSyncLog.getAccountId();
        this.accountName = rawBillSyncLog.getAccountName();
        this.stage = CommonConstant.LOG_STAGE_AGGREGATION;
    }
}

