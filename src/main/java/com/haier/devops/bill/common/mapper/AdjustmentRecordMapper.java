package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.haier.devops.bill.common.entity.AdjustmentRecord;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AdjustmentRecordMapper
* @Description: 调账记录Mapper
* @author: 张爱苹
* @date: 2024/1/31 10:21
*/
@Repository
public interface AdjustmentRecordMapper extends BaseMapper<AdjustmentRecord> {


    List<AdjustmentRecordVo> getAdjustmentRecordList(@Param(Constants.WRAPPER) QueryWrapper<AdjustmentRecord> queryWrapper);
}
