package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ResourceInstance;
import com.haier.devops.bill.common.mapper.ResourceInstanceMapper;
import com.haier.devops.bill.common.service.ResourceInstanceService;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @ClassName: ResourceInstanceServiceImpl
* @Description: 资源实例
* @author: 张爱苹
* @date: 2024/3/12 14:26
*/
@Service
public class ResourceInstanceServiceImpl extends ServiceImpl<ResourceInstanceMapper, ResourceInstance> implements ResourceInstanceService {
    private Logger logger = LoggerFactory.getLogger(ResourceInstanceServiceImpl.class);

    @Override
    public List<String> getCommodityCodeList(String accountName) {
        LambdaQueryWrapper<ResourceInstance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceInstance::getResourceUserName,accountName);
        return baseMapper.selectList(queryWrapper).stream().map(ResourceInstance::getCommodityCode).distinct().collect(Collectors.toList());
    }

    @Override
    public PageInfo<CmdbProductOverview> listByPage(ResourceInstanceDTO dto) {
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->baseMapper.listByPage(dto)
            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, dto:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    public ResourceInstanceVo queryResourceInstanceList(CmdbProductOverview cmdbProductOverviewList) {
        List<ResourceInstanceVo> list = baseMapper.queryResourceInstanceList(cmdbProductOverviewList);
        if(!CollectionUtils.isEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    @Override
    public ResourceInstance getResourceInstance(ResourceInstance resourceInstance) {
        LambdaQueryWrapper<ResourceInstance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceInstance::getResourceUserId,resourceInstance.getResourceUserId());
        queryWrapper.eq(ResourceInstance::getCommodityCode,resourceInstance.getCommodityCode());
        if(StringUtils.isEmpty(resourceInstance.getApportionCode())){
            queryWrapper.apply("(apportion_code is null or apportion_code = '')");
        }else{
            queryWrapper.eq(ResourceInstance::getApportionCode,resourceInstance.getApportionCode());
        }
        queryWrapper.eq(ResourceInstance::getResourceType,resourceInstance.getResourceType());
        queryWrapper.eq(ResourceInstance::getResourceId,resourceInstance.getResourceId());
        return  baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Map> queryInfoByAccountId(String accountIdentify) {
        return baseMapper.queryInfoByAccountId(accountIdentify);
    }

    @Override
    public void updateCostUnit(List<ResourceInstanceVo> resultList, String finalUnitName, Long finalUnitId) {
        UpdateWrapper<ResourceInstance> updateWrapper = new UpdateWrapper();
        updateWrapper.in("id", resultList.stream().map(ResourceInstanceVo::getId).collect(Collectors.toList()));
        updateWrapper.set("cost_unit", finalUnitName);
        updateWrapper.set("cost_unit_id",finalUnitId);
        update(updateWrapper);
    }
}
