package com.haier.devops.bill.common.controller;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.HostInfo;
import com.haier.devops.bill.common.service.HostInfoService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;

import static com.haier.devops.bill.common.enums.CurrencyEnum.CNY;
import static com.haier.devops.bill.common.enums.CurrencyEnum.USD;

@RestController
@RequestMapping("/api/v1/hcms/bill/host")
@Slf4j
public class HostInfoController {
    private final HostInfoService hostInfoService;

    public HostInfoController(HostInfoService hostInfoService) {
        this.hostInfoService = hostInfoService;
    }

    /**
     * 分页查询服务器信息
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/list")
    public ResponseEntityWrapper<PageInfo<HostInfo>> listByPage(@Valid @RequestBody PageRequest request) throws Exception{
        try {
            PageInfo<HostInfo> pageInfo = hostInfoService.list(request.getScodes(),
                    request.getPage(),
                    request.getPer_page(),
                    CNY.getCurrency());
            return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
            log.error("分页查询服务器信息失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "分页查询服务器信息失败", null);
        }
    }


    /**
     * 分页查询服务器信息
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/list/usd")
    public ResponseEntityWrapper<PageInfo<HostInfo>> listByPageWithUSD(@Valid @RequestBody PageRequest request) throws Exception{
        try {
            PageInfo<HostInfo> pageInfo = hostInfoService.list(request.getScodes(),
                    request.getPage(),
                    request.getPer_page(),
                    USD.getCurrency());
            return new ResponseEntityWrapper<>(pageInfo);
        } catch (Exception e) {
            log.error("分页查询服务器信息失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "分页查询服务器信息失败", null);
        }
    }

    @Data
    static class PageRequest {
        @Min(1)
        private int page;
        @Min(10)
        private int per_page;
        private List<String> scodes;
    }

}
