package com.haier.devops.bill.common.api;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import feign.Param;
import feign.RequestLine;
import lombok.Data;

import java.util.List;

/**
 * 混合云管理系统-资源管理中心
 */
public interface HcmsApi {
    /**
     * 查询海尔员工信息
     * @param userCode
     * @return
     */
    @RequestLine("GET /api/v1/hcms/resource/cmdb/user?q={userCode}")
    ResultWrapper queryHaierUserInfo(@Param("userCode") String userCode);

    @Data
    class ResultWrapper {
        private int code;
        private String msg;
        private String requestId;
        private List<User> data;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public class User {
        /**
         * 用户ID
         */
        @SerializedName("UserId")
        private String userId;

        /**
         * 用户名
         */
        @SerializedName("UserName")
        private String userName;

        /**
         * 邮箱
         */
        @SerializedName("Email")
        private String email;

        /**
         * 一线经理ID
         */
        @SerializedName("FirstLineManagerId")
        private String firstLineManagerId;

        /**
         * 一线经理姓名
         */
        @SerializedName("FirstLineManagerName")
        private String firstLineManagerName;

        /**
         * 一线经理邮箱
         */
        @SerializedName("FirstLineManagerEmail")
        private String firstLineManagerEmail;

        /**
         * 部门
         */
        @SerializedName("Dept")
        private String dept;
    }

}
