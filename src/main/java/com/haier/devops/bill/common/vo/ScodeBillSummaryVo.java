package com.haier.devops.bill.common.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScodeBillSummaryVo {
    /**
     * 云厂商
     */
    @ExcelProperty("云厂商")
    @ColumnWidth(10)
    private String vendor;

    /**
     * 所在账号
     */
    @ExcelProperty("所在账号")
    @ColumnWidth(16)
    private String accountName;

    /**
     * 项目S码
     */
    @ExcelProperty("项目S码")
    @ColumnWidth(10)
    private String scode;

    /**
     * 项目简写
     */
    @ExcelProperty("项目简写")
    @ColumnWidth(10)
    private String projectCode;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ColumnWidth(16)
    private String projectName;

    /**
     * 项目上云时间
     */
    @ExcelProperty("项目上云时间")
    @ColumnWidth(10)
    private String projectCloudingDate;

    /**
     * 出账公司
     */
    @ExcelProperty("出账公司")
    @ColumnWidth(20)
    private String billingCompany;

    /**
     * 应用负责人工号
     */
    @ExcelProperty("应用负责人工号")
    private String chargerEmpCode;

    /**
     * 应用负责人姓名
     */
    @ExcelProperty("应用负责人姓名")
    private String chargerName;


    @ExcelProperty("领域")
    private String domain;

    /**
     * 部门
     */
    @ExcelProperty("部门")
    @ColumnWidth(40)
    private String deptName;

    /**
     * 部门对接人工号
     */
    @ExcelProperty("部门对接人工号")
    private String counterpartEmpCode;

    /**
     * 部门对接人姓名
     */
    @ExcelProperty("部门对接人姓名")
    private String counterpartEmpName;

    /**
     * 费用金额
     */
    @ExcelProperty("费用金额")
    @ColumnWidth(16)
    private String cost;

    /**
     * 费用类型
     */
    @ExcelProperty("费用类型")
    private String expenseType;
}
