package com.haier.devops.bill.common.task;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.service.ExportLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class ExportLogListener {
    @Autowired
    private ExportLogService exportLogService;

    public void listen()
    {
        UpdateWrapper<ExportLog> lambdaQueryWrapper = new UpdateWrapper<>();
        lambdaQueryWrapper.eq("is_ready",0);
        lambdaQueryWrapper.set("is_ready", 2);
        exportLogService.update(lambdaQueryWrapper);
    }
}
