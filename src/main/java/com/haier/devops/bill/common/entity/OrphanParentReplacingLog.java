package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 父子资源替换日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-08
 */
@TableName("bc_orphan_parent_replacing_log")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrphanParentReplacingLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String orphanInstanceId;

    private String orphanSupplementId;

    private String orphanProductCode;

    private String orphanProductName;

    private String orphanAggregatedId;

    private String parentAggregatedId;

    private String parentInstanceId;

    private String parentProductCode;

    private String parentProductName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
