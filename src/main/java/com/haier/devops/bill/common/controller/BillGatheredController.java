
package com.haier.devops.bill.common.controller;


import com.haier.devops.bill.common.dto.BillGatheredDTO;
import com.haier.devops.bill.common.service.BillGatheredService;
import com.haier.devops.bill.common.vo.AlarmLogBillDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: BillGatheredController
 * @Description:  每日账单汇总
 * @author: 张爱苹
 * @date: 2024/1/12 17:19
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/bill-gathered")
public class BillGatheredController {

	@Autowired
	private BillGatheredService billGatheredService;

	/**
	* @Description: 查询告警账单明细
	* @author: 张爱苹
	* @date: 2024/1/22 09:56
	* @param billGatheredDTO:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.haier.devops.bill.common.vo.AlarmLogBillDetailVo>
	*/
	@GetMapping("/getAlarmBillDetail")
	public ResponseEntityWrapper<AlarmLogBillDetailVo> getAlarmBillDetail(BillGatheredDTO billGatheredDTO) {
		try{
			return new ResponseEntityWrapper<>(billGatheredService.getAlarmBillDetail(billGatheredDTO));
		}catch (Exception e){
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
	}
}
