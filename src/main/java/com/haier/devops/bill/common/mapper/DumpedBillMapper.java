package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.DumpedBill;
import com.haier.devops.bill.common.vo.DumpedDetailBillVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface DumpedBillMapper extends BaseMapper<DumpedBill> {
    /**
     * 按照S码分组查询月度账单
     * 按照S码、账期、账号汇总，并取2位小数
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<DumpedBill> queryBillGroupByScode(@Param("vendor") String vendor,
                                           @Param("billingCycle") String billingCycle,
                                           @Param("account") String... account);


    /**
     * 按照资源查询月度账单明细
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<DumpedDetailBillVo> queryDetailBill(@Param("vendor") String vendor,
                                             @Param("billingCycle") String billingCycle,
                                             @Param("account") String... account);
}
