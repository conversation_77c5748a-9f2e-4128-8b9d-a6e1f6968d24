package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (BcDefaultScode)实体类
 *
 * <AUTHOR>
 * @since 2023-12-14 14:59:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class BcDefaultScode implements Serializable {
    private static final long serialVersionUID = 441736192931286883L;
       
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;
     
       
    @Schema(description = "账户")
    private String account;
     
       
    @Schema(description = "s码")
    private String scode;
     
       
    @Schema(description = "用户ID")
    private String uid;
     
       
    private LocalDateTime createTime;
     
       
    private LocalDateTime updateTime;
     
       
    private String vendor;
     

}

