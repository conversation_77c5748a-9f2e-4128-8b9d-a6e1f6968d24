package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * (RcMiddlewareInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-12-12 14:00:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class RcMiddlewareInfo implements Serializable {
    private static final long serialVersionUID = 262732323793038368L;
       
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
     
       
    @Schema(description = "云厂商")
    private String vendor;
     
       
    @Schema(description = "云账号")
    private String accountName;
     
       
    @Schema(description = "实例ID")
    private String instanceId;
     
       
    @Schema(description = "实例名")
    private String instanceName;
     
       
    @Schema(description = "创建时间")
    private LocalDateTime creationTime;
     
       
    @Schema(description = "过期时间")
    private LocalDateTime expiredTime;
     
       
    @Schema(description = "是否已删除")
    private Integer isDeleted;
     
       
    @Schema(description = "实例类型")
    private String instanceType;
     
       
    @Schema(description = "实例版本")
    private String instanceVersion;
     
       
    @Schema(description = "实例类别")
    private String instanceCategory;
     
       
    @Schema(description = "实例状态")
    private String instanceStatus;
     
       
    @Schema(description = "产品编码")
    private String classCode;
     
       
    @Schema(description = "资源组")
    private String resourceGroup;
     
       
    @Schema(description = "资源ID")
    private String resourceId;
     
       
    @Schema(description = "实例所属区域")
    private String region;
     
       
    @Schema(description = "实例所属可用区")
    private String zone;
     
       
    @Schema(description = "实例所属统一区域")
    private String uniRegionId;
     
       
    @Schema(description = "实例私有网络地址")
    private String privateEndpoints;
     
       
    @Schema(description = "实例公网地址")
    private String publicEndpoints;
     
       
    @Schema(description = "付费类型")
    private String payType;
     
       
    @Schema(description = "描述")
    private String description;
     
       
    @Schema(description = "硬盘大小(M)")
    private BigInteger diskSize;
     
       
    @Schema(description = "磁盘类型")
    private String diskType;
     
       
    @Schema(description = "Topic数量")
    private Integer topicNum;
     
       
    @Schema(description = "Topic配额")
    private Integer topicQuota;
     
       
    @Schema(description = "Group数量")
    private Integer groupNum;
     
       
    @Schema(description = "Group配额")
    private Integer groupQuota;
     
       
    @Schema(description = "最大TPS")
    private Integer maxTps;
     
       
    @Schema(description = "最大带宽")
    private Integer maxBandwidth;
     
       
    @Schema(description = "VPC ID")
    private String vpcId;
     
       
    @Schema(description = "子网ID")
    private String subnetId;
     
       
    @Schema(description = "实例所属环境")
    private String env;
     
       
    @Schema(description = "实例所属项目")
    private String project;
     
       
    @Schema(description = "实例所属项目S码")
    private String scode;
     
       
    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;
     
       
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;
     
       
    private String domain;
     
       
    private String team;
     
       
    private String ownerId;
     
       
    private String ownerName;
     

}

