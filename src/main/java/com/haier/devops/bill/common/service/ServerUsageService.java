package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.ServerUsageEntity;
import com.haier.devops.bill.common.vo.ServiceResult;

/**
 * 服务器使用效能服务接口
 */
public interface ServerUsageService extends IService<ServerUsageEntity> {

    /**
     * 从数据源获取服务器使用效能数据并持久化到数据库
     * @return 操作结果
     */
    ServiceResult<Boolean> syncServerUsageData();

    /**
    * @Description: 获取服务器不达标数量
    * @author: 张爱苹
    * @date: 2025/6/27 13:23
    * @param scode:
    * @param resourceUsageQualified:
    * @Return: java.lang.Object
    */
    Integer getResourceUsageQualified(String scode, String resourceUsageQualified);
}
