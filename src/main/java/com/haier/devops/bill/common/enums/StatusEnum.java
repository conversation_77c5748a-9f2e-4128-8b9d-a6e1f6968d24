package com.haier.devops.bill.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 权限类型
 */
@Getter
public enum StatusEnum {

    COMMON_SUC(10000, "发起"),
    COMMON_BADREQUEST(10001, "请求错误，请更正后重试"),
    COMMON_PARAM_ERR(10002, "请求参数错误，请更正后重试"),
    COMMON_PARAM_MISS(10003, "请求参数缺失，请补充后重试"),
    COMMON_NOT_FOUND(10004, "未找到请求资源，请稍后重试"),
    COMMON_CONFILCT(10005, "请求冲突，请稍后重试"),
    COMMON_NOT_PERMISSION(10006, "无权限操作，请更正后重试"),
    COMMON_ILLEGAL_WORD(10007, "含有非法字符，请更正后重试"),
    COMMON_INTERNAL_ERR(15000, "内部错误，请稍后重试"),
    COMMON_INTERNAL_CALLING_ERR(15001, "内部调用错误，请稍后重试"),
    COMMON_INTERNAL_CALLLING_TIMEOUT(15002, "内部调用无响应，请稍后重试"),
    COMMON_INTERNAL_DB_ERR(15003, "内部调用数据库错误，请稍后重试"),
    COMMON_INTERNAL_BADGATEWAY(15004, "网关错误，请稍后重试"),
    COMMON_THIRD_INTERFACE(15005, "调用外部接口错误");

    StatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String message;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
