package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 合同验收
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@TableName("bc_contract")
@Data
public class Contract implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    private String contractName;

    private String contractVendor;

    private Date startTime;

    private Date endTime;

    private String partyA;

    private String partyB;

    private String contractStatus;

}
