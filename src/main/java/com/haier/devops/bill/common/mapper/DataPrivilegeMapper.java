package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.DataPrivilege;
import com.haier.devops.bill.common.vo.UserDataPrivilegeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface DataPrivilegeMapper extends BaseMapper<DataPrivilege> {

    /**
     * 查询用户某一维度下的数据权限
     * @param dimension
     * @param userId
     * @return
     */
    List<UserDataPrivilegeVo> selectUserDataPrivilegeByDimension(@Param("dimension") String dimension,
                                                                 @Param("userId") String userId);
}
