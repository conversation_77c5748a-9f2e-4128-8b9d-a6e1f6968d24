package com.haier.devops.bill.common.runner;

import com.haier.devops.bill.common.task.FileUploaderProbeTask;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Profile("!dev")
@Component
public class FileUploader<PERSON>robeRunner implements CommandLineRunner {

    private final FileUploaderProbeTask fileUploaderProbeTask;

    public FileUploaderProbeRunner(FileUploaderProbeTask fileUploaderProbeTask) {
        this.fileUploaderProbeTask = fileUploaderProbeTask;
    }

    @Override
    public void run(String... args) throws Exception {
        fileUploaderProbeTask.execute();
    }
}
