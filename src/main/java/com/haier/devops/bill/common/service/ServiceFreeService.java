package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.ServiceFreeCreateDTO;
import com.haier.devops.bill.common.dto.ServiceFreeDTO;
import com.haier.devops.bill.common.entity.ServiceFree;

/**
* @ClassName: ServiceFreeService
* @Description: 服务费汇率 服务类
* @author: 张爱苹
* @date: 2024/3/8 13:19
*/
public interface ServiceFreeService extends IService<ServiceFree> {

    /**
    * @Description: 分页查询所有服务费&汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:10
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.ServiceFree>
    */
    PageInfo<ServiceFree> listByPage(ServiceFreeDTO dto)throws Exception;

    /**
    * @Description:  新增服务费&汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:31
    * @param dto:
    * @Return: void
    */
    void createServiceFree(ServiceFreeCreateDTO dto) throws Exception;

    /**
    * @Description:  修改服务费&汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:32
    * @param id:
    * @param dto:
    * @Return: void
    */
    void updateServiceFree(Integer id, ServiceFreeCreateDTO dto) throws Exception;

    /**
    * @Description:   删除服务费&汇率
    * @author: 张爱苹
    * @date: 2024/3/8 13:32
    * @param id:
    * @Return: void
    */
    void deleteServiceFree(Integer id) throws Exception;
}
