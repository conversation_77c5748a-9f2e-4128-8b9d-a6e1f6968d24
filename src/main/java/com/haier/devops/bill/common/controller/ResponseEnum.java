package com.haier.devops.bill.common.controller;

import java.util.HashMap;
import java.util.Map;

/**
 * hds状态码规范
 * <AUTHOR>
 */
public enum ResponseEnum {
    SUCCESS(10000, "success"),
    REQUEST_ERR(10001, "请求错误，请更正后重试"),
    REQUEST_PARAM_ERR(10002, "请求参数错误，请更正后重试"),
    REQUEST_PARAM_LOST(10003, "请求参数缺失，请补充后重试"),
    RESOURCE_NOT_FOUND(10004, "未找到请求资源，请稍后重试"),
    REQUEST_CONFLICTS(10005, "请求冲突，请稍后重试"),
    TOO_MANY_REQUESTS(10006, "请求受限，请稍后重试"),
    REQUEST_AUTH_FAILED(20001, "请求未授权"),
    INTERNAL_ERR(15000, "内部错误，请稍后重试"),
    INTERNAL_CALL_ERR(15001, "内部调用错误，请稍后重试"),
    INTERNAL_CALL_NO_RESP(15002, "内部调用无响应，请稍后重试"),
    DATABASE_ERR(15003, "内部调用数据库错误，请稍后重试"),
    GATEWAY_ERR(15004, "网关错误，请稍后重试");

    private final int code;
    private final String message;
    private static final Map<Integer, String> codeMap = new HashMap<>();

    ResponseEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    static {
        for (ResponseEnum re : ResponseEnum.values()) {
            codeMap.put(re.getCode(), re.getMessage());
        }
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getMessageByCode(int code) {
        return codeMap.get(code);
    }
}
