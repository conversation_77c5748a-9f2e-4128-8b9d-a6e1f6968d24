package com.haier.devops.bill.common.runner;

import com.haier.devops.bill.common.task.InvalidDistributedLockSweepingTask;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

/**
 * 无效的分布式锁清理
 */
@Profile("!dev")
@Component
public class InvalidDistributedLockSweepingRunner implements CommandLineRunner {
    private InvalidDistributedLockSweepingTask sweepingTask;

    public InvalidDistributedLockSweepingRunner(InvalidDistributedLockSweepingTask sweepingTask) {
        this.sweepingTask = sweepingTask;
    }

    @Override
    public void run(String... args) throws Exception {
        sweepingTask.execute();
    }
}
