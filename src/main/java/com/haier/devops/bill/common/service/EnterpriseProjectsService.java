package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.EnterpriseProjects;

import java.util.List;

/**
* @ClassName: EnterpriseProjectsService
* @Description: 企业项目
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
public interface EnterpriseProjectsService extends IService<EnterpriseProjects> {

    /**
    * @Description: 新增企业项目
    * @author: 张爱苹
    * @date: 2025/2/13 15:00
    * @param accountName:
    * @Return: void
    */
    void insertEnterpriseProjects(String accountName) throws Exception;

    /**
    * @Description: 获取企业项目
    * @author: 张爱苹
    * @date: 2025/2/14 14:47
    * @param accountName:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.EnterpriseProjects>
    */
    List<EnterpriseProjects> getEnterpriseProjects(String accountName);

    /**
    * @Description: 根据S码查企业项目
    * @author: 张爱苹
    * @date: 2025/2/17 14:29
    * @param scode:
    * @Return: com.haier.devops.bill.common.entity.EnterpriseProjects
    */
    EnterpriseProjects getOneByScode(String scode,String accountName);

    /**
    * @Description: 启用项目
    * @author: 张爱苹
    * @date: 2025/2/17 14:53
    * @param id:
    * @param accountName:
    * @Return: void
    */
    void enableEnterpriseProject(String id, String accountName) throws Exception;

    /**
    * @Description: 停用项目
    * @author: 张爱苹
    * @date: 2025/2/17 14:54
    * @param scode:
    * @param accountName:
    * @Return: void
    */
    String createEnterpriseProject(String name, String accountName) throws Exception;
}
