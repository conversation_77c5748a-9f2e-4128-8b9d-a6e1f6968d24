package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.AcceptanceBlacklist;

/**
 * <p>
 * 验收推送黑名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface AcceptanceBlacklistService extends IService<AcceptanceBlacklist> {
    
    /**
     * 检查指定的云厂商和系统编码是否在黑名单中
     * 
     * @param vendor 云厂商
     * @param sysCode 系统编码
     * @return 存在返回true，不存在返回false
     */
    Boolean isInBlacklist(String vendor, String sysCode);
}
