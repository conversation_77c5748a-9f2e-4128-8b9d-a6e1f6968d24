package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.task.AbstractTask;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/hcms/v1/task")
public class TaskController {
    /**
     * 查看任务是否在当前节点执行
     * @param taskId
     * @return
     */
    @GetMapping("/if-task-running")
    public ResponseEntityWrapper<Boolean> isTaskRunning(@RequestParam String taskId) {
        Boolean isTaskRunning = AbstractTask.runningTaskMap.get(taskId);
        if (null == isTaskRunning || !isTaskRunning) {
            return new ResponseEntityWrapper<>(false);
        }

        return new ResponseEntityWrapper<>(true);
    }

}
