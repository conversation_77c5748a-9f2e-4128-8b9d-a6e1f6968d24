package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

/**
 * (RefinedRawBill)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-29 10:54:41
 */
public interface RefinedRawBillService extends IService<RefinedRawBill>{
    PageInfo<BillingSummaryVo> getBillingSummaryData(String vendor, String accountName, String billingCycle, int page, int perPage);
    Integer getBillingSummaryCount(String vendor, String accountName, String billingCycle);

    /**
     * 获取待聚合的账单ID
     */
    PageInfo<String> getPendingAggregatedId(String vendor, String accountName, String billingCycle, int page, int perPage);

    /**
     * 获取聚合后的账单数据
     * @param vendor
     * @param accountName
     * @param billingCycle
     * @param aggregatedIds
     * @return
     */
    List<BillingSummaryVo> getBillingSummaryDataWithInAggregatedId(SqlSession sqlSession, String vendor, String accountName, String billingCycle, List<String> aggregatedIds);

}
