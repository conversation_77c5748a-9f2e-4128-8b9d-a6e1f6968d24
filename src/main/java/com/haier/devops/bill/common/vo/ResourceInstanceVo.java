package com.haier.devops.bill.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResourceInstanceVo implements Serializable {

    private static final long serialVersionUID = -8150612337434700914L;
    private Long id;
    private String aggregatedId;
    /**财务单元*/
    private java.lang.String costUnit;
    /**财务单元id*/
    private java.lang.String costUnitId;
    /**资源的商品code*/
    @Schema(description = "资源的商品code")
    private java.lang.String commodityCode;
    /**资源属主的用户名*/
    @Schema(description = "资源属主的用户名")
    private java.lang.String resourceUserName;

    /**资源的属主用户ID*/
    @Schema(description = "资源的属主用户ID")
    private long resourceUserId;

    /**资源分拆code*/
    @Schema(description = "资源分拆code")
    private java.lang.String apportionCode;

    /**资源的实例ID*/
    @Schema(description = "资源的实例ID")
    private java.lang.String resourceId;

    private String accessKey;

    private String accessKeySecret;

    private String encrypted;

    private String resourceType;

    private Integer reconciliationId;
}
