package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.CloudVendor;
import com.haier.devops.bill.common.mapper.CloudVendorMapper;
import com.haier.devops.bill.common.service.CloudVendorService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
* @ClassName: CloudVendorServiceImpl
* @Description:  云厂商服务实现类
* @author: 张爱苹
* @date: 2024/3/4 10:03
*/
@Service
@Slf4j
public class CloudVendorServiceImpl extends ServiceImpl<CloudVendorMapper, CloudVendor> implements CloudVendorService {
    private Logger logger = LoggerFactory.getLogger(CloudVendorServiceImpl.class);

    @Override
    public CloudVendor getCloudVendor(String vendorCode) {
        return getOne(new QueryWrapper<CloudVendor>().lambda().eq(CloudVendor::getVendorCode, vendorCode)
                .eq(CloudVendor::getIsDeleted, "0"));
    }
}
