package com.haier.devops.bill.common.service;

import com.haier.devops.bill.common.dto.BillGatheredDTO;
import com.haier.devops.bill.common.vo.AlarmLogBillDetailVo;

/**
* @ClassName: BillGatheredService
* @Description:  每日账单汇总服务
* @author: 张爱苹
* @date: 2024/1/19 18:33
*/
public interface BillGatheredService{

    /**
    * @Description: 查询告警账单明细
    * @author: 张爱苹
    * @date: 2024/1/22 09:46
    * @param billGatheredDTO:
    * @Return: com.haier.devops.bill.common.vo.AlarmLogBillDetailVo
    */
    AlarmLogBillDetailVo getAlarmBillDetail(BillGatheredDTO billGatheredDTO) throws Exception;
}
