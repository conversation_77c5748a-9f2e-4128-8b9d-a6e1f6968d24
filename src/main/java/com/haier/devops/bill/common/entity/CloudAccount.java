package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 云账号表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@TableName("cloud_account")
@Data
public class CloudAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是启用
     */
    private String isEnabled;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账号名
     */
    private String accountName;

    /**
     * 账号身份识别码
     */
    private String accountIdentify;

    /**
     * 账号身份提供商
     */
    private String accountIdentifyProvider;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 账号AccessKey
     */
    private String accessKey;

    /**
     * 账号AccessKeySecret
     */
    private String accessKeySecret;

    /**
     * 认证域, CHINA|FOREIGN
     */
    private String authRegion;

    /**
     * 账号用途
     */
    private String purposeType;

    /**
     * 币种
     */
    private String currency;

    /**
     * 账号描述
     */
    private String description;

    /**
     * 是否已加密
     */
    private String encrypted;

}
