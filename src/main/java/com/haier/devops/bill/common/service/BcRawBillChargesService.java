package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.BcRawBillCharges;
import com.haier.devops.bill.common.vo.BillChargesProductsVo;

import java.util.List;

/**
 * (BcRawBillCharges)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-02 11:08:19
 */
public interface BcRawBillChargesService extends IService<BcRawBillCharges> {
    public void insertBillCharges();

    List<BillChargesProductsVo> getProductChargeInfo(String productCode, String vendor);
}
