package com.haier.devops.bill.common.task;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TaskEntity {
    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 所运行的节点host
     */
    @TableField("host")
    private String host;

    /**
     * 生成任务id
     * @return
     */
    public static String generateTaskId() {
        return UUID.randomUUID().toString();
    }
}
