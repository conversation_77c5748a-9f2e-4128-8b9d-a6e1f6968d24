package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 合同预算
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@TableName("bc_contract_budget")
@Data
public class ContractBudget implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    private Integer contractId;

    private Integer stageId;

    private String budget;

    private String signDate;

    private Integer currentSignIndex;

    private String contractProjects;

    private String filePath;

    /**
     * psi | other
     */
    private String namespace;

    private String owners;

    private String ownerLeaders;

    private String smallMicroOwners;

    private String digitalBusinessDeptOwners;

    private String status;

    private Double cost;

    private String comment;

}
