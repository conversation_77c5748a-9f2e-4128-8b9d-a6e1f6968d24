package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (RefinedRawBill)实体类
 *
 * <AUTHOR>
 * @since 2024-02-29 10:54:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
@TableName("bc_refined_raw_bill")
public class RefinedRawBill implements Serializable {
    private static final long serialVersionUID = 997624041450745924L;

    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "云厂商")
    private String vendor;


    @Schema(description = "记录的账号信息-账户名称")
    private String accountName;


    @Schema(description = "记录的账号信息-账户ID")
    private String accountId;


    @Schema(description = "账单粒度")
    private String granularity;


    @Schema(description = "账期")
    private LocalDateTime billingCycle;


    @Schema(description = "产品编码")
    private String productCode;


    @Schema(description = "产品名称")
    private String productName;


    @Schema(description = "产品明细")
    private String productDetail;


    @Schema(description = "资源组")
    private String resourceGroup;


    @Schema(description = "资源ID")
    private String instanceId;


    @Schema(description = "分拆项id（对实例id的补充）")
    private String supplementId;


    @Schema(description = "结算单元")
    private String costUnit;


    @Schema(description = "聚合id")
    private String aggregatedId;


    @Schema(description = "服务期")
    private String servicePeriod;


    @Schema(description = "服务期单位")
    private String servicePeriodUnit;


    @Schema(description = "地域")
    private String region;


    @Schema(description = "可用区")
    private String zone;


    @Schema(description = "计费项")
    private String billingItem;


    @Schema(description = "定价")
    private String listPrice;


    @Schema(description = "定价单位")
    private String listPriceUnit;


    @Schema(description = "使用量单位")
    private String usageUnit;


    @Schema(description = "费用")
    private String cost;

    @Schema(description = "应付费用")
    private String payableAmount;

    @Schema(description = "代金券费用")
    private String voucherAmount;

    @Schema(description = "现金费用")
    private String cashAmount;

    @Schema(description = "币种")
    private String currency;


    @Schema(description = "消费类型")
    private String subscriptionType;


    @Schema(description = "原始账单内容")
    private String content;

    @Schema(description = "未经验证的S码")
    private String scodeUnverified;

    /**
     * 匹配S码的规则id，默认为0，即没走规则
     */
    @Schema(description = "匹配S码的规则id，默认为0，即没走规则")
    private Integer scodeRuleMatched;


    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;


    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "子产品名称")
    private String subProductName;

    /**
     * 子产品id
     */
    private String subInstanceId;

    /**
     * 子产品编码
     */
    private String subProductCode;

    /**
     * 子产品收费项
     */
    private String subBillingItem;

    /**
     * 找到父资源
     */
    private Integer parentFound;

}

