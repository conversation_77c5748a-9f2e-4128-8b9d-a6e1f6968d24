package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.entity.ExchangeRate;
import com.haier.devops.bill.common.service.ExchangeRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: ExchangeRateController
 * @Description: 汇率查询控制器
 * @author: System
 * @date: 2025/06/30
 */
@Tag(name = "汇率查询")
@RestController
@RequestMapping("/api/v1/hcms/bill/exchangeRate")
@Slf4j
public class ExchangeRateController {

    @Autowired
    private ExchangeRateService exchangeRateService;

    /**
     * 查询指定货币对的汇率
     */
    @GetMapping("/query")
    @Operation(summary = "查询指定货币对的汇率")
    public ResponseEntityWrapper<ExchangeRate> queryExchangeRate(
            @Parameter(description = "基础货币代码", example = "CNY") 
            @RequestParam(defaultValue = "CNY") String baseCurrency,
            @Parameter(description = "目标货币代码", example = "USD") 
            @RequestParam String targetCurrency,
            @Parameter(description = "汇率日期", example = "2025-06-30") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date rateDate) {
        
        try {
            ExchangeRate exchangeRate = exchangeRateService.getExchangeRate(baseCurrency, targetCurrency, rateDate);
            return new ResponseEntityWrapper<>(exchangeRate);
        } catch (Exception e) {
            log.error("查询汇率失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 查询指定日期的所有汇率
     */
    @GetMapping("/listByDate")
    @Operation(summary = "查询指定日期的所有汇率")
    public ResponseEntityWrapper<List<ExchangeRate>> listExchangeRatesByDate(
            @Parameter(description = "汇率日期", example = "2025-06-30") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date rateDate) {
        
        try {
            List<ExchangeRate> exchangeRates = exchangeRateService.getExchangeRatesByDate(rateDate);
            return new ResponseEntityWrapper<>(exchangeRates);
        } catch (Exception e) {
            log.error("查询汇率列表失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 手动触发汇率同步（仅供测试使用）
     */
    @PostMapping("/sync")
    @Operation(summary = "手动触发汇率同步")
    public ResponseEntityWrapper<String> manualSync() {
        try {
            exchangeRateService.performFullSync();
            return new ResponseEntityWrapper<>("汇率同步成功");
        } catch (Exception e) {
            log.error("手动同步汇率失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(), e.getMessage(), null);
        }
    }
}
