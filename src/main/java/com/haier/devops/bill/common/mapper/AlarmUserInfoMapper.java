package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.AlarmUserInfo;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AlarmUserInfoMapper
* @Description:  告警用户信息表 Mapper
* @author: 张爱苹
* @date: 2024/1/17 10:32
*/
@Repository
public interface AlarmUserInfoMapper extends BaseMapper<AlarmUserInfo> {


    List<AlarmNoticeObjVo> getUserListByUserIdArray(@Param("array") String[] noticeObjIdArray);

    List<AlarmNoticeObjVo> getUserListByGroupId(@Param("array")String[] noticeObjIdArray);
}
