package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: SysDict
* @Description:  字典表
* @author: 张爱苹
* @date: 2024/2/4 10:07
*/
@Data
public class SysDictDTO extends BaseDTO implements Serializable {


    private static final long serialVersionUID = -5719144561166003304L;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典编码
     */
    private String dictCode;


}
