package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.SysDictItemDTO;
import com.haier.devops.bill.common.entity.SysDictItem;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import com.haier.devops.bill.common.vo.SysDictItemVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: SysDictItemMapper
* @Description: 字典项Mapper
* @author: 张爱苹
* @date: 2024/1/12 10:59
*/
@Repository
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {

    @Select("select item_value,item_text,sdi.description from sys_dict_item sdi right join sys_dict sd on sdi.dict_id = sd.id and sdi.status = 1 \n" +
            "where sd.del_flag = 0 and sd.dict_code = #{dictCode}")
    List<SysDictItemVo> getDictItemList(String dictCode);

    List<AlarmNoticeObjVo> getDictItemByItemValueArray(@Param("array") String[] noticeObjIdArray, @Param("dictCode") String dictCode);

    List<SysDictItem> listByPage(@Param("dto") SysDictItemDTO dto);
}
