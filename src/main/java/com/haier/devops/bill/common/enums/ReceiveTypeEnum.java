package com.haier.devops.bill.common.enums;

/**
 * <AUTHOR>
 */

public enum ReceiveTypeEnum {
    CHATID("chat_id","群id"),
    USERID("user_id","用户id"),
    MOBILE("mobile","短信"),
    EMAIL("email","邮件"),
    HWORK("hwork","hwork");


    ReceiveTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
