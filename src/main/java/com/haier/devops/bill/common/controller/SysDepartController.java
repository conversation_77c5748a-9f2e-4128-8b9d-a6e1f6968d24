package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.service.SysDepartService;
import com.haier.devops.bill.common.vo.DepartIdVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * (SysDepart)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-19 15:36:14
 */
@RestController
@Slf4j
@RequestMapping("/api/v1/hcms/bill")
public class SysDepartController {
    /**
     * 服务对象
     */
    @Resource
    private SysDepartService sysDepartService;

    @GetMapping("/queryDepartIdTreeList")
    public ResponseEntityWrapper<List<DepartIdVo>> queryDepartIdTreeList() {
        List<DepartIdVo> departIdVos = sysDepartService.queryDepartIdTreeList();
        return new ResponseEntityWrapper<>(departIdVos);
    }
}

