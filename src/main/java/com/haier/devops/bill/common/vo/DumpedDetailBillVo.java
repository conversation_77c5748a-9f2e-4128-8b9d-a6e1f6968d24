package com.haier.devops.bill.common.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 云资源账单明细value object
 * <AUTHOR>
 */
@Data
public class DumpedDetailBillVo {
    /**
     * 账单类型 1-云资源，2-云桌面
     */
    private String billType;

    /**
     * 账期
     */
    private String payDate;

    /**
     * S码/平台编码
     */
    private String sysCode;

    /**
     * 子产品名称/（产业/平台）
     */
    private String sysName;

    /**
     * 费用金额
     */
    private BigDecimal costAmount;

    /**
     * 云厂商
     */
    private String factory;

    /**
     * 所在账号
     */
    private String account;

    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 消费类型
     */
    private String costType;

    /**
     * 服务项
     */
    private String serviceItem;

    /**
     * 服务内容
     */
    private String serviceContent;

    /**
     * 服务数量
     */
    private int serviceCount;

    /**
     * 数据条数
     */
    private int detailNumber;

    /**
     * 明细金额合计
     */
    private BigDecimal detailTotalAmount;
}
