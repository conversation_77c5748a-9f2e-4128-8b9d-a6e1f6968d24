package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.mapper.MilestoneMapper;
import com.haier.devops.bill.common.service.MilestoneService;
import com.haier.devops.bill.common.vo.MilestoneVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 云服务项目（ALM）的里程碑，包括所属项目和合同等信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@Service
public class MilestoneServiceImpl extends ServiceImpl<MilestoneMapper, Milestone> implements MilestoneService {
    private final MilestoneMapper milestoneMapper;

    public MilestoneServiceImpl(MilestoneMapper milestoneMapper) {
        this.milestoneMapper = milestoneMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchSaveOrUpdateByParams(List<Milestone> milestones) {
        return SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, milestones, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {//这里主要是查询唯一约束对应的记录是否存在
            LambdaQueryWrapper<Milestone> queryWrapper = Wrappers.<Milestone>lambdaQuery()
                    .eq(Milestone::getMilestoneId, entity.getMilestoneId());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<Milestone> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(Milestone::getMilestoneId, entity.getMilestoneId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public List<Milestone> queryValidMilestones() {
        QueryWrapper<Milestone> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Milestone::getFlag, 0);
        return milestoneMapper.selectList(wrapper);
    }

    @Override
    public List<MilestoneVo> queryByVendorAndBillingCycle(String vendor, String billingCycle) {
        return milestoneMapper.queryByVendorAndBillingCycle(vendor, billingCycle + "-01");
    }
}
