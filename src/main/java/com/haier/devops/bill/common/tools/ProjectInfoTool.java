package com.haier.devops.bill.common.tools;

import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.util.RedisUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 项目信息工具
 * @Author: A0018437
 * @Date：2024-02-29
 */
@Component
public class ProjectInfoTool {
    @Resource
    HdsOpenApi hdsOpenApi;
    @Resource
    RedisUtils redisUtils;
    /**
     * 根据项目代码获取项目信息
     *
     * @param scode 项目代码
     * @return 项目信息对象
     */
    public HdsOpenApi.Project getProjectInfo(String scode) {
        // 从Redis中获取项目信息列表
        List<HdsOpenApi.Project> projectInfos = JSONObject.parseArray(String.valueOf(redisUtils.get("projectInfos")), HdsOpenApi.Project.class);

        // 如果项目信息列表为空
        if (CollectionUtils.isEmpty(projectInfos)) {
            // 获取项目信息列表
            projectInfos = hdsOpenApi.queryProjectsInfo().getData();

            // 如果项目信息列表不为空
            if (CollectionUtils.isNotEmpty(projectInfos)) {
                // 将项目信息列表存储到Redis中
                redisUtils.set("projectInfos", JSONObject.toJSONString(projectInfos), CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
            }
        }
        // 将项目信息列表转换为以项目代码为键、项目信息为值的映射
        Map<String, HdsOpenApi.Project> projectMap = projectInfos.stream()
                .collect(Collectors.toMap(HdsOpenApi.Project::getAlmSCode, Function.identity(), (existing, replacement) -> replacement));

        // 返回项目信息对象
        return projectMap.get(scode);
    }
}
