package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
* @ClassName: AggregateReconciliationDTO
* @Description: TODO
* @author: 张爱苹
* @date: 2024/3/11 10:01
*/
@Data
public class ReconciliationBatchDTO implements Serializable {
    private static final long serialVersionUID = 3095375545480746234L;


    /**
     * @Description: 调账范围
     */
    @NotEmpty
    private String range;

    /**
     * @Description: S码
     */
    private String scode;

    /**
    * @Description: 聚合id
    */
    private List<String> aggregateIdList;

    /**
     * @Description: 账期开始时间
     */
    private String startDate;

    /**
     * @Description: 账期结束时间
     */
    private String endDate;

    private ResourceInstanceDTO resourceInstanceDTO;

    private CmdbProductOverviewDTO cmdbProductOverviewDTO;

    /**
     * @Description: 是否更新规则表
     */
    private boolean  isUpdate;

    private String reconciliationType;

    private String vendor;


}
