package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 账单明细汇总表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@Builder
@TableName("bc_migrated_bill")
public class MigratedBill implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * S码
     */
    private String scode;

    /**
     * 账期
     */
    private LocalDateTime billingCycle;

    /**
     * 金额之和
     */
    private BigDecimal summer;

    /**
     * 应付汇总
     */
    private BigDecimal payableSum;

    /**
     * 代金券总和
     */
    private BigDecimal voucherSum;

    /**
     * 优惠券总和
     */
    private BigDecimal couponSum;

    /**
     * 现金金额汇总
     */
    private BigDecimal cashSum;

    /**
     * 聚合id
     */
    private String aggregatedId;

    /**
     * 数据的粒度
     */
    private String granularity;

    /**
     * 替换任务id
     */
    private String subTaskId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
