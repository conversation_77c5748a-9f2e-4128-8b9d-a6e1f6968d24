package com.haier.devops.bill.common.redis;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.CloudAccount;
import com.haier.devops.bill.common.entity.DataSupplementConfig;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class CacheConfig {
    private RedisTemplate<String, HworkAuthorityApi.User> hcmsUserTemplate;
    private RedisTemplate<String, List<CloudAccount>> cloudAccountRedisTemplate;
    private RedisTemplate<String, List<DataSupplementConfig>> dataSupplementTemplate;
    private RedisTemplate<String, List<String>> authorityScodesTemplate;
    private RedisTemplate<String, List<HdsSubProducts>> hdsSubProductsTemplate;


    @Bean("hcmsUserCache")
    public Cache<String, HworkAuthorityApi.User> hcmsUserCache() {
        return new Cache<>(hcmsUserTemplate);
    }

    @Bean("cloudAccountCache")
    public Cache<String, List<CloudAccount>> cloudAccountCache() {
        return new Cache<>(cloudAccountRedisTemplate);
    }

    @Bean("dataSupplementCache")
    public Cache<String, List<DataSupplementConfig>> dataSupplementCache() {
        return new Cache<>(dataSupplementTemplate);
    }

    @Bean("authorityScodesCache")
    public Cache<String, List<String>> authorityScodesCache() {
        return new Cache<>(authorityScodesTemplate);
    }

    @Bean("hdsSubProductsCache")
    public Cache<String, List<HdsSubProducts>> hdsSubProductsCache() {
        return new Cache<>(hdsSubProductsTemplate);
    }

    @Autowired
    public void setRunningTaskTemplate(
                                       @Qualifier("hcmsUserTemplate")
                                       RedisTemplate<String, HworkAuthorityApi.User> hcmsUserTemplate,
                                       @Qualifier("cloudAccountTemplate")
                                       RedisTemplate<String, List<CloudAccount>> cloudAccountTemplate,
                                       @Qualifier("dataSupplementTemplate")
                                       RedisTemplate<String, List<DataSupplementConfig>> dataSupplementTemplate,
                                       @Qualifier("authorityScodesTemplate")
                                       RedisTemplate<String, List<String>> authorityScodesTemplate,
                                       @Qualifier("hdsSubProductsTemplate")
                                       RedisTemplate<String, List<HdsSubProducts>> hdsSubProductsTemplate) {
        this.hcmsUserTemplate = hcmsUserTemplate;
        this.cloudAccountRedisTemplate = cloudAccountTemplate;
        this.dataSupplementTemplate = dataSupplementTemplate;
        this.authorityScodesTemplate = authorityScodesTemplate;
        this.hdsSubProductsTemplate = hdsSubProductsTemplate;
    }
}
