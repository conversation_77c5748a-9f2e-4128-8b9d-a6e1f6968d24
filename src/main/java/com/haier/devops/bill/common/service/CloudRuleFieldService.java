package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.dto.RuleFieldValueDTO;
import com.haier.devops.bill.common.entity.CloudRuleField;

import java.util.List;
/**
* @ClassName: CloudRuleFieldService
* @Description: 规则字段详情
* @author: 张爱苹
* @date: 2024/3/12 10:59
*/
public interface CloudRuleFieldService extends IService<CloudRuleField> {

    /**
    * @Description: 根据规则类型查询规则字段详情
    * @author: 张爱苹
    * @date: 2024/3/12 10:59
    * @param ruleType:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.CloudRuleField>
    */
    List<CloudRuleField> getRuleFieldList(String ruleType);

    /**
    * @Description:  规则值列表
    * @author: 张爱苹
    * @date: 2024/3/12 14:32
    * @param ruleFieldValueDTO:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getRuleFieldValueList(RuleFieldValueDTO ruleFieldValueDTO) throws Exception;
}
