package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.aws.commitment.vo.AggregatedBillCommitmentVo;
import com.haier.devops.bill.common.dto.*;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.vo.*;
import com.haier.devops.bill.substitution.vo.PassBillVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* @ClassName: AggregatedBillService
* @Description:  账单明细汇总表 服务类
* @author: 张爱苹
* @date: 2024/1/31 10:22
*/
public interface AggregatedBillService extends IService<AggregatedBill> {

    /**
    * @Description: 更新S码
    * @author: 张爱苹
    * @date: 2024/2/22 17:04
    * @param id:
    * @param scode:
    * @Return: void
    */
    void updateScode(Integer id, String scode) throws Exception;

    /**
     * 分页获取账单汇总列表
     * @param dto
     * @return
     */
    PageInfo<AggregatedBillVo> listByPage(BillGatheredDTO dto) throws Exception;

    /**
    * @Description: 批量更新S码
    * @author: 张爱苹
    * @date: 2024/3/1 17:23
    * @param dto:
    * @Return: void
    */
    void updateScodeBatch(AggregatedBillEditDTO dto) throws Exception;

    /**
    * @Description: 统计S码为空的账单汇总信息条数
    * @author: 张爱苹
    * @date: 2024/3/4 09:33

    * @Return: java.lang.Integer
     * @return
    */
    List<Map> countByS();

    /**
    * @Description: 获取实例id列表
    * @author: 张爱苹
    * @date: 2024/3/4 16:15
    * @param vendor:
    * @param instanceId:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getInstanceIdList(String vendor, String instanceId);

    /**
    * @Description: 根据汇总Id查询汇总账单信息
    * @author: 张爱苹
    * @date: 2024/3/8 17:14
    * @param aggregatedId:
    * @Return: com.haier.devops.bill.common.vo.AggregatedBillInfoVo
    */
    List<AggregatedBillInfoVo> getInfoByAggregatedId(String aggregatedId);

    /**
    * @Description: 按实例调账
    * @author: 张爱苹
    * @date: 2024/3/11 09:52
    * @param dto:
    * @Return: void
    */
    void reconciliation(AggregateReconciliationDTO dto) throws Exception;

    /**
    * @Description: 批量调账
    * @author: 张爱苹
    * @date: 2024/3/11 16:21
    * @param dto:
    * @Return: void
    */
    void reconciliationBatch(ReconciliationBatchDTO dto) throws Exception;

    /**
    * @Description: 按全部资源实例调账
    * @author: 张爱苹
    * @date: 2024/3/13 09:27
    * @param dto:
    * @Return: void
    */
    void reconciliationAll(ReconciliationBatchDTO dto) throws Exception;

    /**
    * @Description: 按部分资源实例调账
    * @author: 张爱苹
    * @date: 2024/3/13 09:41
    * @param dto:
    * @Return: void
    */
    void reconciliationPart(ReconciliationBatchDTO dto) throws Exception;

    /**
    * @Description:  按单个资源实例调账
    * @author: 张爱苹
    * @date: 2024/3/13 10:34
    * @param dto:
    * @Return: void
    */
    void reconciliationSingle(AggregateReconciliationDTO dto) throws Exception;

    /**
    * @Description: 全局调账
    * @author: 张爱苹
    * @date: 2024/3/19 13:56
    * @param dto:
    * @Return: void
    */
    void reconciliationGlobal(ReconciliationBatchDTO dto) throws Exception;

    /**
     * 获取账单汇总列表
     * @param dto
     * @return
     */
    Page<TechnicalArchitectureBillVo> getBillOfBillingCycle(AggregatedBillDTO dto);

    /**
     * 汇总时保存或更新
     * @param list
     */
    void saveOrUpdateWhenAggregatingInDay(List<AggregatedBill> list);


    /**
     * 查询当前年度的账单汇总
     *
     * @param scodes
     * @return
     */
    BigDecimal getCurrentYearTotalAmount(List<String> scodes, String... currency);

    /**
     * 查询上一年度的账单汇总
     * @param scodes
     * @return
     */
    BigDecimal getLastYearTotalAmount(List<String> scodes, String... currency);

    /**
     * 查询过去第n个月的总金额，n=1时表示上个月
     * @param scodes
     * @param nth
     * @return
     */
    BigDecimal getLastNthMonthTotalAmount(List<String> scodes, int nth, String... currency);


    /**
     * 查询过去第n个月的总金额，n=1时表示上个月
     * @param scodes
     * @param nth
     * @return
     */
    BigDecimal getLastNthMonthTotalAmount(List<String> scodes,String vendor, int nth,String currency);

    BigDecimal getLastNthMonthTotalAmount(List<String> scodes,String vendor, int nth,BigDecimal exchangeRate);

    /**
     * 获取当前月份截止到昨天的总金额
     * @param scodes
     * @return
     */
    BigDecimal getCurrentMonthTotalAmountUntilYesterday(List<String> scodes, String... currency);

    /**
     * 获取昨天的总金额
     * @param scodes
     * @return
     */
    BigDecimal getTotalAmountOfYesterday(List<String> scodes, String... currency);

    /**
     * 获取指定月的按天汇总金额
     * @param scodes
     * @param vendor
     * @param month
     * @return
     */
    List<DayBillAnalysisVo> getMonthAggregatedBill(List<String> scodes,
                                                   String vendor,
                                                   String month,
                                                   String... currency);


    /**
     * 获取阿里容器云待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getAliyunPaasPendingSubstitutionBills(String billingCycle, int page, int perPage);


    /**
     * 获取大数据HDOP PaaS待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getBigDataHdopPaasPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取大数据Starrocks PaaS待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getBigDataStarrocksPaasPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取阿里云专有PaaS待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getAliyunDedicatedPaasPendingSubstitutionBills(String billingCycle, int page, int perPage);


    /**
     * 获取华为容器云待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getHuaweiPaasPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取华为ucs产品待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getHuaweiUcsPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取云监控待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getCloudMonitorPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取aws节省计划、预留实例待替换账单
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBill> getAwsCommitmentPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 获取aws节省计划、预留实例待替换账单（带资源信息）
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<AggregatedBillCommitmentVo> getAwsPendingSubstitutionBills(String billingCycle, int page, int perPage);

    /**
     * 清除阿里云需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunPassBills(String billingCycle);

    /**
     * 清除云监控需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanCloudMonitorBills(String billingCycle);


    /**
     * 清除大数据HDOP需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataHdopPassBills(String billingCycle);

    /**
     * 清除大数据Starrocks需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanBigDataStarrocksPassBills(String billingCycle);

    /**
     * 清除阿里专属云需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanAliyunDedicatedPassBills(String billingCycle);


    /**
     * 清除华为云需要被替换的账单
     * @param billingCycle
     * @return
     */
    int cleanHuaweiPassBills(String billingCycle);


    /**
     *
     * @param billingCycle
     * @return
     */
    int cleanHuaweiUcsBills(String billingCycle);

    /**
     * 清理上一次任务产生的数据
     * @param subTaskId
     * @return
     */
    int clearDirtyData(String subTaskId);

    /**
     * 获取近半年容器云账单
     * @return
     */
    List<PassBillVo> getRecentHalfYearPassBill(String vendor,
                                               List<String> scodes,
                                               String startDate,
                                               String endDate,
                                               String... currency);

    List<PassBillVo> getRecentHalfYearPassBill(String vendor,
                                               List<String> scodes,
                                               String startDate,
                                               String endDate,
                                               BigDecimal exchangeRate);


    /**
     * 孤儿资源更新成父级信息
     * @param parent
     * @param orphan
     */
    void replaceOrphanWithParent(CmdbProductOverview orphan, CmdbProductOverview parent);

    /**
    * @Description: 获取有权限的S码
    * @author: 张爱苹
    * @date: 2025/3/12 10:43

    * @Return: java.lang.String
    */
    String getDomainSubProducts();

    String getDomainSubProducts(List<String> domainList,boolean isAdmin,String userCode);

    /**
    * @Description: 查资源申请/退订
    * @author: 张爱苹
    * @date: 2025/3/18 15:59
    * @param vendor:
    * @param scode:
    * @Return: java.util.List<java.util.Map>
    */
    List<Map> getAgentResourceApplications(String vendor, String scode,String startDate,String endDate);


    /**
    * @Description: 按云产品查实例消费
    * @author: 张爱苹
    * @date: 2025/3/21 10:44
    * @param request:
    * @Return: java.util.List<com.haier.devops.bill.common.vo.BillDetailVo>
    */
    List<BillDetailVo> listDetail(DetailBillParam request,String currency) throws Exception;

    List<BillDetailVo> listDetail(DetailBillParam request,BigDecimal exchangeRate) throws Exception;

}
