package com.haier.devops.bill.common.controller;


import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.service.HdsSubProductsService;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* @ClassName: HdsSubProductsController
* @Description: hds子产品
* @author: 张爱苹
* @date: 2024/1/15 15:40
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/hds-sub-products")
public class HdsSubProductsController {

	@Autowired
	private HdsSubProductsService hdsSubProductsService;

	/**
	* @ClassName: HdsSubProductsController
	* @Description:  获取hds子产品列表
	* @author: 张爱苹
	* @date: 2024/1/15 15:42
	*/
	@GetMapping("/list")
	public ResponseEntityWrapper<List<HdsSubProducts>> getHdsSubProductList(@Param("searchContent") String searchContent) {
		return new ResponseEntityWrapper<>(hdsSubProductsService.getHdsSubProductList(searchContent));
	}

}
