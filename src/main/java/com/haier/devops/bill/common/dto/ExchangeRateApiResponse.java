package com.haier.devops.bill.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @ClassName: ExchangeRateApiResponse
 * @Description: 汇率API响应DTO
 * @author: System
 * @date: 2025/06/30
 */
@Data
public class ExchangeRateApiResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求结果状态
     */
    private String result;

    /**
     * 文档链接
     */
    private String documentation;

    /**
     * 使用条款
     */
    @JsonProperty("terms_of_use")
    private String termsOfUse;

    /**
     * 最后更新时间（Unix时间戳）
     */
    @JsonProperty("time_last_update_unix")
    private Long timeLastUpdateUnix;

    /**
     * 最后更新时间（UTC格式）
     */
    @JsonProperty("time_last_update_utc")
    private String timeLastUpdateUtc;

    /**
     * 下次更新时间（Unix时间戳）
     */
    @JsonProperty("time_next_update_unix")
    private Long timeNextUpdateUnix;

    /**
     * 下次更新时间（UTC格式）
     */
    @JsonProperty("time_next_update_utc")
    private String timeNextUpdateUtc;

    /**
     * 基础货币代码
     */
    @JsonProperty("base_code")
    private String baseCode;

    /**
     * 汇率转换表
     * key: 目标货币代码
     * value: 汇率值
     */
    @JsonProperty("conversion_rates")
    private Map<String, Double> conversionRates;
}
