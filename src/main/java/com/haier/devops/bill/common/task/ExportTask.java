package com.haier.devops.bill.common.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.haier.devops.bill.common.entity.ExportLog;
import com.haier.devops.bill.common.redis.Queue;
import com.haier.devops.bill.common.service.ExportLogService;
import com.haier.devops.bill.export.Exporter;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import com.haier.devops.bill.export.vo.ExportVo;
import com.haier.devops.bill.export.vo.OmnibearingExportApplicationVo;
import com.haier.devops.bill.export.vo.UploadResult;
import com.haier.devops.bill.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExportTask extends AbstractTask {

    private Queue<OmnibearingExportApplicationVo> omnibearingExportQueue;
    private Queue<DetailExportApplicationVo> detailExportQueue;
    private Queue<ExportVo> uploadingProbeQueue;

    private Exporter<OmnibearingExportApplicationVo> omnibearingExporter;
    private Exporter<DetailExportApplicationVo> detailExporter;

    private ExportLogService exportLogService;

    public ExportTask(
            @Qualifier("omnibearingExportQueue")
            Queue<OmnibearingExportApplicationVo> omnibearingExportQueue,

            @Qualifier("detailExportQueue")
            Queue<DetailExportApplicationVo> detailExportQueue,

            @Qualifier("uploadingProbeQueue")
            Queue<ExportVo> uploadingProbeQueue,



            ExportLogService exportLogService) {
        this.omnibearingExportQueue = omnibearingExportQueue;
        this.detailExportQueue = detailExportQueue;
        this.uploadingProbeQueue = uploadingProbeQueue;
        this.exportLogService = exportLogService;
    }

    @Override
    @SuppressWarnings("InfiniteLoopStatement")
    public void doExecute() {
        while (true) {
            doExport(omnibearingExportQueue, omnibearingExporter);
            doExport(detailExportQueue, detailExporter);

            sleep(1);
        }
    }

    private <T extends ExportVo> void doExport(Queue<T> queue, Exporter<T> exporter) {
        if (queue.size() > 0) {
            T vo = queue.pop();
            String podIp = IpUtil.getPodIp();
            vo.setPodIp(podIp);

            runningTaskMap.put(vo.getSerialNo(), true);

            /* 将下载任务添加到下载探测队列 */
            uploadingProbeQueue.add(vo);


            /* 修改执行导出上传oss任务的pod ip */
            if (StringUtils.isNotBlank(podIp)) {
                LambdaUpdateWrapper<ExportLog> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ExportLog::getSerialNo, vo.getSerialNo());
                ExportLog entity = ExportLog.builder()
                        .podIp(podIp)
                        .build();
                exportLogService.update(entity, updateWrapper);
            }


            log.info("导出任务：{}", vo);
            UploadResult result = exporter.doExport(vo);
            log.info("导出结果：{}", JSON.toJSONString(result));

            runningTaskMap.remove(vo.getSerialNo());
        }
    }
}
