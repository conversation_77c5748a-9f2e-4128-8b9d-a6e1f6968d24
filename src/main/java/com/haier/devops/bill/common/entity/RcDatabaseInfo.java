package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (RcDatabaseInfo)实体类
 *
 * <AUTHOR>
 * @since 2023-12-12 13:52:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class RcDatabaseInfo implements Serializable {
    private static final long serialVersionUID = 487669476035245415L;
       
    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
     
       
    @Schema(description = "云厂商")
    private String vendor;
     
       
    @Schema(description = "云账号")
    private String accountName;
     
       
    @Schema(description = "实例ID")
    private String instanceId;
     
       
    @Schema(description = "实例名称")
    private String instanceName;
     
       
    @Schema(description = "实例类型: 主实例，只读实例等")
    private String instanceType;
     
       
    @Schema(description = "实例角色：主库，备库等")
    private String instanceRole;
     
       
    @Schema(description = "实例类型：基础版，高可用，集群版等")
    private String category;
     
       
    @Schema(description = "主实例ID")
    private String primaryInstanceId;
     
       
    @Schema(description = "宿主机ID")
    private String hostInsId;
     
       
    @Schema(description = "实例状态")
    private String status;
     
       
    @Schema(description = "规格码")
    private String classCode;
     
       
    @Schema(description = "付费类型")
    private String chargeType;
     
       
    @Schema(description = "主机创建时间")
    private LocalDateTime creationTime;
     
       
    @Schema(description = "主机过期时间")
    private LocalDateTime expiredTime;
     
       
    @Schema(description = "连接串")
    private String host;
     
       
    @Schema(description = "端口")
    private Integer port;
     
       
    @Schema(description = "数据库类型")
    private String engineType;
     
       
    @Schema(description = "数据库版本")
    private String engineVersion;
     
       
    @Schema(description = "S码")
    private String scode;
     
       
    @Schema(description = "所属项目")
    private String project;
     
       
    @Schema(description = "所属环境")
    private String env;
     
       
    @Schema(description = "主机资源组")
    private String resourceGroup;
     
       
    @Schema(description = "CPU核数")
    private Integer cpu;
     
       
    @Schema(description = "内存大小(M)")
    private Long memory;
     
       
    @Schema(description = "硬盘大小(M)")
    private Long diskSize;
     
       
    @Schema(description = "磁盘类型")
    private String diskType;
     
       
    @Schema(description = "所属VPC")
    private String vpcId;
     
       
    @Schema(description = "所属子网")
    private String subnetId;
     
       
    @Schema(description = "DG网络域")
    private String dgDomain;
     
       
    @Schema(description = "实例所属的DG")
    private String dgId;
     
       
    @Schema(description = "所在地区")
    private String region;
     
       
    @Schema(description = "所在机房")
    private String zone;
     
       
    @Schema(description = "主机描述")
    private String description;
     
       
    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;
     
       
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;
     
       
    @Schema(description = "是否已删除")
    private String isDeleted;
     
       
    @Schema(description = "资源唯一ID")
    private String resourceId;
     
       
    private String uniRegionId;
     
       
    private String domain;
     
       
    private String team;
     
       
    private String ownerId;
     
       
    private String ownerName;
     

}

