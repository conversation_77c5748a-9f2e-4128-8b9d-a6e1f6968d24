package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * (BcRawBillCharges)实体类
 *
 * <AUTHOR>
 * @since 2024-01-02 11:08:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "$tableInfo.comment")
public class BcRawBillCharges implements Serializable {
    private static final long serialVersionUID = 280844322502592954L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "productId")
    private Integer productId;

    @Schema(description = "计费项")
    private String billingItem;


    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Schema(description = "创建人账号")
    private String createBy;


    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Schema(description = "更新人账号")
    private String updateBy;


    @Schema(description = "删除标识 0：有效 1：无效")
    private String delFlag;

    public BcRawBillCharges() {
    }

    public BcRawBillCharges(Integer productId, String billingItem) {
        this.productId = productId;
        this.billingItem = billingItem;
    }
}

