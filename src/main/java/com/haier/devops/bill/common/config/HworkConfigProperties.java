package com.haier.devops.bill.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2024年01月24日 13:27
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "api.howrk")
public class HworkConfigProperties {
    private String domain;
    private String appId;
    private String secret;
}
