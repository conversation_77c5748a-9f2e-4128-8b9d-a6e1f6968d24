package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
* @ClassName: AlarmConfigurationDTO
* @Description: 告警配置 dto
* @author: 张爱苹
* @date: 2024/1/11 11:14
*/
@Data
public class BaseDTO implements Serializable {

    private static final long serialVersionUID = -5484855757755303572L;

    @Min(1)
    Integer page = 1;
    @Min(1)
    Integer per_page = 10;
}
