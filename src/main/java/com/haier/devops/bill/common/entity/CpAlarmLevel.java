package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("bc_cp_alarm_level")
public class CpAlarmLevel implements Serializable {
    private static final long serialVersionUID = 7871105710590398068L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云产品告警配置表id
     */
    @NotNull
    private Integer cpAlarmConfigurationId;

    /**
     * 告警级别
     */
    @NotEmpty
    private String level;

    /**
     * 幅度
     */
    @NotNull
    private BigDecimal amplitudeRatio;

}
