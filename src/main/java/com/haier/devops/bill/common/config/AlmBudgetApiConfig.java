package com.haier.devops.bill.common.config;

import com.haier.devops.bill.common.api.AlmBudgetApi;
import feign.Feign;
import feign.Logger;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ALM预算API配置
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Configuration
public class AlmBudgetApiConfig {

    @Value("${alm.budget.api.url:https://alm.haier.net}")
    private String almBudgetApiUrl;

    /**
     * 配置ALM预算API客户端
     *
     * @return ALM预算API客户端
     */
    @Bean
    public AlmBudgetApi almBudgetApi() {
        return Feign.builder()
                .encoder(new FastjsonEncoder())
                .decoder(new Fastjson2Decoder())
                .logger(new Slf4jLogger(AlmBudgetApi.class))
                .logLevel(Logger.Level.FULL)
                .target(AlmBudgetApi.class, almBudgetApiUrl);
    }
}
