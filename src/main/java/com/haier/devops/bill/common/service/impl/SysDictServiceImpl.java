package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictCreateDTO;
import com.haier.devops.bill.common.dto.SysDictDTO;
import com.haier.devops.bill.common.entity.SysDict;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.SysDictMapper;
import com.haier.devops.bill.common.service.SysDictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@Service
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {
	private Logger logger = LoggerFactory.getLogger(SysDictServiceImpl.class);

	@Override
	public PageInfo<SysDict> listByPage(SysDictDTO dto) {
		try{
			return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
					() ->  baseMapper.listByPage(dto)
			);
		}catch (Exception e){
			e.printStackTrace();
			logger.error("listByPage error, dto:{}", dto, e.getMessage(),e.getMessage(),e);
			throw new RuntimeException("listByPage error");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void createSysDict(SysDictCreateDTO sysDictCreateDTO) {
		SysDict sysDict = new SysDict();
		BeanUtils.copyProperties(sysDictCreateDTO, sysDict);
		User currentUser = LoginContextHolder.getCurrentUser();
		sysDict.setCreateBy(currentUser.getUserCode());
		save(sysDict);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateSysDict(String id, SysDictCreateDTO sysDictCreateDTO) {
		SysDict sysDict = getById(id);
		if(sysDict == null){
			throw new RuntimeException("SysDict not found");
		}
		BeanUtils.copyProperties(sysDictCreateDTO, sysDict);
		sysDict.setUpdateTime(new Date());
		User currentUser = LoginContextHolder.getCurrentUser();
		sysDict.setUpdateBy(currentUser.getUserCode());
		sysDict.setId(id);
		updateById(sysDict);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteSysDict(String id) {
		SysDict sysDict = getById(id);
		if(sysDict == null){
			throw new RuntimeException("SysDict not found");
		}
		removeById(sysDict);
	}

	@Override
	public boolean check(String dictCode) {
		LambdaQueryWrapper
				<SysDict> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysDict::getDictCode, dictCode);
		queryWrapper.eq(SysDict::getDelFlag, "0");
		Long n = baseMapper.selectCount(queryWrapper);
		if(n > 0){
			return false;
		}
		return true;
	}

}
