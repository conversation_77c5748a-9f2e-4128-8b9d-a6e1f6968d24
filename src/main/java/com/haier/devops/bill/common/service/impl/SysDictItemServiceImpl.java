package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictItemCreateDTO;
import com.haier.devops.bill.common.dto.SysDictItemDTO;
import com.haier.devops.bill.common.entity.SysDictItem;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.SysDictItemMapper;
import com.haier.devops.bill.common.service.SysDictItemService;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import com.haier.devops.bill.common.vo.SysDictItemVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* @ClassName: SysDictItemServiceImpl
* @Description: 字典项
* @author: 张爱苹
* @date: 2024/1/12 11:02
*/
@Service
public class SysDictItemServiceImpl extends ServiceImpl<SysDictItemMapper, SysDictItem> implements SysDictItemService {
    private Logger logger = LoggerFactory.getLogger(SysDictItemServiceImpl.class);
    @Autowired
    private SysDictItemMapper sysDictItemMapper;

    @Override
    public List<SysDictItemVo> getDictItemList(String dictCode) {
        return sysDictItemMapper.getDictItemList(dictCode);
    }

    @Override
    public List<AlarmNoticeObjVo> getDictItemByItemValueArray(String[] noticeObjIdArray, String dictCode) {
        return sysDictItemMapper.getDictItemByItemValueArray(noticeObjIdArray,dictCode);
    }

    @Override
    public PageInfo<SysDictItem> listByPage(SysDictItemDTO dto) {
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->  baseMapper.listByPage(dto)
            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, dto:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSysDictItem(SysDictItemCreateDTO sysDictItemCreateDTO) {
        SysDictItem sysDictItem = new SysDictItem();
        BeanUtils.copyProperties(sysDictItemCreateDTO, sysDictItem);
        User currentUser = LoginContextHolder.getCurrentUser();
        sysDictItem.setCreateBy(currentUser.getUserCode());
        save(sysDictItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysDictItem(String id, SysDictItemCreateDTO sysDictItemCreateDTO) {
        SysDictItem sysDictItem = getById(id);
        if(sysDictItem == null){
            throw new RuntimeException("SysDictItem not found");
        }
        BeanUtils.copyProperties(sysDictItemCreateDTO,  sysDictItem);
        User currentUser = LoginContextHolder.getCurrentUser();
        sysDictItem.setUpdateBy(currentUser.getUserCode());
        sysDictItem.setId(id);
        updateById(sysDictItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSysDictItem(String id) {
        SysDictItem sysDictItem = getById(id);
        if(sysDictItem == null){
            throw new RuntimeException("SysDictItem not found");
        }
        removeById(id);
    }

    @Override
    public boolean check(String dictId,String itemValue) {
        LambdaQueryWrapper
                <SysDictItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictItem::getDictId, dictId);
        queryWrapper.eq(SysDictItem::getItemValue, itemValue);
        Long n = baseMapper.selectCount(queryWrapper);
        if(n > 0){
            return false;
        }
        return true;
    }
}
