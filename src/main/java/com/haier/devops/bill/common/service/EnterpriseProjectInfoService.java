package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.EnterpriseProjectInfo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-28
 */
public interface EnterpriseProjectInfoService extends IService<EnterpriseProjectInfo> {
    /**
     * 根据s码批量查询企业信息
     * @param scodes
     * @return
     */
    List<EnterpriseProjectInfo> selectByScodes(List<String> scodes);
}
