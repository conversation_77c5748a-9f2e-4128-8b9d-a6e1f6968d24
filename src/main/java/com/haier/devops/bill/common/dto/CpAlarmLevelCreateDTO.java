package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data

public class CpAlarmLevelCreateDTO implements Serializable {

    private static final long serialVersionUID = 9009412308519783805L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 告警级别
     */
    @NotEmpty
    private String level;

    /**
     * 幅度
     */
    @NotNull
    private BigDecimal amplitudeRatio;

    /**
     * 通知详情
     */
    @NotEmpty
    private List<CpAlarmRuleDetailDTO> cpAlarmRuleDetailDTOList;


}
