package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.AlarmNoticeGroupDTO;
import com.haier.devops.bill.common.entity.AlarmNoticeGroup;
import com.haier.devops.bill.common.vo.AlarmNoticeGroupVo;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupMapper
* @Description:  告警通知组Mapper
* @author: 张爱苹
* @date: 2024/1/12 17:23
*/
@Repository
public interface AlarmNoticeGroupMapper extends BaseMapper<AlarmNoticeGroup> {

    List<AlarmNoticeGroupVo> listByPage(@Param("dto") AlarmNoticeGroupDTO dto);

    List<AlarmNoticeObjVo> getAlarmNoticeGroupByIdList(@Param("array")String[] noticeObjIdArray);
}
