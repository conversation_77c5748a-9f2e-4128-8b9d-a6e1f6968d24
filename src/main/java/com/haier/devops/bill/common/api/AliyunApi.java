package com.haier.devops.bill.common.api;

import com.alibaba.fastjson.JSON;
import com.aliyun.bssopenapi20171214.models.TagResourcesRequest;
import com.aliyun.bssopenapi20171214.models.TagResourcesResponse;
import com.aliyun.bssopenapi20171214.models.*;
import com.aliyun.cr20181201.models.GetInstanceResponse;
import com.aliyun.cr20181201.models.GetInstanceResponseBody;
import com.aliyun.ecs20140526.models.DescribeDisksResponse;
import com.aliyun.ecs20140526.models.DescribeDisksResponseBody;
import com.aliyun.mse20190531.models.QueryClusterInfoResponse;
import com.aliyun.mse20190531.models.QueryClusterInfoResponseBody;
import com.aliyun.resourcemanager20200331.models.*;
import com.aliyun.sae20190506.models.DescribeApplicationConfigResponse;
import com.aliyun.sae20190506.models.DescribeApplicationConfigResponseBody;
import com.aliyun.slb20140515.models.DescribeLoadBalancerAttributeResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.vpc20160428.models.*;
import com.aliyun.yundun_bastionhost20191209.models.DescribeInstanceAttributeResponse;
import com.aliyun.yundun_bastionhost20191209.models.DescribeInstanceAttributeResponseBody;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.haier.devops.bill.common.enums.BastionhostEdnpointEnum;
import com.haier.devops.bill.common.enums.RegionEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 阿里云api
 * @Author: A0018437
 * @Date：2023-12-27
 */
public class AliyunApi {
    private static Logger logger = LoggerFactory.getLogger(AliyunApi.class);
    /**
     * 查询slb实例信息
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param region
     * @param loadBalancerId
     * @return
     * @throws Exception
     */
    public static String describeLoadBalancerAttribute(String accessKeyId, String accessKeySecret, String region, String loadBalancerId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Slb
        config.endpoint = "slb.aliyuncs.com";
        com.aliyun.slb20140515.Client client = new com.aliyun.slb20140515.Client(config);
        com.aliyun.slb20140515.models.DescribeLoadBalancerAttributeRequest describeLoadBalancerAttributeRequest = new com.aliyun.slb20140515.models.DescribeLoadBalancerAttributeRequest()
                .setRegionId(region)
                .setLoadBalancerId(loadBalancerId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            List<DescribeLoadBalancerAttributeResponseBody.DescribeLoadBalancerAttributeResponseBodyBackendServersBackendServer> backendServer = client.describeLoadBalancerAttributeWithOptions(describeLoadBalancerAttributeRequest, runtime).body.backendServers.getBackendServer();
            if (CollectionUtils.isNotEmpty(backendServer)){
                return backendServer.get(0).getServerId();
            }
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static List<DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk> describeDisks(String accessKeyId, String accessKeySecret, String region, String instanceIds) throws Exception {
        region = getRegion(region);
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Ecs
        config.endpoint = "ecs."+region+".aliyuncs.com";


        // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        com.aliyun.ecs20140526.Client client =  new com.aliyun.ecs20140526.Client(config);
        com.aliyun.ecs20140526.models.DescribeDisksRequest describeDisksRequest = new com.aliyun.ecs20140526.models.DescribeDisksRequest()
                .setRegionId(region)
                .setDiskIds(instanceIds);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeDisksResponse describeDisksResponse = client.describeDisksWithOptions(describeDisksRequest, runtime);
            return describeDisksResponse.body.disks.disk;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static DescribeVpcAttributeResponseBody describeVpcAttribute(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Vpc
        config.endpoint = "vpc"+RegionEnum.getRegion(region).get(1);
        com.aliyun.vpc20160428.Client client = new com.aliyun.vpc20160428.Client(config);
        com.aliyun.vpc20160428.models.DescribeVpcAttributeRequest describeVpcAttributeRequest = new com.aliyun.vpc20160428.models.DescribeVpcAttributeRequest()
                .setRegionId(RegionEnum.getRegion(region).get(0))
                .setVpcId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeVpcAttributeResponse describeVpcAttributeResponse = client.describeVpcAttributeWithOptions(describeVpcAttributeRequest, runtime);
            return describeVpcAttributeResponse.body;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }


    public static DescribeVpnGatewayResponseBody describeVpnGateway(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Vpc
        config.endpoint = "vpc"+RegionEnum.getRegion(region).get(1);
        com.aliyun.vpc20160428.Client client = new com.aliyun.vpc20160428.Client(config);
        com.aliyun.vpc20160428.models.DescribeVpnGatewayRequest describeVpcAttributeRequest = new com.aliyun.vpc20160428.models.DescribeVpnGatewayRequest()
                .setRegionId(RegionEnum.getRegion(region).get(0))
                .setVpnGatewayId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeVpnGatewayResponse describeVpnGatewayResponse = client.describeVpnGatewayWithOptions(describeVpcAttributeRequest, runtime);
            return describeVpnGatewayResponse.body;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }
    public static GetInstanceResponseBody getCrInstance(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/cr
        config.endpoint = "cr"+RegionEnum.getRegion(region).get(1);

        com.aliyun.cr20181201.Client client = new com.aliyun.cr20181201.Client(config);
        com.aliyun.cr20181201.models.GetInstanceRequest getInstanceRequest = new com.aliyun.cr20181201.models.GetInstanceRequest()
                .setInstanceId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            GetInstanceResponse instanceWithOptions = client.getInstanceWithOptions(getInstanceRequest, runtime);
            return instanceWithOptions.body;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static DescribeInstanceAttributeResponseBody describeInstanceAttribute(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Yundun-bastionhost
        config.endpoint = BastionhostEdnpointEnum.getEndpoint(region);

        com.aliyun.yundun_bastionhost20191209.Client client = new com.aliyun.yundun_bastionhost20191209.Client(config);
        com.aliyun.yundun_bastionhost20191209.models.DescribeInstanceAttributeRequest describeInstanceAttributeRequest = new com.aliyun.yundun_bastionhost20191209.models.DescribeInstanceAttributeRequest()
                .setInstanceId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeInstanceAttributeResponse describeInstanceAttributeResponse = client.describeInstanceAttributeWithOptions(describeInstanceAttributeRequest, runtime);
            return describeInstanceAttributeResponse.body;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static QueryClusterInfoResponseBody.QueryClusterInfoResponseBodyData queryClusterInfo(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/mse
        config.endpoint = "mse" + RegionEnum.getRegion(region).get(1);

        com.aliyun.mse20190531.Client client = new com.aliyun.mse20190531.Client(config);
        com.aliyun.mse20190531.models.QueryClusterInfoRequest queryClusterInfoRequest = new com.aliyun.mse20190531.models.QueryClusterInfoRequest()
                .setInstanceId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            QueryClusterInfoResponse queryClusterInfoResponse = client.queryClusterInfoWithOptions(queryClusterInfoRequest, runtime);
            return queryClusterInfoResponse.body.data;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    /**
     * 描述通用带宽包信息
     *
     * @param accessKeyId     AccessKey ID
     * @param accessKeySecret AccessKey Secret
     * @param region          地域
     * @param instanceId      带宽包实例ID
     * @return
     * @throws Exception 异常
     *                   https://help.aliyun.com/zh/internet-shared-bandwidth/developer-reference/api-vpc-2016-04-28-describecommonbandwidthpackages-brandwidths?spm=a2c4g.11186623.0.i4
     */
    public static DescribeCommonBandwidthPackagesResponseBody.DescribeCommonBandwidthPackagesResponseBodyCommonBandwidthPackagesCommonBandwidthPackage describeCommonBandwidthPackages(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Vpc
        config.endpoint = "vpc" + RegionEnum.getRegion(region).get(1);

        com.aliyun.vpc20160428.Client client = new com.aliyun.vpc20160428.Client(config);
        com.aliyun.vpc20160428.models.DescribeCommonBandwidthPackagesRequest describeCommonBandwidthPackagesRequest = new com.aliyun.vpc20160428.models.DescribeCommonBandwidthPackagesRequest()
                .setRegionId(RegionEnum.getRegion(region).get(0))
                .setBandwidthPackageId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeCommonBandwidthPackagesResponse describeCommonBandwidthPackagesResponse = client.describeCommonBandwidthPackagesWithOptions(describeCommonBandwidthPackagesRequest, runtime);
            if(describeCommonBandwidthPackagesResponse.body.commonBandwidthPackages.commonBandwidthPackage.size() > 0){
                return describeCommonBandwidthPackagesResponse.body.commonBandwidthPackages.commonBandwidthPackage.get(0);
            }
            return null;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static com.aliyun.hologram20220601.models.GetInstanceResponseBody.GetInstanceResponseBodyInstance getHologramInstance(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Hologram
        config.endpoint = "hologram" + RegionEnum.getRegion(region).get(1);

        com.aliyun.hologram20220601.Client client = new com.aliyun.hologram20220601.Client(config);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        java.util.Map<String, String> headers = new java.util.HashMap<>();
        try {
            // 复制代码运行请自行打印 API 的返回值
            com.aliyun.hologram20220601.models.GetInstanceResponse instanceWithOptions = client.getInstanceWithOptions(instanceId, headers, runtime);
            return instanceWithOptions.body.instance;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static GetNatGatewayAttributeResponseBody getNatGatewayAttribute(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Vpc
        config.endpoint = "vpc" + RegionEnum.getRegion(region).get(1);

        com.aliyun.vpc20160428.Client client = new com.aliyun.vpc20160428.Client(config);
        com.aliyun.vpc20160428.models.GetNatGatewayAttributeRequest getNatGatewayAttributeRequest = new com.aliyun.vpc20160428.models.GetNatGatewayAttributeRequest()
                .setRegionId(RegionEnum.getRegion(region).get(0))
                .setNatGatewayId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            GetNatGatewayAttributeResponse natGatewayAttributeWithOptions = client.getNatGatewayAttributeWithOptions(getNatGatewayAttributeRequest, runtime);
            return natGatewayAttributeWithOptions.body;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static DescribeApplicationConfigResponseBody.DescribeApplicationConfigResponseBodyData describeApplicationConfig(String accessKeyId, String accessKeySecret, String region, String instanceId) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/sae
        config.endpoint = "sae" + RegionEnum.getRegion(region).get(1);

        com.aliyun.sae20190506.Client client = new com.aliyun.sae20190506.Client(config);
        com.aliyun.sae20190506.models.DescribeApplicationConfigRequest describeApplicationConfigRequest = new com.aliyun.sae20190506.models.DescribeApplicationConfigRequest()
                .setAppId(instanceId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        java.util.Map<String, String> headers = new java.util.HashMap<>();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeApplicationConfigResponse describeApplicationConfigResponse = client.describeApplicationConfigWithOptions(describeApplicationConfigRequest, headers, runtime);
            return describeApplicationConfigResponse.body.data;
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.bssopenapi20171214.Client createBssopenapiClient(String accessKeyId, String accessKeySecret ) throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(accessKeyId)
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/BssOpenApi
        config.endpoint = "business.aliyuncs.com";
        return new com.aliyun.bssopenapi20171214.Client(config);
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.resourcemanager20200331.Client createResourcemanagerClient(String accessKeyId, String accessKeySecret ) throws Exception {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(accessKeyId)
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/ResourceManager
        config.endpoint = "resourcemanager.aliyuncs.com";
        return new com.aliyun.resourcemanager20200331.Client(config);
    }

    public static String allocateCostUnitResourceWithOptions(String accessKeyId, String accessKeySecret, AllocateCostUnitResourceRequest resourceRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String errorMsg = "";
        try {
            // 复制代码运行请自行打印 API 的返回值
            AllocateCostUnitResourceResponse response = client.allocateCostUnitResourceWithOptions(resourceRequest, runtime);
            return null;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            logger.error("allocateCostUnitResourceWithOptions requests failed: {}", JSON.toJSONString(error));
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            errorMsg = JSON.toJSONString(error);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            logger.error("allocateCostUnitResourceWithOptions requests failed: {}", JSON.toJSONString(error));
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            errorMsg = JSON.toJSONString(error);
        }
        return errorMsg;
    }

    public static List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> getCostUnitDtoList(String accessKeyId, String accessKeySecret, QueryCostUnitRequest queryCostUnitRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            QueryCostUnitResponse response = client.queryCostUnitWithOptions(queryCostUnitRequest, runtime);
            return response.getBody().getData().getCostUnitDtoList();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static QueryCostUnitResourceResponseBody.QueryCostUnitResourceResponseBodyData queryCostUnitResourceWithOptions(String accessKeyId, String accessKeySecret, QueryCostUnitResourceRequest queryCostUnitRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId, accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            QueryCostUnitResourceResponse resourceResponse = client.queryCostUnitResourceWithOptions(queryCostUnitRequest, runtime);
            return resourceResponse.getBody().getData();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static Map tagResourcesWithOptions(String accessKeyId, String accessKeySecret, TagResourcesRequest tagResourcesRequest) throws Exception {
        Map map = new HashMap();
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        String errorMsg = "";
        try {
            // 复制代码运行请自行打印 API 的返回值
            TagResourcesResponse response = client.tagResourcesWithOptions(tagResourcesRequest, runtime);
            map.put("data",response.getBody().getData());
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            errorMsg = JSON.toJSONString(error);
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            logger.error("tagResourcesWithOptions requests failed: {}",JSON.toJSONString(error));
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            errorMsg = JSON.toJSONString(error);
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            logger.error("tagResourcesWithOptions requests failed: {}",JSON.toJSONString(error));
        }
        map.put("errorMsg",errorMsg);
        return map;
    }

    public static CreateCostUnitResponseBody.CreateCostUnitResponseBodyData createCostUnitWithOptions(String accessKeyId, String accessKeySecret, CreateCostUnitRequest createCostUnitRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            CreateCostUnitResponse response = client.createCostUnitWithOptions(createCostUnitRequest, runtime);
            CreateCostUnitResponseBody.CreateCostUnitResponseBodyData data =  response.getBody().getData();
            return data;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroups listResourceGroupsWithOptions(String accessKeyId, String accessKeySecret, ListResourceGroupsRequest listResourceGroupsRequest) throws Exception {
        com.aliyun.resourcemanager20200331.Client client = createResourcemanagerClient(accessKeyId, accessKeySecret);
       com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            ListResourceGroupsResponse response = client.listResourceGroupsWithOptions(listResourceGroupsRequest, runtime);
            return response.getBody().getResourceGroups();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static UpdateResourceGroupResponseBody.UpdateResourceGroupResponseBodyResourceGroup updateResourceGroupWithOptions(String accessKeyId, String accessKeySecret, UpdateResourceGroupRequest updateResourceGroupRequest) throws Exception {
        com.aliyun.resourcemanager20200331.Client client = createResourcemanagerClient(accessKeyId,accessKeySecret);
       com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            UpdateResourceGroupResponse updateResourceGroupWithOptions = client.updateResourceGroupWithOptions(updateResourceGroupRequest, runtime);
            return updateResourceGroupWithOptions.getBody().getResourceGroup();
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }

    public static void createResourceGroupWithOptions(String accessKeyId, String accessKeySecret, CreateResourceGroupRequest createResourceGroupRequest) throws Exception {
        com.aliyun.resourcemanager20200331.Client client = createResourcemanagerClient(accessKeyId,accessKeySecret);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            CreateResourceGroupResponse response = client.createResourceGroupWithOptions(createResourceGroupRequest, runtime);
            System.out.println(JSON.toJSONString(response));
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

    public static void deleteResourceGroupWithOptions(String accessKeyId, String accessKeySecret, DeleteResourceGroupRequest deleteResourceGroupRequest) throws Exception {
        com.aliyun.resourcemanager20200331.Client client = createResourcemanagerClient(accessKeyId, accessKeySecret);
       com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DeleteResourceGroupResponse response = client.deleteResourceGroupWithOptions(deleteResourceGroupRequest, runtime);
            System.out.println(JSON.toJSONString(response));
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

    public static void deleteCostUnitWithOptions(String accessKeyId, String accessKeySecret, DeleteCostUnitRequest deleteCostUnitRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
       com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.deleteCostUnitWithOptions(deleteCostUnitRequest, runtime);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }


    public static QueryCommodityListResponse queryCommodityList(String accessKeyId, String accessKeySecret, QueryCommodityListRequest queryCommodityListRequest) throws Exception {
        com.aliyun.bssopenapi20171214.Client client = createBssopenapiClient(accessKeyId,accessKeySecret);
        try {
            // 复制代码运行请自行打印 API 的返回值
            QueryCommodityListResponse response = client.queryCommodityList(queryCommodityListRequest);
            return response;
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }


    public static String getRegion(String region){
        int i = region.indexOf("-",3);
        if (region.startsWith("cn")) {
            return region.substring(0,i);
        } else if (region.startsWith("ap")) {
            return region.substring(0,i+2);
        } else {
            return region;
        }
    }
}
