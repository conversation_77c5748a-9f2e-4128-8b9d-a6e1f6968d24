package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 验收推送黑名单，配置的云厂商和S码不会被推送到hwork
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@TableName("bc_acceptance_blacklist")
@Data
@NoArgsConstructor
public class AcceptanceBlacklist implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 系统编码（S码）
     */
    private String sysCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
