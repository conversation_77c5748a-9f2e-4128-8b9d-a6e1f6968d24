package com.haier.devops.bill.common.vo;

/**
 * <AUTHOR>
 */
public class ServiceResult<T> {
    private boolean isSuccess = true;
    private String errMsg;
    private T data;

    public ServiceResult() {}

    public ServiceResult(String errMsg, T data) {
        this.isSuccess = false;
        this.errMsg = errMsg;
        this.data = data;
    }

    public void error(String errMsg) {
        this.isSuccess = false;
        this.errMsg = errMsg;
    }

    public ServiceResult(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return isSuccess;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public T getData() {
        return data;
    }
}
