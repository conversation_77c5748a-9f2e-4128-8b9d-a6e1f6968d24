package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.RawBill;
import com.haier.devops.bill.common.entity.RefinedRawBill;
import com.haier.devops.bill.common.vo.ColumnValueVo;
import com.haier.devops.bill.common.vo.CostProcedureVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RawBillService extends IService<RawBill> {

    /**
     * 分页查询
     *
     * @param queryWrapper
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<RawBill> selectPage(QueryWrapper<RawBill> queryWrapper, int page, int perPage);

    /**
     * 查询列表
     *
     * @param queryWrapper
     * @return
     */
    List<RawBill> selectList(QueryWrapper<RawBill> queryWrapper);


    List<ColumnValueVo> selectColumnValueCountByQueryWrapper(QueryWrapper<RawBill> queryWrapper, String column);


    /**
     * 查询计算过程
     *
     * @param vendor
     * @param costUnit
     * @param productCode
     * @param billingCycle
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<CostProcedureVo> selectCostProcedure(String vendor,
                                                  String costUnit,
                                                  String productCode,
                                                  String billingCycle,
                                                  int page, int perPage);


    /**
     * 账单更新
     * @param list
     * @throws InterruptedException
     */
    void updateRawBills(List<RawBill> list);

    RefinedRawBill getResourceId(String aggregatedId);
}
