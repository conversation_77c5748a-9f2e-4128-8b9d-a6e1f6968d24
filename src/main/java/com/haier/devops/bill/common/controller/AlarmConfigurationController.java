package com.haier.devops.bill.common.controller;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmConfigurationCreateDTO;
import com.haier.devops.bill.common.dto.AlarmConfigurationDTO;
import com.haier.devops.bill.common.dto.IsEnableAlarmDTO;
import com.haier.devops.bill.common.service.AlarmConfigurationService;
import com.haier.devops.bill.common.vo.CpAlarmConfigurationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
* @ClassName: AlarmConfigurationController
* @Description: 告警配置管理控制器
* @author: 张爱苹
* @date: 2024/1/11 10:49
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/alarm/configuration")
public class AlarmConfigurationController {

    @Autowired
    private AlarmConfigurationService alarmConfigurationService;

    /**
     * 分页查询所有告警配置
     *
     * @return 告警配置列表
     */
    @GetMapping("/listByPage")
    public ResponseEntityWrapper<PageInfo<CpAlarmConfigurationVo>> listByPage(AlarmConfigurationDTO dto)  {
        try{
            PageInfo<CpAlarmConfigurationVo> pageInfo =
                    alarmConfigurationService.listByPage(dto);
            return new ResponseEntityWrapper<>(pageInfo);
        }catch (Exception e){
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
    }


    /**
     * 根据ID获取告警配置
     *
     * @param id 告警配置ID
     * @return 告警配置
     */
    @GetMapping("/{id}")
    public ResponseEntityWrapper<CpAlarmConfigurationVo> getAlarmConfigurationById(@PathVariable("id") Integer id) {
        try {
            return new ResponseEntityWrapper<>(alarmConfigurationService.getAlarmConfigurationById(id));
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
    }

    /**
     * 创建告警配置
     *
     * @param alarmConfigurationCreateDTO 告警配置
     * @return 创建成功的告警配置
     */
    @PostMapping("/create")
    public ResponseEntityWrapper createAlarmConfiguration(@RequestBody @Validated AlarmConfigurationCreateDTO alarmConfigurationCreateDTO){
        try {
            alarmConfigurationService.createAlarmConfiguration(alarmConfigurationCreateDTO);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
     * 更新告警配置
     *
     * @param id                 告警配置ID
     * @param alarmConfigurationCreateDTO 更新的告警配置
     * @return 更新后的告警配置
     */
    @PutMapping("/update/{id}")
    public ResponseEntityWrapper updateAlarmConfiguration(@PathVariable("id") Integer id, @RequestBody @Validated AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) {
        try {
            alarmConfigurationService.updateAlarmConfiguration(id, alarmConfigurationCreateDTO);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
    * @Description: 更新告警配置启用状态
    * @author: 张爱苹
    * @date: 2024/1/15 14:16
    * @param isEnableAlarmDTO:
    * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
    */
    @PutMapping("/updateEnableStatus")
    public ResponseEntityWrapper updateEnableStatus(@RequestBody @Validated IsEnableAlarmDTO isEnableAlarmDTO) {
        try {
            alarmConfigurationService.updateEnableStatus(isEnableAlarmDTO);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }

    /**
     * 删除告警配置
     *
     * @param id 告警配置ID
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntityWrapper deleteAlarmConfiguration(@PathVariable("id") Integer id) {
        try {
            alarmConfigurationService.deleteAlarmConfiguration(id);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
        return new ResponseEntityWrapper<>();
    }
}
