package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * (IdmOrg)实体类
 *
 * <AUTHOR>
 * @since 2024-01-23 16:49:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class IdmOrg implements Serializable {
    private static final long serialVersionUID = -92320382795095371L;

    @TableField("orgid")
    private String orgId;


    @TableField("orgname")
    private String orgName;


    @TableField("up_id")
    private String upId;


    @TableField("endtime")
    private Date endTime;

}

