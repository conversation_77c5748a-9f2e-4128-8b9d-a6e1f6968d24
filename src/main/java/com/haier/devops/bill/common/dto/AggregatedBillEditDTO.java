package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
* @ClassName: AggregatedBillDTO
* @Description:  账单明细汇总
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class AggregatedBillEditDTO implements Serializable {


    private static final long serialVersionUID = -2289392453850973751L;
    /**
     * S码
     */
    @NotEmpty
    private String scode;

    @NotNull
    private List<Integer> idList;

}
