package com.haier.devops.bill.common.service;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.api.HworkBillApi;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.interceptor.User;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthorityService {
    String AUTH_CACHE_KEY = "_bill_user_authed_scode_";
    String SUB_PRODUCT_CACHE_KEY = "_bill_user_authed_sub_product_";

    /**
     * 获取授权的S码
     * @param userCode
     * @return
     */
    List<String> getAuthorizedScodes(String userCode);

    /**
    * @Description: 获取人员信息
    * @author: 张爱苹
    * @date: 2024/2/1 09:56
    * @param userCode:
    * @Return: com.haier.devops.bill.common.interceptor.User
    */
    User getUserByUserCode(String userCode);

    /**
    * @Description: 获取用户手机号
    * @author: 张爱苹
    * @date: 2024/2/1 10:12
    * @param account:
    * @Return: java.lang.String
    */
    String getUserPhoneByUserCode(String account);

    /**
     * 获取授权的子产品信息
     * @param userCode
     * @return
     */
    List<HdsSubProducts> getAuthorizedSubProducts(String userCode);

    /**
     * 获取管理员或非管理员的产品信息
     * @param departIds
     * @return
     */
    List<HdsSubProducts> getAuthedSubProducts(String departIds);

    /**
     * 获取用户数据权限（领域内+领域外）
     * @param userCode
     * @return
     */
    HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> getUserAuthorities(String userCode);
    
    HworkBillApi.AuthAppForItResponse getAuthAppForIt(HworkBillApi.AuthAppForItRequest request);
}
