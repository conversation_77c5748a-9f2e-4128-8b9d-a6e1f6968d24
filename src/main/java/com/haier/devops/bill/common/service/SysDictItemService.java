package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictItemCreateDTO;
import com.haier.devops.bill.common.dto.SysDictItemDTO;
import com.haier.devops.bill.common.entity.SysDictItem;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import com.haier.devops.bill.common.vo.SysDictItemVo;

import java.util.List;

/**
* @ClassName: SysDictItemService
* @Description: 字典项
* @author: 张爱苹
* @date: 2024/1/12 11:01
*/
public interface SysDictItemService extends IService<SysDictItem> {

    /**
    * @Description: 获取字典项列表
    * @author: 张爱苹
    * @date: 2024/1/12 11:01
    * @param dictCode:
    * @Return: com.haier.devops.bill.common.entity.SysDictItem
    */
    List<SysDictItemVo> getDictItemList(String dictCode);

    /**
    * @Description: 根据字典项值数组获取字典项列表
    * @author: 张爱苹
    * @date: 2024/1/17 13:07
    * @param noticeObjIdArray:
    * @Return: java.util.List<com.haier.devops.bill.common.dto.AlarmNoticeObjDTO>
    */
    List<AlarmNoticeObjVo> getDictItemByItemValueArray(String[] noticeObjIdArray, String dictCode);

    /**
    * @Description: 分页查询字典项
    * @author: 张爱苹
    * @date: 2024/2/4 11:12
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.SysDictItem>
    */
    PageInfo<SysDictItem> listByPage(SysDictItemDTO dto);

    /**
    * @Description:  新增字典项
    * @author: 张爱苹
    * @date: 2024/2/4 11:13
    * @param sysDictItemCreateDTO:
    * @Return: void
    */
    void createSysDictItem(SysDictItemCreateDTO sysDictItemCreateDTO);

    /**
    * @Description: 更新字典项
    * @author: 张爱苹
    * @date: 2024/2/4 11:13
    * @param id:
    * @param sysDictItemCreateDTO:
    * @Return: void
    */
    void updateSysDictItem(String id, SysDictItemCreateDTO sysDictItemCreateDTO);

    /**
    * @Description:  删除字典项
    * @author: 张爱苹
    * @date: 2024/2/4 11:13
    * @param id:
    * @Return: void
    */
    void deleteSysDictItem(String id);

    /**
    * @Description: 检查字典code
    * @author: 张爱苹
    * @date: 2024/2/4 13:44
    * @param itemValue:
    * @Return: boolean
    */
    boolean check(String dictId,String itemValue);
}
