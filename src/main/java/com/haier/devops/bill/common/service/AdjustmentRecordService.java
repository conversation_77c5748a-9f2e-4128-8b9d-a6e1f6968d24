package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.AdjustmentRecord;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;

import java.util.List;

/**
* @ClassName: AdjustmentRecordService
* @Description: 调账记录
* @author: 张爱苹
* @date: 2024/3/11 18:29
*/
public interface AdjustmentRecordService extends IService<AdjustmentRecord> {

    /**
    * @Description: 获取未完成的调账记录数量
    * @author: 张爱苹
    * @date: 2024/3/15 13:37
    * @param aggregatedIdList:
    * @Return: int
    */
    Long getNotCompleteReconciliationCount(List<String> aggregatedIdList);

    /**
    * @Description: 调账记录查询
    * @author: 张爱苹
    * @date: 2024/9/5 13:26
    * @param queryWrapper :
    * @Return: java.util.List<com.haier.devops.bill.common.entity.AdjustmentRecord>
     * @return
    */
    List<AdjustmentRecordVo> getAdjustmentRecordList(QueryWrapper<AdjustmentRecord> queryWrapper);
}
