package com.haier.devops.bill.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
* @ClassName: HdsSendNoticeProperties
* @Description:  发送通知配置
* @author: 张爱苹
* @date: 2024/1/22 17:36
*/
@Data
@Configuration
@ConfigurationProperties(prefix = "hds.notice")
public class HdsSendNoticeProperties {
    private String template_sms;
    private String template_email;
    private String template_hwork;
    private String template_ihaier2;
    private String template_ihaier2_2;
    private String sendor_sms;
    private String sendor_email;
    private String sendor_hwork;
    private String sendor_ihaier2;
}
