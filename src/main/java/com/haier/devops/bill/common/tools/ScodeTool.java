package com.haier.devops.bill.common.tools;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.haier.devops.bill.common.dto.AggregatedBillDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.mapper.AggregatedBillMapper;
import com.haier.devops.bill.common.service.BcDefaultScodeService;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import com.haier.devops.bill.common.vo.AggregatedBillVo;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import com.haier.devops.bill.util.RegularUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: Aliyun获取S码
 * @Author: A0018437
 * @Date：2024-02-27
 */
@Component
public class ScodeTool {
    @Resource
    CmdbProductOverviewService cmdbProductOverviewService;
    @Resource
    private BcDefaultScodeService bcDefaultScodeService;
    @Resource
    AggregatedBillMapper aggregateMapper;

   /**
     * 根据财务单元信息获取相应的Scode。
     *
     * @param billingSummaryVo 账单摘要视图对象，包含财务单元、账户名称和聚合ID等信息。
     * @return 返回相应的Scode字符串。如果找不到合适的Scode，则返回成本单元字符串。
     */
    public String getScode(BillingSummaryVo billingSummaryVo){
        String appScode = ""; // 初始化应用Scode为空字符串
        String last6Digits = ""; // 初始化最后6位数字为空字符串

        // 根据财务单元的长度，提取最后6位数字
        int length = billingSummaryVo.getCostUnit().length();
        if (length > 6) {
            last6Digits = billingSummaryVo.getCostUnit().substring(length - 6);
        }

        // 检查最后6位数字是否符合Scode的规范
        if (RegularUtil.isSCode(last6Digits)) {
            // 如果是Scode，则根据最后6位数字匹配相应的应用Scode
            switch (last6Digits) {
                case "S01764":
                    appScode = "容器云分摊";
                    break;
                default:
                    appScode = last6Digits; // 默认使用最后6位数字作为应用Scode
            }
        } else {
            // 尝试获取默认的Scode
            String defaultScode = bcDefaultScodeService.getDefaultScode(billingSummaryVo.getAccountName());
            if (defaultScode != null) {
                appScode = defaultScode; // 如果存在默认Scode，则使用默认Scode
            } else {
                // 如果不是Scode，则根据财务单元的值匹配相应的应用Scode
                switch (billingSummaryVo.getCostUnit()) {
                    case "未分配":
                    case "未归集":
                    case "default":
                        appScode = "基础设施"; // 默认为基础设施
                        break;
                    default:
                        appScode = billingSummaryVo.getCostUnit(); // 无法匹配时，使用财务单元字符串作为应用Scode
                }
            }
        }

        // 如果应用Scode不是"容器云分摊"，则尝试根据聚合ID查询并设置应用Scode
        if (!appScode.equals("容器云分摊")){
            // 根据聚合ID查询Scode
            List<CmdbProductOverview> list = cmdbProductOverviewService.list(new LambdaQueryWrapper<CmdbProductOverview>().eq(CmdbProductOverview::getAggregatedId, billingSummaryVo.getAggregatedId()));
            if (CollectionUtils.isNotEmpty(list)){
                appScode = list.get(0).getScode(); // 如果查询结果非空，使用查询到的Scode
            }
        }

        // 创建聚合账单数据传输对象（DTO）实例并初始化
        AggregatedBillDTO aggregatedBillDTO = new AggregatedBillDTO();
        aggregatedBillDTO.setAggregatedId(billingSummaryVo.getAggregatedId()); // 设置聚合ID
        aggregatedBillDTO.setBillingCycle(billingSummaryVo.getBillingCycle()); // 设置计费周期
        aggregatedBillDTO.setAccountName(billingSummaryVo.getAccountName()); // 设置账户名称
        // 根据聚合ID、计费周期和账户名称，查询上一计费周期的详细账单信息
        List<AggregatedBillVo> scodeOfThePreviousCycle = aggregateMapper.getScodeOfThePreviousCycle(aggregatedBillDTO);

        // 如果查询结果不为空，则获取并设置appScode的值为第一个账单信息的scode
        if (CollectionUtils.isNotEmpty(scodeOfThePreviousCycle)){
            appScode = scodeOfThePreviousCycle.get(0).getScode();
        }

        return appScode; // 返回应用Scode
    }

}
