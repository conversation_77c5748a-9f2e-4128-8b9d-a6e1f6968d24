package com.haier.devops.bill.common.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoUniqueBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SpringContextAware implements ApplicationContextAware {
    private static ApplicationContext context;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextAware.context = applicationContext;
    }

    public static <T> T getBean(Class<T> type) {
        try {
            return context.getBean(type);
        } catch (NoUniqueBeanDefinitionException e) {
            //出现多个，选第一个
            String beanName = context.getBeanNamesForType(type)[0];
            return context.getBean(beanName, type);
        }
    }

    public static <T> T getBean(String beanName, Class<T> type) {
        return context.getBean(beanName, type);
    }
}
