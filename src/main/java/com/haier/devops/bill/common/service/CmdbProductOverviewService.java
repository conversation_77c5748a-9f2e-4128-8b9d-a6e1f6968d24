package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.CmdbProductOverviewDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ReconciliationTask;
import com.haier.devops.bill.common.vo.AdjustmentRecordVo;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

/**
 * @ClassName: CmdbProductOverviewService
 * @Description: cmdb总表
 * @author: 张爱苹
 * @date: 2024/3/8 16:12
 */
public interface CmdbProductOverviewService extends IService<CmdbProductOverview>{
    boolean saveOrUpdateBatchByParams(List<CmdbProductOverview> list);
    /**
     * @Description:  查询调账列表
     * @author: 张爱苹
     * @date: 2024/3/8 16:14
     * @param dto:
     * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CmdbProductOverview>
     */
    PageInfo<CmdbProductOverview> listByPage(CmdbProductOverviewDTO dto);

    /**
    * @Description: 修改记录查询
    * @author: 张爱苹
    * @date: 2024/3/14 10:58
    * @param dto:
    * @Return: java.util.List<com.haier.devops.bill.common.entity.AdjustmentRecord>
    */
    List<AdjustmentRecordVo> getAdjustmentRecordList(CmdbProductOverviewDTO dto);


    /**
    * @Description: 分拆项查询
    * @author: 张爱苹
    * @date: 2024/3/14 13:16
    * @param vendor:
    * @param productCode:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getSupplementIdList(String vendor, String productCode);

    /**
     * 查找父级未找到的资源
     * @return
     */
    PageInfo<CmdbProductOverview> listParentNotFoundItems(int page, int perPage);


    /**
     * 根据实例id查询父级资源
     * @param instanceId
     * @return
     */
    CmdbProductOverview getParentInstanceByInstanceId(String instanceId);

    List<CmdbProductOverview> queryCmdbProductOverviewList(List<String> aggregatedIdList);

    /**
    * @Description: 根据aggregatedId查询调账任务列表
    * @author: 张爱苹
    * @date: 2024/10/22 15:46
    * @param adjustmentId:
    * @Return: java.lang.Object
    */
    List<ReconciliationTask> getReconcliationTaskList(String adjustmentId);

    /**
     * 根据scode修正项目信息
     * @param scode
     * @param projectId
     * @param projectName
     */
    int correctProjectInfoByScode(String scode, String projectId, String projectName);

    /**
     * 根据aggregatedId查询cmdb总表信息
     * @param aggregatedId
     * @param sqlSession
     * @return
     */
    List<CmdbProductOverview> listByAggregatedId(SqlSession sqlSession, String aggregatedId);

    /**
    * @Description: 根据汇总id查cmdb
    * @author: 张爱苹
    * @date: 2025/2/17 14:25
    * @param aggregatedId:
    * @Return: com.haier.devops.bill.common.entity.CmdbProductOverview
    */
    CmdbProductOverview getOneByAggregatedId(String aggregatedId);
}
