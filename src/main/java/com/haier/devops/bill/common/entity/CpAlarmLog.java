package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* @ClassName: CpAlarmLog
* @Description:  告警日志表
* @author: 张爱苹
* @date: 2024/1/18 13:24
*/
@Data
@Builder
@TableName("bc_cp_alarm_log")
public class CpAlarmLog implements Serializable {


    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 汇总资源id
     */
    private String gatheringId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * S码
     */
    private String appScode;

    /**
     * 告警级别
     */
    private String levelName;

    /**
     * 告警对象详情
     */
    private String noticeObjDetail;


    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 告警级别
     */
    private String level;

    /**
     * 账期
     */
    private String billingCycle;

}
