package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.vo.DomainSubProductVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
* @ClassName: HdsSubProductsMapper
* @Description: hds子产品表 Mapper 接口
* @author: 张爱苹
* @date: 2024/1/11 17:53
*/
@Repository
public interface HdsSubProductsMapper extends BaseMapper<HdsSubProducts> {

    /**
     * 获取指定部门下的所有S码
     * @param orgId
     * @return
     */
    Set<String> getAuthorizedScodes(String orgId);

    void deleteByAppScodeIn(Set<String> list);

    List<String> getAllAppScodeList();

    /**
     * 获取指定部门下的所有子产品信息
     * @param orgId
     * @return
     */
    List<HdsSubProducts> getAuthorizedSubProducts(String orgId);

    /**
     * 根据部门过滤子产品
     * @param list
     * @return
     */
    List<HdsSubProducts> getDeptFilteredSubProducts(@Param("list") List<String> list);


    /**
     * 根据领域查询子产品信息
     * @param domainIds
     * @return
     */
    List<DomainSubProductVo> getDomainSubProductVoList(@Param("domainIds") List<String> domainIds);


    /**
     * 查询所有领域子产品信息
     * @return
     */
    List<DomainSubProductVo> getAllDomainSubProductVoList();


    /**
     * 领域模糊查询
     * @param query
     * @return
     */
    List<HdsSubProducts> searchDomain(String query);
}
