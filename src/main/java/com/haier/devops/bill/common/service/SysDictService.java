package com.haier.devops.bill.common.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictCreateDTO;
import com.haier.devops.bill.common.dto.SysDictDTO;
import com.haier.devops.bill.common.entity.SysDict;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
public interface SysDictService extends IService<SysDict> {

    /**
    * @Description: 分页查询字典列表
    * @author: 张爱苹
    * @date: 2024/2/4 10:55
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.SysDict>
    */
    PageInfo<SysDict> listByPage(SysDictDTO dto);

    /**
    * @Description:  创建字典
    * @author: 张爱苹
    * @date: 2024/2/4 10:55
    * @param sysDictCreateDTO:
    * @Return: void
    */
    void createSysDict(SysDictCreateDTO sysDictCreateDTO);

    /**
    * @Description:  更新字典
    * @author: 张爱苹
    * @date: 2024/2/4 10:55
    * @param id:
    * @param sysDictCreateDTO:
    * @Return: void
    */
    void updateSysDict(String id, SysDictCreateDTO sysDictCreateDTO);

    /**
    * @Description: 删除字典
    * @author: 张爱苹
    * @date: 2024/2/4 10:55
    * @param id:
    * @Return: void
    */
    void deleteSysDict(String id);

    /**
    * @Description: 检查字典code
    * @author: 张爱苹
    * @date: 2024/2/4 13:46
    * @param dictCode:
    * @Return: boolean
    */
    boolean check(String dictCode);
}
