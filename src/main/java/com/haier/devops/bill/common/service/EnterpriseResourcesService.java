package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.EnterpriseResources;

/**
* @ClassName: EnterpriseResourcesService
* @Description: 企业项目资源
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
public interface EnterpriseResourcesService extends IService<EnterpriseResources> {

    void insertEnterpriseResourcesJobHandler(String accountName) throws Exception;

    /**
    * @Description: 根据资源id查询资源信息
    * @author: 张爱苹
    * @date: 2025/2/17 14:28
    * @param resourceId:
    * @Return: com.haier.devops.bill.common.entity.EnterpriseResources
    */
    EnterpriseResources getOneByResourceId(String resourceId);

    /**
    * @Description: 调账
    * @author: 张爱苹
    * @date: 2025/2/17 14:37
    * @param enterpriseResources:
    * @param id:
    * @Return: void
    */
    String migrateResource(EnterpriseResources enterpriseResources, String id) throws Exception;

    /**
    * @Description: 同步资源
    * @author: 张爱苹
    * @date: 2025/3/31 10:00
    * @param accountName:
    * @Return: void
    */
    void insertEnterpriseResources(String accountName) throws Exception;
}
