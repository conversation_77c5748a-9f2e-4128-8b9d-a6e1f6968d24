package com.haier.devops.bill.common.enums;

/**
* @ClassName: RuleTypeEnum
* @Description: 规则类型
* @author: 张爱苹
* @date: 2024/3/11 16:29
*/
public enum RuleFieldEnum {
    RESOURCEID("resource_id","资源id"),
    RESOURCETAG("resource_tag","资源标签"),
    RESOURCENICK("resource_nick","昵称"),
    RESOURCE_GROUP("resource_group","资源组");

    RuleFieldEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
