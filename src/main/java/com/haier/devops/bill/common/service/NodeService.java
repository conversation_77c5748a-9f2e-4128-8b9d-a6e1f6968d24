package com.haier.devops.bill.common.service;

import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.param.DimensionDetailParam;
import com.haier.devops.bill.common.vo.NodeDetailBillsVo;
import com.haier.devops.bill.export.vo.UploadResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 节点 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface NodeService {

    Map<String, Object> getConsumptionAnalysis(String scode);

    Map<String, Integer> getMainAssets(String scode,List<String> vendors, String... currency);

    Map<String, Integer> getMainAssets(String scode,List<String> vendors);

    List<Map<String, Object>> getConsumerTrends(List<String> vendors,
                                                LocalDate startDate,
                                                LocalDate endDate,
                                                String type,
                                                String scode,
                                                String... currency);

    List<Map<String, Object>> getConsumerTrends(List<String> vendors,
                                                LocalDate startDate,
                                                LocalDate endDate,
                                                String type,
                                                String scode,
                                                BigDecimal exchangeRate);

    List<Map<String, Object>> getConsumptionStepByStep(LocalDate startDate,
                                                       LocalDate endDate,
                                                       String type,
                                                       String scode,
                                                       String filter,
                                                       List<String> vendors,
                                                       String... currency);

    List<Map<String, Object>> getConsumptionStepByStep(LocalDate startDate,
                                                       LocalDate endDate,
                                                       String type,
                                                       String scode,
                                                       String filter,
                                                       List<String> vendors,
                                                       BigDecimal exchangeRate);

    IPage<NodeDetailBillsVo> getDetailBillsByPage(DetailBillParam request) ;

//    List<DimensionDetailVo> getDimensionCustDetail(DimensionDetailParam dimensionDetailParam);
    List<Map<String, Object>> getDimensionCustDetail(DimensionDetailParam dimensionDetailParam, String... currency);

    List<Map<String, Object>> getDimensionCustDetail(DimensionDetailParam dimensionDetailParam,BigDecimal exchangeRate);

    UploadResult downloadDetailBills(DetailBillParam request);

    List<String> getPermScodes(String scode,String className);


    void updateLog(DetailBillParam request, PutObjectResult result, String url,
                   boolean isUploadingSuccess, String... remark);

    String getFilePath(DetailBillParam unit);

    void exportBillExcel(DimensionDetailParam dto, HttpServletRequest request, HttpServletResponse response);

    Map getSummerMap(String startDateStr,String endDateStr, List<String> columns,String type);

    /**
    * @Description: 根据币种获取账户列表
    * @author: 张爱苹
    * @date: 2025/5/29 14:11
    * @param currentCy:
    * @Return: java.util.List<java.util.Map>
    */
    List<String> getAccountListByCurrentCy(String currentCy);

    Map getMainAssetsDetail(String scode, List<String> vendors,LocalDate startDate, LocalDate endDate,String type);
}
