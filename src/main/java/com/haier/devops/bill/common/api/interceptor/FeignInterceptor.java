package com.haier.devops.bill.common.api.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * feign拦截器
 * 需要在yml中指明属于哪个feignClient - ${feign.client.config.xxxx.requestInterceptors}
 *
 */
@Slf4j
@Component
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header("Authorization", GatewayApiKeyHelper.API_KEY);
        requestTemplate.header("apiKey", GatewayApiKeyHelper.API_KEY);
        requestTemplate.header("Content-Type", "application/json;charset=UTF-8");
        log.info("{}:{}", "FeignInterceptor apiKey", requestTemplate.headers().get("apiKey"));

        ServletRequestAttributes servletContainer = null;
        try {
            servletContainer = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        } catch (Exception e) {
            log.info("exceptionInfoHandle fail:{}", e.toString());
        }
        if (servletContainer == null) {
            requestTemplate.header("X-USER", "workbench");
        }else {
            HttpServletRequest request = servletContainer.getRequest();
            requestTemplate.header("X-USER", request.getHeader("X-USER"));
        }
        log.info("{}:{}", "FeignInterceptor X-USER", requestTemplate.headers().get("X-USER"));
    }
}

