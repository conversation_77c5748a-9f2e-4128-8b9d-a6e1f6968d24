package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.DocConfig;

/**
 * <p>
 * 账单中心文档配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface DocConfigService extends IService<DocConfig> {
    /**
     * 根据类型和模块获取最新的配置
     * @param type
     * @param business
     * @return
     */
    DocConfig getLatestDocConfig(String type, String business);

    /**
     * 根据类型和模块和账期获取指定账期的配置
     * @param type
     * @param business
     * @param billingCycle
     * @return
     */
    DocConfig getDocConfig(String type, String business, String billingCycle);
}
