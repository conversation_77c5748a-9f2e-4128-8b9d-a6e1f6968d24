package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.CpAlarmLevel;
import com.haier.devops.bill.common.vo.CpAlarmLevelVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* @ClassName: CpAlarmLevelMapper
* @Description:  告警级别表 Mapper 接口
* @author: 张爱苹
* @date: 2024/1/12 14:40
*/
@Repository
public interface CpAlarmLevelMapper extends BaseMapper<CpAlarmLevel> {

    List<CpAlarmLevelVo> getAlarmLevelListByAlarmConfigurationId(@Param("id") Integer id);

    List<Map> getAlarmLevelListByGroupProduct(@Param("list") Set<Integer> productIdList);
}
