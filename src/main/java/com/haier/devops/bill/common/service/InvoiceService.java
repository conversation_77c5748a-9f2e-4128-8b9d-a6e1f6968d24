package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.Invoice;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface InvoiceService extends IService<Invoice> {
    /**
     * 获取发票列表
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<Invoice> getInvoiceList(String vendor, String billingCycle, String... account);
}
