package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @ClassName: CmdbProductOverview
* @Description: cmdb总表
* @author: 张爱苹
* @date: 2024/2/22 14:31
*/
@Builder
@AllArgsConstructor
@TableName("bc_cmdb_product_overview")
@Data
public class CmdbProductOverview implements Serializable {

    private static final long serialVersionUID = 4778282083238859526L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 聚合id
     *
     */
    private String aggregatedId;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账户id
     */
    private String accountId;

    /**
     * 云账号
     */
    private String accountName;

    /**
     * 产品编码
     *
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源实例id
     */
    private String instanceId;


    /**
     * 分拆项
     */
    private String supplementId;


    @Schema(description = "私网ip")
    private String privateIp;


    @Schema(description = "S码")
    private String scode;


    /**
     * 币种
     */
    private String currency;

    /**
     * 项目编码
     */
    private String projectCode;


    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 消费类型
     */
    private String subscriptionType;


    /**
     * 资源创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    /**
     * 资源释放时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releasingTime;

    @Schema(description = "区域")
    @TableField(exist = false)
    private String region;

    @TableField(exist = false)
    private String zone;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String costUnit;

    @TableField(exist = false)
    private String reconciliationStatus;

    @Schema(description = "校准前的S码")
    private String scodeUnverified;

    /**
     * 匹配S码的规则id，默认为0，即没走规则
     */
    @Schema(description = "匹配S码的规则id，默认为0，即没走规则")
    private Integer scodeRuleMatched;

    /**
     * 父资源是否找到 -1未找到，1找到，0无需寻找
     */
    private Integer parentFound;


    @TableField(exist = false)
    private String appName;

    @TableField(exist = false)
    private String appShortName;

    public CmdbProductOverview() {
    }

    public CmdbProductOverview(BillingSummaryVo billingSummaryVo) {
        this.aggregatedId = billingSummaryVo.getAggregatedId();
        this.vendor = billingSummaryVo.getVendor();
        this.accountId = billingSummaryVo.getAccountId();
        this.accountName = billingSummaryVo.getAccountName();
        this.productCode = billingSummaryVo.getProductCode();
        this.productName = billingSummaryVo.getProductName();
        this.instanceId = billingSummaryVo.getInstanceId();
        this.updateTime = LocalDateTime.now();
        this.zone = billingSummaryVo.getZone();
        this.subscriptionType = billingSummaryVo.getSubscriptionType();
    }

    @TableField(exist = false)
    private String resourceId;

    @TableField(exist = false)
    private String apportionCode;
}

