package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @ClassName: AggregatedBillDTO
* @Description:  账单明细汇总
* @author: 张爱苹
* @date: 2024/1/12 17:26
*/
@Data
public class AggregatedBillDTO extends BaseDTO implements Serializable {


    private static final long serialVersionUID = -8397024323087062552L;

    /**
     * S码
     */
    private String scode;

    /**
     * 账期
     */
    private LocalDateTime billingCycle;

    /**
     * 账期开始时间
     */
    private String startDate;

    /**
     * 账期结束时间
     */
    private String endDate;


    /**
     * 聚合粒度
     *
     */
    private String granularity;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账户id
     */
    private String accountId;

    /**
     * 云账户
     */
    private String accountName;

    /**
     * 产品编码
     *
     */
    private String productCode;

    /**
     * 资源实例id
     */
    private String instanceId;

    private String aggregatedId;

    /**
     * 排序字段
     */
    private String sortCode;

    /**
     * 0 :升序 1:降序
     */
    private Integer sort;
    private Integer pageSize = 100;
    private Integer pageNum = 1;


}
