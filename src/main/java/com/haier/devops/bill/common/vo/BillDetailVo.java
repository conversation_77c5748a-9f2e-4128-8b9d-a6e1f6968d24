package com.haier.devops.bill.common.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @ClassName: BillDetailVo
* @Description: 账单明细
* @author: 张爱苹
* @date: 2024/4/12 10:35
*/
@Data
public class BillDetailVo implements Serializable {

    private static final long serialVersionUID = 1944176783117099054L;

    private String aggregatedId;

    /**
     * 账期
     */
    @ExcelProperty("账期")
    @ColumnWidth(8)
    private String billingCycle;

    /**
     * 云厂商
     */
    @ExcelProperty("云厂商")
    @ColumnWidth(8)
    private String vendor;

    /**
     * 产品通用编码
     */
    @ExcelProperty("产品通用编码")
    @ColumnWidth(8)
    private String aliasCode;

    /**
     * 云产品类型
     */
    @ExcelProperty("云产品类型")
    @ColumnWidth(8)
    private String aliasName;

    /**
     * 云账户id
     */
    @ExcelIgnore
    private String accountId;

    /**
     * 云账号
     */
    @ExcelProperty("云账号")
    @ColumnWidth(8)
    private String accountName;

    /**
     * 云产品名称
     */
    @ExcelProperty("云产品名称")
    @ColumnWidth(8)
    private String productName;

    /**
     * 产品
     */
    @ExcelProperty("产品")
    @ColumnWidth(8)
    private String fAppName;

    /**
     * 子产品
     */
    @ExcelProperty("子产品")
    @ColumnWidth(8)
    private String appName;

    /**
     * scode
     */
    @ExcelProperty("S码")
    @ColumnWidth(8)
    private String scode;

    /**
     * 项目简称
     */
    @ExcelProperty("项目简称")
    @ColumnWidth(8)
    private String projectCode;


    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ColumnWidth(8)
    private String projectName;


    /**
     * 资源实例id
     */
    @ExcelProperty("实例ID")
    @ColumnWidth(8)
    private String instanceId;

    /**
     * 资源实例名称
     */
    @ExcelProperty("实例名称")
    @ColumnWidth(8)
    private String instanceName;

    /**
     * 内网ip
     */
    @ExcelProperty("ip/host")
    @ColumnWidth(8)
    private String privateIp;


    /**
     * 产品编码
     *
     */
    @ExcelProperty("产品类型")
    @ColumnWidth(8)
    private String productCode;


    /**
     * 分拆项
     */
    @ExcelIgnore
    private String supplementId;



    /**
     * 资源创建时间
     */
    @ExcelIgnore
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    /**
     * 资源释放时间
     */
    @ExcelIgnore
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime releasingTime;



    /**
     * 金额之和
     */
    @ExcelProperty("消费金额")
    @ColumnWidth(8)
    private Double summer;



    /**
     * 消费类型
     */
    @ExcelIgnore()
    private String subscriptionType;

    /**
     * 消费类型
     */
    @ExcelProperty("消费类型")
    @ColumnWidth(8)
    private String subscriptionTypeName;

    /**
     * 季度
     */
    @ExcelIgnore
    private String quarter;

    private String businessDomains;

    private String currency;

    /**
     * 费用类型
     */
    @ExcelProperty("费用类型")
    @ColumnWidth(8)
    private String expenseType;

}

