package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 云服务项目（ALM）的里程碑，包括所属项目和合同等信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@TableName("bc_milestone")
@Data
@NoArgsConstructor
public class Milestone implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 项目编码
     */
    @SerializedName("almProjectCode")
    @Expose
    private String projectCode;

    /**
     * 项目名称
     */
    @SerializedName("almProjectName")
    @Expose
    private String projectName;

    /**
     * 合同编码
     */
    @SerializedName("contractCode")
    @Expose
    private String contractCode;

    /**
     * 合同名称
     */
    @SerializedName("contractName")
    @Expose
    private String contractName;

    /**
     * 里程碑id
     */
    @SerializedName("almMilestoneId")
    @Expose
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @SerializedName("milestoneName")
    @Expose
    private String milestoneName;


    @SerializedName("milestoneStatus")
    @Expose
    private String milestoneStatus;

    /**
     * 供应商V码
     */
    @SerializedName("supplierCode")
    @Expose
    private String supplierCode;

    /**
     * 供应商名称
     */
    @SerializedName("supplierName")
    @Expose
    private String supplierName;

    /**
     * 里程碑开始日期，yyyy-MM-dd
     */
    @SerializedName("planStartDate")
    @Expose
    private String startDate;

    /**
     * 里程碑结束日期(甲方验收时间) yyyy-MM-dd
     */
    @SerializedName("planEndDate")
    @Expose
    private String endDate;

    /**
     * 乙方交付日期，yyyy-MM-dd
     */
    @SerializedName("partybDeliveryDate")
    @Expose
    private String deliveryDate;

    /**
     * 付款截止日期，yyyy-MM-dd
     */
    @SerializedName("payCloseDate")
    @Expose
    private String paymentDueDate;

    /**
     * 是否已验收 0-未验收  1-已验收
     */
    @SerializedName("flag")
    @Expose
    private Integer flag;

}
