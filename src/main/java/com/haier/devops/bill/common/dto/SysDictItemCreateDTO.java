package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: SysDict
* @Description:  字典项表
* @author: 张爱苹
* @date: 2024/2/4 10:07
*/
@Data
public class SysDictItemCreateDTO implements Serializable {

    private static final long serialVersionUID = -8227274283765165141L;
    /**
     * 字典id
     */
    @NotEmpty
    private String dictId;

    /**
     * 字典项文本
     */
    @NotEmpty
    private String itemText;

    /**
     * 字典项值
     */
    @NotEmpty
    private String itemValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sortOrder;


    /**
     * 状态（1启用 0不启用）
     */
    private Integer status;



}
