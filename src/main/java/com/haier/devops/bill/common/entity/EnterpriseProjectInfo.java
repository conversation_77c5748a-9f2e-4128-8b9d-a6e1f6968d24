package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 企业项目信息
 * <AUTHOR>
 * @since 2023-08-28
 */
@Data
@TableName("cloud_enterprise_project_info")
public class EnterpriseProjectInfo implements Serializable {
    /**
     * S码
     */
    private String appscode;

    /**
     * 出账公司
     */
    private String company;

    /**
     * 业务领域
     */
    private String businessArea;

    /**
     * 对接人工号
     */
    private String manager;

    /**
     * 产业
     */
    private String executive;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modify;

    /**
     * 状态（‘使用中’、‘已退订’）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
