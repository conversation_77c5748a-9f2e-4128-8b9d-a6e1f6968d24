package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (RawBillDailySyncLog)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 16:51:06
 */
@Data
@TableName("bc_raw_bill_daily_sync_log")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="$tableInfo.comment")
public class RawBillDailySyncLog implements Serializable {
    private static final long serialVersionUID = -96591057641307498L;
       
    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
     
       
    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;
     
       
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;
     
       
    @Schema(description = "云厂商字段")
    private String vendor;
     
       
    @Schema(description = "记录的账号信息-账户名称")
    private String accountName;
     
       
    @Schema(description = "记录的账号信息-账户ID")
    private String accountId;
     
       
    @Schema(description = "账单周期")
    private String billingCycle;
     
       
    @Schema(description = " 同步状态 ")
    private String status;
     
       
    @Schema(description = " 同步异常信息 ")
    private String errMsg;
     
       
    @Schema(description = "执行阶段")
    private String stage;
     

}

