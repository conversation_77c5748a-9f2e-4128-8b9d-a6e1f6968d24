package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ReconciliationTask;
import com.haier.devops.bill.common.mapper.ReconciliationTaskMapper;
import com.haier.devops.bill.common.service.ReconciliationTaskService;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* @ClassName: BcReconciliationTaskServiceImpl
* @Description: 调账任务
* @author: 张爱苹
* @date: 2024/3/13 13:40
*/
@Service
public class ReconciliationTaskServiceImpl extends ServiceImpl<ReconciliationTaskMapper, ReconciliationTask> implements ReconciliationTaskService {

    @Override
    public List<ReconciliationTask> queryList(String type) {
        return baseMapper.queryList(type);
    }

    @Override
    public void updateTaskStatus(List<ResourceInstanceVo> resultList, int status,String errorMsg) {
        UpdateWrapper<ReconciliationTask> updateWrapper = new UpdateWrapper();
        updateWrapper.in("id", resultList.stream().map(ResourceInstanceVo::getReconciliationId).collect(Collectors.toList()));
        updateWrapper.set("status", status);
        updateWrapper.set("execute_time",new Date());
        updateWrapper.set("error_msg", errorMsg);
        if(status == 2){
            updateWrapper.setSql("error_num = error_num + 1");
        }
        update(updateWrapper);
    }
}
