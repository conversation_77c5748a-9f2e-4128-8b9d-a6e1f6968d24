package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务器信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@TableName("rc_host_info")
public class HostInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 云账号
     */
    private String accountName;

    /**
     * 主机ID
     */
    private String instanceId;

    /**
     * 主机名称
     */
    private String instanceName;

    /**
     * 主机创建时间
     */
    private LocalDateTime creationTime;

    /**
     * 主机过期时间
     */
    private LocalDateTime expiredTime;

    /**
     * 是否已删除
     */
    private String isDeleted;

    /**
     * 内网IP
     */
    private String privateIp;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * S码
     */
    private String scode;

    /**
     * 所属项目
     */
    private String project;

    /**
     * 所属环境
     */
    private String env;

    /**
     * 主机资源组
     */
    private String resourceGroup;

    /**
     * 操作系统类型
     */
    private String osType;

    /**
     * 操作系统名称
     */
    private String osName;

    /**
     * 操作系统架构
     */
    private String osArch;

    /**
     * 系统镜像id
     */
    private String imageId;

    /**
     * CPU核数
     */
    private Integer cpu;

    /**
     * 是否开启numa
     */
    private String numa;

    /**
     * 内存大小(M)
     */
    private Long memory;

    /**
     * 硬盘大小(M)
     */
    private BigDecimal diskSize;

    /**
     * 磁盘类型
     */
    private String diskType;

    /**
     * 主机状态
     */
    private String hostStatus;

    /**
     * 主机类型，云主机、虚拟机或物理机
     */
    private String hostType;

    /**
     * 宿主机ID
     */
    private String hostInsId;

    /**
     * 所在地区
     */
    private String region;

    /**
     * 所在机房
     */
    private String zone;

    /**
     * 网络类型
     */
    private String networkType;

    /**
     * 所属VPC
     */
    private String vpcId;

    /**
     * 所属子网
     */
    private String subnetId;

    /**
     * 云上主机规格码
     */
    private String classCode;

    /**
     * 主机付费类型
     */
    private String chargeType;

    /**
     * 机架位
     */
    private String rack;

    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 主机品牌
     */
    private String brandName;

    /**
     * 主机型号
     */
    private String model;

    /**
     * 主机SN号
     */
    private String sn;

    /**
     * 主机描述
     */
    private String description;

    /**
     * 资源唯一ID
     */
    private String resourceId;

    private String uniRegionId;

    private String idracIp;

    private String domain;

    private String team;

    private String ownerId;

    private String ownerName;

    /**
     * 运维人员
     */
    private String maintainer;

    /**
     * 0：未接监控，1:zabbix，2:prometheus，3:不接监控, 4:集团感知网监控
     */
    private Integer monitoringMode;

    /**
     * 数据中心
     */
    private String dataCenter;

    /**
     * 启用时间
     */
    private LocalDateTime enableDate;

    /**
     * 退用时间
     */
    private LocalDateTime unsubscribeDate;

    /**
     * 服务器位置
     */
    private String address;

    /**
     * IP地址2
     */
    private String ipAddr2;

    /**
     * GPU信息
     */
    private String gpuInfo;

    /**
     * GPU数量
     */
    private Integer gpuAmount;

    /**
     * GPU型号
     */
    private String gpuModel;

    /**
     * 每个GPU的显存大小,单位为MB
     */
    private Integer gpuMemory;

    /**
     * 标准化主机状态
     */
    private String uniHostStatus;

    /**
     * 订阅状态 1正在订阅 0已退订
     */
    private Integer subscriptionStatus;

    /**
     * 运维团队
     */
    private String maintainingTeam;
}
