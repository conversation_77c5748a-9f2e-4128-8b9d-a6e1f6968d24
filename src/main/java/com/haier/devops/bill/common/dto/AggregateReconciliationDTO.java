package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
* @ClassName: AggregateReconciliationDTO
* @Description: TODO
* @author: 张爱苹
* @date: 2024/3/11 10:01
*/
@Data
public class AggregateReconciliationDTO implements Serializable {
    private static final long serialVersionUID = 3095375545480746234L;

    /**
     * @Description: 最新S码
     */
    private String scode;

    /**
    * @Description: 聚合id
    */
    @NotEmpty
    private String aggregatedId;

    /**
     * @Description: 聚合区间信息
     */
    private List<ReconciliationDTO> list;

    private ResourceInstanceDTO resourceInstanceDTO;

    private String startDate;

    private String endDate;

    private String reconciliationType;

    private String vendor;

}
