package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.vo.MilestoneVo;

import java.util.List;

/**
 * <p>
 * 云服务项目（ALM）的里程碑，包括所属项目和合同等信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
public interface MilestoneService extends IService<Milestone> {
    /**
     * 批量保存或更新
     * @param milestones
     * @return
     */
    boolean batchSaveOrUpdateByParams(List<Milestone> milestones);

    List<Milestone> queryValidMilestones();

    /**
     * 根据云厂商和账期查询里程碑
     * @param vendor
     * @param billingCycle
     * @return
     */
    List<MilestoneVo> queryByVendorAndBillingCycle(String vendor, String billingCycle);
}
