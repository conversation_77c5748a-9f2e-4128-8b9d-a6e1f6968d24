package com.haier.devops.bill.common.task.data_completion;

import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.controller.ResponseEnum;
import com.haier.devops.bill.util.RedisUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @ClassName: CommonUtil
* @Description: 数据补全公共方法
* @author: 张爱苹
* @date: 2024/1/30 18:55
*/
@Service
public class CommonUtil {

    @Resource
    HdsOpenApi hdsOpenApi;

    @Resource
    RedisUtils redisUtils;

    /**
     * 根据项目代码获取项目信息
     *
     * @param scode 项目代码
     * @return 项目信息对象
     */
    public HdsOpenApi.Project getProjectInfo(String scode) {
        // 从Redis中获取项目信息列表
        List<HdsOpenApi.Project> projectInfos = JSONObject.parseArray(String.valueOf(redisUtils.get("projectInfos")), HdsOpenApi.Project.class);

        // 如果项目信息列表为空
        if (CollectionUtils.isEmpty(projectInfos)) {
            // 获取项目信息列表
            projectInfos = hdsOpenApi.queryProjectsInfo().getData();

            // 如果项目信息列表不为空
            if (CollectionUtils.isNotEmpty(projectInfos)) {
                // 将项目信息列表存储到Redis中
                redisUtils.set("projectInfos", JSONObject.toJSONString(projectInfos), CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
            }
        }
        // 将项目信息列表转换为以项目代码为键、项目信息为值的映射
        Map<String, HdsOpenApi.Project> projectMap = projectInfos.stream()
                .collect(Collectors.toMap(HdsOpenApi.Project::getAlmSCode, Function.identity()));

        // 返回项目信息对象
        return projectMap.get(scode);
    }
    
    /**
     * 根据项目代码获取ALM项目信息，使用1天缓存
     *
     * @param almSCode 项目代码
     * @return ALM项目信息对象
     */
    public HdsOpenApi.AlmProject getAlmProjectInfo(String almSCode) {
        if (almSCode == null || almSCode.isEmpty()) {
            return null;
        }
        
        // 缓存键
        String cacheKey = "almProject:" + almSCode;
        
        // 从Redis中获取ALM项目信息
        String cachedAlmProject = (String) redisUtils.get(cacheKey);
        
        if (cachedAlmProject != null && !cachedAlmProject.equals("null")) {
            // 如果缓存中有数据，则解析并返回
            return JSONObject.parseObject(cachedAlmProject, HdsOpenApi.AlmProject.class);
        }
        
        // 如果缓存中没有数据，则调用API获取
        HdsOpenApi.AlmProjectWrapper wrapper = hdsOpenApi.queryAlmProject(almSCode);
        
        if (wrapper != null && wrapper.getCode() == ResponseEnum.SUCCESS.getCode() && wrapper.getData() != null) {
            // 将ALM项目信息存储到Redis中，缓存1天
            redisUtils.set(cacheKey, JSONObject.toJSONString(wrapper.getData()), CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
            return wrapper.getData();
        } else {
            // 如果API调用失败或返回空数据，缓存null值1小时，避免频繁调用API
            redisUtils.set(cacheKey, "null", 3600000L, TimeUnit.MILLISECONDS);
            return null;
        }
    }
}
