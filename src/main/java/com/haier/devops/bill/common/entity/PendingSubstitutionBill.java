package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 待替换账单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@Builder
@TableName("bc_pending_substitution_bill")
public class PendingSubstitutionBill implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 替换任务id
     */
    private String subTaskId;

    private String aggregatedId;

    private String scode;

    /**
     * 数据粒度
     */
    private String granularity;

    /**
     * 账期
     */
    private LocalDateTime billingCycle;

    /**
     * 金额
     */
    private BigDecimal summer;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账户名
     */
    private String accountName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 分拆项id
     */
    private String supplementId;

    /**
     * 收费类型 1按量 2包年包月
     */
    private String subscriptionType;

    private LocalDateTime creationTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
