package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.ExchangeRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: ExchangeRateMapper
 * @Description: 汇率Mapper接口
 * @author: System
 * @date: 2025/06/30
 */
@Mapper
public interface ExchangeRateMapper extends BaseMapper<ExchangeRate> {

    /**
     * 根据货币对和日期查询汇率
     * @param baseCurrency 基础货币
     * @param targetCurrency 目标货币
     * @param rateDate 汇率日期
     * @return 汇率信息
     */
    ExchangeRate selectByDateAndCurrency(@Param("baseCurrency") String baseCurrency, 
                                       @Param("targetCurrency") String targetCurrency, 
                                       @Param("rateDate") Date rateDate);

    /**
     * 查询指定日期的所有汇率
     * @param rateDate 汇率日期
     * @return 汇率列表
     */
    List<ExchangeRate> selectByDate(@Param("rateDate") Date rateDate);

    /**
     * 批量插入或更新汇率数据
     * @param exchangeRates 汇率列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<ExchangeRate> exchangeRates);

    /**
     * 删除指定日期的汇率数据
     * @param rateDate 汇率日期
     * @return 影响行数
     */
    int deleteByDate(@Param("rateDate") Date rateDate);
}
