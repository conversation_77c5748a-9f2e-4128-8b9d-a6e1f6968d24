package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 财务单元分配规则
 * @Author: jeecg-boot
 * @Date:   2024-03-12
 * @Version: V1.0
 */
@Data
@TableName("cloud_costunit_move_rule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="财务单元分配规则")
public class CloudCostunitMoveRule implements Serializable {


    private static final long serialVersionUID = 4113750958443799118L;
    /**主键*/
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键")
    private java.lang.Integer id;
    /**云厂商*/
    @Schema(description = "云厂商")
    private java.lang.String cloud;
    /**云账号*/
    @Schema(description = "云账号")
    private java.lang.String account;
    /**S码*/
    @Schema(description = "S码")
    private java.lang.String appscode;
    /**优先级 （null为S码独享产品）*/
    @Schema(description = "优先级 （null为S码独享产品）")
    private java.lang.String priority;
    /**财务单元编号（阿里云专用）*/
    @Schema(description = "财务单元编号（阿里云专用）")
    private java.lang.String costMove;
    /**商品名称*/
    @Schema(description = "商品名称")
    private java.lang.String commoditycode;
    /**资源昵称*/
    @Schema(description = "资源昵称")
    private java.lang.String resourcenick;
    /**资源标签*/
    @Schema(description = "资源标签")
    private java.lang.String resourcetag;
    /**资源ID*/
    @Schema(description = "资源ID")
    private java.lang.String resourceid;
    /**资源组*/
    @Schema(description = "资源组")
    private java.lang.String resourcegroup;
    /**状态 "on"为启用*/
    @Schema(description = "状态 on为启用")
    private java.lang.String status;
    /**备注*/
    @Schema(description = "备注")
    private java.lang.String remark;
    /**规则值*/
    @Schema(description = "规则值")
    private java.lang.String ruleValue;
}
