package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.ResourceInstanceDTO;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.ResourceInstance;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
* @ClassName: ResourceInstanceMapper
* @Description: 资源实例
* @author: 张爱苹
* @date: 2024/3/12 13:39
*/
@Resource
public interface ResourceInstanceMapper extends BaseMapper<ResourceInstance> {

   List<CmdbProductOverview> listByPage(@Param("dto") ResourceInstanceDTO dto);

   List<ResourceInstanceVo> queryResourceInstanceList(@Param("item")CmdbProductOverview dto);

   List<Map> queryInfoByAccountId(String accountIdentify);
}
