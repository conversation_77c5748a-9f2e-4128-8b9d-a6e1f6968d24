package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: IamProjects
* @Description: 项目
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
@TableName("bc_iam_projects")
@Data
@Builder
public class IamProjects implements Serializable {

    private static final long serialVersionUID = 7873685745416707107L;

    private String id;

    private String domainId;

    private String name;

    private String delFlag;

    private String accountName;

}
