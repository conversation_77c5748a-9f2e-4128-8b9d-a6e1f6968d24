package com.haier.devops.bill.common.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.haier.devops.bill.aws.commitment.vo.AggregatedBillCommitmentVo;
import com.haier.devops.bill.common.dto.*;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.enums.ReconciliationEnum;
import com.haier.devops.bill.common.enums.ReconciliationStatusEnum;
import com.haier.devops.bill.common.enums.RuleFieldEnum;
import com.haier.devops.bill.common.enums.RuleTypeEnum;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.*;
import com.haier.devops.bill.common.param.DetailBillParam;
import com.haier.devops.bill.common.service.AdjustmentRecordService;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.AggregatedService;
import com.haier.devops.bill.common.vo.*;
import com.haier.devops.bill.privelege.DomainPrivilegeFinder;
import com.haier.devops.bill.substitution.vo.PassBillVo;
import com.haier.devops.bill.util.AuthUtil;
import com.haier.devops.bill.util.DateUtil;
import com.haier.devops.bill.util.ListSplitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName: AggregatedBillServiceImpl
 * @Description: 账单明细汇总表 服务实现类
 * @author: 张爱苹
 * @date: 2024/1/31 10:23
 */
@Service
@Slf4j
public class AggregatedBillServiceImpl extends ServiceImpl<AggregatedBillMapper, AggregatedBill> implements AggregatedBillService {
    private Logger logger = LoggerFactory.getLogger(AggregatedBillServiceImpl.class);
    private static final String YEAR_MONTH_FORMAT = "yyyy-MM";

    private final DomainPrivilegeFinder domainPrivilegeFinder;


    private PlatformTransactionManager transactionManager;

    @Autowired
    private AdjustmentRecordService adjustmentRecordService;

    @Autowired
    private CmdbProductOverviewMapper cmdbProductOverviewMapper;

    @Autowired
    private ResourceInstanceMapper resourceInstanceMapper;

    @Autowired
    private AggregatedService aggregatedService;

    @Resource
    AggregatedBillMapper aggregatedBillMapper;

    private final RefinedRawBillMapper refinedRawBillMapper;
    private final OrphanParentReplacingLogMapper replacingLogMapper;

    public AggregatedBillServiceImpl(PlatformTransactionManager transactionManager,
                                     RefinedRawBillMapper refinedRawBillMapper,
                                     OrphanParentReplacingLogMapper replacingLogMapper,  DomainPrivilegeFinder domainPrivilegeFinder) {
        this.transactionManager = transactionManager;
        this.refinedRawBillMapper = refinedRawBillMapper;
        this.replacingLogMapper = replacingLogMapper;
        this.domainPrivilegeFinder = domainPrivilegeFinder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScode(Integer id, String scode) throws Exception {
        AggregatedBill aggregatedBill = getById(id);
        if (aggregatedBill == null) {
            throw new Exception(id + "账单明细汇总数据不存在");
        }
        String originalScode = aggregatedBill.getScode();
        if (originalScode.equals(scode)) {
            throw new Exception(id + "账单明细汇总数据scode未发生变化");
        }
        try {
            aggregatedBill.setScode(scode);
            aggregatedBill.setUpdateTime(new Date());
            baseMapper.updateById(aggregatedBill);
            String aggregatedId = aggregatedBill.getAggregatedId();
            //判断调账的汇总数据是否是最新的一次汇总
            AggregatedBill latestAggregatedBill = getLatestAggregatedBill(aggregatedId);
            if (latestAggregatedBill.getId().equals(aggregatedBill.getId())) {
                //更新cmdb汇总表
                cmdbProductOverviewMapper.updateCmdbAggregatedBill(scode, aggregatedId);
            }
            //插入调账记录
            User user = LoginContextHolder.getCurrentUser();
            AdjustmentRecord adjustmentRecord = AdjustmentRecord.builder().aggregatedId(aggregatedId)
                   .createBy(user.getUserCode()).build();
            adjustmentRecordService.save(adjustmentRecord);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("updateScode error:", e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    @Override
    public PageInfo<AggregatedBillVo> listByPage(BillGatheredDTO dto) throws Exception {
        try {
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () -> baseMapper.listByPage(dto)
            );
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("listByPage error, AlarmNoticeGroupDTO:{}", dto, e.getMessage(), e.getMessage(), e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    public void updateScodeBatch(AggregatedBillEditDTO dto) throws Exception {
        for (Integer id : dto.getIdList()) {
            updateScode(id, dto.getScode());
        }

    }

    @Override
    public List<Map> countByS() {
        return baseMapper.countByS();
    }

    @Override
    public List<String> getInstanceIdList(String vendor, String instanceId) {
        LambdaQueryWrapper<CmdbProductOverview> lambdaQueryWrapper = new LambdaQueryWrapper<CmdbProductOverview>().eq(CmdbProductOverview::getVendor, vendor);
        if (!StringUtils.isEmpty(instanceId)) {
            lambdaQueryWrapper.like(CmdbProductOverview::getInstanceId, instanceId);
        }
        List<CmdbProductOverview> list =
                cmdbProductOverviewMapper.selectList(lambdaQueryWrapper);
        return list.stream().map(CmdbProductOverview::getInstanceId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<AggregatedBillInfoVo> getInfoByAggregatedId(String aggregatedId) {
        List<AggregatedBillInfoVo> list = baseMapper.getInfoByAggregatedId(aggregatedId);
        List<AggregatedBillInfoVo> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(list)){
            String scode = list.get(0).getScode();
            String startDate = list.get(0).getBillingCycle();
            String endDate = startDate;
            if (list.size() > 1) {
                for (int i = 1; i < list.size(); i++) {
                    AggregatedBillInfoVo billInfoVo = list.get(i);
                    if(!billInfoVo.getScode().equals(scode)){
                        resultList.add(createAggregatedBillInfoVo(scode, startDate, endDate, aggregatedId));
                        scode = billInfoVo.getScode();
                        startDate = billInfoVo.getBillingCycle();
                        endDate = startDate;
                    }else{
                        endDate = billInfoVo.getBillingCycle();
                    }
                    if(i ==  list.size()-1){
                        resultList.add(createAggregatedBillInfoVo(scode, startDate, endDate, aggregatedId));
                    }
                }
            } else {
                resultList.add(createAggregatedBillInfoVo(scode, startDate, endDate, aggregatedId));
            }
        }
        return resultList;
    }

    // 创建AggregatedBillInfoVo对象的方法
    private AggregatedBillInfoVo createAggregatedBillInfoVo(String scode, String startDate, String endDate, String aggregatedId) {
        AggregatedBillInfoVo vo = new AggregatedBillInfoVo();
        vo.setScode(scode);
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);
        vo.setAggregatedId(aggregatedId);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reconciliation(AggregateReconciliationDTO dto) throws Exception {
        //Pre-check
        AggregatedDTO aggregatedDTO = preCheck(dto);
        ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
        aggregatedService.executeReconciliation(aggregatedDTO, resourceInstanceDTO,dto.getVendor());
    }

    private AggregatedDTO preCheck(AggregateReconciliationDTO dto) throws Exception {
        try {
            //校验当前聚合实例是否有未完成的调账
            Long count = adjustmentRecordService.getNotCompleteReconciliationCount(Arrays.asList(dto.getAggregatedId()));
            if (count > 0) {
                throw new RuntimeException("当前聚合实例有未完成的调账");
            }
            String aggregatedId = dto.getAggregatedId();
            CmdbProductOverview cmdbProductOverview = cmdbProductOverviewMapper.getOneByAggregatedId(aggregatedId);
            if (cmdbProductOverview == null) {
                throw new RuntimeException("未找到聚合实例");
            }
            dto.setScode(cmdbProductOverview.getScode());
            String vendor = cmdbProductOverview.getVendor();
            List<ReconciliationDTO> list = dto.getList();
            //根据list中startDate升序排序
            list.sort(Comparator.comparing(ReconciliationDTO::getStartDate));

            String startBillingCycle = list.get(0).getStartDate();
            String endBillingCycle = list.get(list.size() - 1).getEndDate();
            dto.setStartDate(startBillingCycle);
            dto.setEndDate(endBillingCycle);
            //查询账期
            AggregatedBillInfoVo aggregatedBillInfoVo = baseMapper.getBilingCycleByAggregatedId(aggregatedId);
            if (startBillingCycle.compareTo(aggregatedBillInfoVo.getStartDate()) != 0 || endBillingCycle.compareTo(aggregatedBillInfoVo.getEndDate()) != 0) {
                throw new RuntimeException("您选择的账期起止时间与实际实例的账期起止时间不一致，请检查修改后重新保存。");
            }
            String nextStartDate = startBillingCycle;
            String appScode = "";
            List<AdjustmentRecord> recordList = new ArrayList<>();
            User user = LoginContextHolder.getCurrentUser();
            //遍历list，判断账期区间是否完整
            for (int i = 0; i < list.size(); i++) {
                ReconciliationDTO reconciliationDTO = list.get(i);
                reconciliationDTO.setAggregatedId(aggregatedId);
                String startDate = reconciliationDTO.getStartDate();
                //判断nextStartDate是否等于startDate
                if (nextStartDate.compareTo(startDate) > 0) {
                    throw new RuntimeException("您选择的账期时间段之间存在交叉，请检查修改后重新保存。");
                }
                if (nextStartDate.compareTo(startDate) < 0) {
                    throw new RuntimeException("您选择的账期时间段之间存在断点，请检查修改后重新保存。");
                }
                String endDate = reconciliationDTO.getEndDate();
                //比较startDate和endDate
                //如果startDate大于endDate，则抛出异常
                if (startDate.compareTo(endDate) > 0) {
                    throw new RuntimeException("开始日期不能大于结束日期");
                }
                if (i == list.size() - 1) {
                    appScode = reconciliationDTO.getScode();
                }
                //获取endDate的下一天
                nextStartDate = DateUtil.getNextDayOf(endDate);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            List<AggregatedBillInfoVo> aggregatedBillInfoVoList =getInfoByAggregatedId(aggregatedId);
            AdjustmentRecord adjustmentRecord = AdjustmentRecord.builder().aggregatedId(aggregatedId).type("单个实例调账").reconciliationType(dto.getReconciliationType())
                    .newContent(objectMapper.writeValueAsString(list)).originalContent(objectMapper.writeValueAsString(aggregatedBillInfoVoList)).createBy(user.getUserCode()).reconciliationStatus(ReconciliationStatusEnum.INPROGRESS.getKey()).build();
            recordList.add(adjustmentRecord);
            //插入调账记录
            adjustmentRecordService.saveBatch(recordList);
            AggregatedDTO aggregatedDTO = AggregatedDTO.builder().vendor(vendor).aggregatedId(aggregatedId).list(list).scode(dto.getScode()).appScode(appScode).recordList(recordList).build();
            return aggregatedDTO;
        } catch (Exception e) {
            logger.error("预调账失败", e, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reconciliationBatch(ReconciliationBatchDTO dto) throws Exception {
        //预处理
        AggregatedDTO aggregatedDTO = preCheckBatch(dto);
        //执行调账
        aggregatedService.executeReconciliation(dto, aggregatedDTO,dto.getVendor());

    }

    private AggregatedDTO preCheckBatch(ReconciliationBatchDTO dto) throws Exception {
        try {
            String range = dto.getRange();
            List<String> list = dto.getAggregateIdList();
            //校验当前聚合实例是否有未完成的调账
            Long count = adjustmentRecordService.getNotCompleteReconciliationCount(list);
            if (count > 0) {
                throw new RuntimeException("当前实例有未完成的调账");
            }
            //部分调账
            List<String> newAggregatedIdList = new ArrayList<>();
            String bilingCycle = "";
            String type = "全部调账";
            if (range.equals(ReconciliationEnum.PART.getKey())) {
                if (StringUtils.isEmpty(dto.getStartDate()) || StringUtils.isEmpty(dto.getEndDate())) {
                    throw new RuntimeException("调账日期不能为空");
                }
                String nowDate = DateUtil.getCurrentDay();
                if(dto.getStartDate().compareTo(nowDate) > 0 || dto.getEndDate().compareTo(nowDate) > 0){
                    throw new RuntimeException("账期起止日期不能大于当前日期");
                }
                if (dto.getStartDate().compareTo(dto.getEndDate()) > 0) {
                    throw new RuntimeException("开始日期不能大于结束日期");
                }
                bilingCycle = dto.getStartDate() + "——" + dto.getEndDate();
                //查调账日期包含账单汇总最新日期的汇总id
                newAggregatedIdList = getLatestAggregatedIdList(dto.getStartDate(),dto.getEndDate(), list);
                if (list.size() == newAggregatedIdList.size()) {
                    dto.setUpdate(true);
                }
                type = "部分调账";
            } else {
                bilingCycle = "全部";
                newAggregatedIdList
                        .addAll(list);
                dto.setUpdate(true);
            }
            //插入调账记录
            List<AdjustmentRecord> recordList = insertLog(dto, list, bilingCycle,type);
            AggregatedDTO aggregatedDTO = AggregatedDTO.builder().recordList(recordList).aggregatedIdList(newAggregatedIdList).build();
            return aggregatedDTO;
        } catch (Exception e) {
            logger.error("预调账失败", e, e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<String> getLatestAggregatedIdList(String startDate,String endDate, List<String> aggregatedIdList) throws Exception {
        List<List<String>> list = ListSplitter.splitList(aggregatedIdList, 2);
        ExecutorService executor = Executors.newFixedThreadPool(list.size());
        CountDownLatch cd = new CountDownLatch(list.size());
        AtomicInteger successCount = new AtomicInteger(0);
        List<String> newAggregatedIdList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            List<String> subList = list.get(i);
            executor.submit(() -> {
                try {
                    newAggregatedIdList.addAll(baseMapper.getLatestAggregatedIdList(startDate,endDate, subList));
                    successCount.getAndAdd(1);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    cd.countDown();
                }
            });
        }
        try {
            cd.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new RuntimeException("异常");
        }
        executor.shutdown();
        if (successCount.get() != list.size()) {
            throw new RuntimeException("异常");
        }
        return newAggregatedIdList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reconciliationAll(ReconciliationBatchDTO dto) throws Exception {
        ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
        if (resourceInstanceDTO == null) {
            throw new RuntimeException("参数缺失！");
        }
        //如果是全部调账且调账类型是原生字段
        if (resourceInstanceDTO.getReconciliationType().equals(ReconciliationEnum.ALL.getKey()) && resourceInstanceDTO.getRuleType().equals(RuleTypeEnum.NATIVEFIELD.getKey())) {
            //如果S码未发生变化，不允许调账
            if (resourceInstanceDTO.getRuleFieldValue().contains(dto.getScode())) {
                throw new RuntimeException("调账S码未发生改变，不允许调账");
            }
        }
        //查询所有资源实例
        List<CmdbProductOverview> list = resourceInstanceMapper.listByPage(resourceInstanceDTO);

        if (!CollectionUtils.isEmpty(list)) {
            dto.setAggregateIdList(list.stream().map(CmdbProductOverview::getAggregatedId).collect(Collectors.toList()));
            //预处理
            AggregatedDTO aggregatedDTO = preCheckBatch(dto);
            //执行调账
            aggregatedService.executeReconciliation(dto, aggregatedDTO,dto.getVendor());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reconciliationPart(ReconciliationBatchDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(dto.getAggregateIdList())) {
            throw new RuntimeException("参数缺失！");
        }
        ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
        if (resourceInstanceDTO == null) {
            throw new RuntimeException("参数缺失！");
        }
        if (resourceInstanceDTO.getRuleField().equals(RuleFieldEnum.RESOURCE_GROUP.getKey()) && resourceInstanceDTO.getReconciliationType().equals(ReconciliationEnum.PART.getKey())) {
            throw new RuntimeException("资源组规则不允许部分调账");
        }
        //预处理
        AggregatedDTO aggregatedDTO = preCheckBatch(dto);
        //执行调账
        aggregatedService.executeReconciliation(dto, aggregatedDTO,dto.getVendor());
    }

    @Override
    public void reconciliationSingle(AggregateReconciliationDTO dto) throws Exception {
        ResourceInstanceDTO resourceInstanceDTO = dto.getResourceInstanceDTO();
        if (resourceInstanceDTO == null) {
            throw new RuntimeException("参数缺失！");
        }
        AggregatedDTO aggregatedDTO = preCheck(dto);
        aggregatedService.executeReconciliation(aggregatedDTO, resourceInstanceDTO,dto.getVendor());
    }

    @Override
    public void reconciliationGlobal(ReconciliationBatchDTO dto) throws Exception {
        CmdbProductOverviewDTO cmdbProductOverviewDTO = dto.getCmdbProductOverviewDTO();
        if (cmdbProductOverviewDTO == null) {
            throw new RuntimeException("参数缺失！");
        }
        //查询所有资源实例
        List<CmdbProductOverview> list = cmdbProductOverviewMapper.listByPage(cmdbProductOverviewDTO);
        if (!CollectionUtils.isEmpty(list)) {
            dto.setAggregateIdList(list.stream().map(CmdbProductOverview::getAggregatedId).collect(Collectors.toList()));
            //预处理
            AggregatedDTO aggregatedDTO = preCheckBatch(dto);
            //执行调账
            aggregatedService.executeReconciliation(dto, aggregatedDTO,dto.getVendor());
        }
    }


    public List<AdjustmentRecord> insertLog(ReconciliationBatchDTO dto, List<String> aggregatedIdList, String bilingCycle,String type) throws Exception {
        List<AdjustmentRecord> recordList = new ArrayList<>();
        List<List<String>> list = ListSplitter.splitList(aggregatedIdList, 500);
        ExecutorService executorService = Executors.newFixedThreadPool(list.size());
        CountDownLatch latch = new CountDownLatch(list.size());
        AtomicInteger successCount = new AtomicInteger(0);
        User user = LoginContextHolder.getCurrentUser();
        ObjectMapper objectMapper = new ObjectMapper();
        for (int i = 0; i < list.size(); i++) {
            List<String> subList = list.get(i);
            executorService.submit(() -> {
                try {
                    subList.stream().forEach(aggregatedId -> {
                        List<AggregatedBillInfoVo> aggregatedBillInfoVoList =getInfoByAggregatedId(aggregatedId);
                        ReconciliationDTO reconciliationDTO = new ReconciliationDTO();
                        BeanUtils.copyProperties(dto,reconciliationDTO);
                        AdjustmentRecord adjustmentRecord = null;
                        try {
                            adjustmentRecord = AdjustmentRecord.builder().aggregatedId(aggregatedId).type(type).reconciliationType(dto.getReconciliationType())
                                    .originalContent(objectMapper.writeValueAsString(aggregatedBillInfoVoList)).newContent(objectMapper.writeValueAsString(Arrays.asList(reconciliationDTO))).createBy(user.getUserCode()).reconciliationStatus(ReconciliationStatusEnum.INPROGRESS.getKey()).build();
                        } catch (JsonProcessingException e) {
                            e.printStackTrace();
                        }
                        recordList.add(adjustmentRecord);
                    });
                    adjustmentRecordService.saveBatch(recordList);
                    successCount.getAndAdd(1);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
            throw new RuntimeException("异常");
        }
        executorService.shutdown();
        if (successCount.get() != list.size()) {
            throw new RuntimeException("异常");
        }
        return recordList;
    }


    private AggregatedBill getLatestAggregatedBill(String aggregatedId) {
        AggregatedBill latestAggregatedBill =
                list(new QueryWrapper<AggregatedBill>().eq("aggregated_id", aggregatedId
                ).orderByDesc("billing_cycle")).get(0);
        return latestAggregatedBill;
    }

    /**
     * 获取技术架构账单
     *
     * @param dto
     * @return
     */
    @Override
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<TechnicalArchitectureBillVo> getBillOfBillingCycle(AggregatedBillDTO dto) {
        return aggregatedBillMapper.getBillOfBillingCycle(
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(dto.getPageNum(), dto.getPageSize()), dto);
    }

    @Override
    public void saveOrUpdateWhenAggregatingInDay(List<AggregatedBill> list) {
        SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AggregatedBill> queryWrapper =
                    Wrappers.<AggregatedBill>lambdaQuery()
                            .eq(AggregatedBill::getAggregatedId, entity.getAggregatedId())
                            .eq(AggregatedBill::getTaskId, entity.getTaskId())
                            .eq(AggregatedBill::getGranularity, entity.getGranularity())
                            .eq(AggregatedBill::getBillingCycle, entity.getBillingCycle());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AggregatedBill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AggregatedBill::getAggregatedId, entity.getAggregatedId());
            lambdaUpdateWrapper.eq(AggregatedBill::getTaskId, entity.getTaskId());
            lambdaUpdateWrapper.eq(AggregatedBill::getGranularity, entity.getGranularity());
            lambdaUpdateWrapper.eq(AggregatedBill::getBillingCycle, entity.getBillingCycle());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });

    }

    @Override
    public BigDecimal getCurrentYearTotalAmount(List<String> scodes, String... currency) {
        BigDecimal currentYearTotalAmount = aggregatedBillMapper.getCurrentYearTotalAmount(scodes, currency);
        return null == currentYearTotalAmount ? BigDecimal.ZERO : currentYearTotalAmount;
    }

    @Override
    public BigDecimal getLastYearTotalAmount(List<String> scodes, String... currency) {
        String firstDayOfLastYear = DateUtil.getFirstDayOfLastYearStr();
        String firstDayOfCurrentMonthLastYear = DateUtil.getFirstDayOfCurrentMonthLastYear();
        String todayLastYear = DateUtil.getTodayLastYear();
        BigDecimal lastYearTotalAmount = aggregatedBillMapper
                .getLastYearTotalAmount(firstDayOfLastYear,
                        firstDayOfCurrentMonthLastYear,
                        todayLastYear,
                        scodes,
                        currency);
        return null == lastYearTotalAmount ? BigDecimal.ZERO : lastYearTotalAmount;
    }

    @Override
    public BigDecimal getLastNthMonthTotalAmount(List<String> scodes, int nth, String... currency) {
        BigDecimal lastNthMonthTotalAmount = aggregatedBillMapper.getLastNthMonthTotalAmount(scodes,null, nth, currency);
        return null == lastNthMonthTotalAmount ? BigDecimal.ZERO : lastNthMonthTotalAmount;
    }

    @Override
    public BigDecimal getLastNthMonthTotalAmount(List<String> scodes, String vendor, int nth,String currency) {
        BigDecimal lastNthMonthTotalAmount = aggregatedBillMapper.getLastNthMonthTotalAmount(scodes, vendor, nth,currency);
        return null == lastNthMonthTotalAmount ? BigDecimal.ZERO : lastNthMonthTotalAmount;
    }

    @Override
    public BigDecimal getLastNthMonthTotalAmount(List<String> scodes, String vendor, int nth, BigDecimal exchangeRate) {
        BigDecimal lastNthMonthTotalAmount = aggregatedBillMapper.getLastNthMonthTotalAmountAll(scodes, vendor, nth,exchangeRate);
        return null == lastNthMonthTotalAmount ? BigDecimal.ZERO : lastNthMonthTotalAmount;
    }

    @Override
    public BigDecimal getCurrentMonthTotalAmountUntilYesterday(List<String> scodes, String... currency) {
        BigDecimal currentMonthTotalAmountUntilYesterday = aggregatedBillMapper.getCurrentMonthTotalAmountUntilYesterday(scodes, currency);
        return null == currentMonthTotalAmountUntilYesterday ? BigDecimal.ZERO : currentMonthTotalAmountUntilYesterday;
    }

    @Override
    public BigDecimal getTotalAmountOfYesterday(List<String> scodes, String... currency) {
        BigDecimal totalAmountOfYesterday = aggregatedBillMapper.getTotalAmountOfYesterday(scodes, currency);
        return null == totalAmountOfYesterday ? BigDecimal.ZERO : totalAmountOfYesterday;
    }

    @Override
    public List<DayBillAnalysisVo> getMonthAggregatedBill(List<String> scodes, String vendor, String month, String... currency) {
        return aggregatedBillMapper.getMonthAggregatedBill(scodes, vendor, month, currency);
    }

    @Override
    public PageInfo<AggregatedBill> getAliyunPaasPendingSubstitutionBills(
            String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageHelper.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getAliyunPaasPendingSubstitutionBills(billingCycle));
        }
    }

    @Override
    public PageInfo<AggregatedBill> getBigDataHdopPaasPendingSubstitutionBills(
            String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getBigDataHdopPaasPendingSubstitutionBills(billingCycle));
        }
    }

    @Override
    public PageInfo<AggregatedBill> getBigDataStarrocksPaasPendingSubstitutionBills(
            String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getBigDataStarrocksPaasPendingSubstitutionBills(billingCycle));
        }
    }

    @Override
    public PageInfo<AggregatedBill> getAliyunDedicatedPaasPendingSubstitutionBills(String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getAliyunDedicatedPaasPendingSubstitutionBills(billingCycle)
            );
        }
    }

    @Override
    public PageInfo<AggregatedBill> getHuaweiPaasPendingSubstitutionBills(String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getHuaweiPaasPendingSubstitutionBills(billingCycle));
        }
    }

    @Override
    public PageInfo<AggregatedBill> getHuaweiUcsPendingSubstitutionBills(String billingCycle, int page, int perPage) {
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            return pager.doSelectPageInfo(
                    () -> aggregatedBillMapper.getHuaweiUcsPendingSubstitutionBills(billingCycle));
        }
    }

    /**
     * 获取云监控待处理的代金券列表
     *
     * @param billingCycle 账单周期
     * @param page 页码
     * @param perPage 每页显示的条数
     * @return 返回一个包含代金券信息的分页对象
     */
    @Override
    public PageInfo<AggregatedBill> getCloudMonitorPendingSubstitutionBills(
            String billingCycle, int page, int perPage) {
        // 使用PageMethod.startPage方法创建一个分页对象，并设置当前页码和每页显示的条数
        try (Page<AggregatedBill> pager = PageMethod.startPage(page, perPage)) {
            // 调用aggregatedBillMapper的getCloudMonitorPendingSubstitutionBills方法获取代金券列表，并返回分页信息
            return pager.doSelectPageInfo(
                    () ->
                            aggregatedBillMapper.getCloudMonitorPendingSubstitutionBills(
                                    billingCycle));
        }
    }

    @Override
    public PageInfo<AggregatedBill> getAwsCommitmentPendingSubstitutionBills(String billingCycle, int page, int perPage) {
        return PageMethod
                .startPage(page, perPage)
                .doSelectPageInfo(() -> aggregatedBillMapper.getAwsCommitmentPendingSubstitutionBills(billingCycle));
    }

    @Override
    public PageInfo<AggregatedBillCommitmentVo> getAwsPendingSubstitutionBills(String billingCycle, int page, int perPage) {
        return PageMethod
                .startPage(page, perPage)
                .doSelectPageInfo(() -> aggregatedBillMapper.getAwsPendingSubstitutionBills(billingCycle));
    }

    @Override
    public int cleanAliyunPassBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanAliyunPassDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanAliyunPassMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanCloudMonitorBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanCloudMonitorDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanCloudMonitorMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanBigDataHdopPassBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanBigDataHdopPassDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanBigDataHdopPassMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanBigDataStarrocksPassBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanBigDataStarrocksPassDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanBigDataStarrocksPassMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanAliyunDedicatedPassBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanAliyunDedicatedPassDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanAliyunDedicatedPassMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanHuaweiPassBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanHuaweiPassDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanHuaweiPassMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int cleanHuaweiUcsBills(String billingCycle) {
        int dayCnt = aggregatedBillMapper.cleanHuaweiUcsDayBills(billingCycle);
        int monthCnt = aggregatedBillMapper.cleanHuaweiUcsMonthBills(billingCycle);
        return dayCnt + monthCnt;
    }

    @Override
    public int clearDirtyData(String subTaskId) {
        return aggregatedBillMapper.delete(
                new LambdaQueryWrapper<AggregatedBill>()
                        .eq(AggregatedBill::getSubTaskId, subTaskId)
        );
    }

    @Override
    public List<PassBillVo> getRecentHalfYearPassBill(String vendor,
                                                      List<String> scodes,
                                                      String startDate,
                                                      String endDate,
                                                      String... currency) {
        return aggregatedBillMapper.getPassBill(vendor,scodes, startDate, endDate,currency);
    }

    @Override
    public List<PassBillVo> getRecentHalfYearPassBill(String vendor, List<String> scodes, String startDate, String endDate, BigDecimal exchangeRate) {
        return aggregatedBillMapper.getPassBillAll(vendor,scodes, startDate, endDate,exchangeRate);
    }

    @Override
    public void replaceOrphanWithParent(CmdbProductOverview orphan, CmdbProductOverview parent) {
        // 1、查找出孤儿资源的详单
        List<RefinedRawBill> orphans = refinedRawBillMapper.getByAggregatedId(orphan.getAggregatedId());

        // 2、将孤儿资源替换成父级资源
        for (RefinedRawBill orphanRawBill : orphans) {
            orphanRawBill.setSubInstanceId(orphanRawBill.getInstanceId());
            orphanRawBill.setSubProductCode(orphanRawBill.getProductCode());
            orphanRawBill.setSubProductName(orphanRawBill.getProductName());

            orphanRawBill.setInstanceId(parent.getInstanceId());
            orphanRawBill.setProductCode(parent.getProductCode());
            orphanRawBill.setProductName(parent.getProductName());
            orphanRawBill.setAggregatedId(parent.getAggregatedId());
            orphanRawBill.setSubscriptionType(parent.getSubscriptionType());
            orphanRawBill.setCostUnit(parent.getScode());
            orphanRawBill.setParentFound(1);
        }


        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            refinedRawBillMapper.batchUpdateById(orphans);

            /* 3、将父级资源重新进行到日、到月汇总 */
            // 3.1 删除父级资源的日、月汇总
            aggregatedBillMapper.delete(
                    new LambdaQueryWrapper<AggregatedBill>()
                            .eq(AggregatedBill::getAggregatedId, parent.getAggregatedId()));
            // 3.2 查询进行到日、到月汇总（带task_id）
            List<AggregatedBill> aggregatedInDay = refinedRawBillMapper.getAggregatedInDay(parent.getAggregatedId());
            List<AggregatedBill> aggregatedInMonth = refinedRawBillMapper.getAggregatedInMonth(parent.getAggregatedId());

            // 3.3 插入到日、到月汇总
            aggregatedBillMapper.insertBatch(aggregatedInDay);
            aggregatedBillMapper.insertBatch(aggregatedInMonth);
            // 4、清除总表中孤儿资源
            cmdbProductOverviewMapper.deleteOrphanData(orphan.getAggregatedId());
            // 5、记录日志
            replacingLogMapper.insert(OrphanParentReplacingLog.builder()
                    .orphanInstanceId(orphan.getInstanceId())
                    .orphanProductCode(orphan.getProductCode())
                    .orphanProductName(orphan.getProductName())
                    .orphanSupplementId(orphan.getSupplementId())
                    .orphanAggregatedId(orphan.getAggregatedId())
                    .parentAggregatedId(parent.getAggregatedId())
                    .parentInstanceId(parent.getInstanceId())
                    .parentProductCode(parent.getProductCode())
                    .parentProductName(parent.getProductName())
                    .build());

            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("替换孤儿资源失败：orphan = {}, parent = {}", JSON.toJSONString(orphan), JSON.toJSONString(parent), e);
            transactionManager.rollback(status);
        }
    }


    @Override
    public String getDomainSubProducts() {
        List<DomainSubProductVo> domainPrivileges = new ArrayList<>();
        User currentUser = LoginContextHolder.getCurrentUser();
        try {
            domainPrivileges = domainPrivilegeFinder.findDomainPrivileges(currentUser.getUserCode(), AuthUtil.isAdmin(currentUser));
        } catch (Exception e) {
            log.error(e.getMessage());
            return "";
        }
        return domainPrivileges.stream().map(DomainSubProductVo::getScode).collect(Collectors.joining(","));
    }

    @Override
    public String getDomainSubProducts(List<String> domainList,boolean isAdmin,String userCode) {
        List<DomainSubProductVo> domainPrivileges = new ArrayList<>();
        try {
            domainPrivileges = domainPrivilegeFinder.findDomainPrivileges(userCode,isAdmin,domainList);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
        if(domainPrivileges.size()==0){
            return null;
        }
        return domainPrivileges.stream().map(DomainSubProductVo::getScode).collect(Collectors.joining(","));
    }

    @Override
    public List<Map> getAgentResourceApplications(String vendor, String scode,String startDate,String endDate) {
        List<String> validScodes = AuthUtil.extractAuthedScodes(scode);
        return aggregatedBillMapper.getAgentResourceApplications(vendor,validScodes,startDate,endDate);
    }

    @Override
    public List<BillDetailVo> listDetail(DetailBillParam request,String currency) throws Exception{
        List<String> validScodes = AuthUtil.extractAuthedScodes(request.getScode());
        request.setScodeList(validScodes);
        request.setType("1");
        request.setCurrency(currency);
        try {
            return aggregatedBillMapper.listDetailByPage(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<BillDetailVo> listDetail(DetailBillParam request,BigDecimal exchangeRate) throws Exception{
        List<String> validScodes = AuthUtil.extractAuthedScodes(request.getScode());
        request.setScodeList(validScodes);
        request.setType("1");
        request.setExchangeRate(exchangeRate);
        try {
            return aggregatedBillMapper.listDetailByPageAll(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
