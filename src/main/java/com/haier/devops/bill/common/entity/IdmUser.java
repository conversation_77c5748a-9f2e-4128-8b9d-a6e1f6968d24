package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@TableName("idm_user")
public class IdmUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private String username;

    private String fullname;

    private String email;

    private String haierusermail;

    private String internetemailaddress;

    private String mobile;

    private String haiergender;

    private String isdisable;

    private String employeestatus;

    private String haierbirthday;

    private String haieridcardnumber;

    private String haierpositionlevel;

    private String haierdutycode;

    private String haierdutycodenew;

    private String title;

    private String haieruserfirstlineid;

    private String haierusersecondlineid;

    private String haierpositioncode;

    private String haierpositionname;

    private String deptname;

    private String deptcode;

    private String haierpersg;

    private String haierpersk;

    private String haieruserziorgcode;

    private String haieruserqydwcode;

    private String haieruserhrpsn;

    private String haieruserorgmanagerid;

    private String status;

    private String haiersyspreferedname;

    private String haierdatasrcnew;

    private String haiercompanyname;

    private String haierarea;

    private String haieratdcard;

    private String usercreatedate;

    private String userenddate;

    private String loginexpirationtime;

    private String dcCompanyCode;

    private String dcCompanyName;

    private String haierzzjytpinpai;

    private String haierbuid;

    private String haierjoinbtbdate;

    private String haierproductid;

    private String haieruserorgcode;

    private String haierusershopcode;

    private String haieruserzzjytcode;

    private String haieruserzzjytmanagercode;

    private String haieruserqydwname;

    private String haierzxyusertype;

    private String haieruserziorgname;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getHaierusermail() {
        return haierusermail;
    }

    public void setHaierusermail(String haierusermail) {
        this.haierusermail = haierusermail;
    }
    public String getInternetemailaddress() {
        return internetemailaddress;
    }

    public void setInternetemailaddress(String internetemailaddress) {
        this.internetemailaddress = internetemailaddress;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    public String getHaiergender() {
        return haiergender;
    }

    public void setHaiergender(String haiergender) {
        this.haiergender = haiergender;
    }
    public String getIsdisable() {
        return isdisable;
    }

    public void setIsdisable(String isdisable) {
        this.isdisable = isdisable;
    }
    public String getEmployeestatus() {
        return employeestatus;
    }

    public void setEmployeestatus(String employeestatus) {
        this.employeestatus = employeestatus;
    }
    public String getHaierbirthday() {
        return haierbirthday;
    }

    public void setHaierbirthday(String haierbirthday) {
        this.haierbirthday = haierbirthday;
    }
    public String getHaieridcardnumber() {
        return haieridcardnumber;
    }

    public void setHaieridcardnumber(String haieridcardnumber) {
        this.haieridcardnumber = haieridcardnumber;
    }
    public String getHaierpositionlevel() {
        return haierpositionlevel;
    }

    public void setHaierpositionlevel(String haierpositionlevel) {
        this.haierpositionlevel = haierpositionlevel;
    }
    public String getHaierdutycode() {
        return haierdutycode;
    }

    public void setHaierdutycode(String haierdutycode) {
        this.haierdutycode = haierdutycode;
    }
    public String getHaierdutycodenew() {
        return haierdutycodenew;
    }

    public void setHaierdutycodenew(String haierdutycodenew) {
        this.haierdutycodenew = haierdutycodenew;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getHaieruserfirstlineid() {
        return haieruserfirstlineid;
    }

    public void setHaieruserfirstlineid(String haieruserfirstlineid) {
        this.haieruserfirstlineid = haieruserfirstlineid;
    }
    public String getHaierusersecondlineid() {
        return haierusersecondlineid;
    }

    public void setHaierusersecondlineid(String haierusersecondlineid) {
        this.haierusersecondlineid = haierusersecondlineid;
    }
    public String getHaierpositioncode() {
        return haierpositioncode;
    }

    public void setHaierpositioncode(String haierpositioncode) {
        this.haierpositioncode = haierpositioncode;
    }
    public String getHaierpositionname() {
        return haierpositionname;
    }

    public void setHaierpositionname(String haierpositionname) {
        this.haierpositionname = haierpositionname;
    }
    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }
    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }
    public String getHaierpersg() {
        return haierpersg;
    }

    public void setHaierpersg(String haierpersg) {
        this.haierpersg = haierpersg;
    }
    public String getHaierpersk() {
        return haierpersk;
    }

    public void setHaierpersk(String haierpersk) {
        this.haierpersk = haierpersk;
    }
    public String getHaieruserziorgcode() {
        return haieruserziorgcode;
    }

    public void setHaieruserziorgcode(String haieruserziorgcode) {
        this.haieruserziorgcode = haieruserziorgcode;
    }
    public String getHaieruserqydwcode() {
        return haieruserqydwcode;
    }

    public void setHaieruserqydwcode(String haieruserqydwcode) {
        this.haieruserqydwcode = haieruserqydwcode;
    }
    public String getHaieruserhrpsn() {
        return haieruserhrpsn;
    }

    public void setHaieruserhrpsn(String haieruserhrpsn) {
        this.haieruserhrpsn = haieruserhrpsn;
    }
    public String getHaieruserorgmanagerid() {
        return haieruserorgmanagerid;
    }

    public void setHaieruserorgmanagerid(String haieruserorgmanagerid) {
        this.haieruserorgmanagerid = haieruserorgmanagerid;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getHaiersyspreferedname() {
        return haiersyspreferedname;
    }

    public void setHaiersyspreferedname(String haiersyspreferedname) {
        this.haiersyspreferedname = haiersyspreferedname;
    }
    public String getHaierdatasrcnew() {
        return haierdatasrcnew;
    }

    public void setHaierdatasrcnew(String haierdatasrcnew) {
        this.haierdatasrcnew = haierdatasrcnew;
    }
    public String getHaiercompanyname() {
        return haiercompanyname;
    }

    public void setHaiercompanyname(String haiercompanyname) {
        this.haiercompanyname = haiercompanyname;
    }
    public String getHaierarea() {
        return haierarea;
    }

    public void setHaierarea(String haierarea) {
        this.haierarea = haierarea;
    }
    public String getHaieratdcard() {
        return haieratdcard;
    }

    public void setHaieratdcard(String haieratdcard) {
        this.haieratdcard = haieratdcard;
    }
    public String getUsercreatedate() {
        return usercreatedate;
    }

    public void setUsercreatedate(String usercreatedate) {
        this.usercreatedate = usercreatedate;
    }
    public String getUserenddate() {
        return userenddate;
    }

    public void setUserenddate(String userenddate) {
        this.userenddate = userenddate;
    }
    public String getLoginexpirationtime() {
        return loginexpirationtime;
    }

    public void setLoginexpirationtime(String loginexpirationtime) {
        this.loginexpirationtime = loginexpirationtime;
    }
    public String getDcCompanyCode() {
        return dcCompanyCode;
    }

    public void setDcCompanyCode(String dcCompanyCode) {
        this.dcCompanyCode = dcCompanyCode;
    }
    public String getDcCompanyName() {
        return dcCompanyName;
    }

    public void setDcCompanyName(String dcCompanyName) {
        this.dcCompanyName = dcCompanyName;
    }
    public String getHaierzzjytpinpai() {
        return haierzzjytpinpai;
    }

    public void setHaierzzjytpinpai(String haierzzjytpinpai) {
        this.haierzzjytpinpai = haierzzjytpinpai;
    }
    public String getHaierbuid() {
        return haierbuid;
    }

    public void setHaierbuid(String haierbuid) {
        this.haierbuid = haierbuid;
    }
    public String getHaierjoinbtbdate() {
        return haierjoinbtbdate;
    }

    public void setHaierjoinbtbdate(String haierjoinbtbdate) {
        this.haierjoinbtbdate = haierjoinbtbdate;
    }
    public String getHaierproductid() {
        return haierproductid;
    }

    public void setHaierproductid(String haierproductid) {
        this.haierproductid = haierproductid;
    }
    public String getHaieruserorgcode() {
        return haieruserorgcode;
    }

    public void setHaieruserorgcode(String haieruserorgcode) {
        this.haieruserorgcode = haieruserorgcode;
    }
    public String getHaierusershopcode() {
        return haierusershopcode;
    }

    public void setHaierusershopcode(String haierusershopcode) {
        this.haierusershopcode = haierusershopcode;
    }
    public String getHaieruserzzjytcode() {
        return haieruserzzjytcode;
    }

    public void setHaieruserzzjytcode(String haieruserzzjytcode) {
        this.haieruserzzjytcode = haieruserzzjytcode;
    }
    public String getHaieruserzzjytmanagercode() {
        return haieruserzzjytmanagercode;
    }

    public void setHaieruserzzjytmanagercode(String haieruserzzjytmanagercode) {
        this.haieruserzzjytmanagercode = haieruserzzjytmanagercode;
    }
    public String getHaieruserqydwname() {
        return haieruserqydwname;
    }

    public void setHaieruserqydwname(String haieruserqydwname) {
        this.haieruserqydwname = haieruserqydwname;
    }
    public String getHaierzxyusertype() {
        return haierzxyusertype;
    }

    public void setHaierzxyusertype(String haierzxyusertype) {
        this.haierzxyusertype = haierzxyusertype;
    }
    public String getHaieruserziorgname() {
        return haieruserziorgname;
    }

    public void setHaieruserziorgname(String haieruserziorgname) {
        this.haieruserziorgname = haieruserziorgname;
    }

    @Override
    public String toString() {
        return "IdmUser{" +
            "username=" + username +
            ", fullname=" + fullname +
            ", email=" + email +
            ", haierusermail=" + haierusermail +
            ", internetemailaddress=" + internetemailaddress +
            ", mobile=" + mobile +
            ", haiergender=" + haiergender +
            ", isdisable=" + isdisable +
            ", employeestatus=" + employeestatus +
            ", haierbirthday=" + haierbirthday +
            ", haieridcardnumber=" + haieridcardnumber +
            ", haierpositionlevel=" + haierpositionlevel +
            ", haierdutycode=" + haierdutycode +
            ", haierdutycodenew=" + haierdutycodenew +
            ", title=" + title +
            ", haieruserfirstlineid=" + haieruserfirstlineid +
            ", haierusersecondlineid=" + haierusersecondlineid +
            ", haierpositioncode=" + haierpositioncode +
            ", haierpositionname=" + haierpositionname +
            ", deptname=" + deptname +
            ", deptcode=" + deptcode +
            ", haierpersg=" + haierpersg +
            ", haierpersk=" + haierpersk +
            ", haieruserziorgcode=" + haieruserziorgcode +
            ", haieruserqydwcode=" + haieruserqydwcode +
            ", haieruserhrpsn=" + haieruserhrpsn +
            ", haieruserorgmanagerid=" + haieruserorgmanagerid +
            ", status=" + status +
            ", haiersyspreferedname=" + haiersyspreferedname +
            ", haierdatasrcnew=" + haierdatasrcnew +
            ", haiercompanyname=" + haiercompanyname +
            ", haierarea=" + haierarea +
            ", haieratdcard=" + haieratdcard +
            ", usercreatedate=" + usercreatedate +
            ", userenddate=" + userenddate +
            ", loginexpirationtime=" + loginexpirationtime +
            ", dcCompanyCode=" + dcCompanyCode +
            ", dcCompanyName=" + dcCompanyName +
            ", haierzzjytpinpai=" + haierzzjytpinpai +
            ", haierbuid=" + haierbuid +
            ", haierjoinbtbdate=" + haierjoinbtbdate +
            ", haierproductid=" + haierproductid +
            ", haieruserorgcode=" + haieruserorgcode +
            ", haierusershopcode=" + haierusershopcode +
            ", haieruserzzjytcode=" + haieruserzzjytcode +
            ", haieruserzzjytmanagercode=" + haieruserzzjytmanagercode +
            ", haieruserqydwname=" + haieruserqydwname +
            ", haierzxyusertype=" + haierzxyusertype +
            ", haieruserziorgname=" + haieruserziorgname +
        "}";
    }
}
