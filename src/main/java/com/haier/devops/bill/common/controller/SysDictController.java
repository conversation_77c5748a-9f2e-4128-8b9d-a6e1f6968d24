package com.haier.devops.bill.common.controller;


import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.SysDictCreateDTO;
import com.haier.devops.bill.common.dto.SysDictDTO;
import com.haier.devops.bill.common.entity.SysDict;
import com.haier.devops.bill.common.service.SysDictItemService;
import com.haier.devops.bill.common.service.SysDictService;
import com.haier.devops.bill.common.vo.SysDictItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* @ClassName: SysDictController
* @Description: 字典
* @author: 张爱苹
* @date: 2024/1/12 10:54
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/dict")
public class SysDictController {

	@Autowired
	private SysDictItemService sysDictItemService;

	@Autowired
	private SysDictService sysDictService;

	/**
	* @Description: 根据字典code获取字典信息
	* @author: 张爱苹
	* @date: 2024/1/12 11:01
	* @param dictCode:
	* @Return: com.haier.devops.bill.common.entity.SysDictItem
	*/
	@GetMapping("/{dictCode}")
	public ResponseEntityWrapper<List<SysDictItemVo>> getDictItemList(@PathVariable("dictCode") String dictCode) {
		return new ResponseEntityWrapper<>(sysDictItemService.getDictItemList(dictCode));
	}

	/**
	* @Description: 检查字典Code
	* @author: 张爱苹
	* @date: 2024/2/4 13:43
	* @param dictCode:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper
	*/
	@GetMapping("/check")
	public ResponseEntityWrapper check(@RequestParam("dictCode") String dictCode) {
		return new ResponseEntityWrapper<>(sysDictService.check(dictCode));
	}



	/**
	 * 分页查询所有字典
	 *
	 * @return 字典列表
	 */
	@GetMapping("/listByPage")
	public ResponseEntityWrapper<PageInfo<SysDict>> listByPage(SysDictDTO dto) {
		PageInfo<SysDict> pageInfo =
				sysDictService.listByPage(dto);
		return new ResponseEntityWrapper<>(pageInfo);
	}

	/**
	 * 创建字典
	 *
	 * @param SysDictCreateDTO 字典
	 * @return 创建成功的字典
	 */
	@PostMapping("/create")
	public ResponseEntityWrapper createSysDict(@RequestBody @Validated SysDictCreateDTO SysDictCreateDTO) {
		try {
			sysDictService.createSysDict(SysDictCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 更新字典
	 *
	 * @param id                 字典ID
	 * @param SysDictCreateDTO 更新的字典
	 * @return 更新后的字典
	 */
	@PutMapping("/update/{id}")
	public ResponseEntityWrapper updateSysDict(@PathVariable("id") String id, @RequestBody @Validated SysDictCreateDTO SysDictCreateDTO) {
		try {
			sysDictService.updateSysDict(id, SysDictCreateDTO);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

	/**
	 * 删除字典
	 *
	 * @param id 字典ID
	 */
	@DeleteMapping("/delete/{id}")
	public ResponseEntityWrapper deleteSysDict(@PathVariable("id") String id) {
		try {
			sysDictService.deleteSysDict(id);
		} catch (Exception e) {
			return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
		}
		return new ResponseEntityWrapper<>();
	}

}
