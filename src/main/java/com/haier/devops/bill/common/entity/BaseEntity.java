package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class BaseEntity implements Serializable {
    @Min(1)
    @TableField(exist = false)
    private int page = 1;
    @Min(1)
    @TableField(exist = false)
    private int per_page = 20;
}
