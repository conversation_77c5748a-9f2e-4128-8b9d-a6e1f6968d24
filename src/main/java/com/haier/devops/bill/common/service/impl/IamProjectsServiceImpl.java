package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HuaweiApi;
import com.haier.devops.bill.common.entity.IamProjects;
import com.haier.devops.bill.common.mapper.IamProjectsMapper;
import com.haier.devops.bill.common.service.IamProjectsService;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* @ClassName: IamProjectsServiceImpl
* @Description: 项目
* @author: 张爱苹
* @date: 2025/2/13 14:58
*/
@Service
public class IamProjectsServiceImpl
        extends ServiceImpl<IamProjectsMapper, IamProjects> implements IamProjectsService {
    private static Logger logger = LoggerFactory.getLogger(IamProjectsServiceImpl.class);
    @Autowired
    private HuaweiApi huaweiApi;


    @Override
    public void insertIamProjects(String accountName) throws Exception{
        IamClient iamClient = huaweiApi.getIamClient(accountName,"cn-north-4");
        KeystoneListAuthProjectsResponse response = huaweiApi.keystoneListAuthProjects(iamClient);
        if(response == null){
            throw new RuntimeException("获取企业项目失败");
        }
        List<AuthProjectResult> list = response.getProjects();
        List<String> idList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            List<IamProjects> addList = new ArrayList<>();
            List<IamProjects> updateList = new ArrayList<>();
            list.stream().forEach(item->{
                IamProjects iamProjects = IamProjects.builder()
                        .id(item.getId())
                        .domainId(item.getDomainId())
                        .name(item.getName())
                        .accountName(accountName)
                        .delFlag("0")
                        .build();
                IamProjects old = getById(item.getId());
                if(old == null){
                    addList.add(iamProjects);
                }else{
                    iamProjects.setId(old.getId());
                    updateList.add(iamProjects);
                }
                idList.add(item.getId());

            });
            if(CollectionUtils.isNotEmpty(addList)){
                saveBatch(addList);
            }
            if(CollectionUtils.isNotEmpty(updateList)){
                updateBatchById(updateList);
            }
        }
        //删除不在idList的项目
        deleteBatchNoIds(idList,accountName);

    }

    @Override
    public List<IamProjects> getIamProjects(String accountName) {
        LambdaQueryWrapper<IamProjects> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IamProjects::getAccountName,accountName);
        queryWrapper.eq(IamProjects::getDelFlag,"0");
        return list(queryWrapper);
    }

    private void deleteBatchNoIds(List<String> idList, String accountName) {
        UpdateWrapper<IamProjects> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("account_name",accountName);
        updateWrapper.set("del_flag","1");
        updateWrapper.eq("del_flag","0");
        if(CollectionUtils.isNotEmpty(idList)){
            updateWrapper.notIn("id",idList);
        }
        update(updateWrapper);
    }
}
