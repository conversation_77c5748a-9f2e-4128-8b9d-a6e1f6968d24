package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.MigratedBill;

/**
 * <p>
 * 账单明细汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface MigratedBillMapper extends BaseMapper<MigratedBill> {
    /**
     * 获取替换任务的总金额
     * @param subTaskId
     * @return
     */
    String getSumBySubTaskId(String subTaskId);

}
