package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.BudgetAppRel;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface BudgetAppRelMapper extends BaseMapper<BudgetAppRel> {

    /**
     * 根据id查询关联的账期
     * @param id
     * @return
     */
    List<String> getBillingCycles(Integer id);


    /**
     * 根据scode查询最新的预算配置关系
     * @param scode
     * @return
     */
    List<BudgetAppRel> findLatestByCode(String scode);
}
