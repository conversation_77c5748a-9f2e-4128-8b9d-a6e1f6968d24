package com.haier.devops.bill.common.config;

import com.alibaba.fastjson.JSON;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.util.HworkSignUtil;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.commons.httpclient.OkHttpClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class HworkAuthorityOkHttpFeignConfiguration extends OkHttpFeignConfiguration {
    private final HworkAuthorityConfigProperties hworkAuthorityConfigProperties;
    private final String APP_ID = "appId";
    private final String SIGNATURE = "sign";

    private okhttp3.OkHttpClient hworkAuthorityOkHttp3Client;

    private OkHttpProperties properties;
    private OkHttpClientFactory httpClientFactory;
    private OkHttpClientConnectionPoolFactory connectionPoolFactory;

    public HworkAuthorityOkHttpFeignConfiguration(HworkAuthorityConfigProperties hworkAuthorityConfigProperties,
                                                  OkHttpProperties properties,
                                                  OkHttpClientFactory httpClientFactory,
                                                  OkHttpClientConnectionPoolFactory connectionPoolFactory) {
        this.hworkAuthorityConfigProperties = hworkAuthorityConfigProperties;
        this.properties = properties;
        this.httpClientFactory = httpClientFactory;
        this.connectionPoolFactory = connectionPoolFactory;
    }

    @Bean
    public HworkAuthorityApi hworkAuthorityApi() {
        this.hworkAuthorityOkHttp3Client = setConnectionProperties(properties, httpClientFactory, connectionPoolFactory);

        this.hworkAuthorityOkHttp3Client = hworkAuthorityOkHttp3Client.newBuilder()
                .addInterceptor(authentication(hworkAuthorityConfigProperties))
                .build();

        return Feign.builder()
                .client(new OkHttpClient(hworkAuthorityOkHttp3Client))
                .encoder(new GsonEncoder())
                .decoder(new GsonDecoder())
                .target(HworkAuthorityApi.class, hworkAuthorityConfigProperties.getDomain());
    }

    private Interceptor authentication(HworkAuthorityConfigProperties authorityConfigProperties) {
        return chain -> {
            Request request = chain.request();
            String body = null;
            String sign = null;

            if ("GET".equals(request.method())&& null != request.url().queryParameterNames()) {
                Map params = new HashMap<>();
                // Get the request body
                Request finalRequest = request;
                request.url().queryParameterNames()
                        .forEach(key -> params.put(key, finalRequest.url().queryParameter(key)));
                sign = HworkSignUtil.generateSign(authorityConfigProperties.getAppId(),
                        params, authorityConfigProperties.getSecret(), null);
                log.info("Request params: {}, Signature: {}", JSON.toJSONString(params), sign);

                // re-create the request body since it's a one-time read
                request = request.newBuilder().get().build();
            } else if ("POST".equals(request.method()) && null != request.body()) {
                // Get the request body
                Buffer buffer = new Buffer();
                request.body().writeTo(buffer);
                body = buffer.readUtf8();

                sign = HworkSignUtil.generateSign(authorityConfigProperties.getAppId(),
                        null, authorityConfigProperties.getSecret(), body);
                log.info("Request Body: {}, Signature: {}", body, sign);

                // re-create the request body since it's a one-time read
                request = request.newBuilder()
                        .post(okhttp3.RequestBody.create(request.body().contentType(), body))
                        .build();
            }

            log.info("Hwork authority request: {}", request);
            long start = System.currentTimeMillis();
            Request requestWithUserAgent = request.newBuilder()
                    .header(APP_ID, authorityConfigProperties.getAppId())
                    .header(SIGNATURE, sign)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .build();
            Response response = chain.proceed(requestWithUserAgent);

            long end = System.currentTimeMillis();
            log.info("Hwork authority response: {}, costs: {} milliseconds", response, end - start);

            return response;
        };
    }

    /**
     * Hwork权限中心配置
     */
    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "api.hwork.authority")
    public static class HworkAuthorityConfigProperties {
        private String domain;
        private String appId;
        private String secret;
    }
}
