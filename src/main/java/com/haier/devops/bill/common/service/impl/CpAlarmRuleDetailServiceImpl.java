package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.CpAlarmRuleDetail;
import com.haier.devops.bill.common.mapper.AlarmUserInfoMapper;
import com.haier.devops.bill.common.mapper.CpAlarmRuleDetailMapper;
import com.haier.devops.bill.common.mapper.SysDictItemMapper;
import com.haier.devops.bill.common.service.CpAlarmRuleDetailService;
import com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* @ClassName: CpAlarmRuleDetailServiceImpl
* @Description:  告警规则明细表 服务实现类
* @author: 张爱苹
* @date: 2024/1/12 14:53
*/
@Service
public class CpAlarmRuleDetailServiceImpl extends ServiceImpl<CpAlarmRuleDetailMapper, CpAlarmRuleDetail> implements CpAlarmRuleDetailService {

    @Autowired
    private AlarmUserInfoMapper alarmUserInfoMapper;

    @Autowired
    private SysDictItemMapper sysDictItemMapper;

    @Override
    public void deleteByCpAlarmConfigurationId(Integer id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.apply("cp_alarm_level_id in (select id from bc_cp_alarm_level where cp_alarm_configuration_id = {0})",id);
        remove(queryWrapper);
    }

    @Override
    public List<CpAlarmRuleDetailVo> getAlarmRuleDetailListByAlarmLevelId(Integer alarmLevelId) {
        List<CpAlarmRuleDetailVo> alarmRuleDetailVoList = baseMapper.getAlarmRuleDetailListByAlarmLevelId(alarmLevelId);
        return alarmRuleDetailVoList;
    }

    @Override
    public List<CpAlarmRuleDetailVo> queryAlarmRuleDetailListByAlarmLevelId(Map map) {
        List<CpAlarmRuleDetailVo> alarmRuleDetailVoList = baseMapper.queryAlarmRuleDetailListByAlarmLevelId(map);
        return alarmRuleDetailVoList;
    }
}
