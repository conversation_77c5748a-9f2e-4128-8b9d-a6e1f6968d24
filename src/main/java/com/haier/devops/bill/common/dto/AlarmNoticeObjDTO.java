package com.haier.devops.bill.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: AlarmNoticeObjDTO
* @Description:  告警通知对象
* @author: 张爱苹
* @date: 2024/1/17 11:22
*/
@Data
public class AlarmNoticeObjDTO implements Serializable {


    private static final long serialVersionUID = -3639553602174685691L;
    /**
     * 通知人账号或通知组ID或群id
     */
    private String noticeObjId;

//    /**
//     * 通知人姓名
//     */
//    private String noticeObjName;
//
//
//    /**
//     * 邮箱
//     */
//    private String email;
//
//    /**
//     * 手机号
//     */
//    private String mobile;

}
