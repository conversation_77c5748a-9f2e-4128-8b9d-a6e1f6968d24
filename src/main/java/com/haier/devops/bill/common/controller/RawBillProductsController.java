package com.haier.devops.bill.common.controller;


import com.haier.devops.bill.common.dto.RawBillProductsDTO;
import com.haier.devops.bill.common.dto.RawBillProductsWithVendorsDTO;
import com.haier.devops.bill.common.entity.RawBillProducts;
import com.haier.devops.bill.common.service.RawBillProductsService;
import com.haier.devops.bill.common.vo.RawBillProductTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
* @ClassName: RawBillProductsController
* @Description: 云产品
* @author: 张爱苹
* @date: 2024/1/15 16:00
*/
@RestController
@RequestMapping("/api/v1/hcms/bill/raw-bill-products")
public class RawBillProductsController {

	@Autowired
	private RawBillProductsService rawBillProductsService;

	/**
	* @Description:  云产品树
	* @author: 张爱苹
	* @date: 2024/1/19 15:45
	* @param searchContent:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<java.util.List<com.haier.devops.bill.common.vo.RawBillProductTreeVo>>
	*/
	@GetMapping("/treeList")
	public ResponseEntityWrapper<List<RawBillProductTreeVo>> getRawBillProductTreeList(@RequestParam(value = "searchContent",required = false ) String searchContent, @RequestParam(value = "appScode",required = false)String appScode) {
		return new ResponseEntityWrapper<>(rawBillProductsService.getRawBillProductTreeList(searchContent,appScode));
	}

	/**
	* @Description: 查询所有云产品
	* @author: 张爱苹
	* @date: 2024/1/19 15:45
	* @param dto:
	* @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CpAlarmLog>>
	*/
	@GetMapping("/list")
	public ResponseEntityWrapper<List<RawBillProducts>> getRawBillProductList(RawBillProductsDTO dto) {
		List<RawBillProducts> list =
				rawBillProductsService.getRawBillProductList(dto);
		return new ResponseEntityWrapper<>(list);
	}

	/**
	 * @Description: 查询通用产品类型
	 * @author: 张爱苹
	 * @date: 2024/1/19 15:45
	 * @param dto:
	 * @Return: com.haier.devops.bill.common.controller.ResponseEntityWrapper<com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.CpAlarmLog>>
	 */
	@GetMapping("/listAliasProducts")
	public ResponseEntityWrapper<List<Map>> getAliasProducts(RawBillProductsDTO dto) {
		List<Map> list =
				rawBillProductsService.getAliasProducts(dto);
		return new ResponseEntityWrapper<>(list);
	}


	@GetMapping("/listAliasProductsWithVendors")
	public ResponseEntityWrapper<List<Map>> getAliasProductsWithVendors(RawBillProductsWithVendorsDTO dto) {
		List<Map> list =
				rawBillProductsService.getAliasProductsWithVendors(dto);
		return new ResponseEntityWrapper<>(list);
	}
}
