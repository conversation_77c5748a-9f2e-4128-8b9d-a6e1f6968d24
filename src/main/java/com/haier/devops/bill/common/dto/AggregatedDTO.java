package com.haier.devops.bill.common.dto;

import com.haier.devops.bill.common.entity.AdjustmentRecord;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* @ClassName: AggregatedDTO
* @Description: TODO
* @author: 张爱苹
* @date: 2024/3/11 10:01
*/
@Data
@Builder
public class AggregatedDTO implements Serializable {


    private static final long serialVersionUID = -1658274150577207038L;
    /**
     * @Description: 调整前的S码
     */
    private String scode;

    /**
    * @Description: 聚合id
    */
    private String aggregatedId;

    /**
     * @Description: 聚合区间信息
     */
    private List<ReconciliationDTO> list;

    /**
     * @Description: 云厂商
     */
    private String vendor;

    /**
     * @Description: 调账记录
     */
    private List<AdjustmentRecord> recordList;

    private List<String> aggregatedIdList;

    private List<String> currentAggregatedIdList;

    /**
     * @Description: 调整后的S码
     */
    private String appScode;
}
