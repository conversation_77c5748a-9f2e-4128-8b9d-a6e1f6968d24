package com.haier.devops.bill.common.controller;

import com.haier.devops.bill.common.entity.ZabbixMonitorData;
import com.haier.devops.bill.common.service.ZabbixMonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping(value = "/api/v1/hcms/bill/zabbix")
@RestController
@Slf4j
public class ZabbixMonitorDataController {

    @Autowired
    private ZabbixMonitorDataService zabbixMonitorDataService;

    @PostMapping("/monitor/data/save")
    public ResponseEntityWrapper saveBatch(@RequestBody List<ZabbixMonitorData> param){
        log.info("执行保存zabbix监控数据开始，入参size:{}",param.size());
        zabbixMonitorDataService.saveMonitorDataBatch(param);
        return  new ResponseEntityWrapper<>();
    }
}
