package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.dto.AlarmConfigurationCreateDTO;
import com.haier.devops.bill.common.dto.AlarmNoticeObjDTO;
import com.haier.devops.bill.common.dto.CpAlarmLevelCreateDTO;
import com.haier.devops.bill.common.dto.CpAlarmRuleDetailDTO;
import com.haier.devops.bill.common.entity.AlarmConfiguration;
import com.haier.devops.bill.common.entity.CpAlarmLevel;
import com.haier.devops.bill.common.entity.CpAlarmRuleDetail;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.CpAlarmLevelMapper;
import com.haier.devops.bill.common.service.AlarmUserInfoService;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.service.CpAlarmLevelService;
import com.haier.devops.bill.common.service.CpAlarmRuleDetailService;
import com.haier.devops.bill.common.vo.CpAlarmLevelVo;
import com.haier.devops.bill.util.AesUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
* @ClassName: CpAlarmLevelServiceImpl
* @Description:  告警级别服务实现类
* @author: 张爱苹
* @date: 2024/1/12 14:40
*/
@Service
public class CpAlarmLevelServiceImpl extends ServiceImpl<CpAlarmLevelMapper, CpAlarmLevel> implements CpAlarmLevelService {

    private Logger logger = LoggerFactory.getLogger(CpAlarmLevelServiceImpl.class);

    @Autowired
    private CpAlarmRuleDetailService alarmRuleDetailService;


    @Autowired
    private AlarmUserInfoService alarmUserInfoService;

    @Autowired
    private AuthorityService authorityService;

    @Override
    public void deleteByCpAlarmConfigurationId(Integer id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("cp_alarm_configuration_id", id);
        remove(queryWrapper);
    }

    @Override
    public List<CpAlarmLevelVo> getAlarmLevelListByAlarmConfigurationId(Integer id) {
        return baseMapper.getAlarmLevelListByAlarmConfigurationId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateAlarmLevelAndRuleDetail(AlarmConfiguration alarmConfiguration, AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception {
        try{
            Integer alarmConfigurationId = alarmConfiguration.getId();
            //告警级别
            List<CpAlarmLevelCreateDTO> bcCpAlarmLevelList = alarmConfigurationCreateDTO.getBcCpAlarmLevelList();
            List<CpAlarmRuleDetail> cpAlarmRuleDetailList = new ArrayList<>();
            //遍历bcCpAlarmLevelList
            for (CpAlarmLevelCreateDTO cpAlarmLevelCreateDTO : bcCpAlarmLevelList) {
                CpAlarmLevel cpAlarmLevel = new CpAlarmLevel();
                BeanUtils.copyProperties(cpAlarmLevelCreateDTO, cpAlarmLevel);
                cpAlarmLevel.setCpAlarmConfigurationId(alarmConfigurationId);
                //保存告警级别
                save(cpAlarmLevel);
                Integer  alarmLevelId = cpAlarmLevel.getId();
                //组装规则
                cpAlarmRuleDetailList = getAlarmRuleDetailList(cpAlarmRuleDetailList, cpAlarmLevelCreateDTO.getCpAlarmRuleDetailDTOList(), alarmLevelId);
            }
            //保存通知规则
            alarmRuleDetailService.saveBatch(cpAlarmRuleDetailList);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("saveOrUpdateAlarmLevelAndRuleDetail error:{}",e.getMessage(),e);
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<CpAlarmRuleDetail> getAlarmRuleDetailList(List<CpAlarmRuleDetail> cpAlarmRuleDetailList, List<CpAlarmRuleDetailDTO> cpAlarmRuleDetailDTOList, Integer alarmLevelId) throws Exception{
        //遍历cpAlarmRuleDetailDTOList
        for (CpAlarmRuleDetailDTO cpAlarmRuleDetailDTO : cpAlarmRuleDetailDTOList) {
            //通知对象
            List<AlarmNoticeObjDTO> alarmNoticeObjDTOList = cpAlarmRuleDetailDTO.getAlarmNoticeObjList();
            for (AlarmNoticeObjDTO alarmNoticeObjDTO : alarmNoticeObjDTOList) {
                CpAlarmRuleDetail cpAlarmRuleDetail = genneraterAlarmRuleDetail(alarmLevelId, cpAlarmRuleDetailDTO, alarmNoticeObjDTO.getNoticeObjId());
                cpAlarmRuleDetailList.add(cpAlarmRuleDetail);
            }
        }
        return cpAlarmRuleDetailList;
    }

    private String getPhone(String account) {
        String phone = authorityService.getUserPhoneByUserCode(account);
        if(!StringUtils.isEmpty(phone)){
            return AesUtil.encryptCBC(phone);
        }
        return null;
    }

    private String getEmail(String account) {
        User user = authorityService.getUserByUserCode(account);
        return user == null?null:user.getEmail();
    }

    @NotNull
    private CpAlarmRuleDetail genneraterAlarmRuleDetail(Integer alarmLevelId, CpAlarmRuleDetailDTO cpAlarmRuleDetailDTO, String noticeObjId) {
        CpAlarmRuleDetail cpAlarmRuleDetail = CpAlarmRuleDetail.builder().build();
        BeanUtils.copyProperties(cpAlarmRuleDetailDTO, cpAlarmRuleDetail);
        cpAlarmRuleDetail.setCpAlarmLevelId(alarmLevelId);
        cpAlarmRuleDetail.setNoticeObjId(noticeObjId);
        return cpAlarmRuleDetail;
    }
}
