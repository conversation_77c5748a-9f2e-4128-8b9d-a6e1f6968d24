package com.haier.devops.bill.common.task.data_completion;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.vpc20160428.models.DescribeVpcAttributeResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haier.devops.bill.common.api.AliyunApi;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.util.AesUtil;
import com.haier.devops.bill.util.RedisUtils;
import com.haier.devops.bill.util.RegularUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 阿里云数据补全类
 * @Author: ********
 * @Date：2023-12-07
 */
@Service
public class AliyunDataCompletion {
    @Resource
    private CloudAccountService cloudAccountService;
    @Resource
    private RcHostInfoService rcHostInfoService;
    @Resource
    private RcDatabaseInfoService rcDatabaseInfoService;
    @Resource
    private RcMiddlewareInfoService rcMiddlewareInfoService;
    @Resource
    private BcResourceInfoService bcResourceInfoService;
    @Resource
    private BcDefaultScodeService bcDefaultScodeService;
    @Resource
    private RawBillService rawBillService;
    @Resource
    Executor localBootAsyncExecutor;
    @Resource
    HdsOpenApi hdsOpenApi;
    @Resource
    RedisUtils redisUtils;


    /**
     * 主机数据补全
     *
     * @param rawBillList
     */
    public void basicDataByHost(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
            instanceIds.remove(null);
            // 数据补全
            return hostInfo(item, instanceIds);
        }, localBootAsyncExecutor)).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    /**
     * 数据库数据补全
     *
     * @param rawBillList
     */
    public void basicDataByDataBase(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
            instanceIds.remove(null);
            // 数据补全
            return databaseInfo(item, instanceIds);
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    /**
     * 中间件数据补全
     *
     * @param rawBillList
     */
    public void basicDataByMiddleware(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            Set<String> instanceIds = new HashSet<>();
//            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
//            instanceIds.remove(null);
//
//            if (instanceIds.size() > 0){
//                // 数据补全
//                List<RcMiddlewareInfo> middlewareInfos = rcMiddlewareInfoService.list(new LambdaQueryWrapper<RcMiddlewareInfo>().in(RcMiddlewareInfo::getInstanceId, instanceIds));
//                return item.stream().map(rawBill -> {
//                    Optional<RcMiddlewareInfo> optional = middlewareInfos.stream().filter(rcMiddlewareInfo -> rcMiddlewareInfo.getInstanceId().equals(rawBill.getResourceId())).findFirst();
//
//                    if (optional.isPresent()) {
//                        RcMiddlewareInfo rcMiddlewareInfo = optional.get();
//                        rawBill.setIp(rcMiddlewareInfo.getPrivateEndpoints());
//                        rawBill.setInstanceId(rcMiddlewareInfo.getInstanceId());
//                        rawBill.setInstanceName(rcMiddlewareInfo.getInstanceName());
//                        rawBill.setScode((StringUtils.isBlank(rcMiddlewareInfo.getScode()) || (StringUtils.isNotBlank(rawBill.getScode()) && rawBill.getScode().equals("S01764"))) ? getScode(rawBill) : rcMiddlewareInfo.getScode());
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                    return rawBill;
//                }).collect(Collectors.toList());
//            } else {
//                return projectInfo(item);
//            }
//
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
//        rawBillService.updateRawBills(res);
//    }
//
//    /**
//     * 对Polardb的基本数据进行处理
//     *
//     * @param rawBillList 原始账单列表
//     */
//    public void basicDataOfPolardb(List<RawBill> rawBillList) {
//        // 对每种产品的数据进行拆分
//        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
//        // 结果集
//        List<RawBill> res = new ArrayList<>();
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            Set<String> instanceIds = new HashSet<>();
//            // 将resourceId添加到instanceIds集合中
//            item.forEach(rawBill -> {
//                String[] resourceIds = rawBill.getResourceId().split(";");
//                instanceIds.add(resourceIds[0]);
//            });
//            instanceIds.remove(null);
//            // 数据补全
//            return databaseInfo(item, instanceIds);
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }


    /**
     * 对Oceanbase的数据进行基本数据处理
     *
     * @param rawBillList 原始账单列表
     */
    public void basicDataOfOceanbase(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);

        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();

            // 将resourceId添加到instanceIds集合中
            item.forEach(rawBill -> {
//                String[] resourceIds = rawBill.getResourceId().split(";");
////                String[] resourceId = resourceIds[0].split("_");
////                if (resourceIds.length >= 2) {
////                    instanceIds.add(resourceId[1]);
//                }
            });
            instanceIds.remove(null);
            return databaseInfo(item, instanceIds);
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    /**
     * 对SLB的基本数据进行处理
     *
     * @param rawBillList 原始账单列表
     */
    public void basicDataOfSlb(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
        Map<String, String> openApiKey = getOpenApiKey(rawBillList);

        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
            item.forEach(rawBill -> {
                try {
//                    String instanceId = AliyunApi.describeLoadBalancerAttribute(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getZone(), rawBill.getResourceId());
//                    if (StringUtils.isNotEmpty(instanceId)) {
//                        instanceIds.add(instanceId);
//                        rawBill.setInstanceId(instanceId);
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            instanceIds.remove(null);
            if (CollectionUtils.isNotEmpty(instanceIds)) {
                return hostInfoByInstanceId(item, instanceIds);
            }
            return item;
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }


    /**
     * 处理Yundisk的基本数据
     *
     * @param rawBillList Yundisk原始账单列表
     */
    public void basicDataOfYundisk(List<RawBill> rawBillList) {
        // 按照区域分组
        Map<String, List<RawBill>> yundiskMap = rawBillList.stream().collect(Collectors.groupingBy(RawBill::getRegion));

        // 遍历每种产品的数据
        yundiskMap.forEach((k, v) -> {
            // 对每种产品的数据进行拆分
            List<List<RawBill>> partition = ListUtils.partition(v, 100);

            // 结果集
            List<RawBill> res = new ArrayList<>();

//            // 获取账号信息
//            Map<String, String> openApiKey = getOpenApiKey(v);
//
//            // 并行处理数据
//            List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//                Set<String> instanceIds = new HashSet<>();
//
//                // 将resourceId添加到instanceIds集合中
//                item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
//
//                try {
//                    // 查询Disk信息
//                    List<DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk> describeDisksResponseBodyDisksDisks = AliyunApi.describeDisks(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBillList.get(0).getZone(), JSONObject.toJSONString(instanceIds));
//
//                    if (CollectionUtils.isNotEmpty(describeDisksResponseBodyDisksDisks)) {
//                        // 更新原始账单中的InstanceId
//                        item.forEach(rawBill -> {
//                            Optional<DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk> optional = describeDisksResponseBodyDisksDisks.stream().filter(disk -> disk.diskId.equals(rawBill.getResourceId())).findFirst();
//                            if (optional.isPresent()) {
//                                DescribeDisksResponseBody.DescribeDisksResponseBodyDisksDisk describeDisksResponseBodyDisksDisk = optional.get();
//                                rawBill.setInstanceId(describeDisksResponseBodyDisksDisk.instanceId);
//                                instanceIds.add(describeDisksResponseBodyDisksDisk.instanceId);
//                            } else {
//                                rawBill.setScode(getScode(rawBill));
//                                HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                                rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                                rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                            }
//                        });
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//
//                // 获取实例信息
//                if (CollectionUtils.isNotEmpty(instanceIds)) {
//                    return hostInfoByInstanceId(item, instanceIds);
//                }
//                return item;
//            }, localBootAsyncExecutor).exceptionally((e) -> {
//                e.printStackTrace();
//                return null;
//            })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//            // 将结果加入到总结果集中
//            if (resList != null) {
//                resList.forEach(res::addAll);
//            }

            // 更新原始账单
            rawBillService.updateRawBills(res);
        });
    }

    public void basicDataOfSavingplan(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getSplitItem).forEach(instanceIds::add);
            instanceIds.remove(null);
            // 数据补全
            return hostInfoBySplitItemId(item, instanceIds);
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfVpc(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
        Map<String, String> openApiKey = getOpenApiKey(rawBillList);

        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            // 将resourceId添加到instanceIds集合中
            item.forEach(rawBill -> {
                try {
                    String[] split = rawBill.getInstanceId().split(";");
                    DescribeVpcAttributeResponseBody describeVpcAttributeResponseBody = AliyunApi.describeVpcAttribute(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), split[1]);
                    if (describeVpcAttributeResponseBody != null) {
                        rawBill.setInstanceId(describeVpcAttributeResponseBody.getVpcId());
                        rawBill.setInstanceName(describeVpcAttributeResponseBody.getVpcName());
//                        rawBill.setIp(describeVpcAttributeResponseBody.getCidrBlock());
                        rawBill.setScode(getScode(rawBill));
                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
                    } else {
                        rawBill.setScode(getScode(rawBill));
                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
                    }

                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            return item;
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfVpn(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            // 将resourceId添加到instanceIds集合中
//            item.forEach(rawBill -> {
//                try {
//                    DescribeVpnGatewayResponseBody describeVpnGatewayResponseBody = AliyunApi.describeVpnGateway(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (describeVpnGatewayResponseBody != null) {
//                        rawBill.setInstanceId(describeVpnGatewayResponseBody.vpnGatewayId);
//                        rawBill.setInstanceName(describeVpnGatewayResponseBody.name);
//                        rawBill.setIp(describeVpnGatewayResponseBody.internetIp);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    /**
     * database类型数据补全
     *
     * @param item
     * @param instanceIds
     * @return
     */
    public List<RawBill> databaseInfo(List<RawBill> item, Set<String> instanceIds) {
        if (instanceIds.size() > 0) {
            // 数据补全
            List<RcDatabaseInfo> databaseInfos = rcDatabaseInfoService.list(new LambdaQueryWrapper<RcDatabaseInfo>().in(RcDatabaseInfo::getInstanceId, instanceIds));
            return item.stream().map(rawBill -> {
//                Optional<RcDatabaseInfo> optional = databaseInfos.stream().filter(rcDatabaseInfo -> rawBill.getResourceId().contains(rcDatabaseInfo.getInstanceId())).findFirst();

//                if (optional.isPresent()) {
//                    RcDatabaseInfo databaseInfo = optional.get();
//                    rawBill.setIp(databaseInfo.getHost());
//                    rawBill.setInstanceId(databaseInfo.getInstanceId());
//                    rawBill.setInstanceName(databaseInfo.getInstanceName());
//                    rawBill.setScode((StringUtils.isBlank(databaseInfo.getScode()) || (StringUtils.isNotBlank(rawBill.getScode()) && rawBill.getScode().equals("S01764"))) ? getScode(rawBill) : databaseInfo.getScode());
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                } else {
//                    rawBill.setScode(getScode(rawBill));
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                }
                return rawBill;
            }).collect(Collectors.toList());
        } else {
            projectInfo(item);
        }
        return item;
    }

    /**
     * 获取主机信息
     *
     * @param item        要获取主机信息的原始订单列表
     * @param instanceIds 要获取主机信息的实例ID集合
     * @return 修改后的原始订单列表，其中包含了获取到的主机信息
     */
    public List<RawBill> hostInfo(List<RawBill> item, Set<String> instanceIds) {
        if (instanceIds.size() > 0) {
            // 获取rc_host_info的数据
            List<RcHostInfo> hostInfos = rcHostInfoService.list(new LambdaQueryWrapper<RcHostInfo>().in(RcHostInfo::getInstanceId, instanceIds));
            return item.stream().map(rawBill -> {
//                Optional<RcHostInfo> optional = hostInfos.stream().filter(rcHostInfo -> rcHostInfo.getInstanceId().equals(rawBill.getResourceId())).findFirst();
//
//                if (optional.isPresent()) {
//                    RcHostInfo hostInfo = optional.get();
//                    rawBill.setIp(hostInfo.getPrivateIp());
//                    rawBill.setInstanceId(hostInfo.getInstanceId());
//                    rawBill.setInstanceName(hostInfo.getInstanceName());
//                    rawBill.setScode((StringUtils.isBlank(hostInfo.getScode()) || (StringUtils.isNotBlank(rawBill.getScode()) && rawBill.getScode().equals("S01764"))) ? getScode(rawBill) : hostInfo.getScode());
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                } else {
//                    rawBill.setScode(getScode(rawBill));
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                }
                return rawBill;
            }).collect(Collectors.toList());
        } else {
            return projectInfo(item);
        }
    }


    /**
     * 根据实例ID获取主机信息
     *
     * @param item        原始订单列表
     * @param instanceIds 实例ID集合
     * @return 修改后的原始订单列表，其中包含了获取到的主机信息
     */
    public List<RawBill> hostInfoByInstanceId(List<RawBill> item, Set<String> instanceIds) {
        if (instanceIds.size() > 0) {
            // 获取rc_host_info的数据
            List<RcHostInfo> hostInfos = rcHostInfoService.list(new LambdaQueryWrapper<RcHostInfo>().in(RcHostInfo::getInstanceId, instanceIds));
            return item.stream().map(rawBill -> {
                Optional<RcHostInfo> optional = hostInfos.stream().filter(rcHostInfo -> rcHostInfo.getInstanceId().equals(rawBill.getInstanceId())).findFirst();

                if (optional.isPresent()) {
                    RcHostInfo hostInfo = optional.get();
//                    rawBill.setIp(hostInfo.getPrivateIp());
                    rawBill.setInstanceId(hostInfo.getInstanceId());
                    rawBill.setInstanceName(hostInfo.getInstanceName());
                    rawBill.setScode((StringUtils.isBlank(hostInfo.getScode()) || (StringUtils.isNotBlank(rawBill.getScode()) && rawBill.getScode().equals("S01764"))) ? getScode(rawBill) : hostInfo.getScode());
                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
                } else {
                    rawBill.setScode(getScode(rawBill));
                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
                }
                return rawBill;
            }).collect(Collectors.toList());
        } else {
            return projectInfo(item);
        }
    }

    public List<RawBill> hostInfoBySplitItemId(List<RawBill> item, Set<String> instanceIds) {
//        if (instanceIds.size() > 0) {
//            // 获取rc_host_info的数据
//            List<RcHostInfo> hostInfos = rcHostInfoService.list(new LambdaQueryWrapper<RcHostInfo>().in(RcHostInfo::getInstanceId, instanceIds));
//            return item.stream().map(rawBill -> {
//                Optional<RcHostInfo> optional = hostInfos.stream().filter(rcHostInfo -> rcHostInfo.getInstanceId().equals(rawBill.getSplitItem())).findFirst();
//
//                if (optional.isPresent()) {
//                    RcHostInfo hostInfo = optional.get();
//                    rawBill.setIp(hostInfo.getPrivateIp());
//                    rawBill.setInstanceId(hostInfo.getInstanceId());
//                    rawBill.setInstanceName(hostInfo.getInstanceName());
//                    rawBill.setScode((StringUtils.isBlank(hostInfo.getScode()) || (StringUtils.isNotBlank(rawBill.getScode()) && rawBill.getScode().equals("S01764"))) ? getScode(rawBill) : hostInfo.getScode());
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                } else {
//                    rawBill.setScode(getScode(rawBill));
//                    HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                    rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                    rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                }
//                return rawBill;
//            }).collect(Collectors.toList());
//        } else {
//            return projectInfo(item);
//        }
        return null;
    }

    /**
     * 补全实例是新增或者存量
     *
     * @param rawBillList
     * @return
     */
    public void expenseType(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getResourceId).forEach(instanceIds::add);
            instanceIds.remove(null);
            if (instanceIds.size() == 0){
                return item.stream().map(rawBill -> {
//                    rawBill.setExpenseType("存量");
                    return rawBill;
                }).collect(Collectors.toList());
            } else {
                List<BcResourceInfo> list = bcResourceInfoService.list(new LambdaQueryWrapper<BcResourceInfo>().in(BcResourceInfo::getResourceId, instanceIds));
                return item.stream().map(rawBill -> {
//                    Optional<BcResourceInfo> optional = list.stream().filter(bcResourceInfo -> rawBill.getResourceId().contains(bcResourceInfo.getResourceId())).findFirst();
//                    if (optional.isPresent()) {
//                        BcResourceInfo bcResourceInfo = optional.get();
//                        // 判断是否是当前年
//                        if (bcResourceInfo.getCreateDate().getYear() == LocalDateTime.now().getYear()) {
//                            rawBill.setExpenseType("新增");
//                        } else {
//                            rawBill.setExpenseType("存量");
//                        }
//                    } else {
//                        rawBill.setExpenseType("存量");
//                    }
                    return rawBill;
                }).collect(Collectors.toList());
            }
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    /**
     * 补全实例是新增或者存量
     *
     * @param rawBillList
     * @return
     */
    public void expenseTypeBySplitItem(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            Set<String> instanceIds = new HashSet<>();
            // 将resourceId添加到instanceIds集合中
//            item.stream().map(RawBill::getSplitItem).forEach(instanceIds::add);
            instanceIds.remove(null);
            if (instanceIds.size() == 0){
                return item.stream().map(rawBill -> {
//                    rawBill.setExpenseType("存量");
                    return rawBill;
                }).collect(Collectors.toList());
            } else {
                List<BcResourceInfo> list = bcResourceInfoService.list(new LambdaQueryWrapper<BcResourceInfo>().in(BcResourceInfo::getResourceId, instanceIds));
                return item.stream().map(rawBill -> {
//                    Optional<BcResourceInfo> optional = list.stream().filter(bcResourceInfo -> bcResourceInfo.getResourceId().equals(rawBill.getSplitItem())).findFirst();
//                    if (optional.isPresent()) {
//                        BcResourceInfo bcResourceInfo = optional.get();
//                        // 判断是否是当前年
//                        if (bcResourceInfo.getCreateDate().getYear() == LocalDateTime.now().getYear()) {
//                            rawBill.setExpenseType("新增");
//                        } else {
//                            rawBill.setExpenseType("存量");
//                        }
//                    } else {
//                        rawBill.setExpenseType("存量");
//                    }
                    return rawBill;
                }).collect(Collectors.toList());
            }
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    public void expenseTypeOfVpc(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            Set<String> instanceIds = new HashSet<>();
//            // 将resourceId添加到instanceIds集合中
//            item.forEach(rawBill -> {
//                String[] split = rawBill.getResourceId().split(";");
//                rawBill.setInstanceId(split[1]);
//                instanceIds.add(split[1]);
//            });
//            instanceIds.remove(null);
//            if (instanceIds.size() == 0){
//                return item.stream().map(rawBill -> {
//                    rawBill.setExpenseType("存量");
//                    return rawBill;
//                }).collect(Collectors.toList());
//            } else {
//                List<BcResourceInfo> list = bcResourceInfoService.list(new LambdaQueryWrapper<BcResourceInfo>().in(BcResourceInfo::getResourceId, instanceIds));
//                return item.stream().map(rawBill -> {
//                    Optional<BcResourceInfo> optional = list.stream().filter(bcResourceInfo -> bcResourceInfo.getResourceId().equals(rawBill.getInstanceId())).findFirst();
//                    if (optional.isPresent()) {
//                        BcResourceInfo bcResourceInfo = optional.get();
//                        // 判断是否是当前年
//                        if (bcResourceInfo.getCreateDate().getYear() == LocalDateTime.now().getYear()) {
//                            rawBill.setExpenseType("新增");
//                        } else {
//                            rawBill.setExpenseType("存量");
//                        }
//                    } else {
//                        rawBill.setExpenseType("存量");
//                    }
//                    return rawBill;
//                }).collect(Collectors.toList());
//            }
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfHdm(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            Set<String> instanceIds = new HashSet<>();
//            // 将resourceId添加到instanceIds集合中
//            item.forEach(rawBill -> {
//                String[] resourceIds = rawBill.getResourceId().split(";");
//                if (resourceIds.length > 1) {
//                    instanceIds.add(resourceIds[1]);
//                }
//            });
//            instanceIds.remove(null);
//            // 数据补全
//            return databaseInfo(item, instanceIds);
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfAcr(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    GetInstanceResponseBody crInstance = AliyunApi.getCrInstance(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (crInstance != null) {
//                        rawBill.setInstanceId(crInstance.getInstanceId() == null ? null : crInstance.getInstanceId());
//                        rawBill.setInstanceName(crInstance.getInstanceName() == null ? null : crInstance.getInstanceName());
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }


    public void basicDataOfBastionhost(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    DescribeInstanceAttributeResponseBody describeInstanceAttributeResponseBody = AliyunApi.describeInstanceAttribute(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (describeInstanceAttributeResponseBody != null) {
//                        rawBill.setInstanceId(describeInstanceAttributeResponseBody.instanceAttribute == null ? null : describeInstanceAttributeResponseBody.instanceAttribute.instanceId);
//                        rawBill.setInstanceName(describeInstanceAttributeResponseBody.instanceAttribute == null ? null : describeInstanceAttributeResponseBody.instanceAttribute.description);
//                        rawBill.setIp(describeInstanceAttributeResponseBody.instanceAttribute == null ? null : describeInstanceAttributeResponseBody.instanceAttribute.intranetEndpoint);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfMse(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    QueryClusterInfoResponseBody.QueryClusterInfoResponseBodyData queryClusterInfoResponseBodyData = AliyunApi.queryClusterInfo(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (queryClusterInfoResponseBodyData != null) {
//                        rawBill.setInstanceId(queryClusterInfoResponseBodyData.instanceId);
//                        rawBill.setInstanceName(queryClusterInfoResponseBodyData.clusterAliasName);
//                        rawBill.setIp(queryClusterInfoResponseBodyData.intranetDomain);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfCbwp(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    DescribeCommonBandwidthPackagesResponseBody.DescribeCommonBandwidthPackagesResponseBodyCommonBandwidthPackagesCommonBandwidthPackage cbwp
//                            = AliyunApi.describeCommonBandwidthPackages(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (cbwp != null) {
//                        rawBill.setInstanceId(cbwp.bandwidthPackageId == null ? null : cbwp.bandwidthPackageId);
//                        rawBill.setInstanceName(cbwp.name == null ? null : cbwp.name);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfHologram(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    com.aliyun.hologram20220601.models.GetInstanceResponseBody.GetInstanceResponseBodyInstance hologramInstance
//                            = AliyunApi.getHologramInstance(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (hologramInstance != null) {
//                        rawBill.setInstanceId(hologramInstance.instanceId);
//                        rawBill.setInstanceName(hologramInstance.instanceName);
//                        rawBill.setIp(hologramInstance.endpoints == null ? null : hologramInstance.endpoints.get(0).endpoint);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfNat(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
//        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);
//
//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    GetNatGatewayAttributeResponseBody natGatewayAttribute = AliyunApi.getNatGatewayAttribute(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (natGatewayAttribute != null) {
//                        rawBill.setInstanceId(natGatewayAttribute.natGatewayId);
//                        rawBill.setInstanceName(natGatewayAttribute.name);
//                        rawBill.setIp(natGatewayAttribute.privateInfo == null ? null : natGatewayAttribute.privateInfo.privateIpAddress);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfCdn(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();

//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                rawBill.setIp(rawBill.getSplitItem());
//                rawBill.setScode(getScode(rawBill));
//                HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());
//
//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfSae(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();
        // 获取账号信息
//        Map<String, String> openApiKey = getOpenApiKey(rawBillList);

//        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
//            item.forEach(rawBill -> {
//                try {
//                    DescribeApplicationConfigResponseBody.DescribeApplicationConfigResponseBodyData describeApplicationConfigResponseBodyData
//                            = AliyunApi.describeApplicationConfig(openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"), rawBill.getRegion(), rawBill.getResourceId());
//                    if (describeApplicationConfigResponseBodyData != null) {
//                        rawBill.setInstanceId(describeApplicationConfigResponseBodyData.appId);
//                        rawBill.setInstanceName(describeApplicationConfigResponseBodyData.appName);
//                        rawBill.setIp(describeApplicationConfigResponseBodyData.vpcId);
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    } else {
//                        rawBill.setScode(getScode(rawBill));
//                        HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                        rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                        rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
//                    }
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//            });
//            // 数据补全
//            return item;
//        }, localBootAsyncExecutor).exceptionally((e) -> {
//            e.printStackTrace();
//            return null;
//        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

//        if (resList != null) {
//            resList.forEach(res::addAll);
//        }
        rawBillService.updateRawBills(res);
    }

    public void basicDataOfProjectInfo(List<RawBill> rawBillList) {
        // 对每种产品的数据进行拆分
        List<List<RawBill>> partition = ListUtils.partition(rawBillList, 100);
        // 结果集
        List<RawBill> res = new ArrayList<>();

        List<List<RawBill>> resList = partition.stream().map(item -> CompletableFuture.supplyAsync(() -> {
            item.forEach(rawBill -> {
                rawBill.setScode(getScode(rawBill));
                HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//                rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//                rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
            });
            // 数据补全
            return item;
        }, localBootAsyncExecutor).exceptionally((e) -> {
            e.printStackTrace();
            return null;
        })).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        if (resList != null) {
            resList.forEach(res::addAll);
        }
        rawBillService.updateRawBills(res);
    }

    /**
     * 获取S码
     *
     * @param rawBill
     * @return
     */
    public String getScode(RawBill rawBill) {
        String appScode = "";
        String last6Digits = "";

        int length = rawBill.getCostUnit().length();
        if (length > 6) {
            last6Digits = rawBill.getCostUnit().substring(length - 6);
        }

        // Setting appScode
        if (RegularUtil.isSCode(last6Digits)) {
            switch (last6Digits) {
                case "S01764":
                    appScode = "容器云分摊";
                    break;
                default:
                    appScode = last6Digits;
            }
        } else {
            switch (rawBill.getCostUnit()) {
                case "未分配":
                    appScode = "基础设施";
                    String defaultScode = bcDefaultScodeService.getDefaultScode(rawBill.getAccountName());
                    if (defaultScode != null) {
                        appScode = defaultScode;
                    }
                    break;
                default:
                    appScode = rawBill.getCostUnit();
            }
        }

        return appScode;
    }


    /**
     * 根据项目代码获取项目信息
     *
     * @param scode 项目代码
     * @return 项目信息对象
     */
    public HdsOpenApi.Project getProjectInfo(String scode) {
        // 从Redis中获取项目信息列表
        List<HdsOpenApi.Project> projectInfos = JSONObject.parseArray(String.valueOf(redisUtils.get("projectInfos")), HdsOpenApi.Project.class);

        // 如果项目信息列表为空
        if (CollectionUtils.isEmpty(projectInfos)) {
            // 获取项目信息列表
            projectInfos = hdsOpenApi.queryProjectsInfo().getData();

            // 如果项目信息列表不为空
            if (CollectionUtils.isNotEmpty(projectInfos)) {
                // 将项目信息列表存储到Redis中
                redisUtils.set("projectInfos", JSONObject.toJSONString(projectInfos), CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
            }
        }
        // 将项目信息列表转换为以项目代码为键、项目信息为值的映射
        Map<String, HdsOpenApi.Project> projectMap = projectInfos.stream()
                .collect(Collectors.toMap(HdsOpenApi.Project::getAlmSCode, Function.identity()));

        // 返回项目信息对象
        return projectMap.get(scode);
    }

    public Map<String, String> getOpenApiKey(List<RawBill> rawBillList) {
        // 获取账号信息
        List<CloudAccount> enabledAccounts = cloudAccountService.getEnabledAccounts(rawBillList.get(0).getVendor(), rawBillList.get(0).getAccountName())
                .stream().filter(cloudAccount -> cloudAccount.getPurposeType().equals("MAIN")).collect(Collectors.toList());
        String accessKey;
        String accesskeySecret;
        if (CollectionUtils.isNotEmpty(enabledAccounts)) {
            accessKey = enabledAccounts.get(0).getEncrypted().equals("1") ? AesUtil.decryptCBC(enabledAccounts.get(0).getAccessKey()) : enabledAccounts.get(0).getAccessKey();
            accesskeySecret = enabledAccounts.get(0).getEncrypted().equals("1") ? AesUtil.decryptCBC(enabledAccounts.get(0).getAccessKeySecret()) : enabledAccounts.get(0).getAccessKeySecret();
        } else {
            accesskeySecret = "";
            accessKey = "";
        }
        Map<String, String> map = new HashMap<>();
        map.put("accessKey", accessKey);
        map.put("accessKeySecret", accesskeySecret);
        return map;
    }

    public List<RawBill> projectInfo(List<RawBill> item){
        return item.stream().map(rawBill -> {
            rawBill.setScode(getScode(rawBill));
            HdsOpenApi.Project projectInfo = getProjectInfo(rawBill.getScode());
//            rawBill.setProjectCode(projectInfo == null ? null : projectInfo.getId());
//            rawBill.setProjectName(projectInfo == null ? null : projectInfo.getName());
            return rawBill;
        }).collect(Collectors.toList());
    }
}
