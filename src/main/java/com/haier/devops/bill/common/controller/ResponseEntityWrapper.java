package com.haier.devops.bill.common.controller;

import lombok.Data;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;

/**
 * <AUTHOR>
 */
@Data
public class ResponseEntityWrapper<T> {
    private int code = ResponseEnum.SUCCESS.getCode();
    private String msg = ResponseEnum.SUCCESS.getMessage();
    private String detail;
    private T data;
    private String requestId;

    public ResponseEntityWrapper() {
        this.requestId = TraceContext.traceId();
    }

    public ResponseEntityWrapper(ResponseEnum re, String detail, T data) {
        this.code = re.getCode();
        this.msg = re.getMessage();
        this.detail = detail;
        this.data = data;
        this.requestId = TraceContext.traceId();
    }

    public ResponseEntityWrapper(int code, String detail, T data) {
        this.code = code;
        this.detail = detail;
        this.data = data;
        this.requestId = TraceContext.traceId();
    }

    public ResponseEntityWrapper(T data) {
        this.data = data;
        this.requestId = TraceContext.traceId();
    }

    public static void main(String[] args) {
        System.out.println(ResponseEnum.getMessageByCode(10001));
    }
}


