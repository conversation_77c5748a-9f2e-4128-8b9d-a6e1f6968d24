package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.GroupCosts;
import com.haier.devops.bill.common.mapper.GroupCostsMapper;
import com.haier.devops.bill.common.service.GroupCostsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 开发组费用占比及金额 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@Service
public class GroupCostsServiceImpl
        extends ServiceImpl<GroupCostsMapper, GroupCosts> implements GroupCostsService {
    private GroupCostsMapper groupCostsMapper;

    public GroupCostsServiceImpl(GroupCostsMapper groupCostsMapper) {
        this.groupCostsMapper = groupCostsMapper;
    }

    @Override
    public List<GroupCosts> getConfig() {
        return groupCostsMapper.getConfig();
    }
}
