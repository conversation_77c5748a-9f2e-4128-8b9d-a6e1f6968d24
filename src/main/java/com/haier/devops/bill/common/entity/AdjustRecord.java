package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 调账记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@TableName("bc_adjust_record")
@Data
@Builder
public class AdjustRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账户id
     */
    private String accountId;

    /**
     * 实例id
     */
    private String resourceId;

    private String resourceName;

    /**
     * 实例ip
     */
    private String ip;

    /**
     * 目标S码
     */
    private String targetCode;

    /**
     * 账单月份
     */
    private String billingMonth;

    /**
     * 上一次执行的任务id
     */
    private String lastTaskId;

    /**
     * 1 启用，0 禁用
     */
    private Integer isEnabled;
}
