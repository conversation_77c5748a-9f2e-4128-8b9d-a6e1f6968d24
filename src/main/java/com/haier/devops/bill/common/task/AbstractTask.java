package com.haier.devops.bill.common.task;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public abstract class AbstractTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractTask.class);
    private static final String CHECK_IF_TASK_RUNNING_URL = "http://%s:%s/api/hcms/v1/task/if-task-running";

    @Value("${server.port}")
    protected Integer serverPort;

    private final ConnectionPool connectionPool = new ConnectionPool(10, 5, TimeUnit.MINUTES);
    private final OkHttpClient client = new OkHttpClient().newBuilder()
            .connectionPool(connectionPool)
            .callTimeout(6000, TimeUnit.MILLISECONDS)
            .readTimeout(6000, TimeUnit.MILLISECONDS)
            .build();
    private final Gson gson = new Gson();

    // 任务执行状态 key为任务id，value 为任务是否正在执行
    public static Map<String, Boolean> runningTaskMap = new java.util.concurrent.ConcurrentHashMap<>();

    abstract void doExecute();

    @Async
    public void execute() {
        doExecute();
    }

    static void sleep(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException e) {
            LOGGER.error("任务监听器休眠异常", e);
        }
    }

    protected ResponseEntityWrapper<Boolean> checkIfTaskIsRunning(TaskEntity task) {
        String url = String.format(CHECK_IF_TASK_RUNNING_URL, task.getHost(), this.serverPort);
        HttpUrl.Builder queryUrlBuilder = HttpUrl.get(url).newBuilder();
        queryUrlBuilder.addQueryParameter("taskId", task.getTaskId());

        Request request = new Request.Builder()
                .url(queryUrlBuilder.build())
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (200 == response.code() && null != response.body()) {
                Type type = new TypeToken<ResponseEntityWrapper<Boolean>>() {
                }.getType();
                return gson.fromJson(response.body().string(), type);
            }
        } catch (IOException e) {
            LOGGER.error("checkIfTaskIsRunning error", e);
        }

        return new ResponseEntityWrapper<>(false);
    }
}
