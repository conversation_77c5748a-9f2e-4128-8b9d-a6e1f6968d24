package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmConfigurationCreateDTO;
import com.haier.devops.bill.common.dto.AlarmConfigurationDTO;
import com.haier.devops.bill.common.dto.IsEnableAlarmDTO;
import com.haier.devops.bill.common.entity.AlarmConfiguration;
import com.haier.devops.bill.common.vo.CpAlarmConfigurationVo;

import java.util.List;

/**
* @ClassName: AlarmConfigurationService
* @Description: 告警配置Service接口
* @author: 张爱苹
* @date: 2024/1/11 10:52
*/
public interface AlarmConfigurationService extends IService<AlarmConfiguration> {

    /**
     * 获取所有告警配置
     *
     * @return 告警配置列表
     */
    List<AlarmConfiguration> getAllAlarmConfigurations();

    /**
     * 根据ID获取告警配置
     *
     * @param id 告警配置ID
     * @return 告警配置
     */
    CpAlarmConfigurationVo getAlarmConfigurationById(Integer id) throws Exception;

    /**
     * 创建告警配置
     *
     * @param alarmConfigurationCreateDTO 告警配置
     * @return 创建成功的告警配置
     */
    void createAlarmConfiguration(AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception;

    /**
     * 更新告警配置
     *
     * @param id                 告警配置ID
     * @param alarmConfigurationCreateDTO 更新的告警配置
     * @return 更新后的告警配置
     */
    void updateAlarmConfiguration(Integer id, AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception;

    /**
     * 删除告警配置
     *
     * @param id 告警配置ID
     */
    void deleteAlarmConfiguration(Integer id) throws Exception;

    /**
    * @Description: 分页获取告警配置列表
    * @author: 张爱苹
    * @date: 2024/1/11 11:24
    * @param dto:
    * @Return: com.github.pagehelper.PageInfo<com.haier.devops.bill.common.entity.AlarmConfiguration>
    */
    PageInfo<CpAlarmConfigurationVo> listByPage(AlarmConfigurationDTO dto) throws Exception;

    /**
    * @Description: 更新告警配置启用状态
    * @author: 张爱苹
    * @date: 2024/1/15 14:18
    * @param isEnableAlarmDTO:
    * @Return: void
    */
    void updateEnableStatus(IsEnableAlarmDTO isEnableAlarmDTO) throws Exception;
}
