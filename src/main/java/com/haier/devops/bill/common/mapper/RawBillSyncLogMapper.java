package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.RawBillSyncLog;

import java.util.List;

/**
 * <p>
 *  用于记录已经同步的账单信息  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface RawBillSyncLogMapper extends BaseMapper<RawBillSyncLog> {

    /**
     * 获取待汇总的账单记录
     * @param vendor
     * @return
     */
    List<RawBillSyncLog> getPendingAggregatedBillSyncLog(String vendor);
}
