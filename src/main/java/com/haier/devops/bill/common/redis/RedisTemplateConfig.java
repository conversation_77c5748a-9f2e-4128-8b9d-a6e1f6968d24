package com.haier.devops.bill.common.redis;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.distributed_lock.LockObj;
import com.haier.devops.bill.common.entity.CloudAccount;
import com.haier.devops.bill.common.entity.DataSupplementConfig;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import com.haier.devops.bill.export.vo.ExportVo;
import com.haier.devops.bill.export.vo.OmnibearingExportApplicationVo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import software.amazon.awssdk.services.savingsplans.SavingsplansClient;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisTemplateConfig {
    @Bean("lockTemplate")
    RedisTemplate<String, LockObj> lockTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("omnibearingExportTemplate")
    RedisTemplate<String, OmnibearingExportApplicationVo> omnibearingRedisTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("detailExportTemplate")
    RedisTemplate<String, DetailExportApplicationVo> detailRedisTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }


    @Bean("uploadingProbeTemplate")
    RedisTemplate<String, ExportVo> uploadingProbeTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    // ============ ↑queue  ↓cache ===========


    @Bean("hcmsUserTemplate")
    RedisTemplate<String, HworkAuthorityApi.User> hcmsUserTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("cloudAccountTemplate")
    RedisTemplate<String, List<CloudAccount>> cloudAccountCache(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("dataSupplementTemplate")
    RedisTemplate<String, List<DataSupplementConfig>> dataSupplementCache(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("authorityScodesTemplate")
    RedisTemplate<String, List<String>> authorityScodesTemplate(RedisConnectionFactory factory){
        return composeRedisTemplate(factory);
    }

    @Bean("hdsSubProductsTemplate")
    RedisTemplate<String, List<HdsSubProducts>> hdsSubProductsTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    @Bean("savingsplansClientTemplate")
    RedisTemplate<String, SavingsplansClient> savingsplansClientTemplate(RedisConnectionFactory factory) {
        return composeRedisTemplate(factory);
    }

    private <T> RedisTemplate<String, T> composeRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, T> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }
}
