package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;

import java.util.List;

/**
 * <p>
 * 到资源的汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface ResourceAggregationService extends IService<ResourceAggregation> {
    /**
     * 按照资源查询月度账单明细
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ResourceAggregation> queryBillByResource(String vendor, String billingCycle, String... account);

    /**
     * 根据汇总账单查询明细账单
     * @param aggregation
     * @return
     */
    List<ResourceAggregation> selectByApplication(ApplicationAggregation aggregation);

    /**
     * 根据多个汇总账单查明细账单
     * @param aggregations
     * @return
     */
    List<ResourceAggregation> selectByApplications(List<ApplicationAggregation> aggregations);

    void deleteByVendorAndBillingCycleAndStageAndAccount(String vendor, String billingCycle, String... account);


    /**
     * 查找待推送的项目
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ResourceAggregation> selectPendingSyncingItems(String vendor,
                                                        String billingCycle,
                                                        String... account);
}
