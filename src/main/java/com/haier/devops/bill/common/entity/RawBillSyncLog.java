package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *  用于记录已经同步的账单信息 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@TableName("bc_raw_bill_sync_log")
@Data
public class RawBillSyncLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 云厂商字段
     */
    private String vendor;

    /**
     * 记录的账号信息-账户名称
     */
    private String accountName;

    /**
     * 记录的账号信息-账户ID
     */
    private String accountId;

    /**
     * 账单周期
     */
    private String billingCycle;

    /**
     *  同步状态 
     */
    private String status;

    /**
     * 阶段
     */
    private String stage;

    /**
     *  同步异常信息 
     */
    private String errMsg;
}
