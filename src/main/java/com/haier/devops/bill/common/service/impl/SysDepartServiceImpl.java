package com.haier.devops.bill.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.SysDepart;
import com.haier.devops.bill.common.mapper.SysDepartMapper;
import com.haier.devops.bill.common.service.AuthorityService;
import com.haier.devops.bill.common.service.SysDepartService;
import com.haier.devops.bill.common.vo.DepartIdVo;
import com.haier.devops.bill.util.FindsDepartsChildrenUtil;
import com.haier.devops.bill.util.HeaderUtil;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * (SysDepart)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-19 15:36:14
 */
@Service("sysDepartService")
@DS("pg")
public class SysDepartServiceImpl extends ServiceImpl<SysDepartMapper, SysDepart> implements SysDepartService {
    @Resource
    private SysDepartMapper sysDepartMapper;

    private AuthorityService authorityService;

    public SysDepartServiceImpl(AuthorityService authorityService) {
        this.authorityService = authorityService;
    }

    public List<DepartIdVo> queryDepartIdTreeList() {
        String userCode = HeaderUtil.getUserId();
        LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
        query.eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_FALSE);
        query.orderByAsc(SysDepart::getDepartOrder);
        List<SysDepart> list = this.list(query);
        // 调用wrapTreeDataToTreeList方法生成树状数据
        List<DepartIdVo> listResult = FindsDepartsChildrenUtil.wrapTreeDataToDepartIdTreeList(list);
        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> resultWrapper = authorityService.getUserAuthorities(userCode);
        if (resultWrapper.getCode() == HttpStatus.OK.value()) {
            departmentalAuthorization(listResult, resultWrapper.getData());
        }
        return listResult;
    }

     public void departmentalAuthorization(List<DepartIdVo> listResult,  List<HworkAuthorityApi.Authority> auth){
         for (DepartIdVo departIdVo : listResult) {
             Optional<HworkAuthorityApi.Authority> authority = auth.stream().filter(item -> item.getAuthorityCode().equals(departIdVo.getKey())).findFirst();
             if (authority.isPresent()){
                 departIdVo.setAuthed(true);
             }
             if (CollectionUtils.isNotEmpty(departIdVo.getChildren())){
                 departmentalAuthorization(departIdVo.getChildren(), auth);
             }
         }
     }
}
