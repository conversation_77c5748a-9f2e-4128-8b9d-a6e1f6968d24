package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;

import java.util.List;

/**
 * <p>
 * 待替换账单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface PendingSubstitutionBillService extends IService<PendingSubstitutionBill> {
    int clearDirtyData(String subTaskId);

    List<PendingSubstitutionBill> selectBySubTaskId(String subTaskId);

    /**
     * 获取替换任务的总金额
     * @param subTaskId
     * @return
     */
    String getSumBySubTaskId(String subTaskId);
}
