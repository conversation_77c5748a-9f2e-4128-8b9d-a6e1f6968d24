package com.haier.devops.bill.common.api.interceptor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 帮助类 -读取yml中的参数
 */
@Component
class GatewayApiKeyHelper {

    public GatewayApiKeyHelper() { }

    static String API_KEY;

    @Value("${api.hds.user.token}")
    public void setApiKey(String apiKey){
        API_KEY = apiKey;
    }

}
