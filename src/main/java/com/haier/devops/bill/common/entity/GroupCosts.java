package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 开发组费用占比及金额
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-31
 */
@TableName("va_t_group_costs")
@Data
public class GroupCosts implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 开发组ID
     */
    private Integer groupId;

    /**
     * 开发组名称
     */
    private String groupName;

    /**
     * alm编码
     */
    private String applicationId;

    /**
     * 费用占比
     */
    private BigDecimal proportion;

    /**
     * 月份，格式：2023-10
     */
    private String month;

    /**
     * 金额
     */
    private BigDecimal costs;
}
