package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.BcDefaultScode;
import com.haier.devops.bill.common.mapper.BcDefaultScodeMapper;
import com.haier.devops.bill.common.service.BcDefaultScodeService;
import com.haier.devops.bill.util.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * (BcDefaultScode)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-14 14:59:46
 */
@Service("bcDefaultScodeService")
public class BcDefaultScodeServiceImpl extends ServiceImpl<BcDefaultScodeMapper, BcDefaultScode> implements BcDefaultScodeService {
    @Resource
    private BcDefaultScodeMapper bcDefaultScodeDao;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 根据账号获取对应的S码
     * @param accountName
     * @return
     */
    public String getDefaultScode(String accountName){
        String scode = (String) redisUtils.get(accountName);
        if (StringUtils.isBlank(scode)){
            List<BcDefaultScode> bcDefaultScodes = bcDefaultScodeDao.selectList(new LambdaQueryWrapper<BcDefaultScode>().eq(BcDefaultScode::getAccount, accountName));

            if (CollectionUtils.isNotEmpty(bcDefaultScodes)){
                scode = bcDefaultScodes.get(0).getScode();
                redisUtils.set(accountName, scode, CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
            }
        }

        return scode;
    }
}
