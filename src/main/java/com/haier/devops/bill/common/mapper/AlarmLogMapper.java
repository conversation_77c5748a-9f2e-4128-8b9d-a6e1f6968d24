package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.AlarmLogDTO;
import com.haier.devops.bill.common.entity.CpAlarmLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* @ClassName: AlarmNoticeGroupMapper
* @Description:   告警日志Mapper
* @author: 张爱苹
* @date: 2024/1/12 17:23
*/
@Repository
public interface AlarmLogMapper extends BaseMapper<CpAlarmLog> {

    List<CpAlarmLog> listByPage(@Param("dto") AlarmLogDTO dto);
}
