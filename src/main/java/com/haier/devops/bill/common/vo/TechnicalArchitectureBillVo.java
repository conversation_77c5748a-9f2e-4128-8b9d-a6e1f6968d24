package com.haier.devops.bill.common.vo;

import lombok.Data;

/**
 * @Description: 技术架构账单
 * @Author: A0018437
 * @Date：2024-03-21
 */
@Data
public class TechnicalArchitectureBillVo {
    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账号id
     */
    private String accountId;

    /**
     * 账号
     */
    private String accountName;

    /**
     * 账期
     */
    private String billingCycle;

    /**
     * S码
     */
    private String scode;

    /**
     * 金额
     */
    private String summer;

    /**
     * 云产品编码
     */
    private String productCode;

    /**
     * 云产品名称
     */
    private String productName;

    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 内网ip
     */
    private String privateIp;
}