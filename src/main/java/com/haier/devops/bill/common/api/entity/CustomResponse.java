package com.haier.devops.bill.common.api.entity;

import com.haier.devops.bill.common.enums.StatusEnum;
import com.haier.devops.bill.common.interceptor.CusRequestInterceptor;
import lombok.Data;
import org.slf4j.MDC;

import java.io.Serializable;

/**
 */
@Data
public class CustomResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private T data;
    private Integer code;
    private String msg;

    // detail 暂时定为requestId
    private String detail= MDC.get(CusRequestInterceptor.REQUEST_ID_KEY);


    public static <T> CustomResponse<T> ok(T data) {
        CustomResponse<T> resp = new CustomResponse<>();
        resp.setCode(StatusEnum.COMMON_SUC.getCode());
        resp.setData(data);
        resp.setDetail(StatusEnum.COMMON_SUC.getCode() +"_"+ resp.getDetail());
        return resp;
    }

    public static <T> CustomResponse<T> ok() {
        CustomResponse<T> resp = new CustomResponse<>();
        resp.setCode(StatusEnum.COMMON_SUC.getCode());
        resp.setDetail(StatusEnum.COMMON_SUC.getCode() +"_"+ resp.getDetail());
        return resp;
    }

    public static <T> CustomResponse<T> fail(Integer errorCode, String msg) {
        CustomResponse<T> resp = new CustomResponse<>();
        resp.setCode(errorCode);
        resp.setMsg(msg);
        resp.setDetail(errorCode +"_"+ resp.getDetail());
        return resp;
    }

    public static <T> CustomResponse<T> fail(Integer code, String msg, T data) {
        CustomResponse<T> resp = new CustomResponse<>();
        resp.setCode(code);
        resp.setMsg(msg);
        resp.setData(data);
        return resp;
    }

    @Override
    public String toString() {
        return "CustomResponse{" +
                "data=" + data +
                ", code=" + code +
                ", msg='" + msg + '\'' +
                ", detail='" + detail + '\'' +
                '}';
    }
}



