package com.haier.devops.bill.common.api;

import com.google.gson.annotations.Expose;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import feign.Headers;
import feign.RequestLine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HworkAcceptanceApi {
    /**
     * 查询所有云服务类的项目--对外云管平台
     * @return
     */
    @RequestLine("GET /edop/edop-operation-service/edopportal/api/v1/yunOrder/getAlmMilestoneList")
    ResultWrapper<List<Milestone>> getAlmMilestoneList();

    @RequestLine("POST /edop/edop-operation-service/edopportal/api/v1/yunOrder/saveMainOrder")
    @Headers("Content-Type: application/json;charset=utf-8")
    ResultWrapper<Boolean> syncApplicationAggregation(@RequestBody ApplicationAggregationWrapper body);

    @RequestLine("POST /edop/edop-operation-service/edopportal/api/v1/yunOrder/saveOrderDetail")
    @Headers("Content-Type: application/json;charset=utf-8")
    ResourceAggregationResultWrapper syncResourceAggregation(@RequestBody ResourceAggregationWrapper body);

    @Data
    class ResourceAggregationResultWrapper {
        @Expose
        private Double code;
        @Expose
        private String msg;
        @Expose
        private Boolean data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ApplicationAggregationWrapper {
        @Expose
        private Integer orderNumber;
        @Expose
        private BigDecimal orderTotalAmount;
        @Expose
        private BigDecimal orderTotalActualAmount;
        @Expose
        private List<ApplicationAggregation> orderList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ResourceAggregationWrapper {
        @Expose
        private Integer detailNumber;
        @Expose
        private BigDecimal detailTotalAmount;
        @Expose
        private List<ResourceAggregation> orderDetailList;
    }
}
