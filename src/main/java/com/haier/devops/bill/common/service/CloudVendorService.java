package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.CloudVendor;

/**
* @ClassName: CloudVendorService
* @Description:  云厂商 服务类
* @author: 张爱苹
* @date: 2024/3/4 10:01
*/
public interface CloudVendorService extends IService<CloudVendor> {

    /**
    * @Description: 根据云厂商编码获取云厂商信息
    * @author: 张爱苹
    * @date: 2024/3/4 10:05

    * @Return: com.haier.devops.bill.common.entity.CloudVendor
    */
    CloudVendor getCloudVendor(String vendorCode);
}
