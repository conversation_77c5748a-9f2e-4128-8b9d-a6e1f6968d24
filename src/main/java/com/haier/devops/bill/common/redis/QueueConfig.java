package com.haier.devops.bill.common.redis;

import com.haier.devops.bill.common.distributed_lock.LockObj;
import com.haier.devops.bill.export.vo.DetailExportApplicationVo;
import com.haier.devops.bill.export.vo.ExportVo;
import com.haier.devops.bill.export.vo.OmnibearingExportApplicationVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 */
@Configuration
public class QueueConfig {

    private RedisTemplate<String, LockObj> distributedLockTemplate;
    private RedisTemplate<String, OmnibearingExportApplicationVo> omnibearingRedisTemplate;
    private RedisTemplate<String, DetailExportApplicationVo> detailRedisTemplate;
    private RedisTemplate<String, ExportVo> uploadingProbeTemplate;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Bean("distributedLockQueue")
    public Queue<LockObj> distributedLockQueue() {
        return new Queue<>(distributedLockTemplate, QueueConstants.DISTRIBUTED_LOCK_QUEUE + "-" +
                (StringUtils.isNotBlank(activeProfile) ? activeProfile : ""));
    }

    @Bean("omnibearingExportQueue")
    public Queue<OmnibearingExportApplicationVo> omnibearingExportQueue() {
        return new Queue<>(omnibearingRedisTemplate, QueueConstants.OMNIBEARING_EXPORT_QUEUE + "-" +
                (StringUtils.isNotBlank(activeProfile) ? activeProfile : ""));
    }

    @Bean("detailExportQueue")
    public Queue<DetailExportApplicationVo> detailExportQueue() {
        return new Queue<>(detailRedisTemplate, QueueConstants.DETAIL_EXPORT_QUEUE + "-" +
                (StringUtils.isNotBlank(activeProfile) ? activeProfile : ""));
    }


    @Bean("uploadingProbeQueue")
    public Queue<ExportVo> uploadingProbeQueue() {
        return new Queue<>(uploadingProbeTemplate, QueueConstants.UPLOADING_PROBE_QUEUE + "-" +
                (StringUtils.isNotBlank(activeProfile) ? activeProfile : ""));
    }

    // ------------ setters --------------

    @Autowired
    @Qualifier("lockTemplate")
    public void setDistributedLockTemplate(RedisTemplate<String, LockObj> distributedLockTemplate) {
        this.distributedLockTemplate = distributedLockTemplate;
    }

    @Autowired
    @Qualifier("omnibearingExportTemplate")
    public void setOmnibearingRedisTemplate(RedisTemplate<String, OmnibearingExportApplicationVo> omnibearingRedisTemplate) {
        this.omnibearingRedisTemplate = omnibearingRedisTemplate;
    }

    @Autowired
    @Qualifier("detailExportTemplate")
    public void setDetailRedisTemplate(RedisTemplate<String, DetailExportApplicationVo> detailRedisTemplate) {
        this.detailRedisTemplate = detailRedisTemplate;
    }

    @Autowired
    @Qualifier("uploadingProbeTemplate")
    public void setUploadingProbeTemplate(RedisTemplate<String, ExportVo> uploadingProbeTemplate) {
        this.uploadingProbeTemplate = uploadingProbeTemplate;
    }
}
