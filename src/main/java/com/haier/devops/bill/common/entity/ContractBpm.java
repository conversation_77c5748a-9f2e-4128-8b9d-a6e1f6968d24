package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 合同验收bpm审批流id映射
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@TableName("bc_contract_bpm")
@Data
public class ContractBpm implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 记录更新时间
     */
    private Date updateTime;

    private Integer contractId;

    private Integer stageId;

    private Integer budgetId;

    private String instanceId;

    private String instanceStatus;

    private String bizCode;

}
