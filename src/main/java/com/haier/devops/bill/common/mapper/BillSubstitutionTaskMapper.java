package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.BillSubstitutionTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账单替换任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
public interface BillSubstitutionTaskMapper extends BaseMapper<BillSubstitutionTask> {
    /**
     * 更新任务表阶段和进度
     * @param subTaskId
     * @param stage
     * @param process
     * @return
     */
    int updateStageAndProcessByTaskId(@Param("subTaskId") String subTaskId,
                                      @Param("stage") String stage,
                                      @Param("process") Integer process);


    /**
     * 查询待迁移任务
     * @param type
     * @return
     */
    List<BillSubstitutionTask> getPendingMigrationTaskByType(@Param("type") String type, @Param("billingCycle") String billingCycle);


    /**
     * 查询待计算任务
     * @param type
     * @return
     */
    List<BillSubstitutionTask> getPendingCalculationTaskByType(@Param("type") String type, @Param("billingCycle") String billingCycle);

    /**
     * 查询待计算任务
     * @param type
     * @return
     */
    List<BillSubstitutionTask> getPendingSubstitutionTaskByType(@Param("type") String type, @Param("billingCycle") String billingCycle);


    /**
     * 更新错误信息
     * @param subTaskId
     * @param errorInfo
     */
    void updateErrorInfo(@Param("subTaskId") String subTaskId, @Param("errorInfo") String errorInfo);

    List<BillSubstitutionTask> getPendingCleaningTaskByType(@Param("type") String type, @Param("billingCycle") String billingCycle);
}
