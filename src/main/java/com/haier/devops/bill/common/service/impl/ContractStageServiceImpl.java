package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.ContractStage;
import com.haier.devops.bill.common.mapper.ContractStageMapper;
import com.haier.devops.bill.common.service.ContractStageService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 合同验收阶段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class ContractStageServiceImpl extends ServiceImpl<ContractStageMapper, ContractStage> implements ContractStageService {

}
