package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: SysDict
* @Description:  字典表
* @author: 张爱苹
* @date: 2024/2/4 10:07
*/
@Data
public class SysDictCreateDTO implements Serializable {


    private static final long serialVersionUID = -6860504390162017092L;

    /**
     * 字典名称
     */
    @NotEmpty
    private String dictName;

    /**
     * 字典编码
     */
    @NotEmpty
    private String dictCode;

    /**
     * 描述
     */
    private String description;


}
