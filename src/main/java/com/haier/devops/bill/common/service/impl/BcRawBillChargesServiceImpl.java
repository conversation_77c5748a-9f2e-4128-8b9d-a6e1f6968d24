package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.BcRawBillCharges;
import com.haier.devops.bill.common.entity.BcRawBillProducts;
import com.haier.devops.bill.common.mapper.BcRawBillChargesMapper;
import com.haier.devops.bill.common.mapper.RawBillMapper;
import com.haier.devops.bill.common.service.BcRawBillChargesService;
import com.haier.devops.bill.common.service.BcRawBillProductsService;
import com.haier.devops.bill.common.vo.BillChargesProductsVo;
import com.haier.devops.bill.common.vo.BillChargesVo;
import com.haier.devops.bill.util.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * (BcRawBillCharges)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-02 11:08:19
 */
@Service("bcRawBillChargesService")
public class BcRawBillChargesServiceImpl extends ServiceImpl<BcRawBillChargesMapper, BcRawBillCharges> implements BcRawBillChargesService {
    @Resource
    BcRawBillChargesMapper bcRawBillChargesDao;
    @Resource
    RawBillMapper rawBillMapper;
    @Resource
    BcRawBillProductsService bcRawBillProductsService;
    @Resource
    RedisUtils redisUtils;



    /**
     * 批量插入账单收费项信息
     */
    public void insertBillCharges(){
        // 从中间明细表bc_refined_raw_bill中按照云厂商、产品编码、产品名称、收费项分组查询
        // 一个产品编码对应多个产品名称，一个产品编码+产品名称对应多个收费项
        List<BillChargesVo> rawBillList = rawBillMapper.getBillCharges();

        // 关联查询 云厂商、产品编码、产品名称（在bc_raw_bill_products中）、收费项（在bc_raw_bill_charges中）
        List<BillChargesVo> billCharges = bcRawBillChargesDao.getBillCharges();

        // 将rawBillList（实时查询统计出的结果）中没有落入bc_raw_bill_charges表中的数据筛选出来
        List<BillChargesVo> collect = rawBillList.stream()
                .filter(item -> billCharges.stream()
                        .allMatch(each -> !(item.getVendor().equals(each.getVendor())
                                && item.getProductCode().equals(each.getProductCode())
                                && item.getBillingItem().equals(each.getBillingItem()))))
                .collect(Collectors.toList());

        collect.forEach(item -> {
            Optional<BillChargesVo> chargesVo = billCharges.stream().filter(charges -> filterVendorAndProduct(item, charges)).findFirst();
            Integer id = (Integer)redisUtils.get(item.getVendor() + item.getProductCode() + "id");
            if (chargesVo.isPresent()){
                if (id == null){
                    List<BcRawBillProducts> list = bcRawBillProductsService.list(
                            new LambdaQueryWrapper<BcRawBillProducts>()
                                    .eq(BcRawBillProducts::getProductCode, item.getProductCode())
                                    .eq(BcRawBillProducts::getVendor, item.getVendor())
                    );
                    if (CollectionUtils.isNotEmpty(list)){
                        id = list.get(0).getId();
                        redisUtils.set(item.getVendor() + item.getProductCode() + "id",id,1L, TimeUnit.DAYS);
                    }
                }

                BcRawBillCharges bcRawBillCharges = new BcRawBillCharges(id,item.getBillingItem());
                this.save(bcRawBillCharges);
            } else {
                if (id == null){
                    BcRawBillProducts bcRawBillProducts = new BcRawBillProducts(item);
                    bcRawBillProductsService.save(bcRawBillProducts);
                    id = bcRawBillProducts.getId();
                    redisUtils.set(item.getVendor() + item.getProductCode() + "id",id,1L, TimeUnit.DAYS);
                }

                BcRawBillCharges bcRawBillCharges = new BcRawBillCharges(id,item.getBillingItem());
                this.save(bcRawBillCharges);
            }
        });
    }

    private static boolean filterVendorAndProduct(BillChargesVo one, BillChargesVo two){

        if(one.getVendor().equals(two.getVendor()) && one.getProductCode().equals(two.getProductCode())){
            return true;
        }
        return false;
    }

    public List<BillChargesProductsVo> getProductChargeInfo(String productCode, String vendor){
        QueryWrapper<BcRawBillCharges> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(productCode)) {
            queryWrapper.and(QueryWrapper -> QueryWrapper
                    .like("bp.product_code", productCode).or()
                    .like("bp.product_name", productCode));
        }
        queryWrapper.eq("bc.del_flag", 0);
        if (StringUtils.isNotBlank(vendor)) {
            queryWrapper.eq("bp.vendor", vendor);
        }
        queryWrapper.groupBy("bp.product_code", "bp.product_name");
       return bcRawBillChargesDao.getProductChargeInfo(queryWrapper);
    }
}
