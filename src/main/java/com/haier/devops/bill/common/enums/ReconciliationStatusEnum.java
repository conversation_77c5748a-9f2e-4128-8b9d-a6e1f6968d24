package com.haier.devops.bill.common.enums;

/**
* @ClassName: RuleTypeEnum
* @Description: 规则类型
* @author: 张爱苹
* @date: 2024/3/11 16:29
*/
public enum ReconciliationStatusEnum {
    DONE("done","已完成"),
    INPROGRESS("inProgress","进行中"),
    FAIL("fail","失败");

    ReconciliationStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
