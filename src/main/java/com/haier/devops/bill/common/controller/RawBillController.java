package com.haier.devops.bill.common.controller;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.service.BcRawBillChargesService;
import com.haier.devops.bill.common.service.RawBillService;
import com.haier.devops.bill.common.validation.YearMonthFormat;
import com.haier.devops.bill.common.vo.BillChargesProductsVo;
import com.haier.devops.bill.common.vo.CostProcedureVo;
//import com.haier.devops.bill.schedule.BillDataCompletionTask;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 账单查询
 */
@RestController
@Slf4j
@RequestMapping("/api/v1/hcms/bill")
@Validated
public class RawBillController {
    private RawBillService rawBillService;
//    @Resource
//    private BillDataCompletionTask billDataCompletionTask;

    private BcRawBillChargesService chargesService;

    public RawBillController(RawBillService rawBillService, BcRawBillChargesService chargesService) {
        this.rawBillService = rawBillService;
        this.chargesService = chargesService;
    }

    /**
     * 钻取计算过程
     *
     * @param rw
     * @return
     */
    @GetMapping("/cost-procedure")
    public ResponseEntityWrapper<PageInfo<CostProcedureVo>> query(@Valid RequestWrapper rw) {
        PageInfo<CostProcedureVo> pageInfo =
                rawBillService.selectCostProcedure(rw.getVendor(), rw.getCostUnit(), rw.getProductCode(),
                        rw.getBillingCycle(), rw.getPage(), rw.getPer_page());
        return new ResponseEntityWrapper<>(pageInfo);
    }
    @GetMapping("/billDataCompletion")
    public void billDataCompletion(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date){
//        billDataCompletionTask.billDataCompletion(date);
    }

    /**
     * 查询收费项
     * @param productCode
     * @return
     */
    @GetMapping("/billing-item")
    public ResponseEntityWrapper<List<BillChargesProductsVo>> query(@Valid String productCode, @NotBlank String vendor) {
        return new ResponseEntityWrapper<>(chargesService.getProductChargeInfo(productCode,vendor));
    }

    @Getter
    @Setter
    @ToString
    class RequestWrapper {
        /**
         * 云厂商
         */
        @NotNull(message = "vendor can not be null")
        String vendor;

        /**
         * 结算单元
         */
        @NotNull(message = "costUnit can not be null")
        String costUnit;

        /**
         * 产品编码
         */
        @NotNull(message = "productCode can not be null")
        String productCode;

        /**
         * 账期（格式为"yyyy-mm"，例如"2023-01"）
         */
        @YearMonthFormat(message = "billingCycle is not a valid 'year-month' format")
        String billingCycle;

        @Min(1)
        Integer page = 1;
        @Min(1)
        Integer per_page = 10;
    }
}
