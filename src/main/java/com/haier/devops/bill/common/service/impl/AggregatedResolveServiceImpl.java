package com.haier.devops.bill.common.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.mapper.AggregatedBillMapper;
import com.haier.devops.bill.common.service.AggregatedResolveService;
import com.haier.devops.bill.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AggregatedBillServiceImpl
 * @Description: 账单明细汇总表 服务实现类
 * @author: 张爱苹
 * @date: 2024/1/31 10:23
 */
@Service
@Slf4j
public class AggregatedResolveServiceImpl extends ServiceImpl<AggregatedBillMapper, AggregatedBill> implements AggregatedResolveService {
    private Logger logger = LoggerFactory.getLogger(AggregatedResolveServiceImpl.class);

    private PlatformTransactionManager transactionManager;


    @Resource
    AggregatedBillMapper aggregatedBillMapper;

    public AggregatedResolveServiceImpl(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
    }
    @Override
    public void triggerAggregationTaskAmongAggregatedId(String vendor,String start, String end, String... aggregatedId) {
        // 按月汇总的账单
        List<AggregatedBill> groupedAggregatedBills = getGroupedAggregatedBillsBetweenMonths(start, end, vendor, aggregatedId);

        // 手动控制事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            // 清除旧的汇总账单
            aggregatedBillMapper.clearAdjustedMonthBills(start, end, aggregatedId);
            // 插入新的汇总账单
            saveOrUpdateWhenAggregatingInMonth(groupedAggregatedBills);

            transactionManager.commit(status);
        } catch (Exception e) {
            logger.error("triggerAggregationTaskAmongAggregatedId error: start = {}, end = {}, aggregatedIds = {}", start, end, aggregatedId, e);
            transactionManager.rollback(status);
        }
    }


    @Override
    public List<AggregatedBill> getGroupedAggregatedBillsBetweenMonths(String start, String end, String vendor, String... aggregatedId) {
        start = null != start ? DateUtil.getFirstDayOfMonth(start) : null;
        end = null != end ? DateUtil.getLastDayOfMonth(end) : null;
        return aggregatedBillMapper.getGroupedAggregatedBillsBetweenDays(start, end, vendor, aggregatedId);
    }

    @Override
    public List<AggregatedBill> getAggregatedBillsByAccount(String start, String end, String vendor, String account) {
        start = null != start ? DateUtil.getFirstDayOfMonth(start) : null;
        end = null != end ? DateUtil.getLastDayOfMonth(end) : null;
        return aggregatedBillMapper.getAggregatedBillsByAccount(start, end, vendor, account);
    }

    @Override
    public void clearOldAggregatedBillByAccount(String vendor, String start, String end, String account) {
        aggregatedBillMapper.clearMonthBillsByAccount(vendor, start, end, account);
    }


    @Override
    public void saveOrUpdateWhenAggregatingInMonth(List<AggregatedBill> list) {
        SqlHelper.saveOrUpdateBatch(entityClass, this.mapperClass, super.log, list, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<AggregatedBill> queryWrapper =
                    Wrappers.<AggregatedBill>lambdaQuery()
                            .eq(AggregatedBill::getAggregatedId, entity.getAggregatedId())
                            .eq(AggregatedBill::getScode, entity.getScode())
                            .eq(AggregatedBill::getGranularity, entity.getGranularity())
                            .eq(AggregatedBill::getBillingCycle, entity.getBillingCycle());
            Map<String, Object> map = CollectionUtils.newHashMapWithExpectedSize(1);
            map.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(getSqlStatement(SqlMethod.SELECT_LIST), map));
        }, (sqlSession, entity) -> {
            LambdaUpdateWrapper<AggregatedBill> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AggregatedBill::getAggregatedId, entity.getAggregatedId());
            lambdaUpdateWrapper.eq(AggregatedBill::getScode, entity.getScode());
            lambdaUpdateWrapper.eq(AggregatedBill::getGranularity, entity.getGranularity());
            lambdaUpdateWrapper.eq(AggregatedBill::getBillingCycle, entity.getBillingCycle());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public void clearOldAggregatedBill(String vendor, String start, String end) {
        aggregatedBillMapper.clearMonthBills(vendor, start, end);
    }
}
