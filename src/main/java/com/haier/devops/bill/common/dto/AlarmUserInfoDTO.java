package com.haier.devops.bill.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
* @ClassName: AlarmUserInfoDTO
* @Description:  告警用户信息表
* @author: 张爱苹
* @date: 2024/1/17 10:24
*/
@Data
public class AlarmUserInfoDTO implements Serializable {


    private static final long serialVersionUID = -1917918819697562705L;


    /**
     * 人员工号
     */
    @NotEmpty
    private String account;

    /**
     * 姓名
     */
    @NotEmpty
    private String name;

    /**
     *  邮箱
     */
    @NotEmpty
    private String email;

    /**
     *  手机号
     */
    @NotEmpty
    private String mobile;

}

