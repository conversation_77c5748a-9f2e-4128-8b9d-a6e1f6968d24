package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.vo.HworkPendingSyncBillVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 到S码汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface ApplicationAggregationMapper extends BaseMapper<ApplicationAggregation> {
    /**
     * 按照S码分组查询月度账单
     * 按照S码、账期、账号汇总，并取2位小数
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ApplicationAggregation> queryBillGroupByScode(@Param("vendor") String vendor,
                                                       @Param("billingCycle") String billingCycle,
                                                       @Param("stage") String stage,
                                                       @Param("accounts") String... account);


    /**
     * 批量修改状态
     * @param aggregations
     * @param status
     * @return
     */
    int batchUpdateStatus(@Param("aggregations") List<ApplicationAggregation> aggregations,
                          @Param("status") int status);

    List<HworkPendingSyncBillVo> selectPendingSyncBill(@Param("billVo")HworkPendingSyncBillVo billVo, 
                                                    @Param("startDate") String startDate, 
                                                    @Param("endDate") String endDate);

    /**
     * 根据预算编码列表查询当年的总金额
     * @param budgetCodes 预算编码列表
     * @param year 年份
     * @return 预算编码和对应的总金额
     */
    List<Map<String, Object>> getYearlyTotalByBudgetCodes(@Param("budgetCodes") List<String> budgetCodes, @Param("year") String year);

    void upsertBatchByOrderId(@Param("aggregations") List<ApplicationAggregation> aggregations);

    /**
     * 批量驳回
     * @param orderIds
     * @return
     */
    int batchRejectByOrderId(@Param("orderIds") List<String> orderIds);
}
