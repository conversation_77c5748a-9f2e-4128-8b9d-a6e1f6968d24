package com.haier.devops.bill.common.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.IdmUser;
import com.haier.devops.bill.common.mapper.IdmUserMapper;
import com.haier.devops.bill.common.service.IdmUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@DS("pg")
@Service
public class IdmUserServiceImpl extends ServiceImpl<IdmUserMapper, IdmUser> implements IdmUserService {
    private final IdmUserMapper idmUserMapper;

    public IdmUserServiceImpl(IdmUserMapper idmUserMapper) {
        this.idmUserMapper = idmUserMapper;
    }

    @Override
    public IdmUser selectByUserCode(String userCode) {
        return idmUserMapper
                .selectOne(new LambdaQueryWrapper<IdmUser>()
                        .eq(IdmUser::getUsername, userCode));
    }
}
