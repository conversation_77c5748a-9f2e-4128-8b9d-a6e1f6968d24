package com.haier.devops.bill.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
* @ClassName: ServiceFreeCreateDTO
* @Description: 服务费汇率
* @author: 张爱苹
* @date: 2024/3/8 13:24
*/
@Data
public class ServiceFreeCreateDTO implements Serializable {


    private static final long serialVersionUID = 4874132418842934011L;

    private Integer id;
    /**云厂商*/
    @Schema(description = "云厂商")
    @NotEmpty
    private java.lang.String vendor;
    /**开始日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "开始日期")
    @NotNull
    private java.util.Date startDate;
    /**结束日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "结束日期")
    @NotNull
    private java.util.Date endDate;
    /**率*/
    @Schema(description = "率")
    @NotNull
    private java.math.BigDecimal rate;
    /**1服务费；2汇率*/
    @Schema(description = "1服务费；2汇率")
    @NotNull
    private java.lang.Integer type;
    /**币种*/
    @Schema(description = "币种")
    private java.lang.String currency;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private java.util.Date createTime;
    /**创建人账号*/
    @Schema(description = "创建人账号")
    private java.lang.String createBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private java.util.Date updateTime;
    /**更新人账号*/
    @Schema(description = "更新人账号")
    private java.lang.String updateBy;

}
