package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.haier.devops.bill.common.entity.CloudAccount;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 云账号表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
public interface CloudAccountService extends IService<CloudAccount> {
    /**
     * 获取启用的用户
     * @param vendor
     * @param accountName
     * @return
     */
    List<CloudAccount> getEnabledAccounts(String vendor, String accountName);

    Map<String, String> getOpenApiKey(String vendor, String accountName, String... purposeType);

    /**
    * @Description: 模糊获取云账号
    * @author: 张爱苹
    * @date: 2024/3/4 16:53
    * @param vendor:
    * @param accountName:
    * @Return: java.util.List<java.lang.String>
    */
    List<String> getCloudAccountList(String vendor, String accountName);

    /**
    * @Description: 获取阿里云主账号列表
    * @author: 张爱苹
    * @date: 2024/3/19 18:43

    * @Return: java.util.List<com.haier.devops.bill.common.entity.CloudAccount>
    */
    List<CloudAccount> getAliyunAccountList();

    /**
     * @Description: 获取阿里云及专属云账号列表
     * @author: 张爱苹
     * @date: 2024/3/19 18:43

     * @Return: java.util.List<com.haier.devops.bill.common.entity.CloudAccount>
     */
    List<CloudAccount> getAliyunDedicatedAccountList();

    /**
    * @Description: 获取华为云账号列表
    * @author: 张爱苹
    * @date: 2025/2/13 14:43

    * @Return: java.util.List<com.haier.devops.bill.common.entity.CloudAccount>
    */
    List<CloudAccount> getHuaweiAccountList(String purposeType);

    /**
    * @Description: 根据币种获取账号列表
    * @author: 张爱苹
    * @date: 2025/5/29 14:12
    * @param currentCy:
    * @Return: java.util.List<java.util.Map>
    */
    List<String> getAccountListByCurrentCy(String currentCy);

    /**
     * 获取AWS账号列表
     *
     * @param purposeType 账号用途类型，可选参数
     * @return 云账号列表
     */
    List<CloudAccount> getAwsAccountList(String purposeType);

    /**
     * 根据账号ID获取云账号信息
     *
     * @param id 账号ID
     * @return 云账号信息
     */
    CloudAccount getAccountById(Long id);

    /**
     * 根据账号标识获取云账号信息
     *
     * @param accountIdentify 账号标识
     * @return 云账号信息
     */
    CloudAccount getAccountByIdentify(String accountIdentify);

    /**
     * 获取解密后的访问密钥
     *
     * @param cloudAccount 云账号信息
     * @return 包含解密后的accessKey和accessKeySecret的Map
     */
    Map<String, String> getDecryptedAccessKeys(CloudAccount cloudAccount);
}
