package com.haier.devops.bill.common.config;

import com.alibaba.fastjson.JSON;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;

import java.io.IOException;
import java.lang.reflect.Type;

public class Fastjson2Decoder implements Decoder {
    @Override
    public Object decode(Response response, Type type) throws IOException {
        if (response.body() == null) {
            return null;
        }
        String bodyStr = Util.toString(response.body().asReader(Util.UTF_8));
        try {
            return JSON.parseObject(bodyStr, type);
        } catch (Exception e) {
            throw new DecodeException(response.status(), "Fastjson2 decode error", response.request(), e);
        }
    }
}
