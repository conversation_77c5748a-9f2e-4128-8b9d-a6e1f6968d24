package com.haier.devops.bill.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.dto.AlarmConfigurationCreateDTO;
import com.haier.devops.bill.common.dto.AlarmConfigurationDTO;
import com.haier.devops.bill.common.dto.IsEnableAlarmDTO;
import com.haier.devops.bill.common.entity.AlarmConfiguration;
import com.haier.devops.bill.common.entity.AlarmOperateLog;
import com.haier.devops.bill.common.enums.NoticeObjEnum;
import com.haier.devops.bill.common.enums.NoticeWayEnum;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.mapper.AlarmConfigurationMapper;
import com.haier.devops.bill.common.mapper.CpAlarmLevelMapper;
import com.haier.devops.bill.common.mapper.CpAlarmRuleDetailMapper;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.AlarmNoticeObjVo;
import com.haier.devops.bill.common.vo.CpAlarmConfigurationVo;
import com.haier.devops.bill.common.vo.CpAlarmLevelVo;
import com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo;
import com.huaweicloud.sdk.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
* @ClassName: AlarmConfigurationServiceImpl
* @Description: 告警配置Service实现类
* @author: 张爱苹
* @date: 2024/1/11 10:53
*/
@Service
@Slf4j
public class AlarmConfigurationServiceImpl extends ServiceImpl<AlarmConfigurationMapper, AlarmConfiguration> implements AlarmConfigurationService {
    private Logger logger = LoggerFactory.getLogger(AlarmConfigurationServiceImpl.class);
    @Autowired
    private AlarmConfigurationMapper alarmConfigurationMapper;

    @Autowired
    private CpAlarmLevelService alarmLevelService;

    @Autowired
    private CpAlarmLevelMapper alarmLevelMapper;

    @Autowired
    private CpAlarmRuleDetailService alarmRuleDetailService;

    @Autowired
    private CpAlarmRuleDetailMapper alarmRuleDetailMapper;

    @Autowired
    private AlarmUserInfoService alarmUserInfoService;

    @Autowired
    private AlarmNoticeGroupService alarmNoticeGroupService;

    @Autowired
    private SysDictItemService sysDictItemService;

    @Resource
    Executor localBootAsyncExecutor;

    @Autowired
    private AlarmOperateLogService alarmOperateLogService;

    @Override
    public List<AlarmConfiguration> getAllAlarmConfigurations() {
        return list();
    }

    @Override
    public CpAlarmConfigurationVo getAlarmConfigurationById(Integer id) throws Exception{
        CpAlarmConfigurationVo alarmConfigurationVo = getAlarmConfiguration(id);
        List<CpAlarmLevelVo> alarmLevelList = alarmLevelService.getAlarmLevelListByAlarmConfigurationId(id);
        if(!CollectionUtils.isEmpty(alarmLevelList)){
            for (CpAlarmLevelVo alarmLevelVo : alarmLevelList){
                Integer alarmLevelId = alarmLevelVo.getId();
                //根据alarmLevelId获取对应的告警规则详情
                List<CpAlarmRuleDetailVo> alarmRuleDetailVoList = alarmRuleDetailService.getAlarmRuleDetailListByAlarmLevelId(alarmLevelId);
                for (CpAlarmRuleDetailVo cpAlarmRuleDetailVo: alarmRuleDetailVoList) {
                    String[] noticeObjIdArray = cpAlarmRuleDetailVo.getNoticeObjId().split(",");
                    String noticeWay =  cpAlarmRuleDetailVo.getNoticeWay();
                    if (noticeWay.equals(NoticeWayEnum.SMS.getKey()) || noticeWay.equals("mail")) {
                        String noticeObj = cpAlarmRuleDetailVo.getNoticeObj();
                        if(noticeObj.equals(NoticeObjEnum.GROUP.getKey())){
                            cpAlarmRuleDetailVo.setAlarmNoticeObjList(alarmNoticeGroupService.getAlarmNoticeGroupByIdList(noticeObjIdArray));
                        }else{
                            cpAlarmRuleDetailVo.setAlarmNoticeObjList(alarmUserInfoService.getUserListByUserIdArray(noticeObjIdArray));
                        }
                    }else{
                        cpAlarmRuleDetailVo.setAlarmNoticeObjList(sysDictItemService.getDictItemByItemValueArray(noticeObjIdArray,NoticeWayEnum.getValue(noticeWay)));
                    }
                }
                alarmLevelVo.setCpAlarmRuleDetailVoList(alarmRuleDetailVoList);
            }
        }
        alarmConfigurationVo.setBcCpAlarmLevelList(alarmLevelList);
        return alarmConfigurationVo;
    }

    private List<AlarmNoticeObjVo> getAlarmNoticeObjList(String[] noticeObjIdArray) {
        List<AlarmNoticeObjVo> alarmNoticeObjDTOList = new ArrayList<>();
        Arrays.stream(noticeObjIdArray).map(noticeObjId ->AlarmNoticeObjVo.builder().noticeObjId(noticeObjId).build())
                .forEach(alarmNoticeObjDTOList::add);
        return alarmNoticeObjDTOList;
    }

    private CpAlarmConfigurationVo getAlarmConfiguration(Integer id) throws Exception{
        AlarmConfigurationDTO dto = new AlarmConfigurationDTO();
        dto.setId(id);
        List<CpAlarmConfigurationVo> list = alarmConfigurationMapper.listByPage(dto);
        if(CollectionUtils.isEmpty(list)){
            throw new RuntimeException("告警配置不存在");
        }
        return list.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAlarmConfiguration(AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception{
        try{
            //获取所有配置 拼接规则 S码+产品id
            List<String> configurationList = alarmConfigurationMapper.getAllAlarmConfigurationList();
            //产品id
            List<Integer> productIdList = alarmConfigurationCreateDTO.getProductIdList();
            User currentUser = LoginContextHolder.getCurrentUser();
            //线程计数器
            CountDownLatch latch = new CountDownLatch(productIdList.size());
            //成功请求数
            AtomicInteger successReqCount =  new AtomicInteger(0);
            //遍历productIdList
            for (Integer productId : productIdList) {
                localBootAsyncExecutor.execute(() ->{
                    Integer id = null;
                    try {
                        String appScode =  alarmConfigurationCreateDTO.getAppScode();
                        StringBuffer configuration = new StringBuffer();
                        if(!StringUtils.isEmpty(appScode)){
                            configuration.append(appScode).append(productId);
                        }else{
                            configuration.append(productId);
                        }
                        if(configurationList.contains(configuration.toString())){
                            logger.error("S码{}-产品{}告警配置已存在,",appScode,productId);
                            throw new RuntimeException("存在重复配置");
                        }
                        alarmConfigurationCreateDTO.setCreateBy(currentUser.getUserCode());
                        alarmConfigurationCreateDTO.setCreateByName(currentUser.getUserName());
                        //保存云产品告警配置
                        AlarmConfiguration alarmConfiguration = saveOrUpdateAlarmConfiguration(alarmConfigurationCreateDTO, productId);
                        id = alarmConfiguration.getId();
                        //保存告警级别和告警详情
                        alarmLevelService.saveOrUpdateAlarmLevelAndRuleDetail(alarmConfiguration,alarmConfigurationCreateDTO);
                        Map changeContent = JsonUtils.objectToMap(alarmConfigurationCreateDTO);
                        changeContent.put("productIdList",productId);
                        AlarmOperateLog alarmOperateLog = AlarmOperateLog.builder()
                                .configId(id)
                                .operateType("新增")
                                .operateUser(currentUser.getUserCode())
                                .changeContent(JSON.toJSONString(changeContent))
                                .operateTime(LocalDateTime.now())
                                .build();
                        alarmOperateLogService.save(alarmOperateLog);
                        successReqCount.getAndAdd(1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        if(id != null){
                            //删除配置
                            removeById(id);
                        }
                    }finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
            int errorReqCount = productIdList.size() - successReqCount.get();
            if(errorReqCount > 0){
                throw new RuntimeException("有"+errorReqCount+"个产品配置保存失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("createAlarmConfiguration error, alarmConfigurationCreateDTO:{}", alarmConfigurationCreateDTO, e.getMessage(),e);
            throw new RuntimeException("createAlarmConfiguration error:"+e.getMessage());
        }
    }

    private AlarmConfiguration saveOrUpdateAlarmConfiguration(AlarmConfigurationCreateDTO alarmConfigurationCreateDTO, Integer productId) throws Exception{
        AlarmConfiguration alarmConfiguration = AlarmConfiguration.builder().build();
        BeanUtils.copyProperties(alarmConfigurationCreateDTO, alarmConfiguration);
        alarmConfiguration.setProductId(productId);
        try{
            saveOrUpdate(alarmConfiguration);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("saveOrUpdateAlarmConfiguration error",e.getMessage(),e);
            throw new RuntimeException("保存告警配置失败");
        }
        return alarmConfiguration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAlarmConfiguration(Integer id, AlarmConfigurationCreateDTO alarmConfigurationCreateDTO) throws Exception{
        try{
            validAlarmConfiguration(id);
            //清空告警级别表
            alarmLevelService.deleteByCpAlarmConfigurationId(id);
            //清空告警规则明细表
            alarmRuleDetailService.deleteByCpAlarmConfigurationId(id);
            Integer productId = alarmConfigurationCreateDTO.getProductIdList().get(0);
            //保存云产品告警配置
            alarmConfigurationCreateDTO.setId(id);
            alarmConfigurationCreateDTO.setUpdateTime(new Date());
            User currentUser = LoginContextHolder.getCurrentUser();
            alarmConfigurationCreateDTO.setUpdateBy(currentUser.getUserCode());
            alarmConfigurationCreateDTO.setUpdateByName(currentUser.getUserName());
            AlarmConfiguration alarmConfiguration = saveOrUpdateAlarmConfiguration(alarmConfigurationCreateDTO, productId);;
            alarmLevelService.saveOrUpdateAlarmLevelAndRuleDetail(alarmConfiguration,alarmConfigurationCreateDTO);
            AlarmOperateLog alarmOperateLog = AlarmOperateLog.builder()
                    .configId(id)
                    .operateType("编辑")
                    .operateUser(currentUser.getUserCode())
                    .changeContent(JSON.toJSONString(alarmConfigurationCreateDTO))
                    .operateTime(LocalDateTime.now())
                    .build();
            alarmOperateLogService.save(alarmOperateLog);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("updateAlarmConfiguration error,id:{},alarmConfigurationCreateDTO:{}",id,alarmConfigurationCreateDTO,e.getMessage(),e);
            throw new RuntimeException("updateAlarmConfiguration error"+e.getMessage());
        }
    }

    private AlarmConfiguration validAlarmConfiguration(Integer id) throws Exception{
        AlarmConfiguration oldAlarmConfiguration = baseMapper.selectOne(new QueryWrapper<AlarmConfiguration>().lambda().eq(AlarmConfiguration::getId,id).eq(AlarmConfiguration::getDelFlag,"0"));
        if(oldAlarmConfiguration == null){
            throw new RuntimeException("alarmConfiguration not exist");
        }
//        int enableAlarm = oldAlarmConfiguration.getIsEnableAlarm().intValue();
//        if (enableAlarm == 1) {
//            throw new RuntimeException("alarmConfiguration is enable,can not operate");
//        }
        return oldAlarmConfiguration;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAlarmConfiguration(Integer id) throws Exception{
        try{
            AlarmConfiguration alarmConfiguration = validAlarmConfiguration(id);
            //清空告警规则明细表
            alarmRuleDetailService.deleteByCpAlarmConfigurationId(id);
            //清空告警级别表
            alarmLevelService.deleteByCpAlarmConfigurationId(id);
            //删除告警配置
            alarmConfiguration.setUpdateTime(new Date());
            alarmConfiguration.setDelFlag("1");
            User currentUser = LoginContextHolder.getCurrentUser();
            alarmConfiguration.setUpdateBy(currentUser.getUserCode());
            alarmConfiguration.setUpdateByName(currentUser.getUserName());
            updateById(alarmConfiguration);
            AlarmOperateLog alarmOperateLog = AlarmOperateLog.builder()
                    .configId(id)
                    .operateType("删除")
                    .operateUser(currentUser.getUserCode())
                    .changeContent(JSON.toJSONString(id))
                    .operateTime(LocalDateTime.now())
                    .build();
            alarmOperateLogService.save(alarmOperateLog);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("deleteAlarmConfiguration error,id:{}",id,e.getMessage(),e);
            throw new RuntimeException("deleteAlarmConfiguration error:"+e.getMessage());
        }
    }

    @Override
    public PageInfo<CpAlarmConfigurationVo> listByPage(AlarmConfigurationDTO dto) throws Exception{
        try{
            return PageHelper.startPage(dto.getPage(), dto.getPer_page()).doSelectPageInfo(
                    () ->  alarmConfigurationMapper.listByPage(dto)
            );
        }catch (Exception e){
            e.printStackTrace();
            logger.error("listByPage error, alarmConfigurationDTO:{}", dto, e.getMessage(),e.getMessage(),e);
            throw new RuntimeException("listByPage error");
        }
    }

    @Override
    public void updateEnableStatus(IsEnableAlarmDTO isEnableAlarmDTO) throws Exception{
        try{
            CpAlarmConfigurationVo alarmConfigurationVo = getAlarmConfiguration(isEnableAlarmDTO.getId());
            if(alarmConfigurationVo.getIsEnableAlarm().intValue() == isEnableAlarmDTO.getIsEnableAlarm().intValue()){
                return;
            }
            AlarmConfiguration alarmConfiguration = AlarmConfiguration.builder().build();
            BeanUtils.copyProperties(alarmConfigurationVo, alarmConfiguration);
            alarmConfiguration.setIsEnableAlarm(isEnableAlarmDTO.getIsEnableAlarm());
            alarmConfiguration.setUpdateTime(new Date());
            User currentUser = LoginContextHolder.getCurrentUser();
            alarmConfiguration.setUpdateBy(currentUser.getUserCode());
            alarmConfiguration.setUpdateByName(currentUser.getUserName());
            updateById(alarmConfiguration);
            AlarmOperateLog alarmOperateLog = AlarmOperateLog.builder()
                    .configId(isEnableAlarmDTO.getId())
                    .operateType("编辑启用状态")
                    .operateUser(currentUser.getUserCode())
                    .changeContent(JSON.toJSONString(isEnableAlarmDTO))
                    .operateTime(LocalDateTime.now())
                    .build();
            alarmOperateLogService.save(alarmOperateLog);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("updateEnableStatus error",e.getMessage(),e);
            throw new RuntimeException("updateEnableStatus error:"+e.getMessage());
        }
    }
}
