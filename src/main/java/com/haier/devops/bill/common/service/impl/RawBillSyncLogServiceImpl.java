package com.haier.devops.bill.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.haier.devops.bill.common.entity.RawBillSyncLog;
import com.haier.devops.bill.common.mapper.RawBillSyncLogMapper;
import com.haier.devops.bill.common.service.RawBillSyncLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  用于记录已经同步的账单信息  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
@Service
public class RawBillSyncLogServiceImpl extends ServiceImpl<RawBillSyncLogMapper, RawBillSyncLog> implements RawBillSyncLogService {
    private RawBillSyncLogMapper syncLogMapper;

    public RawBillSyncLogServiceImpl(RawBillSyncLogMapper syncLogMapper) {
        this.syncLogMapper = syncLogMapper;
    }

    @Override
    public List<RawBillSyncLog> getPendingAggregatedBillSyncLog(String vendor) {
        return syncLogMapper.getPendingAggregatedBillSyncLog(vendor);
    }
}
