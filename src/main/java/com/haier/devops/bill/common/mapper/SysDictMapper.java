package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.dto.SysDictDTO;
import com.haier.devops.bill.common.entity.SysDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
public interface SysDictMapper extends BaseMapper<SysDict> {

	List<SysDict> listByPage(@Param("dto") SysDictDTO dto);
}
