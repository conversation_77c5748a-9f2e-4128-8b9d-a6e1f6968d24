package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.AcceptanceBlacklist;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 验收推送黑名单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface AcceptanceBlacklistMapper extends BaseMapper<AcceptanceBlacklist> {
    /**
     * 检查指定的云厂商和系统编码是否在黑名单中
     * 
     * @param vendor 云厂商
     * @param sysCode 系统编码
     * @return 存在返回true，不存在返回false
     */
    Boolean checkInBlacklist(@Param("vendor") String vendor, @Param("sysCode") String sysCode);
}
