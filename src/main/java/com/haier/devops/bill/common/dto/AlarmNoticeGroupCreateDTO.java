package com.haier.devops.bill.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haier.devops.bill.common.entity.AlarmNoticeGroupDetail;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* @ClassName: AlarmNoticeGroupCreateDTO
* @Description:  告警通知组创建DTO
* @author: 张爱苹
* @date: 2024/1/12 17:32
*/
@Data
public class AlarmNoticeGroupCreateDTO implements Serializable {

    private static final long serialVersionUID = 8913379415855299070L;

    private Integer id;
    /**
     * 组名
     */
    @NotEmpty
    private String groupName;

    /**
     * 创建人账号
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人账号
     */
    private String updateBy;

    /**
     * 删除标识 0：有效 1：无效
     */
    private String delFlag;

    @NotNull
    private List<AlarmNoticeGroupDetail> alarmNoticeGroupDetailList;
}
