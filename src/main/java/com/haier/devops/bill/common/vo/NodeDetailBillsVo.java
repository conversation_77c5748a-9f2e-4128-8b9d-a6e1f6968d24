package com.haier.devops.bill.common.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class NodeDetailBillsVo {

    /**
     * 账期
     */
    @ExcelProperty("账期")
    @ColumnWidth(8)
    private String billingCycle;


    /**
     * 云厂商
     */
    @ExcelProperty("云厂商")
    @ColumnWidth(8)
    private String vendor;


    /**
     * 云账号
     */
    @ExcelProperty("云账号")
    @ColumnWidth(8)
    private String accountName;

    /**
     * 产品
     */
    @ExcelProperty("产品")
    @ColumnWidth(10)
    private String productName;

    /**
     * 子产品
     */
    @ExcelProperty("子产品")
    @ColumnWidth(12)
    private String subProductName;

    /**
     * S码
     */
    @ExcelProperty("项目S码")
    @ColumnWidth(8)
    private String scode;

    /**
     * 项目简称
     */
    @ExcelProperty("项目简称")
    @ColumnWidth(8)
    private String projectCode;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ColumnWidth(12)
    private String projectName;

    /**
     * 企业项目
     */
    @ExcelProperty("企业项目")
    @ColumnWidth(20)
    private String costUnit;

    /**
     * 支付类型
     */
    @ExcelProperty("付费类型")
    @ColumnWidth(10)
    private String subscriptionType;

    /**
     * 实例ID
     */
    @ExcelProperty("实例ID")
    @ColumnWidth(22)
    private String instanceId;

    /**
     * 实例名称
     */
    @ExcelProperty("实例名称")
    @ColumnWidth(16)
    private String instanceName;

    @ExcelProperty("ip/host")
    @ColumnWidth(20)
    private String ip;

    @ExcelProperty("地域")
    @ColumnWidth(12)
    private String region;

    /**
     * 产品
     */
    @ExcelProperty("产品类型")
    @ColumnWidth(10)
    private String productCode;

    /**
     * 产品明细
     */
    @ExcelProperty("产品明细")
    @ColumnWidth(18)
    private String productDetail;

    /**
     * 总额
     */
    @ExcelProperty("消费金额")
    @ColumnWidth(10)
    private BigDecimal totalSum;

    /**
     * 费用类型
     */
    @ExcelProperty("费用类型")
    @ColumnWidth(8)
    private String expenseType;

    /**
     * 所属季度
     */
    @ExcelIgnore
    private String belongQuarter;

}
