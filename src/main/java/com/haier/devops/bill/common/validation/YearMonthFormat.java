package com.haier.devops.bill.common.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = YearMonthFormatValidator.class)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface YearMonthFormat {
    String message() default "Invalid year-month format";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
