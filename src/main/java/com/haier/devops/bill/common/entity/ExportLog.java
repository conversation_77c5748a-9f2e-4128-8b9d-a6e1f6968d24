package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 账单导出记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@TableName("bc_export_log")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExportLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 导出人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private Date submitTime;

    private Date updateTime;

    /**
     * 上传结果
     */
    private String putResult;

    /**
     * 文件下载地址
     */
    private String url;

    /**
     * 执行导出任务所在的pod ip
     */
    private String podIp;

    /**
     * 账期开始时间
     */
    private String startCycle;

    /**
     * 账期结束时间
     */
    private String endCycle;

    /**
     * 0 不可下载 1 可下载 2 已失败
     */
    private Integer isReady;

    /**
     * 备注
     */
    private String remark;
}
