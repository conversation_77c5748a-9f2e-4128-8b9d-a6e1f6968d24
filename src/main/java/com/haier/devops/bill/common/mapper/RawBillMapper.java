package com.haier.devops.bill.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.common.entity.RawBill;
import com.haier.devops.bill.common.vo.BillChargesVo;
import com.haier.devops.bill.common.vo.CostProcedureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RawBillMapper extends BaseMapper<RawBill> {

    /**
     * 查询计算过程
     * @param vendor
     * @param costUnit
     * @param productCode
     * @param startBillingCycle
     * @param endBillingCycle
     * @return
     */
    List<CostProcedureVo> selectCostProcedure(@Param("vendor") String vendor,
                                              @Param("costUnit") String costUnit,
                                              @Param("productCode") String productCode,
                                              @Param("startBillingCycle") String startBillingCycle,
                                              @Param("endBillingCycle") String endBillingCycle);


    /**
     * 获取账单所有收费项
     * @return
     */
    List<BillChargesVo> getBillCharges();

    void updateBatchById(List<RawBill> list);

    List<RawBill> getBillByVendor(@Param("vendor") String vendor,
                                  @Param("billingCycle") String billingCycle);
}
