package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 账单替换任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Data
@Builder
@TableName("bc_bill_substitution_task")
public class BillSubstitutionTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 替换任务id
     */
    private String subTaskId;

    /**
     * 替换类型
     */
    private String subType;

    /**
     * 账期
     */
    private String billingCycle;

    /**
     * 阶段：migration, calculation, substitution
     */
    private String stage;

    /**
     * 步骤
     */
    private Integer process;

    private String err;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
