package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-08
 */
@TableName("bc_budget_config")
@Data
public class BudgetConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分类：
     * renew 续签
     * new    新增
     */
    private String category;

    /**
     * 预算编码
     */
    private String budgetCode;

    /**
     * 预算名称
     */
    private String budgetName;

    /**
     * 预算总金额
     */
    private String totalAmount;

    /**
     * 可用余额
     */
    private String availableAmount;

    /**
     * 年份
     */
    private String year;

    /**
     * 账单类型：
     * 公有云 public
     * 私有云 private
     */
    private String cloudType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private Integer updateBy;

}
