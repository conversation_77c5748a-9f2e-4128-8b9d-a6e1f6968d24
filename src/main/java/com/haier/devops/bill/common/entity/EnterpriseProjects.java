package com.haier.devops.bill.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
* @ClassName: EnterpriseProjects
* @Description: 企业项目
* @author: 张爱苹
* @date: 2025/2/13 14:57
*/
@TableName("bc_enterprise_projects")
@Data
@Builder
public class EnterpriseProjects implements Serializable {

    private static final long serialVersionUID = 4640651331589833085L;

    private String id;

    private String name;

    private Integer status;

    private String delFlag;

    private String accountName;

}
