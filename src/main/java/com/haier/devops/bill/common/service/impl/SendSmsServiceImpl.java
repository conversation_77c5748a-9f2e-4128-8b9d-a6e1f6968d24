package com.haier.devops.bill.common.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.haier.devops.bill.common.service.SendSmsService;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SendSmsServiceImpl implements SendSmsService {

    @Value("${statusCallBack}")
    private String statusCallBackUrl;

    private final Logger logger = LoggerFactory.getLogger("SendSmsServiceImpl");

    //校验电话正则
    static private String myreg = "1[3456789]\\d{9}";

    //无需修改,用于格式化鉴权头域,给"X-WSSE"参数赋值
    private static final String WSSE_HEADER_FORMAT = "UsernameToken Username=\"%s\",PasswordDigest=\"%s\",Nonce=\"%s\",Created=\"%s\"";
    //无需修改,用于格式化鉴权头域,给"Authorization"参数赋值
    private static final String AUTH_HEADER_VALUE = "WSSE realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\"";


    private static final String url = "https://rtcsms.cn-north-1.myhuaweicloud.com:10743/sms/batchSendSms/v1"; //APP接入地址 + 接口访问URI
    private static final String appKey = "0Tp0V890R7S2g1k60mo8tdSsxWWf"; //APP_Key
    private static final String appSecret = "6VrP8KFU72iQOg5y4vmmHwExytld"; //APP_Secret
    private static final String sender = "8821010825940"; //签名通道号
    private static final String senderName = "海尔"; //签名名称


    /**
     * 构造请求Body体
     *
     * @param sender
     * @param receiver
     * @param body
     * @param statusCallbackUrl
     * @return
     */

    static String buildRequestBody(String sender, String receiver, String body, String statusCallbackUrl) {
        if (null == sender || null == receiver || null == body || sender.isEmpty() || receiver.isEmpty()
                || body.isEmpty()) {
            System.out.println("buildRequestBody(): sender, receiver or body is null.");
            return null;
        }
        List<NameValuePair> keyValues = new ArrayList<NameValuePair>();
        keyValues.add(new BasicNameValuePair("from", sender));
        keyValues.add(new BasicNameValuePair("to", receiver));
        keyValues.add(new BasicNameValuePair("body", body));
        if (null != statusCallbackUrl && !statusCallbackUrl.isEmpty()) {
            keyValues.add(new BasicNameValuePair("statusCallback", statusCallbackUrl));
        }
        //如果JDK版本是1.6,可使用:URLEncodedUtils.format(keyValues, Charset.forName("UTF-8"));
        return URLEncodedUtils.format(keyValues, StandardCharsets.UTF_8);
    }

    /**
     * 构造X-WSSE参数值
     *
     * @param appKey
     * @param appSecret
     * @return
     */
    static String buildWsseHeader(String appKey, String appSecret) {
        if (null == appKey || null == appSecret || appKey.isEmpty() || appSecret.isEmpty()) {
            System.out.println("buildWsseHeader(): appKey or appSecret is null.");
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String time = sdf.format(new Date()); //Created
        String nonce = UUID.randomUUID().toString().replace("-", ""); //Nonce
        byte[] passwordDigest = DigestUtils.sha256(nonce + time + appSecret);
        String hexDigest = Hex.encodeHexString(passwordDigest);
        //如果JDK版本是1.8,请加载原生Base64类,并使用如下代码
        String passwordDigestBase64Str = Base64.getEncoder().encodeToString(hexDigest.getBytes()); //PasswordDigest
        //如果JDK版本低于1.8,请加载三方库提供Base64类,并使用如下代码
        //String passwordDigestBase64Str = Base64.encodeBase64String(hexDigest.getBytes(Charset.forName("utf-8"))); //PasswordDigest
        //若passwordDigestBase64Str中包含换行符,请执行如下代码进行修正
        //passwordDigestBase64Str = passwordDigestBase64Str.replaceAll("[\\s*\t\n\r]", "");
        return String.format(WSSE_HEADER_FORMAT, appKey, passwordDigestBase64Str, nonce, time);
    }


    /**
     * 发送短信
     *
     * @param phones
     * @param message
     * @throws Exception
     */
    @Override
    public void sendMessageMail(String phones, String message) throws Exception {
        //选填,短信状态报告接收地址,推荐使用域名,为空或者不填表示不接收状态报告
        String statusCallBack = "";
        if (statusCallBackUrl != null && !statusCallBackUrl.equals("")) {
            statusCallBack = statusCallBackUrl;
        }
        //由短信签名和短信内容两部分组成,短信签名必须在短信内容前面,国内短信签名由【】及签名名称组成
        String body = "【" + senderName + "】" + message;
        Set<String> phoneSet = new HashSet<String>();
        //校验电话号码，组装传入参数
        String[] phoneArray = phones.split(";");
        phones = "";
        //电话去重
        for (String p : phoneArray) {
            if (phoneSet == null || !phoneSet.contains(p)) {
                if (isPhone(p)) {
                    phoneSet.add(p);
                }

            }
        }
        if (phoneSet != null && phoneSet.size() != 0) {
            for (String p : phoneSet) {
                phones = phones + p + ",";
            }
        }
        if (phones != null && !phones.equals("")) {
            phones = phones.substring(0, phones.length() - 1);
            try {
                for (int i = 1; i < 4; i++) {
                    //请求Body
                    String content = buildRequestBody(sender, phones, body, statusCallBack);
                    if (null == content || content.isEmpty()) {
                        logger.info("body is null.");
                        return;
                    }
                    //请求Headers中的X-WSSE参数值
                    String wsseHeader = buildWsseHeader(appKey, appSecret);
                    if (null == wsseHeader || wsseHeader.isEmpty()) {
                        logger.info("wsse header is null.");
                        return;
                    }
                    //如果JDK版本低于1.8,可使用如下代码
                    //为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
                    //CloseableHttpClient client = HttpClients.custom()
                    // .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                    // @Override
                    // public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws  CertificateException {
                    // return true;
                    // }
                    // }).build()).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build();
                    //如果JDK版本是1.8,可使用如下代码
                    //为防止因HTTPS证书认证失败造成API调用失败,需要先忽略证书信任问题
                    CloseableHttpClient client = HttpClients.custom()
                            .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null,
                                    (x509CertChain, authType) -> true).build())
                            .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                            .build();
                    HttpResponse response = client.execute(RequestBuilder.create("POST")//请求方法POST
                            .setUri(url)
                            .addHeader(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded")
                            .addHeader(HttpHeaders.AUTHORIZATION, AUTH_HEADER_VALUE)
                            .addHeader("X-WSSE", wsseHeader)
                            .setEntity(new StringEntity(content)).build());
                    logger.info(response.toString()); //打印响应头域信息
                    String result = EntityUtils.toString(response.getEntity());
                    logger.info(result); //打印响应消息实体
                    Gson gson = new Gson();
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (jsonObject.get("code").equals("000000")) {
                        logger.info("短信发送成功");
                        break;
                    } else {
                        logger.error("第" + i + "次短信发送失败:{}", response);
                    }
                }
            } catch (Exception e) {
                logger.error("发送失败:{}", e.getMessage());
                throw e;
            }
            logger.info("给" + phones + "发送" + body + "短信成功");
        } else {
            logger.error("短信发送失败:" + "未存在正确的手机号");
        }
    }

    /**
     * 正则验证
     */
    private static boolean validate(String pattern, String content) throws Exception {
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(content);
        return m.matches();
    }

    /**
     * 验证是否电话
     */
    private static boolean isPhone(String phone) throws Exception {
        if (!validate(myreg, phone)) {
            //不是正确的电话格式
            return false;
        }
        return true;
    }
}
