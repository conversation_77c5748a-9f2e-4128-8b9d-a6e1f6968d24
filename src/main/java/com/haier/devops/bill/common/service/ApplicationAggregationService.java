package com.haier.devops.bill.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.vo.HworkPendingSyncBillVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 到S码汇总表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface ApplicationAggregationService extends IService<ApplicationAggregation> {
    /**
     * 按照S码分组查询月度账单
     * 按照S码、账期、账号汇总，并取2位小数
     * @param vendor
     * @param billingCycle
     * @param account
     * @return
     */
    List<ApplicationAggregation> queryBillGroupByScode(String vendor, String billingCycle, String stage, String... account);

    // todo save or update

    /**
     * 批量修改状态
     * @param applications
     * @param status
     * @return
     */
    int batchUpdateStatus(List<ApplicationAggregation> applications, int status);

    PageInfo<HworkPendingSyncBillVo> selectPendingSyncBill(HworkPendingSyncBillVo hworkPendingSyncBillVo, int pageNum, int pageSize, String startDate, String endDate);

    /**
     * 根据预算编码列表查询当年的总金额
     * @param budgetCodes 预算编码列表
     * @param year 年份
     * @return 预算编码和对应的总金额
     */
    List<Map<String, Object>> getYearlyTotalByBudgetCodes(List<String> budgetCodes, String year);

    boolean saveOrUpdateBatchByOrderId(List<ApplicationAggregation> aggregations);

    /**
     * 批量驳回
     * @param orderIds
     * @return
     */
    int batchRejectByOrderId(List<String> orderIds);
}
