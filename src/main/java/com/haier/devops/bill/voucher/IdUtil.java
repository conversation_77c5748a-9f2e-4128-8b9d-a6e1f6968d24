package com.haier.devops.bill.voucher;

import com.haier.devops.bill.util.DateUtil;

public class IdUtil {
    /**
     * 将账期转换为16进制
     * @param billingCycle
     * @return
     */
    public static String convertBillingCycleToHex(String billingCycle) {
        if (!DateUtil.isValidFormat("yyyy-MM", billingCycle)) {
            return "";
        }

        // 解析年月字符串
        String[] parts = billingCycle.split("-");
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);

        // 计算一个整数值
        int value = year * 100 + month;

        // 将整数值转换为16进制表示
        return Integer.toHexString(value);
    }


    /**
     * 将16进制表示的账期转换回yyyy-MM格式
     * @param hex
     * @return
     */
    public static String convertHexToBillingCycle(String hex) {
        try {
            // 将16进制字符串转换为整数值
            int value = Integer.parseInt(hex, 16);

            // 提取年份和月份
            int year = value / 100;
            int month = value % 100;

            // 将年份和月份格式化为yyyy-MM
            return String.format("%04d-%02d", year, month);
        } catch (NumberFormatException e) {
            return "";
        }
    }

    public static void main(String[] args) {
        String billingCycle = "2024-07";
        String hex = convertBillingCycleToHex(billingCycle);
        System.out.println(hex);
        System.out.println(convertHexToBillingCycle(hex));
    }
}
