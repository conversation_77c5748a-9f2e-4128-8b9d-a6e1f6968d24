package com.haier.devops.bill.voucher;

import com.haier.devops.bill.common.entity.Voucher;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VoucherRequest extends Voucher {
    @NotEmpty(message = "scodeList不能为空")
    private List<String> scodeList;
}
