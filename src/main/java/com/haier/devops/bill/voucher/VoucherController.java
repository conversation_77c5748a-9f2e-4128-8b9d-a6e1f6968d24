package com.haier.devops.bill.voucher;

import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/voucher")
public class VoucherController {

    @PostMapping("/import")
    public ResponseEntityWrapper<String> importVoucher(@RequestBody @Valid VoucherRequest voucher) {

        return new ResponseEntityWrapper<>("导入成功");
    }

}

