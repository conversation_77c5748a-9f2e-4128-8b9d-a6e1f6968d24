package com.haier.devops.bill;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableConfigurationProperties
@EnableFeignClients
@EnableAsync
@EnableWebMvc
@EnableScheduling
public class BillCenterApplication {

	public static void main(String[] args) {
		SpringApplication.run(BillCenterApplication.class, args);
	}

}
