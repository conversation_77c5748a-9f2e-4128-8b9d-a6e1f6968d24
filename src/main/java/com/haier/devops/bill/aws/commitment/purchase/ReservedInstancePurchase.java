package com.haier.devops.bill.aws.commitment.purchase;

import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

@Component
@Slf4j
public class ReservedInstancePurchase extends PurchaseTemplate<ReservedInstancePurchase.RiGroupKey> {
    @Getter
    private final ResourceUsageAgreementService agreementService;
    @Getter
    private final AwsSavingsPlanService savingsPlanService;
    @Getter
    private final CloudAccountService accountService;

    public ReservedInstancePurchase(ResourceUsageAgreementService agreementService, AwsSavingsPlanService savingsPlanService, CloudAccountService accountService) {
        this.agreementService = agreementService;
        this.savingsPlanService = savingsPlanService;
        this.accountService = accountService;
    }

    @Override
    Function<ResourceUsageAgreementVo, RiGroupKey> grouping() {
        return a -> new RiGroupKey(a.getRegion(), a.getInstanceType(), a.getDuration());
    }

    @Override
    List<ResourceUsageAgreementVo> queryPendingAgreements() {
        return agreementService.queryPendingSavingPlanResource(RI);
    }

    @Override
    String doPurchase(RiGroupKey groupKey, List<ResourceUsageAgreementVo> agreements) {
        throw new UnsupportedOperationException("Reserved Instance purchasing not supported");
    }

    @Data
    @AllArgsConstructor
    public static class RiGroupKey extends PurchaseTemplate.GroupKey {
        private String region;
        private String instanceType;
        private Long duration;
    }
}
