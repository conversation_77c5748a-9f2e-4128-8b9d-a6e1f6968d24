package com.haier.devops.bill.aws.commitment.sdk;

import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.rds.RdsClient;
import software.amazon.awssdk.services.rds.model.DescribeReservedDbInstancesOfferingsRequest;
import software.amazon.awssdk.services.rds.model.DescribeReservedDbInstancesOfferingsResponse;
import software.amazon.awssdk.services.rds.model.Filter;
import software.amazon.awssdk.services.rds.model.PurchaseReservedDbInstancesOfferingRequest;
import software.amazon.awssdk.services.rds.model.PurchaseReservedDbInstancesOfferingResponse;
import software.amazon.awssdk.services.rds.model.Tag;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * aws RDS预留实例
 * <AUTHOR>
 */
@Service
@Slf4j
public class AwsRdsReservedInstanceService {

    private final CloudAccountService cloudAccountService;
    private final ConcurrentMap<String, RdsClient> clientCache = new ConcurrentHashMap<>();

    public AwsRdsReservedInstanceService(CloudAccountService cloudAccountService) {
        this.cloudAccountService = cloudAccountService;
    }

    private RdsClient createRdsSdkClient(String accountName, String regionStr) {
        String cacheKey = accountName + ":" + regionStr;
        return clientCache.computeIfAbsent(cacheKey, k -> {
            log.info("RDS RI: 本地缓存未命中，创建新的 RdsClient，账号: {}, 区域: {}", accountName, regionStr);
            Map<String, String> credentials = cloudAccountService.getOpenApiKey(VendorEnum.AWS.getVendor(), accountName, "ACCOUNT_CHECK");
            String accessKey = credentials.get("accessKey");
            String secretKey = credentials.get("accessKeySecret");

            if (accessKey == null || secretKey == null || accessKey.isEmpty() || secretKey.isEmpty()) {
                log.error("RDS RI: 无法获取AWS账号凭证: {} 的 AK/SK", accountName);
                throw new IllegalArgumentException("RDS RI: 无法获取AWS账号凭证: " + accountName);
            }

            AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKey, secretKey);
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCreds);
            Region sdkRegion = Region.of(regionStr);

            return RdsClient.builder()
                    .region(sdkRegion)
                    .credentialsProvider(credentialsProvider)
                    .build();
        });
    }

    /**
     * 查询RDS预留实例价格
     * @param accountName
     * @param region
     * @param reservedDbInstancesOfferingId
     * @param dbInstanceClass 数据库型号
     * @param duration
     * @param productDescription
     * @param offeringType
     * @param multiAZ
     * @param filters
     * @param maxRecords
     * @param marker
     * @return
     */
    public DescribeReservedDbInstancesOfferingsResponse describeRdsReservedInstanceOfferings(
            String accountName,
            String region,
            String reservedDbInstancesOfferingId,
            String dbInstanceClass,
            String duration,
            String productDescription,
            String offeringType,
            Boolean multiAZ,
            List<Filter> filters,
            Integer maxRecords,
            String marker) {

        log.info("RDS RI: 查询 AWS RDS Reserved Instance Offerings，账号: {}, 区域: {}, DB实例类: {}, 时长: {}, 产品描述: {}, Offer类型: {}", 
                 accountName, region, dbInstanceClass, duration, productDescription, offeringType); 
        RdsClient client = null;
        try {
            client = createRdsSdkClient(accountName, region);

            DescribeReservedDbInstancesOfferingsRequest.Builder requestBuilder = DescribeReservedDbInstancesOfferingsRequest.builder();

            if (StringUtils.isNotBlank(reservedDbInstancesOfferingId)) {
                requestBuilder.reservedDBInstancesOfferingId(reservedDbInstancesOfferingId);
            }
            if (StringUtils.isNotBlank(dbInstanceClass)) {
                requestBuilder.dbInstanceClass(dbInstanceClass);
            }
            if (StringUtils.isNotBlank(duration)) {
                requestBuilder.duration(duration);
            }
            if (StringUtils.isNotBlank(productDescription)) {
                requestBuilder.productDescription(productDescription);
            }
            if (StringUtils.isNotBlank(offeringType)) {
                requestBuilder.offeringType(offeringType);
            }
            if (multiAZ != null) {
                requestBuilder.multiAZ(multiAZ);
            }
            if (filters != null && !filters.isEmpty()) {
                requestBuilder.filters(filters);
            }
            if (maxRecords != null && maxRecords > 0) {
                requestBuilder.maxRecords(maxRecords);
            }
            if (StringUtils.isNotBlank(marker)) {
                requestBuilder.marker(marker);
            }

            DescribeReservedDbInstancesOfferingsRequest sdkRequest = requestBuilder.build();

            log.debug("RDS RI: Executing DescribeReservedDbInstancesOfferingsRequest: {}", sdkRequest);
            DescribeReservedDbInstancesOfferingsResponse response = client.describeReservedDBInstancesOfferings(sdkRequest);
            log.debug("RDS RI: DescribeReservedDbInstancesOfferingsResponse received.");
            return response;

        } catch (Exception e) {
            log.error("RDS RI: 查询 AWS RDS Reserved Instance Offerings 失败，账号: {}, 区域: {}. DB实例类: {}, 时长: {}. 错误: {}", 
                      accountName, region, dbInstanceClass, duration, e.getMessage(), e);
            throw new RuntimeException("RDS RI: 查询 AWS RDS Reserved Instance Offerings 失败: " + e.getMessage(), e);
        } finally {
            if (client != null) {
                // Do not close client here if it's managed and cached by createRdsSdkClient
            }
        }
    }

    /**
     * 购买RDS预留实例
     * @param accountName AWS账号名
     * @param region 区域
     * @param reservedDBInstancesOfferingId 要购买的RI Offer ID (必填)
     * @param reservedDBInstanceId 用户指定的RI ID (可选)
     * @param dBInstanceCount 购买数量 (可选, 默认1)
     * @param tags 标签 (可选)
     * @return PurchaseReservedDbInstancesOfferingResponse
     */
    public PurchaseReservedDbInstancesOfferingResponse purchaseRdsReservedInstanceOffering(
            String accountName,
            String region,
            String reservedDBInstancesOfferingId,
            String reservedDBInstanceId,
            Integer dBInstanceCount,
            List<Tag> tags) {

        log.info("RDS RI: 购买 AWS RDS Reserved Instance Offering. 账号: {}, 区域: {}, Offering ID: {}, 用户RI ID: {}, 数量: {}",
                 accountName, region, reservedDBInstancesOfferingId, reservedDBInstanceId, dBInstanceCount);
        RdsClient client = null;
        try {
            client = createRdsSdkClient(accountName, region);

            PurchaseReservedDbInstancesOfferingRequest.Builder requestBuilder = PurchaseReservedDbInstancesOfferingRequest.builder();

            if (StringUtils.isBlank(reservedDBInstancesOfferingId)) {
                log.error("RDS RI Purchase: reservedDBInstancesOfferingId is required.");
                throw new IllegalArgumentException("reservedDBInstancesOfferingId is required for purchasing RDS Reserved Instance.");
            }
            requestBuilder.reservedDBInstancesOfferingId(reservedDBInstancesOfferingId);

            if (StringUtils.isNotBlank(reservedDBInstanceId)) {
                requestBuilder.reservedDBInstanceId(reservedDBInstanceId);
            }

            if (dBInstanceCount != null && dBInstanceCount > 0) {
                requestBuilder.dbInstanceCount(dBInstanceCount);
            } else {
                // SDK defaults to 1 if not set. Explicitly setting to 1 for clarity or if 0/negative is invalid.
                requestBuilder.dbInstanceCount(1);
            }

            if (tags != null && !tags.isEmpty()) {
                requestBuilder.tags(tags);
            }

            PurchaseReservedDbInstancesOfferingRequest sdkRequest = requestBuilder.build();

            log.debug("RDS RI Purchase: Executing PurchaseReservedDbInstancesOfferingRequest: {}", sdkRequest);
            PurchaseReservedDbInstancesOfferingResponse response = client.purchaseReservedDBInstancesOffering(sdkRequest);
            log.info("RDS RI Purchase: PurchaseReservedDbInstancesOfferingResponse received. ReservedDBInstance ID: {}", 
                     response.reservedDBInstance() != null ? response.reservedDBInstance().reservedDBInstanceId() : "N/A");
            return response;

        } catch (IllegalArgumentException iae) {
            log.error("RDS RI Purchase: 参数错误. 账号: {}, 区域: {}, Offering ID: {}. 错误: {}", 
                      accountName, region, reservedDBInstancesOfferingId, iae.getMessage(), iae);
            throw iae; // Re-throw specific argument exceptions
        } catch (Exception e) {
            log.error("RDS RI Purchase: 购买 AWS RDS Reserved Instance Offering 失败. 账号: {}, 区域: {}, Offering ID: {}. 错误: {}", 
                      accountName, region, reservedDBInstancesOfferingId, e.getMessage(), e);
            throw new RuntimeException("RDS RI Purchase: 购买 AWS RDS Reserved Instance Offering 失败: " + e.getMessage(), e);
        } finally {
            if (client != null) {
                // Do not close client here if it's managed and cached by createRdsSdkClient
            }
        }
    }

}
