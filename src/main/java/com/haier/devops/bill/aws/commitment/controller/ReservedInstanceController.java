package com.haier.devops.bill.aws.commitment.controller;

import static com.haier.devops.bill.aws.commitment.enums.CommitTypeEnum.RC;
import static com.haier.devops.bill.aws.commitment.enums.CommitTypeEnum.RI;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.aws.commitment.dto.ReservedCacheNodesOfferingDto;
import com.haier.devops.bill.aws.commitment.dto.ReservedDBInstancesOfferingDto;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import com.haier.devops.bill.aws.commitment.sdk.AwsRdsReservedInstanceService;
import com.haier.devops.bill.aws.commitment.sdk.AwsReservedCacheNodeService;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.controller.ResponseEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import software.amazon.awssdk.services.elasticache.model.DescribeReservedCacheNodesOfferingsResponse;
import software.amazon.awssdk.services.elasticache.model.PurchaseReservedCacheNodesOfferingResponse;
import software.amazon.awssdk.services.elasticache.model.ReservedCacheNodesOffering;
import software.amazon.awssdk.services.rds.model.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 节省计划、预留实例
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/aws")
@Slf4j
public class ReservedInstanceController {
    private final ResourceUsageAgreementService agreementService;
    private final AwsRdsReservedInstanceService reservedInstanceService;
    private final AwsReservedCacheNodeService reservedCacheNodeService;

    public ReservedInstanceController(ResourceUsageAgreementService agreementService,
                                      AwsRdsReservedInstanceService reservedInstanceService,
                                      AwsReservedCacheNodeService reservedCacheNodeService) {
        this.agreementService = agreementService;
        this.reservedInstanceService = reservedInstanceService;
        this.reservedCacheNodeService = reservedCacheNodeService;
    }

    @Data
    public static class PageRequest {
        /**
         * 当前页码
         */
        @Min(1)
        private int page;

        /**
         * 每页条数
         */
        @Min(10)
        private int per_page;

        private ResourceUsageAgreement filter;

        /**
         * 是否已购买 0:未购买 1:已购买
         */
        private Integer purchased;

        /**
         * 类型：ecs/rds/redis
         */
        @NotEmpty(message = "必须指定类型")
        private String type;

        /**
         * 开始日期（资源创建时间过滤）
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate start;

        /**
         * 结束日期（资源创建时间过滤）
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate end;
    }

    /**
     * 签约列表查询
     * @param request
     * @return
     */
    @PostMapping("/commitment/list")
    public ResponseEntityWrapper<PageInfo<ResourceUsageAgreementVo>> listByPage(@RequestBody @Valid PageRequest request) {
        PageInfo<ResourceUsageAgreementVo> pager;
        try {
            pager = agreementService.query(request.getFilter(),
                    request.getType(),
                    request.getPurchased(),
                    request.getStart(),
                    request.getEnd(),
                    request.getPage(),
                    request.getPer_page());
        } catch (Exception e) {
            log.error("查询签约信息失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询签约信息失败", null);
        }
        return new ResponseEntityWrapper<>(pager);
    }

    /**
     * 查询历史签约信息
     * @param vendor
     * @param resourceId
     * @param page
     * @param perPage
     * @return
     */
    @GetMapping("/commitment/history")
    public ResponseEntityWrapper<PageInfo<ResourceUsageAgreementVo>> listHistory(@RequestParam String vendor,
                                                                                 @RequestParam String type,
                                                                                 @RequestParam String resourceId,
                                                                                 @RequestParam(defaultValue = "1") int page,
                                                                                 @RequestParam(defaultValue = "10") int perPage) {
        PageInfo<ResourceUsageAgreementVo> pager;
        try {
            pager = agreementService.queryHistory(vendor, type, resourceId, page, perPage);
        } catch (Exception e) {
            log.error("查询历史签约信息失败：vendor = {}, resourceId = {}", vendor, resourceId, e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询历史签约信息失败", null);
        }
        return new ResponseEntityWrapper<>(pager);
    }

    @Data
    public static class DescribeOfferingRequest {
        @NotNull(message = "需指定要查询的型号")
        private String model;
        @NotEmpty(message = "需指定要查询的项目")
        private List<Integer> ids;
    }

    /**
     * 查询可购买的reserved instance
     */
    @PostMapping("/describeRiOffering")
    public ResponseEntityWrapper<List<ReservedDBInstancesOfferingDto>> describeRiOffering(@RequestBody @Valid DescribeOfferingRequest request) {
        String model = request.getModel().trim();
        List<ResourceUsageAgreementVo> agreements = agreementService.queryByIds(request.getIds());

        if (CollectionUtils.isEmpty(agreements)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND.getCode(), "未找到签约承诺信息", null);
        }

        // 分别判断agreements中的每一个元素的commitType是否为Ri，有不是Ri的返回错误
        Optional<ResourceUsageAgreementVo> invalidItemFound = agreements.stream()
                .filter(a -> !StringUtils.equalsIgnoreCase(a.getCommitType(), RI.getType()))
                .findAny();

        if (invalidItemFound.isPresent()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询RDS预留实例", null);
        }

        // 判断region、duration、productDescription、prepaymentOption是否都相同，有不相同的返回错误
        Set<String> regions = agreements.stream()
                .map(ResourceUsageAgreementVo::getRegion)
                .collect(Collectors.toSet());
        if (regions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同地区的RDS预留实例", null);
        }

        Set<Long> durations = agreements.stream()
                .map(ResourceUsageAgreementVo::getDuration)
                .collect(Collectors.toSet());
        if (durations.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同期限的RDS预留实例", null);
        }

        Set<String> productDescriptions = agreements.stream()
                .map(ResourceUsageAgreementVo::getProductDescription)
                .collect(Collectors.toSet());
        if (productDescriptions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同产品描述的RDS预留实例", null);
        }

        Set<String> prepaymentOptions = agreements.stream()
                .map(ResourceUsageAgreementVo::getPrepaymentOption)
                .collect(Collectors.toSet());
        if (prepaymentOptions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同付款选项的RDS预留实例", null);
        }

        // 使用第一个agreement的信息进行查询
        ResourceUsageAgreementVo firstAgreement = agreements.get(0);
        DescribeReservedDbInstancesOfferingsResponse response = reservedInstanceService.describeRdsReservedInstanceOfferings(
                firstAgreement.getAccount(),
                firstAgreement.getRegion(),
                null,
                // 用户选择的型号
                model,
                String.valueOf(firstAgreement.getDuration()),
                firstAgreement.getProductDescription(),
                firstAgreement.getPrepaymentOption(),
                false,
                null,
                100,
                null);
        if (response.sdkHttpResponse() != null && !response.sdkHttpResponse().isSuccessful()) {
            log.error("查询RDS预留实例价格失败: {}", response.sdkHttpResponse().statusText().orElse(""));
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询RDS预留实例价格失败", null);
        }

        // 转换AWS SDK对象为DTO以支持序列化
        List<ReservedDBInstancesOfferingDto> offeringDtos = ReservedDBInstancesOfferingDto
                .fromAwsModelList(response.reservedDBInstancesOfferings());

        return new ResponseEntityWrapper<>(offeringDtos);

    }

    /**
     * 购买rds预留实例
     * @param request
     * @return
     */
    @PostMapping("/purchaseRi")
    public ResponseEntityWrapper<Boolean> purchaseReservedInstance(@RequestBody @Valid OfferingRequest request) {
        List<ResourceUsageAgreementVo> agreements = agreementService.queryByIds(request.getIds());
        if (CollectionUtils.isEmpty(agreements)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND.getCode(), "没有找到指定的签约信息", false);
        }

        Optional<ResourceUsageAgreementVo> invalidItemFound = agreements.stream()
                .filter(a -> !StringUtils.equalsIgnoreCase(a.getCommitType(), RI.getType()))
                .findAny();

        if (invalidItemFound.isPresent()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能合并购买RDS预留实例", false);
        }

        Set<String> regions = agreements.stream()
                .map(ResourceUsageAgreementVo::getRegion)
                .collect(Collectors.toSet());
        if (regions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能合并购买相同地区的RDS预留实例", false);
        }

        // todo 使用特定账号，不可跨地域
        String account = "test";
        String region = regions.iterator().next();

        String agreementIds = agreements.stream()
                .map(a -> String.valueOf(a.getId()))
                .collect(Collectors.joining(","));

        List<Tag> tags = Collections.singletonList(
                Tag
                        .builder()
                        .key("RiAgreementIds")
                        .value(agreementIds)
                        .build()
        );

        PurchaseReservedDbInstancesOfferingResponse response = reservedInstanceService.purchaseRdsReservedInstanceOffering(
                account,
                region,
                request.getOfferingId(),
                null,
                1,
                tags);

        if (response.sdkHttpResponse() != null && !response.sdkHttpResponse().isSuccessful()) {
            log.error("为{}购买预留实例失败: {}", request.getIds(), response.sdkHttpResponse().statusText().orElse(""));
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "购买预留实例失败", false);
        }

        Double commitRate = 0.0;
        List<RecurringCharge> recurringCharges = response.reservedDBInstance().recurringCharges();
        if (!CollectionUtils.isEmpty(recurringCharges)) {
            commitRate = recurringCharges.get(0).recurringChargeAmount();
        }

        // 成功后将返回的reservedDBInstanceId 更新到表里的purchasedOfferingId字段
        String reservedDBInstanceId = response.reservedDBInstance().reservedDBInstanceId();
        agreementService.update(
                new LambdaUpdateWrapper<ResourceUsageAgreement>()
                        .set(ResourceUsageAgreement::getOfferingId, request.getOfferingId())
                        .set(ResourceUsageAgreement::getPurchasedOfferingId, reservedDBInstanceId)
                        .set(ResourceUsageAgreement::getCommitRate, BigDecimal.valueOf(commitRate))
                        .set(ResourceUsageAgreement::getPurchaseTime, LocalDateTime.now())
                        .in(ResourceUsageAgreement::getId, request.getIds()));

        return new ResponseEntityWrapper<>(true);
    }

    /**
     * 查询可购买的reserved cache
     * @param request
     */
    @PostMapping("/describeRcOffering")
    public ResponseEntityWrapper<List<ReservedCacheNodesOfferingDto>> describeRcOffering(@RequestBody @Valid DescribeOfferingRequest request) {
        String model = request.getModel().trim();
        List<ResourceUsageAgreementVo> agreements = agreementService.queryByIds(request.getIds());
        if (CollectionUtils.isEmpty(agreements)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND.getCode(), "未找到签约承诺信息", null);
        }
        
        // 分别判断agreements中的每一个元素的commitType是否为Rc，有不是Rc的返回错误
        Optional<ResourceUsageAgreementVo> invalidItemFound = agreements.stream()
                .filter(a -> !StringUtils.equalsIgnoreCase(a.getCommitType(), RC.getType()))
                .findAny();

        if (invalidItemFound.isPresent()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询Redis预留实例", null);
        }

        // 判断region、duration、productDescription、prepaymentOption是否都相同，有不相同的返回错误
        Set<String> regions = agreements.stream()
                .map(ResourceUsageAgreementVo::getRegion)
                .collect(Collectors.toSet());
        if (regions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同地区的Redis预留实例", null);
        }

        Set<Long> durations = agreements.stream()
                .map(ResourceUsageAgreementVo::getDuration)
                .collect(Collectors.toSet());
        if (durations.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同期限的Redis预留实例", null);
        }

        Set<String> productDescriptions = agreements.stream()
                .map(ResourceUsageAgreementVo::getProductDescription)
                .collect(Collectors.toSet());
        if (productDescriptions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同产品描述的Redis预留实例", null);
        }

        Set<String> prepaymentOptions = agreements.stream()
                .map(ResourceUsageAgreementVo::getPrepaymentOption)
                .collect(Collectors.toSet());
        if (prepaymentOptions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能查询相同付款选项的Redis预留实例", null);
        }

        // 使用第一个agreement的信息进行查询
        ResourceUsageAgreementVo firstAgreement = agreements.get(0);
        DescribeReservedCacheNodesOfferingsResponse response = reservedCacheNodeService.describeReservedCacheNodesOfferings(
                firstAgreement.getAccount(),
                firstAgreement.getRegion(),
                null,
                model,
                String.valueOf(firstAgreement.getDuration()),
                firstAgreement.getProductDescription(),
                firstAgreement.getPrepaymentOption(),
                100,
                null);

        if (response.sdkHttpResponse() != null && !response.sdkHttpResponse().isSuccessful()) {
            log.error("查询Redis预留实例价格失败: {}", response.sdkHttpResponse().statusText().orElse(""));
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询Redis预留实例价格失败", null);
        }

        // 转换AWS SDK对象为DTO以支持序列化
        List<ReservedCacheNodesOfferingDto> offeringDtos = ReservedCacheNodesOfferingDto
                .fromAwsModelList(response.reservedCacheNodesOfferings());

        return new ResponseEntityWrapper<>(offeringDtos);
    }

    /**
     * 购买redis预留实例
     * @param request
     * @return
     */
    @PostMapping("/purchaseRc")
    public ResponseEntityWrapper<Boolean> purchaseReservedCache(@RequestBody @Valid OfferingRequest request) {
        List<ResourceUsageAgreementVo> agreements = agreementService.queryByIds(request.getIds());
        if (CollectionUtils.isEmpty(agreements)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND.getCode(), "没有找到指定的签约信息", false);
        }

        Optional<ResourceUsageAgreementVo> invalidItemFound = agreements.stream()
                .filter(a -> !StringUtils.equalsIgnoreCase(a.getCommitType(), RC.getType()))
                .findAny();

        if (invalidItemFound.isPresent()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能合并购买Redis预留实例", false);
        }

        Set<String> regions = agreements.stream()
                .map(ResourceUsageAgreementVo::getRegion)
                .collect(Collectors.toSet());
        if (regions.size() > 1) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_CONFLICTS.getCode(), "只能合并购买相同地区的Redis预留实例", false);
        }

        // todo 使用特定账号，不可跨地域
        String account = "test";
        String region = regions.iterator().next();

        String agreementIds = agreements.stream()
                .map(a -> String.valueOf(a.getId()))
                .collect(Collectors.joining(","));

        List<software.amazon.awssdk.services.elasticache.model.Tag> tags = Collections.singletonList(
                software.amazon.awssdk.services.elasticache.model.Tag
                        .builder()
                        .key("RcAgreementIds")
                        .value(agreementIds)
                        .build()
        );

        PurchaseReservedCacheNodesOfferingResponse response = reservedCacheNodeService.purchaseReservedCacheNodesOffering(
                account,
                region,
                request.getOfferingId(),
                null,
                1,
                tags);

        if (response.sdkHttpResponse() != null && !response.sdkHttpResponse().isSuccessful()) {
            log.error("为{}购买Redis预留实例失败: {}", request.getIds(), response.sdkHttpResponse().statusText().orElse(""));
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "购买Redis预留实例失败", false);
        }

        List<software.amazon.awssdk.services.elasticache.model.RecurringCharge> recurringCharges = response.reservedCacheNode().recurringCharges();
        Double commitRate = 0.0;
        if (!CollectionUtils.isEmpty(recurringCharges)) {
            commitRate = recurringCharges.get(0).recurringChargeAmount();
        }

        // 成功后将返回的reservedCacheNodeId 更新到表里的purchasedOfferingId字段
        String reservedCacheNodeId = response.reservedCacheNode().reservedCacheNodeId();
        agreementService.update(
                new LambdaUpdateWrapper<ResourceUsageAgreement>()
                        .set(ResourceUsageAgreement::getOfferingId, request.getOfferingId())
                        .set(ResourceUsageAgreement::getPurchasedOfferingId, reservedCacheNodeId)
                        .set(ResourceUsageAgreement::getCommitRate, BigDecimal.valueOf(commitRate))
                        .set(ResourceUsageAgreement::getPurchaseTime, LocalDateTime.now())
                        .in(ResourceUsageAgreement::getId, request.getIds()));

        return new ResponseEntityWrapper<>(true);
    }

    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    public static final class OfferingRequest {
        @NotEmpty(message = "offeringId不能为空")
        private String offeringId;
        @NotEmpty(message = "需指定要合并购买预留实例的项目")
        private List<Integer> ids;
    }
}
