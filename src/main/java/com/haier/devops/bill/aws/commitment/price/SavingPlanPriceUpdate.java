package com.haier.devops.bill.aws.commitment.price;

import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class SavingPlanPriceUpdate {
    private final SavingPlanPriceQuery priceQuery;
    private final ResourceUsageAgreementService agreementService;

    public SavingPlanPriceUpdate(
            SavingPlanPriceQuery priceQuery,
            ResourceUsageAgreementService agreementService) {
        this.priceQuery = priceQuery;
        this.agreementService = agreementService;
    }

    public void update() {
        List<ResourceUsageAgreementVo> agreementsWithPrices = priceQuery.queryPrice();

        if (CollectionUtils.isEmpty(agreementsWithPrices)) {
            log.info("No agreements found with new prices to update.");
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (ResourceUsageAgreementVo agreement : agreementsWithPrices) {
            agreement.setUpdateTime(now);
        }

        try {
            // ResourceUsageAgreementService.updateBatchById should accept List<ResourceUsageAgreementVo>
            // due to polymorphism (List<ResourceUsageAgreementVo> is a List<? extends ResourceUsageAgreement>).
            // Explicitly convert to List<ResourceUsageAgreement> to satisfy strict type checking if necessary.
            List<com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement> agreementsToUpdate = new ArrayList<>(agreementsWithPrices);
            boolean success = agreementService.updateBatchById(agreementsToUpdate);
            if (success) {
                log.info("Successfully updated {} agreements with new prices.", agreementsToUpdate.size());
            } else {
                log.warn("Batch update of {} agreements may not have been fully successful (service returned false).", agreementsToUpdate.size());
            }
        } catch (Exception e) {
            log.error("Error during batch update of ResourceUsageAgreements: {}", e.getMessage(), e);
        }
    }
}
