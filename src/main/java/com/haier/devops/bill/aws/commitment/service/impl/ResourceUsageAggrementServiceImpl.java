package com.haier.devops.bill.aws.commitment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import com.haier.devops.bill.aws.commitment.mapper.ResourceUsageAgreementMapper;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;

import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 资源使用签约 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Service
public class ResourceUsageAggrementServiceImpl extends ServiceImpl<ResourceUsageAgreementMapper, ResourceUsageAgreement> implements ResourceUsageAgreementService {

    @Override
    public List<ResourceUsageAgreementVo> queryPendingSavingPlanResource(String... planType) {
        return getBaseMapper().queryPendingSavingPlanResource(planType);
    }

    @Override
    public List<ResourceUsageAgreementVo> queryPendingRIResource() {
        return getBaseMapper().queryPendingReservedInstanceResource();
    }

    @Override
    public ResourceUsageAgreementVo queryById(Integer id) {
        return getBaseMapper().queryById(id);
    }

    @Override
    public List<ResourceUsageAgreementVo> queryByIds(List<Integer> ids) {
        return getBaseMapper().queryByIds(ids);
    }

    @Override
    public PageInfo<ResourceUsageAgreementVo> query(ResourceUsageAgreement filter,
                                                    String type,
                                                    Integer purchased,
                                                    LocalDate start,
                                                    LocalDate end,
                                                    int page,
                                                    int perPage) {
        return PageMethod
                .startPage(page, perPage)
                .doSelectPageInfo(() -> getBaseMapper().query(filter, type, purchased, start, end));
    }

    @Override
    public PageInfo<ResourceUsageAgreementVo> queryHistory(String vendor, String type, String resourceId, int page, int perPage) {
        return PageMethod
                .startPage(page, perPage)
                .doSelectPageInfo(() -> getBaseMapper().queryHistory(vendor, type, resourceId));
    }
}
