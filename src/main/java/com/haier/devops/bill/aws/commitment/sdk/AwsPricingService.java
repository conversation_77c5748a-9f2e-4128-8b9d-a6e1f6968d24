package com.haier.devops.bill.aws.commitment.sdk;

import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.pricing.PricingClient;
import software.amazon.awssdk.services.pricing.model.DescribeServicesRequest;
import software.amazon.awssdk.services.pricing.model.DescribeServicesResponse;
import software.amazon.awssdk.services.pricing.model.Filter;
import software.amazon.awssdk.services.pricing.model.GetAttributeValuesRequest;
import software.amazon.awssdk.services.pricing.model.GetAttributeValuesResponse;
import software.amazon.awssdk.services.pricing.model.GetProductsRequest;
import software.amazon.awssdk.services.pricing.model.GetProductsResponse;
import software.amazon.awssdk.services.pricing.model.ListPriceListsRequest;
import software.amazon.awssdk.services.pricing.model.ListPriceListsResponse;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 查询价格服务
 */
@Service
@Slf4j
public class AwsPricingService {

    private final CloudAccountService cloudAccountService;
    private final ConcurrentMap<String, PricingClient> clientCache = new ConcurrentHashMap<>();

    public AwsPricingService(CloudAccountService cloudAccountService) {
        this.cloudAccountService = cloudAccountService;
    }

    private PricingClient createSdkClient(String accountName, String regionStr) {
        String cacheKey = accountName + ":" + regionStr;
        return clientCache.computeIfAbsent(cacheKey, k -> {
            log.info("本地缓存未命中，创建新的 PricingClient，账号: {}, 区域: {}", accountName, regionStr);
            Map<String, String> credentials = cloudAccountService.getOpenApiKey(VendorEnum.AWS.getVendor(), accountName, "ACCOUNT_CHECK");
            String accessKey = credentials.get("accessKey");
            String secretKey = credentials.get("accessKeySecret");

            if (accessKey == null || secretKey == null || accessKey.isEmpty() || secretKey.isEmpty()) {
                log.error("无法获取AWS账号凭证: {} 的 AK/SK", accountName);
                throw new IllegalArgumentException("无法获取AWS账号凭证: " + accountName);
            }

            AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKey, secretKey);
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCreds);
            
            // AWS Pricing API has specific regional endpoints. Default to us-east-1 if not specified.
            // Common regions for Pricing API are us-east-1 and ap-south-1.
            String effectiveRegion = StringUtils.isNotBlank(regionStr) ? regionStr : "us-east-1";
            Region sdkRegion = Region.of(effectiveRegion);

            return PricingClient.builder()
                    .region(sdkRegion)
                    .credentialsProvider(credentialsProvider)
                    .build();
        });
    }



    public GetProductsResponse getProducts(
            String accountName,
            String clientRegion,
            String serviceCode,
            List<Filter> filters,
            String formatVersion,
            String nextToken,
            Integer maxResults) {

        log.info("查询 AWS 产品定价信息，账号: {}, Client区域: {}, 服务代码: {}", 
                 accountName, clientRegion, serviceCode);
        PricingClient client = null; 
        try {
            client = createSdkClient(accountName, clientRegion);

            GetProductsRequest.Builder requestBuilder = GetProductsRequest.builder();
            if (StringUtils.isBlank(serviceCode)) {
                // ServiceCode is typically required for GetProducts, though the SDK might not enforce it at build time.
                // Depending on strictness, could throw IllegalArgumentException here.
                log.warn("ServiceCode is blank for GetProducts request. This may lead to an error from the AWS Pricing API.");
            } else {
                requestBuilder.serviceCode(serviceCode);
            }

            if (filters != null && !filters.isEmpty()) {
                requestBuilder.filters(filters);
            }
            if (StringUtils.isNotBlank(formatVersion)) {
                requestBuilder.formatVersion(formatVersion);
            }
            if (StringUtils.isNotBlank(nextToken)) {
                requestBuilder.nextToken(nextToken);
            }
            if (maxResults != null && maxResults > 0) { // AWS SDK might require positive maxResults
                requestBuilder.maxResults(maxResults);
            }

            GetProductsRequest request = requestBuilder.build();
            log.debug("GetProductsRequest: {}", request);

            GetProductsResponse response = client.getProducts(request); 
            log.info("成功获取产品定价信息. NextToken: {}", response.nextToken());
            // log.debug("PriceList count: {}", response.priceList() != null ? response.priceList().size() : 0);
            log.debug("GetProductsResponse: {}", response);
            return response;

        } catch (AwsServiceException e) {
            log.error("AWS服务异常，查询产品定价信息失败. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.awsErrorDetails().errorMessage(), e);
            throw e;
        } catch (SdkClientException e) {
            log.error("SDK客户端异常，查询产品定价信息失败. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("查询产品定价信息时发生未知错误. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.getMessage(), e);
            throw new RuntimeException("查询产品定价信息时发生未知错误", e);
        } finally {
            if (client != null) {
                try {
                    client.close();
                    log.debug("PricingClient for account {} and region {} closed.", accountName, clientRegion);
                } catch (Exception e) {
                    log.warn("Error closing PricingClient for account {} and region {}: {}", 
                             accountName, clientRegion, e.getMessage(), e);
                }
            }
        }
    }

    public DescribeServicesResponse describeServices(
            String accountName,
            String clientRegion,
            String serviceCode,
            String formatVersion,
            String nextToken,
            Integer maxResults) {

        log.info("查询 AWS 服务描述信息，账号: {}, Client区域: {}, 服务代码: {}, Format版本: {}, NextToken: {}, MaxResults: {}", 
                 accountName, clientRegion, serviceCode, formatVersion, nextToken, maxResults);
        PricingClient client = null;
        try {
            client = createSdkClient(accountName, clientRegion);

            DescribeServicesRequest.Builder requestBuilder = DescribeServicesRequest.builder();

            if (StringUtils.isNotBlank(serviceCode)) {
                requestBuilder.serviceCode(serviceCode);
            }
            if (StringUtils.isNotBlank(formatVersion)) {
                requestBuilder.formatVersion(formatVersion);
            }
            if (StringUtils.isNotBlank(nextToken)) {
                requestBuilder.nextToken(nextToken);
            }
            if (maxResults != null && maxResults > 0) {
                requestBuilder.maxResults(maxResults);
            }

            DescribeServicesRequest request = requestBuilder.build();
            log.debug("DescribeServicesRequest: {}", request);

            DescribeServicesResponse response = client.describeServices(request);
            log.info("成功获取服务描述信息. NextToken: {}, Services count: {}", response.nextToken(), response.services() != null ? response.services().size() : 0);
            log.debug("DescribeServicesResponse: {}", response);
            return response;

        } catch (AwsServiceException e) {
            log.error("AWS服务异常，查询服务描述信息失败. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.awsErrorDetails().errorMessage(), e);
            // 根据需要可以抛出自定义异常或直接抛出e
            throw e;
        } catch (SdkClientException e) {
            log.error("SDK客户端异常，查询服务描述信息失败. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("查询服务描述信息时发生未知错误. 账号: {}, 区域: {}. Error: {}", accountName, clientRegion, e.getMessage(), e);
            // Consider re-throwing a more specific application exception
            throw new RuntimeException("查询服务描述信息时发生未知错误", e);
        }
        // The client from createSdkClient is managed by a cache and should not be closed here.
    }

    public GetAttributeValuesResponse getAttributeValues(
            String accountName,
            String clientRegion,
            String serviceCode,
            String attributeName,
            String nextToken,
            Integer maxResults) {

        log.info("查询 AWS 服务属性值，账号: {}, Client区域: {}, 服务代码: {}, 属性名: {}, NextToken: {}, MaxResults: {}", 
                 accountName, clientRegion, serviceCode, attributeName, nextToken, maxResults);
        
        if (StringUtils.isBlank(serviceCode)) {
            log.error("服务代码 (serviceCode) 不能为空.");
            throw new IllegalArgumentException("Service code cannot be blank.");
        }
        if (StringUtils.isBlank(attributeName)) {
            log.error("属性名 (attributeName) 不能为空.");
            throw new IllegalArgumentException("Attribute name cannot be blank.");
        }

        PricingClient client = null;
        try {
            client = createSdkClient(accountName, clientRegion);

            GetAttributeValuesRequest.Builder requestBuilder = GetAttributeValuesRequest.builder()
                    .serviceCode(serviceCode)
                    .attributeName(attributeName);

            if (StringUtils.isNotBlank(nextToken)) {
                requestBuilder.nextToken(nextToken);
            }
            if (maxResults != null && maxResults > 0) {
                requestBuilder.maxResults(maxResults);
            }

            GetAttributeValuesRequest request = requestBuilder.build();
            log.debug("GetAttributeValuesRequest: {}", request);

            GetAttributeValuesResponse response = client.getAttributeValues(request);
            log.info("成功获取服务属性值. NextToken: {}, Values count: {}", response.nextToken(), response.attributeValues() != null ? response.attributeValues().size() : 0);
            log.debug("GetAttributeValuesResponse: {}", response);
            return response;

        } catch (AwsServiceException e) {
            log.error("AWS服务异常，查询服务属性值失败. 账号: {}, 区域: {}, 服务代码: {}, 属性名: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, attributeName, e.awsErrorDetails().errorMessage(), e);
            throw e;
        } catch (SdkClientException e) {
            log.error("SDK客户端异常，查询服务属性值失败. 账号: {}, 区域: {}, 服务代码: {}, 属性名: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, attributeName, e.getMessage(), e);
            throw e;
        } catch (IllegalArgumentException e) {
            // Already logged, just rethrow
            throw e;
        } catch (Exception e) {
            log.error("查询服务属性值时发生未知错误. 账号: {}, 区域: {}, 服务代码: {}, 属性名: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, attributeName, e.getMessage(), e);
            throw new RuntimeException("查询服务属性值时发生未知错误", e);
        }
    }

    public ListPriceListsResponse listPriceLists(
            String accountName,
            String clientRegion,
            String serviceCode,
            String currencyCode,
            Instant effectiveDate,
            String regionCode, // Optional AWS Region to filter price lists
            String nextToken,
            Integer maxResults) {

        log.info("查询 AWS Price Lists，账号: {}, Client区域: {}, 服务代码: {}, 货币代码: {}, 生效日期: {}, 区域代码: {}, NextToken: {}, MaxResults: {}", 
                 accountName, clientRegion, serviceCode, currencyCode, effectiveDate, regionCode, nextToken, maxResults);
        
        if (StringUtils.isBlank(serviceCode)) {
            log.error("服务代码 (serviceCode) 不能为空.");
            throw new IllegalArgumentException("Service code cannot be blank.");
        }
        if (StringUtils.isBlank(currencyCode)) {
            log.error("货币代码 (currencyCode) 不能为空.");
            throw new IllegalArgumentException("Currency code cannot be blank.");
        }
        if (effectiveDate == null) {
            log.error("生效日期 (effectiveDate) 不能为空.");
            throw new IllegalArgumentException("Effective date cannot be null.");
        }

        PricingClient client = null;
        try {
            client = createSdkClient(accountName, clientRegion);

            ListPriceListsRequest.Builder requestBuilder = ListPriceListsRequest.builder()
                    .serviceCode(serviceCode)
                    .currencyCode(currencyCode)
                    .effectiveDate(effectiveDate);

            if (StringUtils.isNotBlank(regionCode)) {
                requestBuilder.regionCode(regionCode);
            }
            if (StringUtils.isNotBlank(nextToken)) {
                requestBuilder.nextToken(nextToken);
            }
            if (maxResults != null && maxResults > 0) {
                requestBuilder.maxResults(maxResults);
            }

            ListPriceListsRequest request = requestBuilder.build();
            log.debug("ListPriceListsRequest: {}", request);

            ListPriceListsResponse response = client.listPriceLists(request);
            log.info("成功获取 Price Lists. NextToken: {}, PriceLists count: {}", response.nextToken(), response.priceLists() != null ? response.priceLists().size() : 0);
            log.debug("ListPriceListsResponse: {}", response);
            return response;

        } catch (AwsServiceException e) {
            log.error("AWS服务异常，查询 Price Lists 失败. 账号: {}, Client区域: {}, 服务代码: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, e.awsErrorDetails().errorMessage(), e);
            throw e;
        } catch (SdkClientException e) {
            log.error("SDK客户端异常，查询 Price Lists 失败. 账号: {}, Client区域: {}, 服务代码: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, e.getMessage(), e);
            throw e;
        } catch (IllegalArgumentException e) {
            // Already logged, just rethrow
            throw e;
        } catch (Exception e) {
            log.error("查询 Price Lists 时发生未知错误. 账号: {}, Client区域: {}, 服务代码: {}. Error: {}", 
                      accountName, clientRegion, serviceCode, e.getMessage(), e);
            throw new RuntimeException("查询 Price Lists 时发生未知错误", e);
        }
    }

    
}
