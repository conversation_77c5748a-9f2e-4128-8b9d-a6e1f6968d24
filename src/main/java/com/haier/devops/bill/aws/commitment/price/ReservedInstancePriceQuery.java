package com.haier.devops.bill.aws.commitment.price;

import com.haier.devops.bill.aws.commitment.sdk.AwsRdsReservedInstanceService;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.rds.model.DescribeReservedDbInstancesOfferingsResponse;
import software.amazon.awssdk.services.rds.model.ReservedDBInstancesOffering;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 预留实例价格查询
 */
@Component
@Slf4j
public class ReservedInstancePriceQuery implements PriceQuery {
    private final ResourceUsageAgreementService agreementService;
    private final AwsRdsReservedInstanceService reservedInstanceService;
    private static final Boolean MULTI_AZ = false;

    public ReservedInstancePriceQuery(ResourceUsageAgreementService agreementService, AwsRdsReservedInstanceService reservedInstanceService) {
        this.agreementService = agreementService;
        this.reservedInstanceService = reservedInstanceService;
    }

    @Override
    public List<ResourceUsageAgreementVo> queryPrice() {
        List<ResourceUsageAgreementVo> resources = agreementService.queryPendingRIResource();
        if (CollectionUtils.isEmpty(resources)) {
            return new ArrayList<>();
        }

        // todo only for test
        for (ResourceUsageAgreementVo resource : resources) {
            resource.setAccount("test");
        }

        Map<OfferingGroupKey, List<ResourceUsageAgreementVo>> groupedResourceMap = resources.stream().collect(groupingBy(r ->
                new OfferingGroupKey(r.getAccount(),
                        r.getRegion(),
                        r.getDuration(),
                        r.getPrepaymentOption(),
                        r.getInstanceType(),
                        r.getProductDescription())));
        for (OfferingGroupKey key : groupedResourceMap.keySet()) {
            DescribeReservedDbInstancesOfferingsResponse response = reservedInstanceService.describeRdsReservedInstanceOfferings(
                    key.getAccount(),
                    key.getRegion(),
                    null,
                    key.getInstanceType(),
                    String.valueOf(key.getDuration()),
                    key.getProductDescription(),
                    key.getPaymentOption(),
                    MULTI_AZ,
                    null,
                    100,
                    null);

            if (!response.sdkHttpResponse().isSuccessful()) {
                log.error("查询预留实例价格失败:{}。" +
                                "account = {}, region = {}, instanceType = {}, duration = {}, productDescription = {}, paymentOption = {}",
                        response.sdkHttpResponse().statusCode(),
                        key.getAccount(),
                        key.getRegion(),
                        key.getInstanceType(),
                        key.getDuration(),
                        key.getProductDescription(),
                        key.getPaymentOption());
                continue;
            }

            List<ReservedDBInstancesOffering> offerings = response.reservedDBInstancesOfferings();
            if (null == offerings || offerings.isEmpty()) {
                log.error("no reserved instance offering found: " +
                                "account={}, region={}, instanceType={}, duration={}, productDescription={}, paymentOption={}",
                        key.getAccount(),
                        key.getRegion(),
                        key.getInstanceType(),
                        key.getDuration(),
                        key.getProductDescription(),
                        key.getPaymentOption());
                // todo 通知我
                continue;
            }

            offerings = offerings.stream()
                    // exact match
                    .filter(o -> o.productDescription().equals(key.getProductDescription()))
                    .collect(Collectors.toList());

            if (offerings.size() > 1) {
                log.error("multiple reserved instance offerings found: " +
                                "account={}, region={}, instanceType={}, duration={}, productDescription={}, paymentOption={}",
                        key.getAccount(),
                        key.getRegion(),
                        key.getInstanceType(),
                        key.getDuration(),
                        key.getProductDescription(),
                        key.getPaymentOption());
                // todo 通知我
                continue;
            }

//            String offeringId = offerings.get(0).reservedDBInstancesOfferingId();
            Double price = offerings.get(0).recurringCharges().get(0).recurringChargeAmount();
            groupedResourceMap.get(key).forEach(r -> r.setCommitRate(BigDecimal.valueOf(price)));
        }

        return groupedResourceMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }


    @Data
    @AllArgsConstructor
    static
    class OfferingGroupKey {
        private String account;
        private String region;
        private Long duration;
        private String paymentOption;
        private String instanceType;
        private String productDescription;
    }
}
