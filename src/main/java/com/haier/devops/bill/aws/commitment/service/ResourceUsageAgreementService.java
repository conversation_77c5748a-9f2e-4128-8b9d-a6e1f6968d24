package com.haier.devops.bill.aws.commitment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 资源使用签约 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface ResourceUsageAgreementService extends IService<ResourceUsageAgreement> {

    /**
     * 查询购买节省计划的实例
     * @return
     */
    List<ResourceUsageAgreementVo> queryPendingSavingPlanResource(String... planType);

    /**
     * 查询购买预留实例的实例
     * @return
     */
    List<ResourceUsageAgreementVo> queryPendingRIResource();
    ResourceUsageAgreementVo queryById(Integer id);

    List<ResourceUsageAgreementVo> queryByIds(List<Integer> ids);

    PageInfo<ResourceUsageAgreementVo> query(ResourceUsageAgreement filter,
                                             String type,
                                             Integer purchased,
                                             LocalDate start,
                                             LocalDate end,
                                             int page,
                                             int perPage);


    /**
     * 查询历史
     * @param vendor
     * @param resourceId
     * @param page
     * @param perPage
     * @return
     */
    PageInfo<ResourceUsageAgreementVo> queryHistory(String vendor, String type, String resourceId, int page, int perPage);
}
