package com.haier.devops.bill.aws.commitment.purchase;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import com.haier.devops.bill.common.service.CloudAccountService;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.savingsplans.model.SavingsPlanType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class PurchaseTemplate<K extends PurchaseTemplate.GroupKey> implements Purchasable {
    static final String SP_TYPE_COMPUTING = SavingsPlanType.COMPUTE.toString();
    static final String SP_TYPE_EC2 = SavingsPlanType.EC2_INSTANCE.toString();
    static final String RI = "Ri";
    static final String PRODUCT_TYPE_EC2 = "EC2";
    static final String PAYMENT_OPTION_NO_UPFRONT = "No Upfront";

    @Override
    public void purchase() {
        // 1、查询待购买合约的实例
        List<ResourceUsageAgreementVo> agreements = queryPendingAgreements();
        if (CollectionUtils.isEmpty(agreements)) {
            return;
        }

        // 2、不同优惠策略按照不通的维度进行分组
        Map<K, List<ResourceUsageAgreementVo>> purchaseMap = groupBy(agreements, grouping());
        if (CollectionUtils.isEmpty(purchaseMap)) {
            return;
        }

        // 3、将分组的合约进行购买，并处理
        for (Map.Entry<K, List<ResourceUsageAgreementVo>> entry : purchaseMap.entrySet()) {
            String purchaseOfferingId = doPurchase(entry.getKey(), entry.getValue());
            postHandle(purchaseOfferingId, entry.getValue());
        }

    }

    private void postHandle(String purchaseOfferingId, List<ResourceUsageAgreementVo> agreements) {
        // 将agreements根据id批量更新purchase_offering_id字段
        List<Integer> agreementIds = agreements.stream()
                .map(ResourceUsageAgreementVo::getId)
                .collect(Collectors.toList());
        LambdaUpdateWrapper<ResourceUsageAgreement> updateWrapper = new LambdaUpdateWrapper<ResourceUsageAgreement>()
                .set(ResourceUsageAgreement::getPurchasedOfferingId, purchaseOfferingId)
                .set(ResourceUsageAgreement::getPurchaseTime, LocalDateTime.now())
                .in(ResourceUsageAgreement::getId, agreementIds);
        getAgreementService().update(updateWrapper);
    }

    abstract <T, K> Function<T, K> grouping();


    /**
     * 查询待购买的合约
     * @return
     */
    abstract List<ResourceUsageAgreementVo> queryPendingAgreements();

    abstract String doPurchase(K groupKey, List<ResourceUsageAgreementVo> agreements);

    protected static class GroupKey {

    }

    public static <T, K> Map<K, List<T>> groupBy(List<T> list, Function<T, K> classifier) {
        return list.stream().collect(Collectors.groupingBy(classifier));
    }

    abstract ResourceUsageAgreementService getAgreementService();
    abstract AwsSavingsPlanService getSavingsPlanService();
    abstract CloudAccountService getAccountService();
}