package com.haier.devops.bill.aws.commitment.sdk;

import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.savingsplans.SavingsplansClient;
import software.amazon.awssdk.services.savingsplans.model.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;

/**
 * AWS Savings Plan服务
 * 提供对AWS Savings Plan API的封装和业务逻辑处理
 */
@Slf4j
@Service
public class AwsSavingsPlanService {
    private final CloudAccountService cloudAccountService;
    private final ConcurrentMap<String, SavingsplansClient> clientCache = new ConcurrentHashMap<>();

    public AwsSavingsPlanService(CloudAccountService cloudAccountService) {
        this.cloudAccountService = cloudAccountService;
    }

    /**
     * 创建AWS SDK SavingsplansClient
     *
     * @param accountName AWS账号名称
     * @param regionStr 区域
     * @return SavingsplansClient
     */
    private SavingsplansClient createSdkClient(String accountName, String regionStr) {
        String cacheKey = accountName + ":" + regionStr;
        return clientCache.computeIfAbsent(cacheKey, k -> {
            log.info("本地缓存未命中，创建新的SavingsplansClient，账号: {}, 区域: {}", accountName, regionStr);
            Map<String, String> credentials = cloudAccountService.getOpenApiKey(VendorEnum.AWS.getVendor(), accountName, "ACCOUNT_CHECK");
            String accessKey = credentials.get("accessKey");
            String secretKey = credentials.get("accessKeySecret");


            if (accessKey == null || secretKey == null || accessKey.isEmpty() || secretKey.isEmpty()) {
                throw new IllegalArgumentException("无法获取AWS账号凭证: " + accountName);
            }

            AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKey, secretKey);
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCreds);
            Region regionSdk = Region.of(regionStr != null ? regionStr : "us-east-1");

            return SavingsplansClient.builder()
                    .region(regionSdk)
                    .credentialsProvider(credentialsProvider)
                    .build();
        });
    }

    /**
     * 查询Savings Plans产品列表
     *
     * @param accountName AWS账号名称
     * @param region 区域
     * @param productType 产品类型
     * @param serviceCodes 服务代码列表
     * @param savingsPlansTypes 产品类型列表 (String)
     * @param durations 有效期列表 (Long, in seconds)
     * @param paymentOptions 付款选项列表 (String)
     * @param maxResults 最大结果数
     * @return Savings Plans产品列表
     */
    public DescribeSavingsPlansOfferingsResponse describeSavingsPlansOfferings(
            String accountName,
            String region,
            String productType,
            List<String> serviceCodes,
            List<String> savingsPlansTypes,
            List<Long> durations,
            List<String> paymentOptions,
            Integer maxResults, String... instanceFamily) {

        log.info("查询AWS Savings Plans产品列表，账号: {}, 区域: {}", accountName, region);

        try {
            // 创建SDK客户端
            SavingsplansClient client = createSdkClient(accountName, region);

            try {
                // 构建SDK请求
                DescribeSavingsPlansOfferingsRequest.Builder requestBuilder = DescribeSavingsPlansOfferingsRequest.builder();

                if (StringUtils.isNotBlank(productType)) {
                    SavingsPlanProductType savingsPlanProductType = SavingsPlanProductType.fromValue(productType);
                    requestBuilder.productType(savingsPlanProductType);
                }

                if (serviceCodes != null && !serviceCodes.isEmpty()) {
                    requestBuilder.serviceCodes(serviceCodes);
                }

                if (savingsPlansTypes != null && !savingsPlansTypes.isEmpty()) {
                    // Convert String list to SavingsPlanType list
                    List<SavingsPlanType> sdkPlanTypes = savingsPlansTypes.stream()
                            .map(SavingsPlanType::fromValue)
                            .collect(Collectors.toList());
                    requestBuilder.planTypes(sdkPlanTypes);
                }

                if (durations != null && !durations.isEmpty()) {
                    requestBuilder.durations(durations); // Use Long list directly
                }

                if (paymentOptions != null && !paymentOptions.isEmpty()) {
                    // Convert String list to SavingsPlanPaymentOption list
                    List<SavingsPlanPaymentOption> sdkPaymentOptions = paymentOptions.stream()
                            .map(SavingsPlanPaymentOption::fromValue)
                            .collect(Collectors.toList());
                    requestBuilder.paymentOptions(sdkPaymentOptions);
                }

                if (!CollectionUtils.isEmpty(savingsPlansTypes) &&
                        savingsPlansTypes.contains(SavingsPlanType.EC2_INSTANCE.toString())) {
                    List<SavingsPlanOfferingFilterElement> filters = new ArrayList<>();
                    if (instanceFamily != null && instanceFamily.length > 0) {
                        filters.add(SavingsPlanOfferingFilterElement.builder()
                                .name("instanceFamily")
                                .values(instanceFamily)
                                .build());
                    }

                    filters.add(SavingsPlanOfferingFilterElement.builder()
                            .name("region")
                            .values(region)
                            .build());
                    requestBuilder.filters(filters);
                }

                if (maxResults != null) {
                    requestBuilder.maxResults(maxResults);
                }

                // 调用SDK API并直接返回结果
                return client.describeSavingsPlansOfferings(requestBuilder.build());
            } finally {
                // 关闭客户端 - 由缓存管理，此处不再关闭
                // client.close();
            }
        } catch (Exception e) {
            log.error("查询AWS Savings Plans产品列表失败", e);
            throw new RuntimeException("查询AWS Savings Plans产品列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询Savings Plans产品费率
     *
     * @param accountName AWS账号名称
     * @param region 区域
     * @param offeringIds 产品ID列表
     * @param tenancies 租用模式
     * @param productDescriptions 产品描述列表
     * @param serviceCodes 服务代码列表 (String)
     * @param instanceTypes 实例类型列表 (String, mapped to productTypes)
     * @param instanceFamilies 实例族列表 (String, mapped to usageTypes)
     * @param maxResults 最大结果数
     * @return Savings Plans产品费率列表
     */
    public DescribeSavingsPlansOfferingRatesResponse describeSavingsPlansOfferingRates(
            String accountName,
            String region,
            List<String> offeringIds,
            List<String> tenancies,
            List<String> productDescriptions,
            List<String> serviceCodes,
            List<String> instanceTypes,
            List<String> instanceFamilies,
            Integer maxResults) {

        log.info("查询AWS Savings Plans产品费率，账号: {}, 区域: {}", accountName, region);

        try {
            // 创建SDK客户端
            SavingsplansClient client = createSdkClient(accountName, region);

            try {
                // 构建SDK请求
                DescribeSavingsPlansOfferingRatesRequest.Builder requestBuilder =
                    DescribeSavingsPlansOfferingRatesRequest.builder();

                // Build filters list
                List<SavingsPlanOfferingRateFilterElement> filters = new ArrayList<>();
                if (region != null) {
                    filters.add(SavingsPlanOfferingRateFilterElement.builder()
                            .name(SavingsPlanRateFilterAttribute.REGION)
                            .values(region)
                            .build());
                }
                if (instanceTypes != null && !instanceTypes.isEmpty()) {
                    filters.add(SavingsPlanOfferingRateFilterElement.builder()
                            .name(SavingsPlanRateFilterAttribute.INSTANCE_TYPE)
                            .values(instanceTypes)
                            .build());
                }

                if (tenancies != null && !tenancies.isEmpty()) {
                    filters.add(SavingsPlanOfferingRateFilterElement.builder()
                            .name(SavingsPlanRateFilterAttribute.TENANCY)
                            .values(tenancies)
                            .build());
                }

                if (productDescriptions != null && !productDescriptions.isEmpty())  {
                    filters.add(SavingsPlanOfferingRateFilterElement.builder()
                            .name(SavingsPlanRateFilterAttribute.PRODUCT_DESCRIPTION)
                            .values(productDescriptions)
                            .build()
                    );
                }

                if (!filters.isEmpty()) {
                    // todo only BoxUsage
                    requestBuilder.filters(filters);
                }

                if (offeringIds != null && !offeringIds.isEmpty()) {
                    requestBuilder.savingsPlanOfferingIds(offeringIds);
                }

                if (serviceCodes != null && !serviceCodes.isEmpty()) {
                    // Convert String list to SavingsPlanRateServiceCode list
                    List<SavingsPlanRateServiceCode> sdkServiceCodes = serviceCodes.stream()
                            .map(SavingsPlanRateServiceCode::fromValue)
                            .collect(Collectors.toList());
                    requestBuilder.serviceCodes(sdkServiceCodes);
                }

                if (instanceFamilies != null && !instanceFamilies.isEmpty()) {
                    requestBuilder.usageTypes(instanceFamilies);
                }

                if (maxResults != null) {
                    requestBuilder.maxResults(maxResults);
                }

                // 调用SDK API并直接返回结果
                // todo 结果会有BoxUsage和UnusedBox两条，分别代表了使用了和未使用的费用
                return client.describeSavingsPlansOfferingRates(requestBuilder.build());
            } finally {
                // 关闭客户端 - 由缓存管理，此处不再关闭
                // client.close();
            }
        } catch (Exception e) {
            log.error("查询AWS Savings Plans产品费率失败", e);
            throw new RuntimeException("查询AWS Savings Plans产品费率失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建Savings Plan
     *
     * @param accountName AWS账号名称
     * @param region 区域
     * @param offeringId 产品ID
     * @param commitment 承诺金额
     * @param tags 标签Map
     * @return 创建的Savings Plan响应
     */
    public CreateSavingsPlanResponse createSavingsPlan(
            String accountName,
            String region,
            String offeringId,
            BigDecimal commitment,
            Map<String, String> tags) {

        log.info("创建AWS Savings Plan，账号: {}, 区域: {}, 产品ID: {}", accountName, region, offeringId);

        try {
            // 创建SDK客户端
            SavingsplansClient client = createSdkClient(accountName, region);

            try {
                // 构建SDK请求
                CreateSavingsPlanRequest.Builder requestBuilder = CreateSavingsPlanRequest.builder();

                // Use correct method name
                requestBuilder.savingsPlanOfferingId(offeringId);

                requestBuilder.commitment(commitment.toString());
                requestBuilder.clientToken(UUID.randomUUID().toString());
                requestBuilder.tags(tags != null ? tags : Collections.emptyMap());

                // 调用SDK API并直接返回结果
                return client.createSavingsPlan(requestBuilder.build());
            } finally {
                // 关闭客户端 - 由缓存管理，此处不再关闭
                // client.close();
            }
        } catch (Exception e) {
            log.error("创建AWS Savings Plan失败", e);
            throw new RuntimeException("创建AWS Savings Plan失败: " + e.getMessage(), e);
        }
    }

    @PreDestroy
    public void cleanup() {
        log.info("Closing all cached SavingsplansClients...");
        clientCache.values().forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("Error closing SavingsplansClient: {}", e.getMessage());
            }
        });
        clientCache.clear();
        log.info("All cached SavingsplansClients closed.");
    }
}
