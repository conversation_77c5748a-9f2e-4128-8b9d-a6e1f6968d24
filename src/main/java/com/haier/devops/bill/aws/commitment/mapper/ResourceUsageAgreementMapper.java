package com.haier.devops.bill.aws.commitment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 资源使用签约 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
public interface ResourceUsageAgreementMapper extends BaseMapper<ResourceUsageAgreement> {

    /**
     * 查询待购买节省计划的实例
     * @return
     */
    List<ResourceUsageAgreementVo> queryPendingSavingPlanResource(@Param("planType") String... planType);

    /**
     * 查询待购买预留实例的实例
     * @return
     */
    List<ResourceUsageAgreementVo> queryPendingReservedInstanceResource();

    /**
     * 根据id查询签约信息
     * @param id
     * @return
     */
    ResourceUsageAgreementVo queryById(Integer id);

    /**
     * 根据id列表查询签约信息
     * @param ids
     * @return
     */
    List<ResourceUsageAgreementVo> queryByIds(@Param("ids") List<Integer> ids);

    /**
     * 查询签约信息
     * @param filter
     * @param purchased
     * @return
     */
    List<ResourceUsageAgreementVo> query(@Param("filter") ResourceUsageAgreement filter,
                                         @Param("type") String type,
                                         @Param("purchased") Integer purchased,
                                         @Param("start") LocalDate start,
                                         @Param("end") LocalDate end);


    /**
     * 查询历史签约信息
     * @param vendor
     * @param type 资源类型
     * @param resourceId
     * @return
     */
    List<ResourceUsageAgreementVo> queryHistory(@Param("vendor") String vendor,
                                                @Param("type") String type,
                                                @Param("resourceId") String resourceId);
}
