package com.haier.devops.bill.aws.commitment.purchase;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.CommitmentException;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import com.haier.devops.bill.common.entity.CloudAccount;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.savingsplans.model.CreateSavingsPlanResponse;
import software.amazon.awssdk.services.savingsplans.model.DescribeSavingsPlansOfferingsResponse;
import software.amazon.awssdk.services.savingsplans.model.SavingsPlanOffering;
import software.amazon.awssdk.services.savingsplans.model.SavingsPlanType;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算型节省计划购买
 */
@Component
@Slf4j
public class ComputeSpPurchase extends PurchaseTemplate<ComputeSpPurchase.ComputeSpGroupKey> {

    @Getter
    private final ResourceUsageAgreementService agreementService;
    @Getter
    private final AwsSavingsPlanService savingsPlanService;
    @Getter
    private final CloudAccountService accountService;

    public ComputeSpPurchase(ResourceUsageAgreementService agreementService, AwsSavingsPlanService savingsPlanService, CloudAccountService accountService) {
        this.agreementService = agreementService;
        this.savingsPlanService = savingsPlanService;
        this.accountService = accountService;
    }

    @Override
    Function<ResourceUsageAgreementVo, GroupKey> grouping() {
        return a -> new ComputeSpGroupKey(a.getDuration());
    }

    @Override
    List<ResourceUsageAgreementVo> queryPendingAgreements() {
        return agreementService.queryPendingSavingPlanResource(SP_TYPE_COMPUTING);
    }

    @Override
    String doPurchase(ComputeSpGroupKey groupKey, List<ResourceUsageAgreementVo> agreements) {
        BigDecimal commitment = Optional.ofNullable(agreements)
                .orElse(List.of())
                .stream()
                .map(ResourceUsageAgreementVo::getCommitRate)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (commitment.compareTo(BigDecimal.ZERO) <= 0) {
            throw new CommitmentException("commitment is zero");
        }

        LambdaQueryWrapper<CloudAccount> accountQuery = new LambdaQueryWrapper<CloudAccount>()
                .eq(CloudAccount::getIsEnabled, 1)
                .eq(CloudAccount::getVendor, "aws")
                .last("LIMIT 1");
        CloudAccount account = accountService.getBaseMapper().selectOne(accountQuery);

        ResourceUsageAgreementVo defaultAgreement = agreements.get(0);
        String region = defaultAgreement.getRegion();

        DescribeSavingsPlansOfferingsResponse offeringsResponse = savingsPlanService.describeSavingsPlansOfferings(account.getAccountName(),
                region,
                PRODUCT_TYPE_EC2,
                null,
                Collections.singletonList(SP_TYPE_COMPUTING),
                Collections.singletonList(groupKey.getDuration()),
                Collections.singletonList(PAYMENT_OPTION_NO_UPFRONT),
                null);

        if (null == offeringsResponse
                || null == offeringsResponse.searchResults()
                || offeringsResponse.searchResults().isEmpty()) {
            log.error("no savings plan offering found: account={}, region={}, savingPlanType={}, duration={}, paymentOption={}",
                    account.getAccountName(),
                    region,
                    SP_TYPE_COMPUTING,
                    groupKey.getDuration(),
                    PAYMENT_OPTION_NO_UPFRONT);
            throw new CommitmentException("no savings plan offering found");
        }

        List<SavingsPlanOffering> offerings = offeringsResponse.searchResults();
        if (offerings.size() > 1) {
            log.error("more than one savings plan offering found: account={}, region={}, savingPlanType={}, duration={}, paymentOption={}",
                    account.getAccountName(),
                    region,
                    SP_TYPE_COMPUTING,
                    groupKey.getDuration(),
                    PAYMENT_OPTION_NO_UPFRONT);
            // todo 通知我
            throw new CommitmentException("more than one savings plan offering found");
        }

        String offeringId = offeringsResponse.searchResults().get(0).offeringId();
        String agreementIds = agreements.stream()
                .map(a -> String.valueOf(a.getId()))
                .collect(Collectors.joining(","));
        Map<String, String> tags = Collections.singletonMap("agreementIds", agreementIds);

        // 创建节省计划
        CreateSavingsPlanResponse savingsPlan = savingsPlanService.createSavingsPlan(account.getAccountName(), region, offeringId, commitment, tags);
        if (null == savingsPlan) {
            log.error("create savings plan failed: account={}, region={}, offeringId={}",
                    account.getAccountName(),
                    region,
                    offeringId);
            throw new CommitmentException("create savings plan failed");
        }

        if (StringUtils.isBlank(savingsPlan.savingsPlanId())) {
            log.error("create savings plan failed: account={}, region={}, offeringId={}, savingsPlanId={}, requestId={}",
                    account.getAccountName(), region, offeringId, savingsPlan.savingsPlanId(), savingsPlan.responseMetadata().requestId());
            throw new CommitmentException("create savings plan failed");
        }

        return savingsPlan.savingsPlanId();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static
    class ComputeSpGroupKey extends PurchaseTemplate.GroupKey {
        private Long duration;
    }
}
