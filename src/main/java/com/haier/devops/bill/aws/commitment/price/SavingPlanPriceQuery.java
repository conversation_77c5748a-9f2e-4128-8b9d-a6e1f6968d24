package com.haier.devops.bill.aws.commitment.price;

import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.savingsplans.model.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 节省计划价格查询
 */
@Component
@Slf4j
public class SavingPlanPriceQuery implements PriceQuery {
    private final ResourceUsageAgreementService agreementService;
    private final AwsSavingsPlanService awsSavingsPlanService;

    public SavingPlanPriceQuery(ResourceUsageAgreementService agreementService,
                                AwsSavingsPlanService awsSavingsPlanService) {
        this.agreementService = agreementService;
        this.awsSavingsPlanService = awsSavingsPlanService;
    }


    @Override
    public List<ResourceUsageAgreementVo> queryPrice() {
        List<ResourceUsageAgreementVo> resources =
                agreementService.queryPendingSavingPlanResource();
        if (CollectionUtils.isEmpty(resources)) {
            return new ArrayList<>();
        }

        Map<OfferingGroupKey, List<ResourceUsageAgreementVo>> groupedResourceMap =
                resources.stream().collect(groupingBy(r ->
                        new OfferingGroupKey(r.getAccount(),
                                r.getRegion(),
                                r.getDuration(),
                                r.getPrepaymentOption())));
        fillOfferingIdForResources(groupedResourceMap);

        List<ResourceUsageAgreementVo> withOfferingId = groupedResourceMap.values().stream()
                .flatMap(List::stream).collect(Collectors.toList());

        Map<OfferingRateGroupKey, List<ResourceUsageAgreementVo>> rateMap =
                withOfferingId.stream().collect(groupingBy(r -> new OfferingRateGroupKey(
                        r.getAccount(),
                        r.getRegion(),
                        r.getOfferingId(),
                        r.getInstanceType(),
                        r.getTenancy(),
                        r.getOs())));
        fillCommitRateForResources(rateMap);

        return rateMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    private void fillOfferingIdForResources(Map<OfferingGroupKey, List<ResourceUsageAgreementVo>> groupedResourceMap) {
        for (OfferingGroupKey key : groupedResourceMap.keySet()) {
            DescribeSavingsPlansOfferingsResponse response = awsSavingsPlanService.describeSavingsPlansOfferings(
                    key.getAccount(),
                    key.getRegion(),
                    "EC2",
                    null,
                    Collections.singletonList(SavingsPlanType.COMPUTE.toString()),
                    Collections.singletonList(key.getDuration()),
                    Collections.singletonList(key.getPaymentOption()),
                    null);
            if (!response.sdkHttpResponse().isSuccessful()) {
                log.error("describeSavingsPlansOfferings error: {}," +
                                "account={}, region={}, savingPlanType={}, duration={}, paymentOption={}",
                        response.sdkHttpResponse().statusText().orElse(""),
                        key.getAccount(),
                        key.getRegion(),
                        SavingsPlanType.COMPUTE.toString(),
                        key.getDuration(),
                        key.getPaymentOption());
                continue;
            }

            List<SavingsPlanOffering> offerings = response.searchResults();
            if (offerings == null || offerings.isEmpty()) {
                log.error("no savings plan offering found: " +
                                "account={}, region={}, savingPlanType={}, duration={}, paymentOption={}",
                        key.getAccount(),
                        key.getRegion(),
                        SavingsPlanType.COMPUTE.toString(),
                        key.getDuration(),
                        key.getPaymentOption());
                continue;
            }

            if (offerings.size() > 1) {
                log.error("more than one savings plan offering found: " +
                                "account={}, region={}, savingPlanType={}, duration={}, paymentOption={}",
                        key.getAccount(),
                        key.getRegion(),
                        SavingsPlanType.COMPUTE.toString(),
                        key.getDuration(),
                        key.getPaymentOption());
                continue;
            }

            String offeringId = offerings.get(0).offeringId();
            groupedResourceMap.get(key).forEach(vo -> vo.setOfferingId(offeringId));
        }
    }

    private void fillCommitRateForResources(Map<OfferingRateGroupKey, List<ResourceUsageAgreementVo>> rateMap) {
        for (OfferingRateGroupKey key : rateMap.keySet()) {
            DescribeSavingsPlansOfferingRatesResponse rateResponse =
                    awsSavingsPlanService.describeSavingsPlansOfferingRates(
                            key.getAccount(),
                            key.getRegion(),
                            Collections.singletonList(key.getOfferingId()),
                            Collections.singletonList(key.getTenancy()),
                            Collections.singletonList(key.getOs()),
                            null,
                            Collections.singletonList(key.getInstanceType()),
                            null,
                            null);

            List<SavingsPlanOfferingRate> rates = rateResponse.searchResults();
            if (rates == null || rates.isEmpty()) {
                log.error("no savings plan offering rate found: " +
                                "account={}, region={}, offeringId={}, instanceType={}, tenancy={}, os={}",
                        key.getAccount(),
                        key.getRegion(),
                        key.getOfferingId(),
                        key.getInstanceType(),
                        key.getTenancy(),
                        key.getOs());
                continue;
            }

            if (rates.size() > 2) {
                log.error("more than one savings plan offering rate found: " +
                                "account={}, region={}, offeringId={}, instanceType={}, tenancy={}, os={}",
                        key.getAccount(),
                        key.getRegion(),
                        key.getOfferingId(),
                        key.getInstanceType(),
                        key.getTenancy(),
                        key.getOs());
                continue;
            }

            BigDecimal rate = new BigDecimal(Optional.ofNullable(rates.get(0).rate()).orElse("0"));
            rateMap.get(key).forEach(vo -> vo.setCommitRate(rate));
        }
    }

    @Data
    @AllArgsConstructor
    static
    class OfferingGroupKey {
        private String account;
        private String region;
        private Long duration;
        private String paymentOption;
    }

    @Data
    @AllArgsConstructor
    static
    class OfferingRateGroupKey {
        private String account;
        private String region;
        private String offeringId;
        private String instanceType;
        private String tenancy;
        private String os;

    }
}
