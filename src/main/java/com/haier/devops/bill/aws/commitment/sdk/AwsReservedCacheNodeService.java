package com.haier.devops.bill.aws.commitment.sdk;

import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.elasticache.ElastiCacheClient;
import software.amazon.awssdk.services.elasticache.model.DescribeReservedCacheNodesOfferingsRequest;
import software.amazon.awssdk.services.elasticache.model.DescribeReservedCacheNodesOfferingsResponse;
import software.amazon.awssdk.services.elasticache.model.PurchaseReservedCacheNodesOfferingRequest;
import software.amazon.awssdk.services.elasticache.model.PurchaseReservedCacheNodesOfferingResponse;
import software.amazon.awssdk.services.elasticache.model.Tag;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * AWS ElastiCache预留缓存节点服务
 * 提供对AWS ElastiCache Reserved Cache Nodes API的封装和业务逻辑处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class AwsReservedCacheNodeService {

    private final CloudAccountService cloudAccountService;
    private final ConcurrentMap<String, ElastiCacheClient> clientCache = new ConcurrentHashMap<>();

    public AwsReservedCacheNodeService(CloudAccountService cloudAccountService) {
        this.cloudAccountService = cloudAccountService;
    }

    /**
     * 创建AWS SDK ElastiCacheClient
     *
     * @param accountName AWS账号名称
     * @param regionStr 区域
     * @return ElastiCacheClient
     */
    private ElastiCacheClient createElastiCacheSdkClient(String accountName, String regionStr) {
        String cacheKey = accountName + ":" + regionStr;
        return clientCache.computeIfAbsent(cacheKey, k -> {
            log.info("ElastiCache RI: 本地缓存未命中，创建新的 ElastiCacheClient，账号: {}, 区域: {}", accountName, regionStr);
            Map<String, String> credentials = cloudAccountService.getOpenApiKey(VendorEnum.AWS.getVendor(), accountName, "ACCOUNT_CHECK");
            String accessKey = credentials.get("accessKey");
            String secretKey = credentials.get("accessKeySecret");

            if (accessKey == null || secretKey == null || accessKey.isEmpty() || secretKey.isEmpty()) {
                log.error("ElastiCache RI: 无法获取AWS账号凭证: {} 的 AK/SK", accountName);
                throw new IllegalArgumentException("ElastiCache RI: 无法获取AWS账号凭证: " + accountName);
            }

            AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKey, secretKey);
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCreds);
            Region sdkRegion = Region.of(regionStr);

            return ElastiCacheClient.builder()
                    .region(sdkRegion)
                    .credentialsProvider(credentialsProvider)
                    .build();
        });
    }

    /**
     * 查询ElastiCache预留缓存节点价格
     * @param accountName AWS账号名称
     * @param region 区域
     * @param reservedCacheNodesOfferingId 预留缓存节点产品ID
     * @param cacheNodeType 缓存节点类型
     * @param duration 持续时间（秒）
     * @param productDescription 产品描述
     * @param offeringType 产品类型
     * @param maxRecords 最大记录数
     * @param marker 分页标记
     * @return DescribeReservedCacheNodesOfferingsResponse
     */
    public DescribeReservedCacheNodesOfferingsResponse describeReservedCacheNodesOfferings(
            String accountName,
            String region,
            String reservedCacheNodesOfferingId,
            String cacheNodeType,
            String duration,
            String productDescription,
            String offeringType,
            Integer maxRecords,
            String marker) {

        log.info("ElastiCache RI: 查询 AWS ElastiCache Reserved Cache Nodes Offerings，账号: {}, 区域: {}, 缓存节点类型: {}, 时长: {}, 产品描述: {}, Offer类型: {}",
                 accountName, region, cacheNodeType, duration, productDescription, offeringType);

        ElastiCacheClient client = null;
        try {
            client = createElastiCacheSdkClient(accountName, region);

            DescribeReservedCacheNodesOfferingsRequest.Builder requestBuilder =
                DescribeReservedCacheNodesOfferingsRequest.builder();

            if (StringUtils.isNotBlank(reservedCacheNodesOfferingId)) {
                requestBuilder.reservedCacheNodesOfferingId(reservedCacheNodesOfferingId);
            }
            if (StringUtils.isNotBlank(cacheNodeType)) {
                requestBuilder.cacheNodeType(cacheNodeType);
            }
            if (StringUtils.isNotBlank(duration)) {
                requestBuilder.duration(duration);
            }
            if (StringUtils.isNotBlank(productDescription)) {
                requestBuilder.productDescription(productDescription);
            }
            if (StringUtils.isNotBlank(offeringType)) {
                requestBuilder.offeringType(offeringType);
            }
            if (maxRecords != null && maxRecords > 0) {
                requestBuilder.maxRecords(maxRecords);
            }
            if (StringUtils.isNotBlank(marker)) {
                requestBuilder.marker(marker);
            }

            DescribeReservedCacheNodesOfferingsRequest sdkRequest = requestBuilder.build();

            log.debug("ElastiCache RI: Executing DescribeReservedCacheNodesOfferingsRequest: {}", sdkRequest);
            DescribeReservedCacheNodesOfferingsResponse response = client.describeReservedCacheNodesOfferings(sdkRequest);
            log.debug("ElastiCache RI: DescribeReservedCacheNodesOfferingsResponse received.");
            return response;

        } catch (Exception e) {
            log.error("ElastiCache RI: 查询 AWS ElastiCache Reserved Cache Nodes Offerings 失败，账号: {}, 区域: {}. 缓存节点类型: {}, 时长: {}. 错误: {}",
                      accountName, region, cacheNodeType, duration, e.getMessage(), e);
            throw new RuntimeException("ElastiCache RI: 查询 AWS ElastiCache Reserved Cache Nodes Offerings 失败: " + e.getMessage(), e);
        } finally {
            if (client != null) {
                // Do not close client here if it's managed and cached by createElastiCacheSdkClient
            }
        }
    }

    /**
     * 购买ElastiCache预留缓存节点
     * @param accountName AWS账号名
     * @param region 区域
     * @param reservedCacheNodesOfferingId 要购买的RI Offer ID (必填)
     * @param reservedCacheNodeId 用户指定的RI ID (可选)
     * @param cacheNodeCount 购买数量 (可选, 默认1)
     * @param tags 标签 (可选)
     * @return PurchaseReservedCacheNodesOfferingResponse
     */
    public PurchaseReservedCacheNodesOfferingResponse purchaseReservedCacheNodesOffering(
            String accountName,
            String region,
            String reservedCacheNodesOfferingId,
            String reservedCacheNodeId,
            Integer cacheNodeCount,
            List<Tag> tags) {

        log.info("ElastiCache RI: 购买 AWS ElastiCache Reserved Cache Nodes Offering. 账号: {}, 区域: {}, Offering ID: {}, 用户RI ID: {}, 数量: {}",
                 accountName, region, reservedCacheNodesOfferingId, reservedCacheNodeId, cacheNodeCount);

        ElastiCacheClient client = null;
        try {
            client = createElastiCacheSdkClient(accountName, region);

            PurchaseReservedCacheNodesOfferingRequest.Builder requestBuilder =
                PurchaseReservedCacheNodesOfferingRequest.builder();

            if (StringUtils.isBlank(reservedCacheNodesOfferingId)) {
                log.error("ElastiCache RI Purchase: reservedCacheNodesOfferingId is required.");
                throw new IllegalArgumentException("reservedCacheNodesOfferingId is required for purchasing ElastiCache Reserved Cache Nodes.");
            }
            requestBuilder.reservedCacheNodesOfferingId(reservedCacheNodesOfferingId);

            if (StringUtils.isNotBlank(reservedCacheNodeId)) {
                requestBuilder.reservedCacheNodeId(reservedCacheNodeId);
            }

            if (cacheNodeCount != null && cacheNodeCount > 0) {
                requestBuilder.cacheNodeCount(cacheNodeCount);
            } else {
                // SDK defaults to 1 if not set. Explicitly setting to 1 for clarity or if 0/negative is invalid.
                requestBuilder.cacheNodeCount(1);
            }

            if (tags != null && !tags.isEmpty()) {
                requestBuilder.tags(tags);
            }

            PurchaseReservedCacheNodesOfferingRequest sdkRequest = requestBuilder.build();

            log.debug("ElastiCache RI Purchase: Executing PurchaseReservedCacheNodesOfferingRequest: {}", sdkRequest);
            PurchaseReservedCacheNodesOfferingResponse response = client.purchaseReservedCacheNodesOffering(sdkRequest);
            log.info("ElastiCache RI Purchase: PurchaseReservedCacheNodesOfferingResponse received. ReservedCacheNode ID: {}",
                     response.reservedCacheNode() != null ? response.reservedCacheNode().reservedCacheNodeId() : "N/A");
            return response;

        } catch (IllegalArgumentException iae) {
            log.error("ElastiCache RI Purchase: 参数错误. 账号: {}, 区域: {}, Offering ID: {}. 错误: {}",
                      accountName, region, reservedCacheNodesOfferingId, iae.getMessage(), iae);
            throw iae; // Re-throw specific argument exceptions
        } catch (Exception e) {
            log.error("ElastiCache RI Purchase: 购买 AWS ElastiCache Reserved Cache Nodes Offering 失败. 账号: {}, 区域: {}, Offering ID: {}. 错误: {}",
                      accountName, region, reservedCacheNodesOfferingId, e.getMessage(), e);
            throw new RuntimeException("ElastiCache RI Purchase: 购买 AWS ElastiCache Reserved Cache Nodes Offering 失败: " + e.getMessage(), e);
        } finally {
            if (client != null) {
                // Do not close client here if it's managed and cached by createElastiCacheSdkClient
            }
        }
    }

    /**
     * 清理资源，关闭所有缓存的客户端
     */
    @PreDestroy
    public void cleanup() {
        log.info("Closing all cached ElastiCacheClients...");
        clientCache.values().forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("Error closing ElastiCacheClient: {}", e.getMessage());
            }
        });
        clientCache.clear();
        log.info("All cached ElastiCacheClients closed.");
    }
}
