package com.haier.devops.bill.aws.commitment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 资源使用签约
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14
 */
@Data
@TableName("bc_resource_usage_agreement")
public class ResourceUsageAgreement implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 资源实例id（云上instance_id）
     */
    private String resourceId;

    /**
     * 承诺使用时长（秒）
     */
    private Long duration;

    /**
     * 启用日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enableTime;

    /**
     * 承诺类型 Compute, EC2Instance, Ri, Rc
     */
    private String commitType;

    /**
     * shared, dedicated
     */
    private String tenancy;

    /**
     * 承诺后单价
     */
    private BigDecimal commitRate;

    /**
     * 原价
     */
    private BigDecimal ondemandRate;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 产品型号描述
     */
    private String productDescription;

    /**
     * 要购买的节省计划对应的id
     */
    private String offeringId;

    /**
     * 已购买的节省计划对应的id
     */
    private String purchasedOfferingId;


    /**
     * 预付类型：all_upfront, no_upfront, partial_upfront
     */
    private String prepaymentOption;


    /**
     * 预付费比例
     */
    private BigDecimal prepaymentRatio;

    /**
     * 购买日期
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
