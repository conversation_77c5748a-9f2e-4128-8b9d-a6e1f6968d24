package com.haier.devops.bill.aws.commitment.purchase;

import com.haier.devops.bill.aws.commitment.sdk.AwsSavingsPlanService;
import com.haier.devops.bill.aws.commitment.CommitmentException;
import com.haier.devops.bill.aws.commitment.service.ResourceUsageAgreementService;
import com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo;
import com.haier.devops.bill.common.service.CloudAccountService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.savingsplans.model.CreateSavingsPlanResponse;
import software.amazon.awssdk.services.savingsplans.model.DescribeSavingsPlansOfferingsResponse;
import software.amazon.awssdk.services.savingsplans.model.SavingsPlanType;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EC2SpPurchase extends PurchaseTemplate<EC2SpPurchase.EC2GroupKey> {
    private static final String SEPARATOR = "\\.";

    @Getter
    private final ResourceUsageAgreementService agreementService;
    @Getter
    private final AwsSavingsPlanService savingsPlanService;
    @Getter
    private final CloudAccountService accountService;

    public EC2SpPurchase(ResourceUsageAgreementService agreementService,
                         AwsSavingsPlanService savingsPlanService,
                         CloudAccountService accountService) {
        this.agreementService = agreementService;
        this.savingsPlanService = savingsPlanService;
        this.accountService = accountService;
    }

    @Override
    Function<ResourceUsageAgreementVo, EC2GroupKey> grouping() {
        return a -> {
            String instanceType = a.getInstanceType();
            String[] split = instanceType.split(SEPARATOR);

            String family = null;
            if (split.length > 0) {
                family = split[0];
            }

            return new EC2GroupKey(a.getAccount(),
                    a.getRegion(),
                    family,
                    a.getDuration());
        };
    }

    @Override
    List<ResourceUsageAgreementVo> queryPendingAgreements() {
        return agreementService.queryPendingSavingPlanResource(SP_TYPE_EC2);
    }

    @Override
    String doPurchase(EC2GroupKey groupKey, List<ResourceUsageAgreementVo> agreements) {
        BigDecimal commitment = Optional.ofNullable(agreements)
                .orElse(List.of())
                .stream()
                .map(ResourceUsageAgreementVo::getCommitRate)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (commitment.compareTo(BigDecimal.ZERO) <= 0) {
            throw new CommitmentException("commitment is zero");
        }

        String instanceFamily = null;
        String[] split = groupKey.getInstanceType().split(SEPARATOR);
        if (split.length > 0) {
            instanceFamily = split[0];
        }
        DescribeSavingsPlansOfferingsResponse offeringsResponse = savingsPlanService.describeSavingsPlansOfferings(
                groupKey.getAccount(),
                groupKey.getRegion(),
                PRODUCT_TYPE_EC2,
                null,
                Collections.singletonList(SP_TYPE_EC2),
                Collections.singletonList(groupKey.getDuration()),
                Collections.singletonList(PAYMENT_OPTION_NO_UPFRONT),
                null,
                instanceFamily);

        if (null == offeringsResponse
                || null == offeringsResponse.searchResults()
                || offeringsResponse.searchResults().isEmpty()) {
            log.error("no savings plan offering found: " +
                            "account={}, region={}, savingPlanType={}, duration={}, paymentOption={}, instanceFamily={}",
                    groupKey.getAccount(),
                    groupKey.getRegion(),
                    SP_TYPE_EC2,
                    groupKey.getDuration(),
                    PAYMENT_OPTION_NO_UPFRONT,
                    instanceFamily);
            throw new CommitmentException("no savings plan offering found");
        }

        if (offeringsResponse.searchResults().size() > 1) {
            log.error("multiple savings plan offerings found: " +
                            "account={}, region={}, savingPlanType={}, duration={}, paymentOption={}, instanceFamily={}",
                    groupKey.getAccount(),
                    groupKey.getRegion(),
                    SP_TYPE_EC2,
                    groupKey.getDuration(),
                    PAYMENT_OPTION_NO_UPFRONT,
                    instanceFamily);
            // todo 通知我
            throw new CommitmentException("multiple savings plan offerings found");
        }

        String offeringId = offeringsResponse.searchResults().get(0).offeringId();
        String agreementIds = agreements.stream()
                .map(a -> String.valueOf(a.getId()))
                .collect(Collectors.joining(","));
        Map<String, String> tags = Collections.singletonMap("agreementIds", agreementIds);
        // 创建节省计划
        CreateSavingsPlanResponse savingsPlan =
                savingsPlanService.createSavingsPlan(groupKey.getAccount(),
                        groupKey.getRegion(),
                        offeringId,
                        commitment,
                        tags);
        if (null == savingsPlan) {
            log.error("create savings plan failed: account={}, region={}, offeringId={}",
                    groupKey.getAccount(),
                    groupKey.getRegion(),
                    offeringId);
            throw new CommitmentException("create savings plan failed");
        }

        if (StringUtils.isBlank(savingsPlan.savingsPlanId())) {
            log.error("create savings plan failed: account={}, region={}, offeringId={}, savingsPlanId={}, requestId={}",
                    groupKey.getAccount(),
                    groupKey.getRegion(),
                    offeringId,
                    savingsPlan.savingsPlanId(),
                    savingsPlan.responseMetadata().requestId());
            throw new CommitmentException("create savings plan failed");
        }

        return savingsPlan.savingsPlanId();
    }


    @Data
    @AllArgsConstructor
    public static class EC2GroupKey extends PurchaseTemplate.GroupKey {
        private String account;
        private String region;
        private String instanceType;
        private Long duration;
    }
}
