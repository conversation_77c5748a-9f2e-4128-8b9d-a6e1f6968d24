package com.haier.devops.bill.aws.commitment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haier.devops.bill.aws.commitment.entity.ResourceUsageAgreement;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class ResourceUsageAgreementVo extends ResourceUsageAgreement {
    private String account;
    /**
     * 区域
     */
    private String region;

    /**
     *  操作系统
     */
    private String os;

    /**
     * 规格型号
     */
    private String instanceType;


    /**
     * 资源创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime creationTime;

    /**
     * 引擎类型
     */
    private String engineType;

    /**
     * 引擎版本
     */
    private String engineVersion;

    /**
     * cpu
     */
    private Integer cpu;

    /**
     * 内存
     */
    private Integer memory;
}
