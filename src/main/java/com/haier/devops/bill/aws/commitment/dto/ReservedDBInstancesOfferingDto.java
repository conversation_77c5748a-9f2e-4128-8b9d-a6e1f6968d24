package com.haier.devops.bill.aws.commitment.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import software.amazon.awssdk.services.rds.model.ReservedDBInstancesOffering;
import software.amazon.awssdk.services.rds.model.RecurringCharge;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RDS预留实例产品DTO
 * 用于序列化AWS SDK的ReservedDBInstancesOffering对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReservedDBInstancesOfferingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预留实例产品ID
     */
    private String reservedDBInstancesOfferingId;

    /**
     * 数据库实例类型
     */
    private String dbInstanceClass;

    /**
     * 持续时间（秒）
     */
    private Integer duration;

    /**
     * 固定价格
     */
    private Double fixedPrice;

    /**
     * 使用价格
     */
    private Double usagePrice;

    /**
     * 货币代码
     */
    private String currencyCode;

    /**
     * 数据库引擎
     */
    private String productDescription;

    /**
     * 产品类型
     */
    private String offeringType;

    /**
     * 是否支持多可用区
     */
    private Boolean multiAZ;

    /**
     * 循环收费信息
     */
    private List<RecurringChargeDto> recurringCharges;

    /**
     * 从AWS SDK对象转换为DTO
     */
    public static ReservedDBInstancesOfferingDto fromAwsModel(ReservedDBInstancesOffering offering) {
        if (offering == null) {
            return null;
        }

        return ReservedDBInstancesOfferingDto.builder()
                .reservedDBInstancesOfferingId(offering.reservedDBInstancesOfferingId())
                .dbInstanceClass(offering.dbInstanceClass())
                .duration(offering.duration())
                .fixedPrice(offering.fixedPrice())
                .usagePrice(offering.usagePrice())
                .currencyCode(offering.currencyCode())
                .productDescription(offering.productDescription())
                .offeringType(offering.offeringType())
                .multiAZ(offering.multiAZ())
                .recurringCharges(offering.recurringCharges() != null ? 
                    offering.recurringCharges().stream()
                        .map(RecurringChargeDto::fromAwsModel)
                        .collect(Collectors.toList()) : null)
                .build();
    }

    /**
     * 批量转换
     */
    public static List<ReservedDBInstancesOfferingDto> fromAwsModelList(List<ReservedDBInstancesOffering> offerings) {
        if (offerings == null) {
            return null;
        }
        return offerings.stream()
                .map(ReservedDBInstancesOfferingDto::fromAwsModel)
                .collect(Collectors.toList());
    }

    /**
     * 循环收费DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecurringChargeDto implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 循环收费金额
         */
        private Double recurringChargeAmount;

        /**
         * 循环收费频率
         */
        private String recurringChargeFrequency;

        /**
         * 从AWS SDK对象转换为DTO
         */
        public static RecurringChargeDto fromAwsModel(RecurringCharge charge) {
            if (charge == null) {
                return null;
            }

            return RecurringChargeDto.builder()
                    .recurringChargeAmount(charge.recurringChargeAmount())
                    .recurringChargeFrequency(charge.recurringChargeFrequency())
                    .build();
        }
    }
}
