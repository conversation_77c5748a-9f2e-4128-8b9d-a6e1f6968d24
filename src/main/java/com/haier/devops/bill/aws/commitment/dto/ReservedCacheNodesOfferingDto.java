package com.haier.devops.bill.aws.commitment.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import software.amazon.awssdk.services.elasticache.model.ReservedCacheNodesOffering;
import software.amazon.awssdk.services.elasticache.model.RecurringCharge;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ElastiCache预留缓存节点产品DTO
 * 用于序列化AWS SDK的ReservedCacheNodesOffering对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReservedCacheNodesOfferingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预留缓存节点产品ID
     */
    private String reservedCacheNodesOfferingId;

    /**
     * 缓存节点类型
     */
    private String cacheNodeType;

    /**
     * 持续时间（秒）
     */
    private Integer duration;

    /**
     * 固定价格
     */
    private Double fixedPrice;

    /**
     * 使用价格
     */
    private Double usagePrice;

    /**
     * 产品描述
     */
    private String productDescription;

    /**
     * 产品类型
     */
    private String offeringType;

    /**
     * 循环收费信息
     */
    private List<RecurringChargeDto> recurringCharges;

    /**
     * 从AWS SDK对象转换为DTO
     */
    public static ReservedCacheNodesOfferingDto fromAwsModel(ReservedCacheNodesOffering offering) {
        if (offering == null) {
            return null;
        }

        return ReservedCacheNodesOfferingDto.builder()
                .reservedCacheNodesOfferingId(offering.reservedCacheNodesOfferingId())
                .cacheNodeType(offering.cacheNodeType())
                .duration(offering.duration())
                .fixedPrice(offering.fixedPrice())
                .usagePrice(offering.usagePrice())
                .productDescription(offering.productDescription())
                .offeringType(offering.offeringType())
                .recurringCharges(offering.recurringCharges() != null ? 
                    offering.recurringCharges().stream()
                        .map(RecurringChargeDto::fromAwsModel)
                        .collect(Collectors.toList()) : null)
                .build();
    }

    /**
     * 批量转换
     */
    public static List<ReservedCacheNodesOfferingDto> fromAwsModelList(List<ReservedCacheNodesOffering> offerings) {
        if (offerings == null) {
            return null;
        }
        return offerings.stream()
                .map(ReservedCacheNodesOfferingDto::fromAwsModel)
                .collect(Collectors.toList());
    }

    /**
     * 循环收费DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecurringChargeDto implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 循环收费金额
         */
        private Double recurringChargeAmount;

        /**
         * 循环收费频率
         */
        private String recurringChargeFrequency;

        /**
         * 从AWS SDK对象转换为DTO
         */
        public static RecurringChargeDto fromAwsModel(RecurringCharge charge) {
            if (charge == null) {
                return null;
            }

            return RecurringChargeDto.builder()
                    .recurringChargeAmount(charge.recurringChargeAmount())
                    .recurringChargeFrequency(charge.recurringChargeFrequency())
                    .build();
        }
    }
}
