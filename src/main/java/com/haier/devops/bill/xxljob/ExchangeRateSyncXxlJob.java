package com.haier.devops.bill.xxljob;

import com.haier.devops.bill.common.service.ExchangeRateService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName: ExchangeRateSyncXxlJob
 * @Description: 汇率同步定时任务
 * @author: System
 * @date: 2025/06/30
 */
@Slf4j
@Component
public class ExchangeRateSyncXxlJob {
    private ExchangeRateService exchangeRateService;

    public ExchangeRateSyncXxlJob(ExchangeRateService exchangeRateService) {
        this.exchangeRateService = exchangeRateService;
    }

    /**
     * 汇率同步任务
     * 建议执行频率：每日凌晨1点执行一次
     * Cron表达式：0 0 1 * * ?
     */
    @XxlJob("exchangeRateSyncJob")
    public void exchangeRateSyncJob() {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("=== 汇率同步任务开始执行 ===");
        
        try {
            // 执行汇率同步
            exchangeRateService.performFullSync();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            String successMessage = String.format("汇率同步任务执行成功，耗时: %d ms", duration);
            XxlJobHelper.log(successMessage);
            log.info(successMessage);
            
            // 标记任务执行成功
            XxlJobHelper.handleSuccess(successMessage);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            String errorMessage = String.format("汇率同步任务执行失败，耗时: %d ms，错误信息: %s", duration, e.getMessage());
            XxlJobHelper.log(errorMessage);
            log.error(errorMessage, e);
            
            // 标记任务执行失败
            XxlJobHelper.handleFail(errorMessage);
        }
    }

    /**
     * 手动触发汇率同步任务
     * 用于测试或紧急情况下的手动同步
     */
    @XxlJob("manualExchangeRateSync")
    public void manualExchangeRateSync() {
        long startTime = System.currentTimeMillis();
        XxlJobHelper.log("=== 手动汇率同步任务开始执行 ===");
        
        try {
            // 获取任务参数（如果有的话）
            String jobParam = XxlJobHelper.getJobParam();
            XxlJobHelper.log("任务参数: " + (jobParam != null ? jobParam : "无"));
            
            // 执行汇率同步
            exchangeRateService.performFullSync();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            String successMessage = String.format("手动汇率同步任务执行成功，耗时: %d ms", duration);
            XxlJobHelper.log(successMessage);
            log.info(successMessage);
            
            // 标记任务执行成功
            XxlJobHelper.handleSuccess(successMessage);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            String errorMessage = String.format("手动汇率同步任务执行失败，耗时: %d ms，错误信息: %s", duration, e.getMessage());
            XxlJobHelper.log(errorMessage);
            log.error(errorMessage, e);
            
            // 标记任务执行失败
            XxlJobHelper.handleFail(errorMessage);
        }
    }
}
