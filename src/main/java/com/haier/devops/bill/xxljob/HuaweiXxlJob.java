package com.haier.devops.bill.xxljob;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.service.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

/**
* @ClassName: ReconcilationXxlJob
* @Description: 调账相关任务
* @author: 张爱苹
* @date: 2024/3/19 10:55
*/
@Component
public class HuaweiXxlJob {

    private static Logger logger = LoggerFactory.getLogger(HuaweiXxlJob.class);

    @Autowired
    private CloudAccountService cloudAccountService;

    @Autowired
    private EnterpriseProjectsService enterpriseProjectsService;

    @Autowired
    private EnterpriseProvidersService enterpriseProvidersService;

    @Autowired
    private IamProjectsService iamProjectsService;

    @Autowired
    private EnterpriseResourcesService enterpriseResourcesService;

    @Autowired
    private ReconciliationTaskService reconciliationTaskService;

    @Autowired
    private CmdbProductOverviewService cmdbProductOverviewService;

    @Autowired
    private HdsSubProductsService hdsSubProductsService;

    @Resource
    Executor localBootAsyncExecutor;

    /**
    * @Description: 同步企业项目
    * @author: 张爱苹
    * @date: 2025/2/13 14:35

    * @Return: void
    */
    @XxlJob("insEnterpriseProjectsJobHandler")
    public void insEnterpriseProjectsJobHandler() throws Exception{
        logger.info("同步企业项目【insEnterpriseProjectsJobHandler】开始------------");
        try{
           //获取账号列表
            List<CloudAccount> cloudAccountList = cloudAccountService.getHuaweiAccountList("MAIN");
            if(!CollectionUtils.isEmpty(cloudAccountList)){
                cloudAccountList.stream().forEach(cloudAccount ->{
                    try{
                        enterpriseProjectsService.insertEnterpriseProjects(cloudAccount.getAccountName());
                    }catch (Exception e){
                        e.printStackTrace();
                        logger.error("同步{}企业项目【insEnterpriseProjectsJobHandler】异常------------{}",cloudAccount,e.getMessage(),e);
                    }
                });

            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error("同步企业项目【insEnterpriseProjectsJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("同步企业项目【insEnterpriseProjectsJobHandler】结束------------");
    }

    /**
     * @Description: 同步企业项目支持的服务列表
     * @author: 张爱苹
     * @date: 2025/2/13 14:35

     * @Return: void
     */
    @XxlJob("insEnterpriseProvidersJobHandler")
    public void insEnterpriseProvidersJobHandler() throws Exception{
        logger.info("同步企业项目支持的服务列表【insEnterpriseProvidersJobHandler】开始------------");
        try{
            enterpriseProvidersService.insertEnterpriseProviders("hr690n");
        }catch (Exception e){
            e.printStackTrace();
            logger.error("同步企业项目支持的服务列表【insEnterpriseProvidersJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("同步企业项目支持的服务列表【insEnterpriseProjectsJobHandler】结束------------");
    }

    /**
     * @Description: 同步项目列表
     * @author: 张爱苹
     * @date: 2025/2/13 14:35

     * @Return: void
     */
    @XxlJob("insertIamProjectsJobHandler")
    public void insertIamProjectsJobHandler() throws Exception{
        logger.info("同步项目列表【insertIamProjectsJobHandler】开始------------");
        try{
            //获取账号列表
            List<CloudAccount> cloudAccountList = cloudAccountService.getHuaweiAccountList("MAIN");
            if(!CollectionUtils.isEmpty(cloudAccountList)){
                cloudAccountList.stream().forEach(cloudAccount ->{
                    try{
                        iamProjectsService.insertIamProjects(cloudAccount.getAccountName());
                    }catch (Exception e){
                        e.printStackTrace();
                        logger.error("同步{}项目【insertIamProjectsJobHandler】异常------------{}",cloudAccount,e.getMessage(),e);
                    }
                });

            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error("同步项目列表【insertIamProjectsJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("同步项目列表【insertIamProjectsJobHandler】结束------------");
    }

    /**
     * @Description: 同步资源列表
     * @author: 张爱苹
     * @date: 2025/2/13 14:35

     * @Return: void
     */
    @XxlJob("insertEnterpriseResourcesJobHandler")
    public void insertEnterpriseResourcesJobHandler() throws Exception{
        logger.info("同步资源列表【insertEnterpriseResourcesJobHandler】开始------------");
        try{
            //获取账号列表
            List<CloudAccount> cloudAccountList = cloudAccountService.getHuaweiAccountList("MAIN");
            if(!CollectionUtils.isEmpty(cloudAccountList)){
                CountDownLatch latch = new CountDownLatch(cloudAccountList.size());
                cloudAccountList.stream().forEach(cloudAccount ->{
                    localBootAsyncExecutor.execute(() ->{
                        try{
                           // enterpriseResourcesService.insertEnterpriseResourcesJobHandler(cloudAccount.getAccountName());
                            enterpriseResourcesService.insertEnterpriseResources(cloudAccount.getAccountName());
                        }catch (Exception e){
                            e.printStackTrace();
                            logger.error("同步{}资源列表【insertEnterpriseResourcesJobHandler】异常------------{}",cloudAccount,e.getMessage(),e);
                        }finally {
                        latch.countDown();
                        }
                    });


                });
                latch.await();
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("同步资源列表【insertEnterpriseResourcesJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("同步资源列表【insertEnterpriseResourcesJobHandler】结束------------");
    }

    /**
     * @Description: 更新华为云企业项目
     * @author: 张爱苹
     * @date: 2024/3/19 10:57

     * @Return: void
     */
    @XxlJob("updateHuaweiInfoJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void updateHuaweiInfoJobHandler() throws Exception{
        logger.info("更新华为云企业项目【updateHuaweiInfoJobHandler】开始------------");
        try{
            String type
                    = XxlJobHelper.getJobParam();
            //查询华为云调账任务，调账类型为enterpriseProject且状态是未执行的数据
            List<ReconciliationTask> reconciliationTaskList = reconciliationTaskService.queryList(type);
            if(CollectionUtils.isNotEmpty(reconciliationTaskList)){
                logger.info("更新华为云企业项目【updateHuaweiInfoJobHandler】，条数------------",reconciliationTaskList.size());
                reconciliationTaskList.stream().forEach(reconciliationTask ->{
                    try{
                        String scode = reconciliationTask.getScode();
                        HdsSubProducts hdsSubProducts = hdsSubProductsService.queryOne(scode);
                        if(hdsSubProducts == null){
                            logger.info("{}对应的子产品不存在",scode);
                            throw new RuntimeException("调账失败");
                        }
                        String aggregatedId = reconciliationTask.getAggregatedId();
                        //根据aggregatedId查询resource资源信息
                        CmdbProductOverview cmdbProductOverview = cmdbProductOverviewService.getOneByAggregatedId(aggregatedId);
                        String resourceId = cmdbProductOverview.getInstanceId();
                        String accountName = cmdbProductOverview.getAccountName();
                        //根据resourceId查询华为云资源信息
                        EnterpriseResources enterpriseResources = enterpriseResourcesService.getOneByResourceId(resourceId);
                        if(enterpriseResources != null){
                            //查scode对应的企业项目
                            EnterpriseProjects enterpriseProjects = enterpriseProjectsService.getOneByScode(scode,accountName);
                            if(enterpriseProjects != null){
                                if(enterpriseProjects.getStatus().intValue() == 2){
                                    //启用企业项目
                                    enterpriseProjectsService.enableEnterpriseProject(enterpriseProjects.getId(),accountName);
                                    //更新企业项目状态
                                    enterpriseProjects.setStatus(1);
                                    enterpriseProjectsService.updateById(enterpriseProjects);
                                }
                            }else{
                                //创建企业项目
                                String name = new StringBuffer(hdsSubProducts.getAppName()).append("-").append(scode).toString();
                                String enterpriseProjectId = enterpriseProjectsService.createEnterpriseProject(name,accountName);
                                //落库
                                enterpriseProjects  = EnterpriseProjects.builder().id(enterpriseProjectId).name(name)
                                        .delFlag("0").accountName(accountName).status(1).build();
                                enterpriseProjectsService.save(enterpriseProjects);

                            }
                            //迁移资源
                            String errorMsg = enterpriseResourcesService.migrateResource(enterpriseResources,enterpriseProjects.getId());
                            if(StringUtils.isNotEmpty(errorMsg)){
                                reconciliationTask.setStatus(2);
                                reconciliationTask.setErrorMsg(errorMsg);
                            }else{
                                reconciliationTask.setStatus(1);
                            }
                            reconciliationTask.setExecuteTime(new Date());
                        }else{
                            logger.info("更新华为云企业项目【updateHuaweiInfoJobHandler】，资源不存在{}------------",resourceId);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        reconciliationTask.setStatus(2);
                        reconciliationTask.setExecuteTime(new Date());
                        reconciliationTask.setErrorNum(reconciliationTask.getErrorNum() + 1);
                        reconciliationTask.setErrorMsg(JSON.toJSONString(e));
                        logger.error("调账失败------------{}", reconciliationTask,e.getMessage(),e);
                    }
                });
                reconciliationTaskService.updateBatchById(reconciliationTaskList);
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("更新华为云企业项目【updateHuaweiInfoJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("更新华为云企业项目【updateHuaweiInfoJobHandler】结束------------");
    }


}
