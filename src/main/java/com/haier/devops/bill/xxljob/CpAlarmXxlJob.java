package com.haier.devops.bill.xxljob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkApi;
import com.haier.devops.bill.common.api.entity.SubProduct;
import com.haier.devops.bill.common.config.HdsSendNoticeProperties;
import com.haier.devops.bill.common.dto.SendNotifyParam;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.enums.NoticeObjEnum;
import com.haier.devops.bill.common.enums.NoticeWayEnum;
import com.haier.devops.bill.common.enums.ReceiveTypeEnum;
import com.haier.devops.bill.common.mapper.*;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.*;
import com.haier.devops.bill.util.AesUtil;
import com.haier.devops.bill.util.DateUtil;
import com.haier.devops.bill.util.MobileUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
* @ClassName: CpAlarmXxlJob
* @Description: 告警相关job
* @author: 张爱苹
* @date: 2024/1/11 15:27
*/
@Component
public class CpAlarmXxlJob {
    private static Logger logger = LoggerFactory.getLogger(CpAlarmXxlJob.class);

    @Autowired
    private HdsOpenApi hdsOpenApi;

    @Autowired
    private HworkApi hworkApi;

    @Autowired
    private HdsSubProductsService hdsSubProductsService;

    @Autowired
    private HdsSubProductsMapper hdsSubProductsMapper;

    @Autowired
    private CpAlarmLevelMapper alarmLevelMapper;

    @Autowired
    private CpAlarmRuleDetailService alarmRuleDetailService;

    @Autowired
    private SysDictItemService sysDictItemService;

    @Autowired
    private AlarmLogService alarmLogService;

    @Autowired
    private AlarmUserInfoMapper userInfoMapper;

    @Autowired
    private AlarmConfigurationMapper alarmConfigurationMapper;

    @Autowired
    private HdsSendNoticeProperties hdsSendNoticeProperties;

    @Resource
    Executor localBootAsyncExecutor;

    @Autowired
    private AlarmUserInfoService alarmUserInfoService;

    @Autowired
    private AlarmNoticeGroupService alarmNoticeGroupService;

    @Autowired
    private AggregatedBillService aggregatedBillService;

    @Autowired
    private CloudVendorService cloudVendorService;

    @Autowired
    private BillGatheredMapper billGatheredMapper;

    @Autowired
    private SendSmsService sendSmsService;

    /**
    * @Description: 同步hds子产品列表
    * @author: 张爱苹
    * @date: 2024/1/22 13:27

    * @Return: void
    */
    @XxlJob("syncHdsSubProductJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void syncHdsSubProductJobHandler() throws Exception{
        logger.info("同步hds子产品列表【syncHdsSubProductJobHandler】开始------------");
        int count = 0;
        try{
            QueryWrapper queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("del_flag", 0);

            List<HdsSubProducts> oldHdsSubProductsList = hdsSubProductsService.list(queryWrapper);
            //将hdsSubProductsList转成map，key是appScode，value是对象
            Map<String, HdsSubProducts> map = oldHdsSubProductsList.stream().collect(Collectors.toMap(HdsSubProducts::
                    getAppScode, item -> item));
            //取历史S码集合
            Set<String> list = map.keySet();
            List<HdsSubProducts> hdsSubProductsList = new ArrayList<>();
            //新的S码集合
            Set<String> appScodeList = new HashSet<>();
            Integer page = 1;
            Integer pageSize = 100;
            while (true) {
                JSONObject obj = hdsOpenApi.querSubProducts(page, pageSize).getData();
                JSONArray subProductList = obj.getJSONArray("result");
                count = obj.getInteger("totalCount");
                int  totalCount = subProductList.size();
                if(totalCount > 0){
                    for (SubProduct subProduct : subProductList.toJavaList(SubProduct.class)) {
                        HdsSubProducts oldData = map.get(subProduct.getAlm_scode());
                        HdsSubProducts hdsSubProducts = toHdsSubProductEntity(subProduct);
                        if(oldData != null){
                            hdsSubProducts.setId(oldData.getId());
                            hdsSubProducts.setUpdateTime(new Date());
                        }
                        hdsSubProductsList.add(hdsSubProducts);
                        appScodeList.add(subProduct.getAlm_scode());
                    }
                }
                if(totalCount < 100){
                    break;
                }
                page = page + 1;
            }
            hdsSubProductsService.saveOrUpdateBatch(hdsSubProductsList);
            list = list.stream().filter(element -> !appScodeList.contains(element)).collect(Collectors.toSet());
            //删除不在hds里的子产品
            if(!CollectionUtils.isEmpty(list)){
                hdsSubProductsMapper.deleteByAppScodeIn(list);
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("同步hds子产品列表【syncHdsSubProductJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("同步hds子产品列表【syncHdsSubProductJobHandler】结束,同步{}条数据------------",count);
    }


    /**
    * @Description: 日费用突增告警
    * @author: 张爱苹
    * @date: 2024/1/15 16:31

    * @Return: void
    */
    @XxlJob("dailyCostInceaseAlarmJobHandler")
    public void dailyCostInceaseAlarmJobHandler() throws Exception{
        logger.info("日费用突增告警【dailyCostInceaseAlarmJobHandler】开始------------");
        String param = XxlJobHelper.getJobParam();
        try{
            //1.获取昨日与前日日期
            //2.查询（已配置了告警）产品昨日与前日费用汇总详情：分别查产品维度和应用维度的费用汇总详情；注：同一个产品如果维护了产品维护和应用维度的告警，则针对一条汇总费用会生成2条告警
            //3.查询步骤2中的产品告警等级详情
            //4.遍历步骤2的结果集
            //5.走告警逻辑，返回日志
            //6.保存日志
//            //获取昨日日期
            String yesterday = "";
            String beforeYesterday = "";
            String vendor = "aliyun";
            String[] paramArr = param.split(",");
            if(paramArr.length == 1){
                yesterday = DateUtil.getDayOf(-1);
////            //获取前日日期
                beforeYesterday = DateUtil.getDayOf(-2);
                vendor = param;
            }else{
                yesterday = paramArr[0];
                beforeYesterday = paramArr[1];
                vendor = paramArr[2];
            }
            logger.info("云厂商：{}",vendor);
            List<Map> configDetialList = billGatheredMapper.getConfigDetialList(vendor);
            logger.info("获取配置详情数量：{}",configDetialList.size());
            if(!CollectionUtils.isEmpty(configDetialList)){
                List<HdsSubProducts> hdsSubProductsList = hdsSubProductsMapper.selectList(new QueryWrapper<HdsSubProducts>().eq("del_flag",0));
                Map<String, HdsSubProducts> hdsSubProductsMap = hdsSubProductsList.stream().collect(Collectors.toMap(HdsSubProducts::getAppScode, item -> item));
                Map map = new HashMap();
                map.put("startDate",beforeYesterday);
                map.put("endDate",yesterday);
                for (Map configDetial : configDetialList) {
                    map.put("productCode",configDetial.get("product_code"));
                    map.put("appScode",configDetial.get("app_scode"));
                    map.put("productId",configDetial.get("product_id"));
                    map.put("warnAmount",configDetial.get("warn_amount"));
                    map.put("vendor",vendor);
                    List<BillGatheredVo> costList = billGatheredMapper.getDailyCostInceaseAlarmV2(map);
                    logger.info("获取产品{}的账单数量：{}",configDetial.get("product_code"),costList.size());
                    if(!CollectionUtils.isEmpty(costList)){
                        //遍历costList得到productIdList
                        Set<Integer> productIdList = costList
                                .stream()
                                .map(BillGatheredVo::getProductId)
                                .collect(Collectors.toSet());
                        //查询所有productIdList的告警等级信息
                        List<Map> alarmLevelList = alarmLevelMapper.getAlarmLevelListByGroupProduct(productIdList);
                        logger.info("获取产品{}的告警等级配置数量：{}",configDetial.get("product_code"),alarmLevelList.size());
                        if(!CollectionUtils.isEmpty(alarmLevelList)){
                            //转map 其中key是productId=concat(appScode,productId)
                            Map<String,String> alarmLevelMap = alarmLevelList.stream()
                                    .collect(Collectors.toMap(m->m.get("productId").toString(),m->m.get("levelInfo").toString()));
                            //遍历costList
                            for(BillGatheredVo billGatheredVo: costList){
                                if(hdsSubProductsMap.containsKey(billGatheredVo.getScode())){
                                    HdsSubProducts hdsSubProducts = hdsSubProductsMap.get(billGatheredVo.getScode());
                                    billGatheredVo.setAppName(hdsSubProducts.getAppName());
                                    billGatheredVo.setOwner(hdsSubProducts.getOwner());
                                }
                                //异步执行告警逻辑
                                String finalYesterday = yesterday;
                                String finalBeforeYesterday = beforeYesterday;
                                CompletableFuture.supplyAsync(() -> {
                                    //获取产品id对应的告警等级信息 格式：等级信息（等级id-等级-等级名称-阈值） 多个等级逗号隔开
                                    Object alarmLevel = alarmLevelMap.get((billGatheredVo.getAppScode() == null?"":billGatheredVo.getAppScode())+billGatheredVo.getProductId());
                                    if(alarmLevel != null){
                                        String[] levelInfo = alarmLevel.toString().split(",");
                                        //判断是否需要告警，告警返回告警日志
                                        processCostInceaseAlarm(billGatheredVo,levelInfo, finalYesterday, finalBeforeYesterday);
                                    }
                                    return null;
                                },localBootAsyncExecutor).exceptionally(e ->{
                                    e.printStackTrace();
                                    return null;
                                });
                            }
                        }
                    }


                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("日费用突增告警【dailyCostInceaseAlarmJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("日费用突增告警【dailyCostInceaseAlarmJobHandler】结束------------");
    }

    /**
    * @Description: 针对一条汇总费用数据进行处理
    * @author: 张爱苹
    * @date: 2024/1/22 17:15
    * @param billGatheredVo: 汇总费用
    * @param levelInfo:等级信息
    * @param yesterday:昨日
    * @param beforeYesterday:前日
    * @Return: com.haier.devops.bill.common.entity.CpAlarmLog
    */
    private void processCostInceaseAlarm(BillGatheredVo billGatheredVo,String[] levelInfo,String yesterday,String beforeYesterday) {
        //获取费用汇总 格式：昨日费用,前日费用
        String[] costSummary = billGatheredVo.getCostSummary().split(",");
        //昨日费用汇总
        BigDecimal yeaterdayCs = new BigDecimal(costSummary[0]);
        //前日费用汇总
        BigDecimal beforeYesterdayCs = new BigDecimal(0);
        if(costSummary.length == 1){
            logger.info("只有一天的费用不告警:{}",JSON.toJSONString(billGatheredVo));
            //不告警
            return;
        }else{
            beforeYesterdayCs = new BigDecimal(costSummary[1]);
        }
        if(yeaterdayCs.compareTo(billGatheredVo.getWarnAmount())<0){
            logger.info("昨日费用小于预警金额不告警:{}",JSON.toJSONString(billGatheredVo));
            //昨日费用小于预警金额不告警
            return;
        }
        //计算昨日较前日浮动率 （昨日-前日）/前日
        BigDecimal dailyCostIncease = (yeaterdayCs.subtract(beforeYesterdayCs)).divide(beforeYesterdayCs,4, RoundingMode.HALF_UP);
        if(dailyCostIncease.compareTo(new BigDecimal(0))<=0){
            logger.info("昨日费用没有增长或负增长不告警:{}",JSON.toJSONString(billGatheredVo));
            //没增长或负增长不告警
            return;
        }
        //费用上浮
        StringBuffer direction = new StringBuffer("↑");
        //组装消息模版中的变量
        JSONObject tmplParams = geteJsonObject(billGatheredVo,yesterday,beforeYesterday,yeaterdayCs,beforeYesterdayCs,direction,dailyCostIncease);

        //告警日志内容
        String content = new StringBuffer("【").append(yesterday).append("】").append("较").append("【").append(beforeYesterday).append("】云资源").append("日费用").append(direction).toString();

        //遍历levelInfo
        for(int i=0;i<levelInfo.length;i++) {
            //格式：等级信息（等级id-等级名称-阈值）
            String levelDetail = levelInfo[i];
            String[] detail = levelDetail.split("-");
            //等级id
            Integer levelId = Integer.valueOf(detail[0]);
            billGatheredVo.setLevelId(levelId);
            //等级字典值
            String level = detail[1];
            billGatheredVo.setLevel(level);
            //等级名称
            String levelName = detail[2];
            billGatheredVo.setLevelName(levelName);
            //告警日志
            CpAlarmLog alarmLog = genneraterAlarmLog(billGatheredVo, content,yesterday);
            //阈值
            BigDecimal amplitudeRatio = new BigDecimal(detail[3]);
            //比较
            //1.小于最小阈值不告警
            //2.大于最大阈值告警
            //3.在中间阈值范围内告警
            if(i == 0 && dailyCostIncease.compareTo(amplitudeRatio)<0){
                logger.info("昨日费用小于最小阈值不告警:{},{},{}",JSON.toJSONString(billGatheredVo),dailyCostIncease,amplitudeRatio);
                //不告警
                break;
            }
            if(i == levelInfo.length-1 && dailyCostIncease.compareTo(amplitudeRatio)>0){
                //告警
                alarm(billGatheredVo,tmplParams,alarmLog);
                break;
            }
            if(dailyCostIncease.compareTo(amplitudeRatio)<=0) {
                //告警
                alarm(billGatheredVo,tmplParams,alarmLog);
                break;
            }
        }
    }

    private CpAlarmLog genneraterAlarmLog(BillGatheredVo billGatheredVo, String content,String yesterday) {
        return CpAlarmLog.builder()
                .gatheringId(billGatheredVo.getAggregatedId())
                .appScode(billGatheredVo.getAppScode())
                .productId(billGatheredVo.getProductId())
                .levelName(billGatheredVo.getLevelName())
                .alarmContent(content)
                .level(billGatheredVo.getLevel())
                .billingCycle(yesterday).build();
    }

    private JSONObject geteJsonObject(BillGatheredVo billGatheredVo,String startDate,String endDate,BigDecimal startCost,BigDecimal endCost, StringBuffer direction,BigDecimal dailyCostIncease) {
        //标题
        StringBuffer title = new StringBuffer();
        title.append(billGatheredVo.getAppName()).append("-");
        title.append(billGatheredVo.getVendor()).append("-").append(billGatheredVo.getProductName());

        //产品详情
        ProductDetailVo productDetailVo = new ProductDetailVo();
        BeanUtils.copyProperties(billGatheredVo,productDetailVo);
       // String productDetail = JSON.toJSONString(productDetailVo);

        //拼接direction 例：%15
        direction.append(dailyCostIncease.multiply(new BigDecimal(100)).setScale(0,RoundingMode.HALF_UP)+"%");

        JSONObject tmplParams = new JSONObject();
        tmplParams.put("appName",billGatheredVo.getAppName());
        tmplParams.put("title",title.toString());
        tmplParams.put("productDetail",productDetailVo);
        tmplParams.put("triggerTime", DateUtil.getCurrentDate());
        tmplParams.put("startDate",startDate);
        tmplParams.put("endDate",endDate);
        tmplParams.put("startCost",startCost.setScale(2,RoundingMode.HALF_UP));
        tmplParams.put("endCost",endCost.setScale(2,RoundingMode.HALF_UP));
        tmplParams.put("rate",direction.toString());
        return tmplParams;
    }


    /**
    * @Description: 告警发信息
    * @author: 张爱苹
    * @date: 2024/1/22 17:23
    * @param billGatheredVo:
    * @param tmplParams:
    * @param alarmLog:
    * @Return: com.haier.devops.bill.common.entity.CpAlarmLog
    */
    private void alarm(BillGatheredVo billGatheredVo,JSONObject tmplParams,CpAlarmLog alarmLog) {
        tmplParams.put("levelName",billGatheredVo.getLevelName());
        //定义 通知对象详情list
        List<Map> noticeObjDetailList = new ArrayList<>();
        //定义 发送信息array
        List<SendNotifyParam> array = new ArrayList();
        //费用汇总S码
        String appScode = billGatheredVo.getScode();
        Map map = new HashMap();
        map.put("alarmLevelId",billGatheredVo.getLevelId());
        map.put("group", NoticeObjEnum.GROUP.getKey());
        map.put("individual",NoticeObjEnum.INDIVIDUAL.getKey());
        //根据alarmLevelId获取对应的告警规则详情
        List<CpAlarmRuleDetailVo> alarmRuleDetailVoList = alarmRuleDetailService.queryAlarmRuleDetailListByAlarmLevelId(map);
        logger.info("告警规则详情:{},{}",alarmRuleDetailVoList.size(),billGatheredVo.getLevelId());
        if(!CollectionUtils.isEmpty(alarmRuleDetailVoList)){
            List<SysDictItemVo> itemVoList = sysDictItemService.getDictItemList("DAYLY_ALARM_APP_MANANGER");
            String owner = "";
            if(itemVoList.get(0).getItemValue().equals("1")){
                //系统负责人
                owner = billGatheredVo.getOwner();
                //保存告警联系人
                if(!ObjectUtils.isEmpty(owner)){
                    alarmNoticeGroupService.getAlarmNoticeObj(owner);
                }
            }

            //遍历告警规则详情
            for(CpAlarmRuleDetailVo alarmRuleDetailVo : alarmRuleDetailVoList){
                Map noticeObjDetailMap = new HashMap();
                //告警方式
                String noticeWay = alarmRuleDetailVo.getNoticeWay();
                //获取告警对象id 格式：20115778,20115778,20115778，告警方式不同 id意义不同：短信/邮箱代表 工号，ihaier/hwork 代表群id

                //定义 通知对象详情
                String alarmNoticeObj = "";
                switch (noticeWay){
                    case "sms":
                        //针对sms title重制 sms不支持中文符号
                        String content = new StringBuffer("您好，").append(billGatheredVo.getAppName()).append("-").append(billGatheredVo.getVendor()).append("-").append(billGatheredVo.getProductCode()).append("-").append(billGatheredVo.getInstanceId()).append("云资源费用为").append(tmplParams.get("startCost")).append("元，").append(tmplParams.get("startDate")).append("较").append(tmplParams.get("endDate"))
                                .append("日费用").append(tmplParams.get("rate")).toString();
                       // tmplParams.put("title",title);
                        if(!ObjectUtils.isEmpty(owner)){
                            alarmRuleDetailVo.setNoticeObjId(alarmRuleDetailVo.getNoticeObjId()+","+owner);
                        }
                        String[] reqReceiver = alarmRuleDetailVo.getNoticeObjId().split(",");
                        //关联查出手机号 短信不能群发
                        List<AlarmNoticeObjVo> alarmNoticeObjDTOList = userInfoMapper.getUserListByUserIdArray(reqReceiver);
                        if(!CollectionUtils.isEmpty(alarmNoticeObjDTOList)){
                            for (int i = 0; i <alarmNoticeObjDTOList.size() ; i++) {
                                AlarmNoticeObjVo alarmNoticeObjDTO = alarmNoticeObjDTOList.get(i);
                                String phone = AesUtil.decryptCBC(alarmNoticeObjDTO.getMobile());
                                String mobile = MobileUtil.blurPhoneNum(phone);
                                String  name = alarmNoticeObjDTO.getNoticeObjName();
                                if(i ==  alarmNoticeObjDTOList.size()-1){
                                    alarmNoticeObj += mobile+"("+name+")";
                                }else{
                                    alarmNoticeObj += mobile+"("+name+"),";
                                }
                                try {
                                    sendSmsService.sendMessageMail(phone,content);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    logger.error("发送短信失败：{}",phone);
                                }
                            }
                        }
                        break;
                    case "mail":
                        //关联查出邮箱
                        if(!ObjectUtils.isEmpty(owner)){
                            alarmRuleDetailVo.setNoticeObjId(alarmRuleDetailVo.getNoticeObjId()+","+owner);
                        }
                        reqReceiver = alarmRuleDetailVo.getNoticeObjId().split(",");
                        alarmNoticeObjDTOList = userInfoMapper.getUserListByUserIdArray(reqReceiver);
                        if(!CollectionUtils.isEmpty(alarmNoticeObjDTOList)){
                            alarmNoticeObj = alarmNoticeObjDTOList.stream().map(
                                    obj -> new StringBuffer(obj.getEmail()).append("(").append(obj.getNoticeObjName()).append(")")
                            ).collect(Collectors.joining(","));
                            List<String> emailList = alarmNoticeObjDTOList.stream().map(AlarmNoticeObjVo::getEmail).collect(Collectors.toList());
                            //邮件
                            JSONObject emailObject = new JSONObject();
                            emailObject.put("to",emailList);
                            //email
                            SendNotifyParam sendNotifyParam = new SendNotifyParam();
                            sendNotifyParam.setEmail(emailObject);
                            array.add(sendAlarmNotice(sendNotifyParam,appScode, ReceiveTypeEnum.EMAIL.getKey(), NoticeWayEnum.EMAIL.getKey(),hdsSendNoticeProperties.getSendor_email(),emailList, hdsSendNoticeProperties.getTemplate_email(), tmplParams));
                        }
                        break;
                    case "hworkim":
                        reqReceiver = alarmRuleDetailVo.getNoticeObjId().split(",");
                        alarmNoticeObjDTOList = sysDictItemService.getDictItemByItemValueArray(reqReceiver, NoticeWayEnum.HWORK.getValue());
                        if(!CollectionUtils.isEmpty(alarmNoticeObjDTOList)){
                            for (int i = 0; i <alarmNoticeObjDTOList.size() ; i++) {
                                AlarmNoticeObjVo alarmNoticeObjDTO = alarmNoticeObjDTOList.get(i);
                                if(i ==  alarmNoticeObjDTOList.size()-1){
                                    alarmNoticeObj += alarmNoticeObjDTO.getNoticeObjName();
                                }else{
                                    alarmNoticeObj += alarmNoticeObjDTO.getNoticeObjName()+",";
                                }
                                List<String> hworkList = new ArrayList<>();
                                hworkList.add(alarmNoticeObjDTO.getNoticeObjId());
                                SendNotifyParam sendNotifyParam = new SendNotifyParam();
                                JSONObject hworkim = genneraterHwormIm();
                                sendNotifyParam.setHworkim(hworkim);
                                array.add(sendAlarmNotice(sendNotifyParam,appScode, ReceiveTypeEnum.CHATID.getKey(),NoticeWayEnum.HWORK.getKey(),hdsSendNoticeProperties.getSendor_hwork(),hworkList, hdsSendNoticeProperties.getTemplate_hwork(), tmplParams));
                            }
                        }
                        break;
//                    case "ihaier2":
//                        //ihaier
//                        //关联查出群名
//                        reqReceiver = alarmRuleDetailVo.getNoticeObjId().split(",");
//                        alarmNoticeObjDTOList = sysDictItemService.getDictItemByItemValueArray(reqReceiver, NoticeWayEnum.IHAIER.getValue());
//                        if(!CollectionUtils.isEmpty(alarmNoticeObjDTOList)){
//                            alarmNoticeObj = alarmNoticeObjDTOList.stream().map(AlarmNoticeObjVo::getNoticeObjName).collect(Collectors.joining(","));
//                            SendNotifyParam sendNotifyParam = new SendNotifyParam();
//                            array.add(sendAlarmNotice(sendNotifyParam,appScode, ReceiveTypeEnum.CHATID.getKey(),NoticeWayEnum.IHAIER.getKey(),hdsSendNoticeProperties.getSendor_ihaier2(),Arrays.asList(reqReceiver), hdsSendNoticeProperties.getTemplate_ihaier2(), tmplParams));
//                        }
//                        break;
                    default:
                        break;
                }
                noticeObjDetailMap.put("noticeWay",noticeWay);
                noticeObjDetailMap.put("noticeObjId",alarmNoticeObj);
                noticeObjDetailList.add(noticeObjDetailMap);
            }
            logger.info("发送告警消息：{}",JSON.toJSONString(array));
            //发送消息
            if(!CollectionUtils.isEmpty(array)) {
                hdsOpenApi.sendNotify(array,appScode);
            }
            if(!CollectionUtils.isEmpty(noticeObjDetailList)){
                alarmLog.setNoticeObjDetail(JSON.toJSONString(noticeObjDetailList));
                //保存日志
                alarmLogService.save(alarmLog);
            }
        }
    }



    private JSONObject genneraterHwormIm() {
        String msg = "{\"messageOriginator\":\"\",\"mobileSkipType\":0,\"handleMode\":2,\"robotType\":0,\"pcSkipUrl\":\"\",\"mobileSkipUrl\":\"\",\"additionalJsonStr\":\"\",\"appId\":\"\"}";
        return JSONObject.parseObject(msg);
    }

    /**
    * @Description: 组装告警通知消息
    * @author: 张爱苹
    * @date: 2024/1/22 17:27
    * @param appScode:
    * @param reqReceiverType:
    * @param type:
    * @param sender:
    * @param reqReceiver:
    * @param templId:
    * @param tmplParams:
    * @Return: com.haier.devops.bill.common.dto.SendNotifyParam
    */
    private SendNotifyParam sendAlarmNotice(SendNotifyParam sendNotifyParam,String appScode,String reqReceiverType,String type,String sender,List<String> reqReceiver,String templId,JSONObject tmplParams) {
        sendNotifyParam.setAlmScode(appScode);
        sendNotifyParam.setReqReceiver(reqReceiver);
        sendNotifyParam.setTmplId(Long.parseLong(templId));
        sendNotifyParam.setReqReceiverType(reqReceiverType);
        sendNotifyParam.setType(type);
        sendNotifyParam.setSender(sender);
        sendNotifyParam.setTmplParams(tmplParams);
        return sendNotifyParam;
    }

    @NotNull
    private HdsSubProducts toHdsSubProductEntity(SubProduct subProduct) {
        HdsSubProducts hdsSubProducts = new HdsSubProducts();
        hdsSubProducts.setAppScode(subProduct.getAlm_scode());
        hdsSubProducts.setAppName(subProduct.getAlm_name());
        hdsSubProducts.setAppShortName(subProduct.getAlm_code());
        hdsSubProducts.setBusinessDomainIds(subProduct.getBusiness_domain_ids());
        hdsSubProducts.setBusinessDomains(subProduct.getBusiness_domains());
        hdsSubProducts.setItManager(subProduct.getIt_manager());
        hdsSubProducts.setItManagerName(subProduct.getIt_manager_name());
        hdsSubProducts.setDescription(subProduct.getDescription());
        hdsSubProducts.setOwner(subProduct.getOwner());
        hdsSubProducts.setOwnerName(subProduct.getOwner_name());
        hdsSubProducts.setItOpsManager(subProduct.getIt_ops_manager());
        hdsSubProducts.setItOpsManagerName(subProduct.getIt_ops_manager_name());
        hdsSubProducts.setProductId(subProduct.getProduct_id());
        hdsSubProducts.setProductName(subProduct.getProduct_name());
        hdsSubProducts.setSubProductId(subProduct.getPuid());
        hdsSubProducts.setSubProductName(subProduct.getName());
        hdsSubProducts.setSubProductNameEn(subProduct.getName_en());
        String orgId = subProduct.getOrg_id();
        hdsSubProducts.setOrgId(orgId.replaceAll("\\.", ","));
        return hdsSubProducts;
    }

    @XxlJob("sendNotice")
    public void sendNotice() throws Exception{
//        JSONArray array = new JSONArray();
//        JsonArray reqReceiver = new JsonArray();
//        reqReceiver.add("oc_ee50f2ebb0b5179a155c77dfa0e116f1");
//        JsonObject tmplParams = new JsonObject();
//        tmplParams.addProperty("content", "etete");
//        //ihaier
//        array.add(sendAlarmNotice("S02166",ReceiveTypeEnum.CHATID.getKey(),NoticeWayEnum.IHAIER.getKey(),"HCMS",reqReceiver,"10000924",tmplParams,null));
//        reqReceiver = new JsonArray();
//        reqReceiver.add("18560612110");
//        tmplParams = new JsonObject();
//        tmplParams.addProperty("content", "etete");
//        //sms
//        array.add(sendAlarmNotice("S02166",ReceiveTypeEnum.MOBILE.getKey(),NoticeWayEnum.SMS.getKey(),"HCMS",reqReceiver,"10000924",tmplParams,null));
//        reqReceiver = new JsonArray();
//        reqReceiver.add("<EMAIL>");
//        tmplParams = new JsonObject();
//        tmplParams.addProperty("content", "etete");
//        JSONObject emailObject = new JSONObject();
//        emailObject.put("to","<EMAIL>");
//        //email
//        array.add(sendAlarmNotice("S02166",ReceiveTypeEnum.EMAIL.getKey(),NoticeWayEnum.EMAIL.getKey(),"HCMS<<EMAIL>>",reqReceiver,"10000924",tmplParams,emailObject));
//        hdsOpenApi.sendNotify(array,"S02166");
    }

    /**
    * @Description:  定时校验告警配置的有效性
    * @author: 张爱苹
    * @date: 2024/1/22 13:29

    * @Return: void
    */
    @XxlJob("validAlarmConfigPeriodJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void validAlarmConfigPeriodJobHandler() throws Exception {
        logger.info("定时校验告警配置的有效性【validAlarmConfigPeriodJobHandler】开始------------");
        try{
            //停用 当前时间处于有效期 设置启用
            alarmConfigurationMapper
                    .updateAlarmConfigurationStatusByPeriod(1);
            //启用 当前时间不在有效期 设置停用
            alarmConfigurationMapper
                    .updateAlarmConfigurationStatusByPeriod(0);
        }catch (Exception e){
            logger.error("定时校验告警配置的有效性【validAlarmConfigPeriodJobHandler】失败",e);
        }
        logger.info("定时校验告警配置的有效性【validAlarmConfigPeriodJobHandler】结束------------");
    }

    /**
    * @Description: hwork群创建
    * @author: 张爱苹
    * @date: 2024/1/24 10:47
    * @Return: void
    */
    @XxlJob("createHworkGroupJobHandler")
    public void createHworkGroupJobHandler() throws Exception {
        logger.info("hwork群创建【createHworkGroupJobHandler】开始------------");
        try{
            JSONObject object
                    = JSONObject.parseObject(XxlJobHelper.getJobParam());
            HworkApi.Group group = hworkApi.createGroup(object).getData();
            SysDictItem sysDictItem = new SysDictItem();
            sysDictItem.setDictId("5");
            sysDictItem.setItemText(group.getGroupName());
            sysDictItem.setItemValue(group.getGroupId());
            sysDictItemService.save(sysDictItem);
        }catch (Exception e){
            logger.error("hwork群创建【createHworkGroupJobHandler】失败",e);
        }
        logger.info("hwork群创建【createHworkGroupJobHandler】结束------------");
    }

    /**
    * @Description: 更新告警用户信息
    * @author: 张爱苹
    * @date: 2024/2/2 13:34

    * @Return: void
    */
    @XxlJob("updateAlarmUserInfoJobHandler")
    public void updateAlarmUserInfoJobHandler() throws Exception {
        logger.info("更新告警用户信息【updateAlarmUserInfoJobHandler】开始------------");
        try{
            List<AlarmUserInfo>
                    alarmUserInfoList = alarmUserInfoService.list();
            List<AlarmUserInfo> list = new
                    ArrayList<>();
            for (AlarmUserInfo alarmUserInfo : alarmUserInfoList) {
                AlarmUserInfo entity = alarmNoticeGroupService.getUpdateUserInfo(alarmUserInfo);
                list.add(entity);
            }
            //移除list中空的对象
            list.removeIf(Objects::isNull);
            if(!CollectionUtils.isEmpty(list)){
                alarmUserInfoService.updateBatchById(list);
            }
        }catch (Exception e){
            logger.error("更新告警用户信息【updateAlarmUserInfoJobHandler】失败",e);
        }
        logger.info("更新告警用户信息【updateAlarmUserInfoJobHandler】结束------------");
    }

    /**
     * @Description: S码为空告警（账单明细汇总表）
     * @author: 张爱苹
     * @date: 2024/1/15 16:31

     * @Return: void
     */
    @XxlJob("financialUnitAllocationAlarmJobHandler")
    public void financialUnitAllocationAlarmJobHandler() throws Exception{
        logger.info("S码为空告警【financialUnitAllocationAlarmJobHandler】开始------------");
        try{
            //查询S码为空的账单汇总信息条数
            List<Map> mapList = aggregatedBillService.countByS();
            if(!CollectionUtils.isEmpty(mapList)){
                //定义 发送信息array
                List<SendNotifyParam> array = new ArrayList();
                for (Map map : mapList){
                    Integer num =  Integer.valueOf(map.get("num").toString());
                    if(num > 0){
                        //告警
                        String vendor = map.get("vendor").toString();
                        //查云管理员账号
                        CloudVendor cloudVendor = cloudVendorService.getCloudVendor(vendor);
                        String admin = cloudVendor.getCloudAdministrator();
                        if(!ObjectUtils.isEmpty(admin)){
                            //发送ihaier告警
                            SendNotifyParam sendNotifyParam = new SendNotifyParam();
                            List reqReceiver =  new ArrayList();
                            reqReceiver.add(admin);
                            JSONObject tmplParams = new JSONObject();
                            tmplParams.put("vendor",vendor);
                            tmplParams.put("num",num);
                            array.add(sendAlarmNotice(sendNotifyParam,"S02166", ReceiveTypeEnum.USERID.getKey(),NoticeWayEnum.IHAIER.getKey(),hdsSendNoticeProperties.getSendor_ihaier2(),reqReceiver, hdsSendNoticeProperties.getTemplate_ihaier2_2(), tmplParams));
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(array)){
                    hdsOpenApi.sendNotify(array,"S02166");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("S码为空告警【financialUnitAllocationAlarmJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("S码为空告警【financialUnitAllocationAlarmJobHandler】结束------------");
    }

}
