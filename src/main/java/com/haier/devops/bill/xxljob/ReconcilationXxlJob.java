package com.haier.devops.bill.xxljob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.bssopenapi20171214.models.TagResourcesRequest;
import com.aliyun.bssopenapi20171214.models.*;
import com.aliyun.resourcemanager20200331.models.*;
import com.haier.devops.bill.common.api.AliyunApi;
import com.haier.devops.bill.common.dto.RawBillProductsDTO;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.enums.ReconciliationTaskTypeEnum;
import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.ResourceInstanceVo;
import com.haier.devops.bill.util.AesUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.core.instrument.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
* @ClassName: ReconcilationXxlJob
* @Description: 调账相关任务
* @author: 张爱苹
* @date: 2024/3/19 10:55
*/
@Component
public class ReconcilationXxlJob {
    private static Logger logger = LoggerFactory.getLogger(ReconcilationXxlJob.class);

    @Autowired
    private ReconciliationTaskService reconciliationTaskService;

    @Autowired
    private ResourceInstanceService resourceInstanceService;

    @Autowired
    private CloudAccountService cloudAccountService;

    @Autowired
    private CloudProductRelationService cloudProductRelationService;

    @Resource
    Executor localBootAsyncExecutor;

    @Autowired
    private HdsSubProductsService subProductsService;

    @Autowired
    private RawBillProductsService productsService;

    @Autowired
    private CmdbProductOverviewService cmdbProductOverviewService;

    @Autowired
    private RawBillService rawBillService;

    /**
    * @Description: 更新阿里云财务单元、标签、资源组等信息
    * @author: 张爱苹
    * @date: 2024/3/19 10:57

    * @Return: void
    */
    @XxlJob("updateAliInfoJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void updateAliCostUnitJobHandler() throws Exception{
        logger.info("更新阿里云财务单元、标签、资源组等信息【updateAliInfoJobHandler】开始------------");
        try{
            String type
                    = XxlJobHelper.getJobParam();
           //查询阿里云调账任务，调账类型为costUnit且状态是未执行的数据
            List<ReconciliationTask> reconciliationTaskList = reconciliationTaskService.queryList(type);
            //查云账号下的实例信息
            if(!CollectionUtils.isEmpty(reconciliationTaskList)){
                logger.info("更新阿里云财务单元、标签、资源组等信息【updateAliInfoJobHandler】,待调账任务{}条------------",reconciliationTaskList.size());
                //根据要调账S码分组
                Map<String,List<ReconciliationTask>> groupMap = reconciliationTaskList.stream().collect(Collectors.groupingBy(ReconciliationTask::getScode));
                List<String> appScodeList =
                        groupMap.keySet().stream().collect(Collectors.toList());
                Map<String,HdsSubProducts> appScodeMap = getAppScodeMap(appScodeList);
                for (Map.Entry<String, List<ReconciliationTask>> entry : groupMap.entrySet()) {
                    String scode = entry.getKey();
                    Map<String,Integer> aggregatedIdMap = entry.getValue().stream().collect(Collectors.toMap(ReconciliationTask::getAggregatedId,ReconciliationTask::getId));
//                    List<String> aggregatedIdList =
//                            entry.getValue().stream().map(ReconciliationTask::getAggregatedId).collect(Collectors.toList());
                    List<ResourceInstanceVo> resourceInstanceList = queryResourceInstanceList(aggregatedIdMap);

                   // List<ResourceInstanceVo> resourceInstanceList = resourceInstanceService.queryResourceInstanceList(aggregatedIdList);
                    //根据resourceUserId分组
                    Map<Long, List<ResourceInstanceVo>> resourceInstanceMap = resourceInstanceList.stream().collect(Collectors.groupingBy(ResourceInstanceVo::getResourceUserId));
                    for (Map.Entry<Long, List<ResourceInstanceVo>> entry2 : resourceInstanceMap.entrySet()) {
                        List<ResourceInstanceVo> voList = entry2.getValue();
                        String accessKey = voList.get(0).getAccessKey();
                        String accessKeySecret = voList.get(0).getAccessKeySecret();
                        Long accountId = entry2.getKey();
                        String encrypted = voList.get(0).getEncrypted();
                        if (!"0".equals(encrypted)) {
                            accessKey = AesUtil.decryptCBC(accessKey);
                            accessKeySecret = AesUtil.decryptCBC(accessKeySecret);
                        }
                        if(type.equals(ReconciliationTaskTypeEnum.COSTUNIT.getKey())){
                            extracteUpdateCostUnit(scode, accountId, voList, accessKey, accessKeySecret,appScodeMap);
                        }else if(type.equals(ReconciliationTaskTypeEnum.TAG.getKey())){
                            extracteUpdateTag(scode, voList, accessKey, accessKeySecret);
                        }

                    }
                }

            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("更新阿里云财务单元、标签、资源组等信息【updateAliInfoJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("更新阿里云财务单元、标签、资源组等信息【updateAliInfoJobHandler】结束------------");
    }

    private List<ResourceInstanceVo> queryResourceInstanceList(Map<String,Integer> aggregatedIdMap) {
        List<String> aggregatedIdList = aggregatedIdMap.keySet().stream().collect(Collectors.toList());
        List<CmdbProductOverview> cmdbProductOverviewList = cmdbProductOverviewService.queryCmdbProductOverviewList(aggregatedIdList);
        List<ResourceInstanceVo> list = new ArrayList<>();
        List<String> noMatchList = new ArrayList<>();
        for (int i = 0; i < cmdbProductOverviewList.size(); i++) {
            CmdbProductOverview cmdbProductOverview = cmdbProductOverviewList.get(i);
            RefinedRawBill rawBill = getResourceId(cmdbProductOverview.getAggregatedId());
            JSONObject jsonObject = JSONObject.parseObject(rawBill.getContent());
            cmdbProductOverview.setResourceId(jsonObject.getString("InstanceID"));
            cmdbProductOverview.setApportionCode(jsonObject.getString("SplitItemID"));
            ResourceInstanceVo vo = resourceInstanceService.queryResourceInstanceList(cmdbProductOverview);
            if(vo != null){
                vo.setAggregatedId(rawBill.getAggregatedId());
                vo.setReconciliationId(aggregatedIdMap.get(rawBill.getAggregatedId()));
                list.add(vo);
            }else{
                logger.info("更新阿里云财务单元、标签、资源组等信息【updateAliInfoJobHandler】,资源不存在{}------------",JSON.toJSONString(cmdbProductOverview));

            }
        }
        return list;
    }

    private RefinedRawBill getResourceId(String aggregatedId) {
        return rawBillService.getResourceId(aggregatedId);
    }

    private Map<String,HdsSubProducts> getAppScodeMap(List<String> appScodeList) {
        List<HdsSubProducts> hdsSubProductsList = subProductsService.queryList(appScodeList);
        Map<String,HdsSubProducts> appScodeMap = hdsSubProductsList.stream().collect(Collectors.toMap(HdsSubProducts::getAppScode,item->item));
        return appScodeMap;
    }

    private void extracteUpdateCostUnit(String scode, Long accountId, List<ResourceInstanceVo> voList, String accessKey, String accessKeySecret,Map<String,HdsSubProducts> appScodeMap) throws Exception {
        //查财务单元
        Map<String, List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList>> scodeUnitMap = getScodeUnitMap(accessKey, accessKeySecret, accountId);
        Long unitId = null;
        String unitName = null;
        if(!CollectionUtils.isEmpty(scodeUnitMap.get(scode))){
            QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList costUnit = scodeUnitMap.get(scode).get(0);
            unitId = costUnit.getUnitId();
            unitName = costUnit.getUnitName();
        }else{
            if(ObjectUtils.isEmpty(appScodeMap.get(scode))){
                logger.error("appScodeMap中不存在scode:{}",scode);
                return;
            }
            HdsSubProducts hdsSubProducts = appScodeMap.get(scode);
            //创建财务单元
            CreateCostUnitRequest createCostUnitRequest = new CreateCostUnitRequest();
            List<CreateCostUnitRequest.CreateCostUnitRequestUnitEntityList>
                    unitEntityList = new ArrayList<>();
            CreateCostUnitRequest.CreateCostUnitRequestUnitEntityList costUnitRequestUnitEntityList
                    = new CreateCostUnitRequest.CreateCostUnitRequestUnitEntityList();
            costUnitRequestUnitEntityList.setParentUnitId(-1L);
            costUnitRequestUnitEntityList.setOwnerUid(accountId);
            costUnitRequestUnitEntityList.setUnitName(new StringBuffer(hdsSubProducts.getAppShortName()).append("-").append(hdsSubProducts.getAppName()).append("-").append(scode).toString());
            unitEntityList.add(costUnitRequestUnitEntityList);
            createCostUnitRequest.setUnitEntityList(unitEntityList) ;
            CreateCostUnitResponseBody.CreateCostUnitResponseBodyData data = AliyunApi.createCostUnitWithOptions(accessKey, accessKeySecret, createCostUnitRequest);
            if(data == null){
                logger.error("创建财务单元失败{}",scode);
                return;
            }
            unitId = data.getCostUnitDtoList().get(0).getUnitId();
            unitName = costUnitRequestUnitEntityList.getUnitName();
        }
        //根据原costUnitId分组
        Map<String, List<ResourceInstanceVo>> voMap = voList.stream().collect(Collectors.groupingBy(ResourceInstanceVo::getCostUnitId));
        for (Map.Entry<String, List<ResourceInstanceVo>> entry3 : voMap.entrySet()) {
            CountDownLatch latch = new CountDownLatch(entry3.getValue().size());
            Long finalUnitId = unitId;
            String finalUnitName = unitName;
            entry3.getValue().stream().forEach(result ->{
                localBootAsyncExecutor.execute(() ->{
                    List<ResourceInstanceVo> resultList = entry3.getValue();
                    try {
                        AllocateCostUnitResourceRequest allocateCostUnitResourceRequest = new AllocateCostUnitResourceRequest();
                        allocateCostUnitResourceRequest.setFromUnitUserId(accountId);
                        allocateCostUnitResourceRequest.setFromUnitId(Long.valueOf(entry3.getKey()));
                        allocateCostUnitResourceRequest.setToUnitUserId(accountId);
                        allocateCostUnitResourceRequest.setToUnitId(finalUnitId);
                        List<AllocateCostUnitResourceRequest.AllocateCostUnitResourceRequestResourceInstanceList>
                                instanceListList = new ArrayList<>();
                        resultList.forEach(resourceInstance -> instanceListList.add(new AllocateCostUnitResourceRequest.AllocateCostUnitResourceRequestResourceInstanceList().setResourceId(resourceInstance.getResourceId())
                                .setCommodityCode(resourceInstance.getCommodityCode()).setApportionCode(ObjectUtils.isEmpty(resourceInstance.getApportionCode())?null:resourceInstance.getApportionCode()).setResourceUserId(resourceInstance.getResourceUserId())));
                        allocateCostUnitResourceRequest.setResourceInstanceList(instanceListList);
                        String errorMessage = AliyunApi.allocateCostUnitResourceWithOptions(accessKey, accessKeySecret, allocateCostUnitResourceRequest);
                       if(!ObjectUtils.isEmpty(errorMessage)){
                            reconciliationTaskService.updateTaskStatus(resultList,2,errorMessage);
                            logger.error("更新阿里云财务单元失败，{}", JSON.toJSONString(resultList));
                        }else{
                            //更新任务状态已完成
                            reconciliationTaskService.updateTaskStatus(resultList,1,null);
                            //更新资源表财务单元
                            resourceInstanceService.updateCostUnit(resultList, finalUnitName,finalUnitId);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        reconciliationTaskService.updateTaskStatus(resultList,2,JSON.toJSONString(e));
                        logger.error("更新阿里云财务单元失败，{}", JSON.toJSONString(resultList));
                    }finally {
                        latch.countDown();
                    }
                });
            });
            latch.await();
        }

    }

    /**
    * @Description: 更新财务单元的资源信息
    * @author: 张爱苹
    * @date: 2024/3/19 18:30

    * @Return: void
    */
    @XxlJob("updateResourceInstanceJobHandler")
    public void updateResourceInstanceJobHandler() throws Exception{
        AtomicInteger totalCount = new AtomicInteger(0);
        logger.info("更新阿里云财务单元【updateResourceInstanceJobHandler】开始------------");
        try{
            //获取阿里云主账号列表
            List<CloudAccount> cloudAccountList = cloudAccountService.getAliyunDedicatedAccountList();
            if(!CollectionUtils.isEmpty(cloudAccountList)){
                //获取阿里云产品明细关系
                List<CloudProductRelation>  cloudProductRelationList = cloudProductRelationService.list();
                Map <String,CloudProductRelation> cloudProductRelationMap = cloudProductRelationList.stream().collect(Collectors.toMap(CloudProductRelation::getCommodityCode,item -> item));
                cloudAccountList.stream().forEach(cloudAccount ->{
              //      if(cloudAccount.getAccountName().contains("haier31")){
                        //根据账号获取实例信息
                        List<Map> infoList = resourceInstanceService.queryInfoByAccountId(cloudAccount.getAccountIdentify());
                        Map<String,Integer> resourceInstanceMap = infoList.stream().collect(Collectors.toMap(item -> item.get("resourceId").toString(),item -> Integer.parseInt(item.get("id").toString())));
                        String accessKey = cloudAccount.getAccessKey();
                        String accessKeySecret = cloudAccount.getAccessKeySecret();
                        String encrypted = cloudAccount.getEncrypted();
                        if (!"0".equals(encrypted)) {
                            accessKey = AesUtil.decryptCBC(accessKey);
                            accessKeySecret = AesUtil.decryptCBC(accessKeySecret);
                            cloudAccount.setAccessKey(accessKey);
                            cloudAccount.setAccessKeySecret(accessKeySecret);
                        }
                        List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> resultList = null;
                        try {
                            resultList = getCostUnitList(accessKey, accessKeySecret,Long.valueOf(cloudAccount.getAccountIdentify()));
                            QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList unitDto = new QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList();
                            unitDto.setUnitId(0L);
                            resultList.add(unitDto);
                            if(!CollectionUtils.isEmpty(resultList)){
                                //线程计数器
                                CountDownLatch latch = new CountDownLatch(resultList.size());
                                resultList.stream().forEach(result ->{
                                    localBootAsyncExecutor.execute(() ->{
                                        try {
                                            int count = getResourceInstanceList(cloudAccount,result.getUnitId(),cloudProductRelationMap,resourceInstanceMap);
                                            logger.info(cloudAccount.getAccountIdentify()+","+result.getUnitId()+"更新资源"+count+"条");
                                            totalCount.getAndAdd(count);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }finally {
                                            latch.countDown();
                                        }
                                    });
                                });
                                latch.await();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
              //      }

                });
            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error("更新阿里云财务单元【updateResourceInstanceJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("更新阿里云财务单元【updateResourceInstanceJobHandler】结束,更新资源{}条------------",totalCount);
    }

    private int getResourceInstanceList(CloudAccount cloudAccount, Long unitId,Map <String,CloudProductRelation> cloudProductRelationMap,Map<String,Integer> resourceInstanceMap) throws Exception {
        int count = 0;
        QueryCostUnitResourceRequest resourceRequest = new QueryCostUnitResourceRequest();
        long ownerUid = Long.valueOf(cloudAccount.getAccountIdentify());
        resourceRequest.setOwnerUid(ownerUid);
        resourceRequest.setUnitId(unitId);
        int pageNum = 1;
        int pageSize = 300;
        while (true){
            List<ResourceInstance> resourceInstanceList = new ArrayList<>();
            resourceRequest.setPageNum(pageNum);
            resourceRequest.setPageSize(pageSize);
            QueryCostUnitResourceResponseBody.QueryCostUnitResourceResponseBodyData data = AliyunApi.queryCostUnitResourceWithOptions(cloudAccount.getAccessKey(), cloudAccount.getAccessKeySecret(),resourceRequest);
            if(data == null){
                break;
            }
            List<QueryCostUnitResourceResponseBody.QueryCostUnitResourceResponseBodyDataResourceInstanceDtoList> list = data.getResourceInstanceDtoList();
            QueryCostUnitResourceResponseBody.QueryCostUnitResourceResponseBodyDataCostUnit costUnitResourceResponseBodyDataCostUnit = data.getCostUnit();
            long fromUnitId = costUnitResourceResponseBodyDataCostUnit == null?0L:costUnitResourceResponseBodyDataCostUnit.getUnitId();
            String fromUnitName = costUnitResourceResponseBodyDataCostUnit == null?"未分配":costUnitResourceResponseBodyDataCostUnit.getUnitName();
            for (int i = 0; i < list.size(); i++) {
                QueryCostUnitResourceResponseBody.QueryCostUnitResourceResponseBodyDataResourceInstanceDtoList resourceInstanceDtoList = list.get(i);
                ResourceInstance resourceInstance = new ResourceInstance();
                if(resourceInstanceDtoList.getResourceUserId() == ownerUid){
                    BeanUtils.copyProperties(resourceInstanceDtoList, resourceInstance);
                    String info = new StringBuffer(resourceInstance.getCommodityCode()).append(ObjectUtils.isEmpty(resourceInstance.getApportionCode())?"" : resourceInstance.getApportionCode()).append(resourceInstance.getResourceId()).toString();
                    if(!ObjectUtils.isEmpty(resourceInstanceMap.get(info))){
                        resourceInstance.setId(resourceInstanceMap.get(info));
                        resourceInstance.setUpdateTime(new Date());
                    }
                    resourceInstance.setCostUnitId(String.valueOf(fromUnitId));
                    resourceInstance.setCostUnit(fromUnitName);
                    CloudProductRelation cloudProductRelation = cloudProductRelationMap.get(resourceInstance.getCommodityCode());
                    resourceInstance.setProductCode(cloudProductRelation == null? null : cloudProductRelation.getProductCode());
                    resourceInstanceList.add(resourceInstance);
                }
            }
            if(!CollectionUtils.isEmpty(resourceInstanceList)){
                resourceInstanceService.saveOrUpdateBatch(resourceInstanceList);
            }
            count = count + resourceInstanceList.size();
            if(list.size() < pageSize){
                break;
            }
            pageNum = pageNum + 1;
        }
        return count;
    }

    private Map<String, List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList>> getScodeUnitMap(String accessKeyId, String accessKeySecret,Long accountId) throws Exception{
        List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> resultList = getCostUnitList(accessKeyId, accessKeySecret, accountId);
        if(!CollectionUtils.isEmpty(resultList)){
            Map resultMap = resultList.stream().collect(Collectors.groupingBy(item ->{
                String[] parts = item.getUnitName().split("-");
                return parts[parts.length-1];
            }));
            return resultMap;
        }
        return new HashMap<>();
    }

    @NotNull
    private List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> getCostUnitList(String accessKeyId, String accessKeySecret, Long accountId) throws Exception {
        List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> resultList = new ArrayList<>();
        QueryCostUnitRequest request = new QueryCostUnitRequest();
        request.setOwnerUid(accountId);
        request.setParentUnitId(-1L);
        int pageNum = 1;
        int pageSize = 300;
        while (true){
            request.setPageNum(pageNum);
            request.setPageSize(pageSize);
            List<QueryCostUnitResponseBody.QueryCostUnitResponseBodyDataCostUnitDtoList> list = AliyunApi.getCostUnitDtoList(accessKeyId, accessKeySecret,request);
            resultList.addAll(list);
            if(list.size() < pageSize){
                break;
            }
            pageNum = pageNum + 1;
        }
        return resultList;
    }

    private void extracteUpdateTag(String scode, List<ResourceInstanceVo> voList, String accessKey, String accessKeySecret) throws Exception {
        //根据消费类型分组
        Map<String, List<ResourceInstanceVo>> voMap = voList.stream().collect(Collectors.groupingBy(ResourceInstanceVo::getResourceType));
        for (Map.Entry<String, List<ResourceInstanceVo>> entry3 : voMap.entrySet()) {
            //线程计数器
            CountDownLatch latch = new CountDownLatch(entry3.getValue().size());
            entry3.getValue().stream().forEach(result ->{
                localBootAsyncExecutor.execute(() ->{
                    List<ResourceInstanceVo> resultList = entry3.getValue();
                    try {
                        List<String> instanceIdList = entry3.getValue().stream().map(ResourceInstanceVo::getResourceId).collect(Collectors.toList());
                        TagResourcesRequest
                                tagResourcesRequest = new TagResourcesRequest();
                        tagResourcesRequest.setResourceType(entry3.getKey());
                        tagResourcesRequest.setResourceId(instanceIdList);
                        TagResourcesRequest.TagResourcesRequestTag tag = new TagResourcesRequest.TagResourcesRequestTag();
                        tag.setKey("appscode");
                        tag.setValue(scode);
                        tagResourcesRequest.setTag(Collections.singletonList(tag));
                        Map map = AliyunApi.tagResourcesWithOptions(accessKey, accessKeySecret,tagResourcesRequest);

                        String errorMsg = (String) map.get("errorMsg");
                        if(!StringUtils.isEmpty(errorMsg)){
                            reconciliationTaskService.updateTaskStatus(resultList,2,errorMsg);
                            logger.error("更新阿里云tag元失败，{}",JSON.toJSONString(resultList));
                        }else{
                            boolean data = (boolean) map.get("data");
                            if(!data){
                                reconciliationTaskService.updateTaskStatus(resultList,2,"");
                                logger.error("更新阿里云tag元失败，{}",JSON.toJSONString(resultList));
                            }else{
                                //更新任务状态已完成
                                reconciliationTaskService.updateTaskStatus(resultList,1,null);
                            }
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        reconciliationTaskService.updateTaskStatus(resultList,2,JSON.toJSONString(e));
                        logger.error("更新阿里云tag元失败，{}",JSON.toJSONString(resultList));
                    }finally {
                        latch.countDown();
                    }
                });
            });
            latch.await();
        }
    }

    /**
    * @Description: 更新阿里云资源组名称
    * @author: 张爱苹
    * @date: 2024/3/22 10:58

    * @Return: void
    */
    @XxlJob("updateAliResourceGroupJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void updateAliResourceGroupJobHandler() throws Exception{
        logger.info("更新阿里云资源组名称【updateAliResourceGroupJobHandler】开始------------");
        try{
            //查询阿里云调账任务，调账类型为costUnit且状态是未执行的数据
            List<ReconciliationTask> reconciliationTaskList = reconciliationTaskService.queryList(ReconciliationTaskTypeEnum.RESOURCEGROUP.getKey());
            //查云账号下的实例信息
            if(!CollectionUtils.isEmpty(reconciliationTaskList)){
                //根据要调账S码分组
                Map<String,HdsSubProducts> appScodeMap = getAppScodeMap(reconciliationTaskList.stream().map(ReconciliationTask::getScode).distinct().collect(Collectors.toList()));
                //根据账号分组
                Map<String, List<ReconciliationTask>> resourceInstanceMap = reconciliationTaskList.stream().collect(Collectors.groupingBy(ReconciliationTask::getAccount));
                for (Map.Entry<String, List<ReconciliationTask>> entry : resourceInstanceMap.entrySet()){
                    Map<String,String> keyMap = cloudAccountService.getOpenApiKey(VendorEnum.ALIYUN.getVendor(), entry.getKey());
                    String accessKeyId = keyMap.get("accessKey");
                    String accessKeySecret = keyMap.get("accessKeySecret");
                    //查资源组
                    Map<String,List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup>> resourceGroupMap = getResourceGroupList(accessKeyId, accessKeySecret);
                    if(CollectionUtils.isEmpty(resourceGroupMap)){
                       continue;
                    }
                    List<ReconciliationTask> taskList = entry.getValue();
                    for (ReconciliationTask task : taskList){
                        String originalScode = task.getOriginalScode();
                        //查原S码对应的资源组id
                        List<String> groupIdList = getResourceGroupIdList(resourceGroupMap,originalScode);
                        groupIdList.stream().forEach(groupId ->{
                            //更新资源组名称
                            UpdateResourceGroupRequest updateResourceGroupRequest = new UpdateResourceGroupRequest();
                            updateResourceGroupRequest.setResourceGroupId(groupId);
                            if(!ObjectUtils.isEmpty(appScodeMap.get(task.getScode()))){
                                updateResourceGroupRequest.setNewDisplayName(new StringBuffer(appScodeMap.get(task.getScode()).getAppName() == null?"":appScodeMap.get(task.getScode()).getAppName()).append("-").append(task.getScode()).toString());
                                try {
                                    UpdateResourceGroupResponseBody.UpdateResourceGroupResponseBodyResourceGroup group = AliyunApi.updateResourceGroupWithOptions(accessKeyId, accessKeySecret,updateResourceGroupRequest);
                                    task.setStatus(1);
                                    task.setExecuteTime(new Date());
                                    if(group == null){
                                        logger.error("更新资源组失败，groupId:{}",groupId);
                                        task.setErrorNum(task.getErrorNum() + 1);
                                        task.setStatus(2);
                                    }
                                    reconciliationTaskService.updateById(task);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        });

                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("更新阿里云资源组名称【updateAliResourceGroupJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("更新阿里云资源组名称【updateAliResourceGroupJobHandler】结束------------");
    }

    private List<String> getResourceGroupIdList(Map<String, List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup>> resourceGroupMap, String originalScode) {
        List<String> groupIdList = new ArrayList<>();
        for (Map.Entry<String, List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup>> entry : resourceGroupMap.entrySet()){
            String displayName = entry.getKey();
            List<String> idList = entry.getValue().stream()
                    .map(ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup::getId)
                    .collect(Collectors.toList());
            if(originalScode.equals("默认资源组") && displayName.contains("默认资源组")){
                groupIdList.addAll(idList);
                break;
            }
            if(displayName.contains("-"+originalScode)){
                groupIdList.addAll(idList);
            }
        }
        return groupIdList;
    }

    private Map<String,List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup>> getResourceGroupList(String accessKeyId, String accessKeySecret) throws Exception {
        ListResourceGroupsRequest resourceGroupsRequest = new ListResourceGroupsRequest();
        List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup> resultList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 100;
        while (true){
            resourceGroupsRequest.setPageNumber(pageNum);
            resourceGroupsRequest.setPageSize(pageSize);
            ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroups groupsResponseBodyResourceGroups = AliyunApi.listResourceGroupsWithOptions(accessKeyId, accessKeySecret,resourceGroupsRequest);
            List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup> resourceGroupList = groupsResponseBodyResourceGroups.getResourceGroup();
            resultList.addAll(resourceGroupList);
            if(resourceGroupList.size() < pageSize){
                break;
            }
            pageNum = pageNum + 1;
        }
        if(!CollectionUtils.isEmpty(resultList)){
            Map<String,List<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup>> map = resultList.stream().collect(Collectors.groupingBy(ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup::getDisplayName));
            return map;
        }
        return null;
    }


    @XxlJob("createAliResourceGroupJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void createAliResourceGroupJobHandler() throws Exception{
        logger.info("创建阿里云资源组【createAliResourceGroupJobHandler】开始------------");
        try{
            Map<String,String> keyMap = cloudAccountService.getOpenApiKey(VendorEnum.ALIYUN.getVendor(),"<EMAIL>");
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            CreateResourceGroupRequest createResourceGroupRequest = new CreateResourceGroupRequest();
            createResourceGroupRequest.setDisplayName("测试-S10");
            createResourceGroupRequest.setName("test");
            AliyunApi.createResourceGroupWithOptions(accessKeyId,accessKeySecret,createResourceGroupRequest);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("创建阿里云资源组【createAliResourceGroupJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("创建阿里云资源组【createAliResourceGroupJobHandler】结束------------");
    }

    @XxlJob("delAliResourceGroupJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void delAliResourceGroupJobHandler() throws Exception{
        logger.info("删除阿里云资源组【delAliResourceGroupJobHandler】开始------------");
        try{
            Map<String,String> keyMap = cloudAccountService.getOpenApiKey(VendorEnum.ALIYUN.getVendor(),"<EMAIL>");
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            DeleteResourceGroupRequest deleteResourceGroupRequest = new DeleteResourceGroupRequest();
            deleteResourceGroupRequest.setResourceGroupId("rg-aekz6gebt35v64a");
            AliyunApi.deleteResourceGroupWithOptions(accessKeyId,accessKeySecret,deleteResourceGroupRequest);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("删除阿里云资源组【delAliResourceGroupJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("删除阿里云资源组【delAliResourceGroupJobHandler】结束------------");
    }

    @XxlJob("delAliCostUnitJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void delAliCostUnitJobHandler() throws Exception{
        logger.info("删除阿里云财务单元【delAliCostUnitJobHandler】开始------------");
        try{
            Map<String,String> keyMap = cloudAccountService.getOpenApiKey(VendorEnum.ALIYUN.getVendor(),"<EMAIL>");
            String accessKeyId = keyMap.get("accessKey");
            String accessKeySecret = keyMap.get("accessKeySecret");
            DeleteCostUnitRequest deleteCostUnitRequest = new DeleteCostUnitRequest();
            deleteCostUnitRequest.setOwnerUid(1297347225382626L);
            deleteCostUnitRequest.setUnitId(559105L);
            AliyunApi.deleteCostUnitWithOptions(accessKeyId,accessKeySecret,deleteCostUnitRequest);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("删除阿里云财务单元【delAliCostUnitJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("删除阿里云财务单元【delAliCostUnitJobHandler】结束------------");
    }

    /**
    * @Description: 阿里云产品大类与产品子类同步
    * @author: 张爱苹
    * @date: 2024/9/24 11:24

    * @Return: void
    */
    @XxlJob("saveCommodityJobHandler")
    @Transactional(rollbackFor = Exception.class)
    public void saveCommodityJobHandler() throws Exception{
        logger.info("阿里云产品大类与产品子类同步【saveCommodityJobHandler】开始------------");
        try{
            RawBillProductsDTO dto  =new RawBillProductsDTO();
            dto.setVendor("aliyun");
            List<RawBillProducts> productList = productsService.getRawBillProductList(dto);
            if(!CollectionUtils.isEmpty(productList)){
                List<CloudAccount> cloudAccountList = cloudAccountService.getAliyunAccountList();
                if(!CollectionUtils.isEmpty(cloudAccountList)){
                    List<CloudProductRelation> resultList = new ArrayList<>();
                    for (int i = 0; i < productList.size(); i++) {
                        String productCode = productList.get(i).getProductCode();
                        //线程计数器
                        CountDownLatch latch = new CountDownLatch(cloudAccountList.size());
                        cloudAccountList.stream().forEach(cloudAccount ->{
                            localBootAsyncExecutor.execute(() ->{
                                try {
                                    String accessKey = cloudAccount.getAccessKey();
                                    String accessKeySecret = cloudAccount.getAccessKeySecret();
                                    String encrypted = cloudAccount.getEncrypted();
                                    if (!"0".equals(encrypted)) {
                                        accessKey = AesUtil.decryptCBC(accessKey);
                                        accessKeySecret = AesUtil.decryptCBC(accessKeySecret);
                                    }
                                    List<CloudProductRelation> list = saveCloudProductRelation(productCode, cloudAccount, accessKey, accessKeySecret);
                                    resultList.addAll(list);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }finally {
                                    latch.countDown();
                                }
                            });
                        });
                        latch.await();
                    }
                    if(!CollectionUtils.isEmpty(resultList)){
                        List<CloudProductRelation> addList = new ArrayList<>();
                        List<String> sbList = new ArrayList<>();
                        resultList.stream().forEach(relation -> {
                            String sb = new StringBuffer(relation.getProductCode()).append("_").append(relation.getCommodityCode()).toString();
                            if(!sbList.contains(sb)){
                                sbList.add(sb);
                                CloudProductRelation old =  cloudProductRelationService.getEntity(relation.getProductCode(),relation.getCommodityCode());
                                if(old == null){
                                    addList.add(relation);
                                }
                            }
                        });
                        if(!CollectionUtils.isEmpty(addList)){
                            cloudProductRelationService.saveBatch(addList);
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("阿里云产品大类与产品子类同步【saveCommodityJobHandler】异常------------",e.getMessage(),e);
            throw new RuntimeException();
        }
        logger.info("阿里云产品大类与产品子类同步【saveCommodityJobHandler】结束------------");
    }

    private List<CloudProductRelation> saveCloudProductRelation(String productCode, CloudAccount cloudAccount, String accessKey, String accessKeySecret) {
        QueryCommodityListRequest queryCommodityListRequest = new QueryCommodityListRequest();
        queryCommodityListRequest.setProductCode(productCode);
        queryCommodityListRequest.setLang("en");
        List<CloudProductRelation> list = new ArrayList<>();
        //根据产品code查询商品信息列表
        try{
            QueryCommodityListResponse response = AliyunApi.queryCommodityList(accessKey, accessKeySecret,queryCommodityListRequest);
            List<QueryCommodityListResponseBody.QueryCommodityListResponseBodyDataCommodityList> commodityListResponseBodyDataCommodityList = response.getBody().getData().getCommodityList();

            if(!CollectionUtils.isEmpty(commodityListResponseBodyDataCommodityList)){

                commodityListResponseBodyDataCommodityList.stream().forEach(entity ->{
                    CloudProductRelation relation = new CloudProductRelation();
                    relation.setProductCode(productCode);
                    relation.setCommodityCode(entity.getCommodityCode());
                    list.add(relation);
                });
            }
        }catch (Exception e){
          e.printStackTrace();
          logger.error("获取商品信息失败{}{}", cloudAccount.getAccountName(), productCode);
        }
        return list;
    }
}
