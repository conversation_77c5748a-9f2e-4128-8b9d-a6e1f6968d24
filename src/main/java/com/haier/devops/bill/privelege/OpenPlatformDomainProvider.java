package com.haier.devops.bill.privelege;

import com.haier.devops.bill.common.api.HworkBillApi;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * hwork能力平台查询个人的领域权限以及默认领域的产品、子产品
 */
@Component
@Slf4j
public class OpenPlatformDomainProvider implements DomainProvider {
    private final HworkBillApi hworkBillApi;

    public OpenPlatformDomainProvider(HworkBillApi hworkBillApi) {
        this.hworkBillApi = hworkBillApi;
    }

    @Override
    public List<String> getDomains(String userCode) {
        ResultWrapper<HworkBillApi.AuthAppForItResponse> response = hworkBillApi.getAuthAppForIt(
                new HworkBillApi.AuthAppForItRequest(null, null, userCode));
        if (HttpStatus.OK.value() != response.getCode()) {
            log.error("查询能力平台用户权限(userId = {})失败：{}", userCode, response.getMsg());
            throw new DomainFindingException(response.getMsg());
        }

        List<HworkBillApi.Domain> domains = response.getData().getDomains();
        return domains.stream()
                .map(HworkBillApi.Domain::getDomainCode)
                .collect(Collectors.toList());
    }
}
