package com.haier.devops.bill.privelege;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * hwork权限中心领域管理维
 */
@Component
@Slf4j
public class AdministratorDomainProvider implements DomainProvider {
    @Value("${domain.code}")
    private String domainCode;
    private final HworkAuthorityApi hworkAuthorityApi;

    public AdministratorDomainProvider(HworkAuthorityApi hworkAuthorityApi) {
        this.hworkAuthorityApi = hworkAuthorityApi;
    }

    @Override
    public List<String> getDomains(String userCode) {
        HworkAuthorityApi.ResultWrapper<List<HworkAuthorityApi.Authority>> result = hworkAuthorityApi.getUserAuthorities(userCode, domainCode);

        if (HttpStatus.OK.value() != result.getCode()) {
            log.error("查询用户(userId = {})所拥有的领域管理维失败：{}", userCode, result.getMsg());
            throw  new DomainFindingException(result.getMsg());
        }

        return result.getData().stream()
                .filter(authority -> DIMENSION_CODE.equals(authority.getDimensionCode()))
                .map(HworkAuthorityApi.Authority::getAuthorityCode)
                .collect(Collectors.toList());
    }
}
