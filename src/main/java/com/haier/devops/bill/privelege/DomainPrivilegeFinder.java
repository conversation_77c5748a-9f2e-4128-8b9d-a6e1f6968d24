package com.haier.devops.bill.privelege;

import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.service.HdsSubProductsService;
import com.haier.devops.bill.common.vo.DomainSubProductVo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DomainPrivilegeFinder {
    private final ExecutorService executor = Executors.newFixedThreadPool(5);;

    private final HdsSubProductsService subProductsService;

    private final AdministratorDomainProvider administratorDomainProvider;
    private final HcmsDomainProvider hcmsDomainProvider;
    private final OpenPlatformDomainProvider openPlatformDomainProvider;

    public DomainPrivilegeFinder(ApplicationContext context, HdsSubProductsService subProductsService) {
        this.subProductsService = subProductsService;

        this.administratorDomainProvider = context.getBean(AdministratorDomainProvider.class);
        this.hcmsDomainProvider = context.getBean(HcmsDomainProvider.class);
        this.openPlatformDomainProvider = context.getBean(OpenPlatformDomainProvider.class);
    }

    /**
     * 查询领域、子产品权限
     * @param userId
     * @param isAdmin
     * @return
     */
    public List<DomainSubProductVo> findDomainPrivileges(String userId, Boolean isAdmin,List<String> domainList) throws Exception {
        List<String> domains = new ArrayList<>();
        if(!CollectionUtils.isEmpty(domainList)){
            domains.addAll(domainList);
        }else{
            if(isAdmin){
                return subProductsService.getAllDomainSubProductVoList();
            }
            domains = getDomainList(userId,"");
        }
        return subProductsService.getDomainSubProductVoList(new ArrayList<>(new HashSet<>(domains)));
    }

    /**
     * 查询领域、子产品权限
     * @param userId
     * @param isAdmin
     * @return
     */
    public List<DomainSubProductVo> findDomainPrivileges(String userId, Boolean isAdmin) throws Exception {


        if (Boolean.TRUE.equals(isAdmin)) {
            // 管理员返回所有
            return subProductsService.getAllDomainSubProductVoList();
        }
        List<String> domains = getDomainList(userId,"");


        return subProductsService.getDomainSubProductVoList(new ArrayList<>(new HashSet<>(domains)));
    }

    @NotNull
    public List<String> getDomainList(String userId,String role) throws InterruptedException, ExecutionException {
        List<CompletableFuture<List<String>>> futures = new ArrayList<>();
        // 创建并提交异步任务
        futures.add(CompletableFuture.supplyAsync(() -> administratorDomainProvider.getDomains(userId), executor));
        futures.add(CompletableFuture.supplyAsync(() -> hcmsDomainProvider.getDomains(userId), executor));
        if(!"domainAdmin".equals(role)){
            futures.add(CompletableFuture.supplyAsync(() -> openPlatformDomainProvider.getDomains(userId), executor));
        }
        //allOf等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        // 处理结果，如果其中一个future失败，那么这里会抛出异常
        try {
            // 等待所有任务完成
            allFutures.get();
        } catch (Exception e) {
            // 处理异常，例如，记录日志，熔断后续请求
            log.error("One or more requests failed: {}", e.getMessage());
            throw e;
        }

        List<String> domains = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return domains;
    }

    public List<HdsOpenApi.Domain> queryDomainList() {
         return subProductsService.getDomainList();
    }
}
