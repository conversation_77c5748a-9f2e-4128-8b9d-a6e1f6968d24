package com.haier.devops.bill.privelege;

import com.haier.devops.bill.common.service.DataPrivilegeService;
import com.haier.devops.bill.common.vo.UserDataPrivilegeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 云管设置的领域维
 */
@Component
@Slf4j
public class HcmsDomainProvider implements DomainProvider {
    private final DataPrivilegeService privilegeService;

    public HcmsDomainProvider(DataPrivilegeService privilegeService) {
        this.privilegeService = privilegeService;
    }

    @Override
    public List<String> getDomains(String userCode) {
        List<UserDataPrivilegeVo> userDataPrivilegeVos = privilegeService.selectUserDataPrivilegeByDimension(DIMENSION_CODE, userCode);
        return userDataPrivilegeVos.stream()
                .map(UserDataPrivilegeVo::getPrivilegeCode)
                .collect(Collectors.toList());
    }
}
