package com.haier.devops.bill.contract;

import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi.ResultWrapper;
import com.haier.devops.bill.common.api.HworkAuthorityApi.User;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

@Slf4j
public class HcmsProject {
    private static final String U_INFRASTRUCTURE = "U+基础设施";
    private static final String PUBLIC_CLOUD_SECURITY = "公有云信息安全";

    private HdsOpenApi hdsOpenApi;
    private HworkAuthorityApi hworkAuthorityApi;

    private HdsOpenApi.AlmProject project;

    /**
     * S码
     */
    @Getter
    @Setter
    private String scode;

    /**
     * 项目名称-S码
     */
    @Getter
    @Setter
    private String displayName;

    @Getter
    @Setter
    private String projectOwnerId;

    @Getter
    @Setter
    private String domain;

    @Getter
    @Setter
    private User owner;

    @Getter
    @Setter
    private User ownerLeader;

    @Getter
    @Setter
    private User ownerDeptLeader;

    public HcmsProject(HdsOpenApi hdsOpenApi, HworkAuthorityApi hworkAuthorityApi) {
        this.hdsOpenApi = hdsOpenApi;
        this.hworkAuthorityApi = hworkAuthorityApi;
    }

    public HcmsProject init(String scode) {
        this.scode = scode;
        this.initProjectInfo();
        this.initUserInfo();

        switch (this.ownerDeptLeader.getUserName()) {
            case "00970820":
                // 高丽 --> 薛冰
                this.ownerDeptLeader = mustGetUserInfo("01462834");
            case "00960314":
                // 王滨后 --> 宋淑姣
                this.ownerDeptLeader = mustGetUserInfo("00950805");
        }

        return this;
    }

    private void initProjectInfo() {
        if (this.scode.equals(U_INFRASTRUCTURE) || this.scode.equals(PUBLIC_CLOUD_SECURITY)) {
            this.initCustomProject();
            return;
        }

        HdsOpenApi.AlmProjectWrapper projectWrapper = hdsOpenApi.queryAlmProject(this.scode);
        if (null == projectWrapper || projectWrapper.getCode() != 10000) {
            throw new RuntimeException("查询项目信息失败");
        }
        this.project = projectWrapper.getData();

        this.displayName = project.getName() + "-" + project.getAlmSCode();
        this.projectOwnerId = project.getOwner();
        this.domain = project.getDomain();
    }

    private void initCustomProject() {
        switch (this.scode) {
            case U_INFRASTRUCTURE:
                this.displayName = U_INFRASTRUCTURE;
                this.projectOwnerId = "01433537";
                this.domain = "IT";
            case PUBLIC_CLOUD_SECURITY:
                this.displayName = PUBLIC_CLOUD_SECURITY;
                this.projectOwnerId = "20116850";
                this.domain = "IT";
        }
    }

    private void initUserInfo() {
        this.initOwner();
        this.initOwnerLeader();
        this.initSmallMicroLeader();
    }

    private void initOwner() {
        this.owner = mustGetUserInfo(this.project.getOwner());
    }

    private void initOwnerLeader() {
        if (isSmallMicroLeader(this.owner)) {
            this.ownerLeader = this.owner;
            return;
        }

        this.ownerLeader = mustGetUserInfo(this.owner.getFirstLineId());
    }

    private void initSmallMicroLeader() {
        this.ownerDeptLeader = mustFindSmallMicroLeader(this.owner);
    }


    private User mustFindSmallMicroLeader(User user) {
        if (null == user) {
            return null;
        }

        if (isSmallMicroLeader(user)) {
            return user;
        }

        if (StringUtils.isBlank(user.getFirstLineId())) {
            return null;
        }

        User firstLineUser = mustGetUserInfo(user.getFirstLineId());
        if (isSmallMicroLeader(firstLineUser)) {
            return firstLineUser;
        }

        if (StringUtils.isBlank(firstLineUser.getFirstLineId())) {
            return null;
        }

        if (isIndustryLeader(firstLineUser) || isPlatformLeader(firstLineUser)) {
            return firstLineUser;

        }

        return mustFindSmallMicroLeader(firstLineUser);
    }

    private User mustGetUserInfo(String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new RuntimeException("用户ID不能为空");
        }
        ResultWrapper<User> userDetailWrapper = hworkAuthorityApi.getUserDetail(userId, false);
        if (userDetailWrapper.getCode() != HttpStatus.OK.value()) {
            throw new RuntimeException(userDetailWrapper.getMessage());
        }

        User user = userDetailWrapper.getData();
        if (null == user) {
            throw new RuntimeException("用户不存在");
        }

        return user;
    }

    private boolean isSmallMicroLeader(User user) {
        if (null == user) {
            return false;
        }

        return user.getGwName().contains("小微主");
    }

    private boolean isIndustryLeader(User user) {
        if (null == user) {
            return false;
        }

        return user.getGwName().contains("行业主");
    }

    private boolean isPlatformLeader(User user) {
        if (null == user) {
            return false;
        }

        return user.getGwName().contains("平台主");
    }
}
