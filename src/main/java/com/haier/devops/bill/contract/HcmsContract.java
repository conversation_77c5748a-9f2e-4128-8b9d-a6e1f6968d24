package com.haier.devops.bill.contract;

import com.alibaba.excel.util.StringUtils;
import com.aliyun.oss.OSSException;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.Contract;
import com.haier.devops.bill.sdks.aliyun.ContractOssClient;
import com.haier.devops.bill.sdks.aliyun.ImmClient;
import com.haier.devops.bill.sdks.haier.SignUtil;
import com.haier.devops.bill.sdks.haier.SignatureClient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class HcmsContract {
    private static final String SEARCH_TEXT = "签名：";
    private static final String BUSINESS = "business";
    private static final String PSI = "psi";
    private static final String UPLUS = "uplus";

    private static final String reportTemplateFilePath = "template/template_ITServiceStageAcceptanceReport.docx";
    private static final String businessVersionOfReportTemplateFilePath = "template/template_ITServiceStageAcceptanceReport_business_v1.docx";
    private static final String psiVersionOfReportTemplateFilePath = "template/template_ITServiceStageAcceptanceReport_psi.docx";
    private static final String uplusVersionOfReportTemplateFilePath = "template/template_ITServiceStageAcceptanceReport_uplus.docx";

    /**
     * 合同对象
     */
    private Contract contract;
    /**
     * 合同文档
     */
    private XWPFDocument document;

    /**
     * 合同项目
     */
    private List<HcmsProject> projects = new ArrayList<>();

    /**
     * 供应商
     */
    private String vendor;

    /**
     * 合同名称
     */
    private String name;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 阶段开始时间
     */
    private String stageStart;

    /**
     * 阶段结束时间
     */
    private String stageEnd;

    /**
     * 预算
     */
    private String budget;

    /**
     * 当前签署索引
     */
    private int currentSigningIndex;

    /**
     * 乙方负责人
     */
    private String partBUserId;

    /**
     * it负责人
     */
    private List<String> owners = new ArrayList<>();

    /**
     * 团队接口人
     */
    private List<String> ownerLeaders = new ArrayList<>();

    /**
     * 小微主
     */
    private List<String> smallMicroOwners = new ArrayList<>();

    /**
     * 执行周期开始时间
     */
    private String executionCycleStart;

    /**
     * 执行周期结束时间
     */
    private String executionCycleEnd;

    /**
     * 验收日期
     */
    private String acceptanceDate;

    /**
     * 验收状态
     */
    private String acceptanceState;

    /**
     * 签订日期
     */
    private String signDate;

    /**
     * 金额
     */
    private String cost;

    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 文件路径
     */
    private String filePath;


    private SignatureClient signatureClient;
    private ContractOssClient ossClient;
    private ImmClient immClient;

    Consumer<XWPFRun> contractConsumer = null;

    Function<String, SignatureClient.SignInfo> getUserSignatureFunc = null;

    public void initContractProjects(
                                     HdsOpenApi hdsOpenApi,
                                     HworkAuthorityApi hworkAuthorityApi,
                                     List<String> scodes) {
        if (null == this.projects) {
            this.projects = new ArrayList<>();
        }
        for (String scode : scodes) {
            this.projects.add(new HcmsProject(hdsOpenApi, hworkAuthorityApi).init(scode));
        }
    }

    public void init() throws IOException {
        this.contractConsumer = r -> {
            String text = r.getText(0);
            if (StringUtils.isNotBlank(text)) {
                String t = text.trim();
                switch (t) {
                    case "param1":
                        r.setText(text.replace(t, contract.getContractName()), 0);
                        return;
                    case "param2":
                        String projectsName = projects.stream().map(HcmsProject::getDisplayName).collect(Collectors.joining("、"));
                        r.setText(text.replace(t, projectsName), 0);
                        return;
                    case "param_vendor":
                        r.setText(text.replace(t, vendor), 0);
                        return;
                    case "cyclestart":
                        r.setText(text.replace(t, executionCycleStart), 0);
                        return;
                    case "cycleend":
                        r.setText(text.replace(t, executionCycleEnd), 0);
                        return;
                    case "stage":
                        r.setText(text.replace(t, stage), 0);
                        return;
                    case "stagestart":
                        r.setText(text.replace(t, stageStart), 0);
                        return;
                    case "stageend":
                        r.setText(text.replace(t, stageEnd), 0);
                        return;
                    case "budget":
                        r.setText(text.replace(t, budget), 0);
                        return;
                    case "acceptancedate":
                        r.setText(text.replace(t, acceptanceDate), 0);
                        return;
                    case "acceptancestate":
                        r.setText(text.replace(t, acceptanceState), 0);
                        return;
                    case "cost":
                        r.setText(text.replace(t, cost), 0);
                        return;
                    case "signdate1":
                    case "signdate2":
                    case "signdate3":
                    case "signdate4":
                        r.setText(text.replace(t, signDate), 0);
                        return;
                    case "namespace":
                        r.setText(text.replace(t, resolvingNamespace()), 0);
                        return;
                    default:
                }
            }
        };

        this.getUserSignatureFunc = uid -> {
            List<SignatureClient.SignInfo> signInfos = null;
            try {
                signInfos = this.signatureClient.get(uid);
            } catch (IOException | URISyntaxException e) {
                log.error("Failed to get signature for user {}", uid, e);
                throw new SignatureNotFoundException(uid);
            }
            if (signInfos.isEmpty()) {
                throw new SignatureNotFoundException(uid);
            }
            return signInfos.get(0);
        };


        // 将模板读取到document属性中
        this.initDocxFromTemplate();
        this.initNamespace();
        this.initSignDate();
        this.initFilePath();
        this.initSigningUsers();
    }

    private void initNamespace() {
        if (StringUtils.isNotBlank(this.namespace)) {
            return;
        }

        for (HcmsProject project : this.projects) {
            if (StringUtils.isNotBlank(project.getDomain())) {
                this.namespace = project.getDomain();
            }
        }
    }

    private void initSignDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        this.signDate = LocalDate.now().format(formatter);
    }

    /**
     * 根据路径初始化模版
     *
     * @throws IOException
     */
    private void initDocxFromTemplate() throws IOException {
        if (StringUtils.isNotBlank(filePath)) {
            FileInputStream fis = new FileInputStream(filePath);
            this.document = new XWPFDocument(fis);
        }

        String templateFilePath = null;
        switch (this.namespace) {
            case PSI:
                templateFilePath = psiVersionOfReportTemplateFilePath;
            case UPLUS:
                templateFilePath = uplusVersionOfReportTemplateFilePath;
            case BUSINESS:
                templateFilePath = businessVersionOfReportTemplateFilePath;
            default:
                templateFilePath = psiVersionOfReportTemplateFilePath;
        }

        File ossFile = ossClient.getOssFile(templateFilePath);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            try (FileInputStream fis = new FileInputStream(ossFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = fis.read(buffer)) != -1) {
                    baos.write(buffer, 0, length);
                }
            } catch (IOException e) {
                throw new RuntimeException("Failed to read file", e);
            }

            // 将文件内容保存到内存中的 ByteArrayInputStream
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            this.document = new XWPFDocument(bais);
        } catch (Exception e) {
            throw new RuntimeException("Failed to process OSS file", e);
        } finally {
            if (ossFile != null && ossFile.exists()) {
                ossFile.delete();
            }
        }
    }

    /**
     * 初始化路径
     */
    private void initFilePath() {
        if (filePath != null && !filePath.isEmpty()) {
            return;
        }

        List<String> scodes = this.projects.stream().map(HcmsProject::getScode).collect(Collectors.toList());
        String hashSuffix = "";
        try {
            hashSuffix = SignUtil.generateHash(scodes);
        } catch (NoSuchAlgorithmException e) {
            log.error("Failed to generate hash for scodes", e);
        }

        String cleanedStageEnd = stageEnd.replace("-", "");
        String fileName = String.format("%s_%s_%s_%s_%s.docx", this.name, stage, budget, cleanedStageEnd, hashSuffix);
        String tempDir = System.getProperty("java.io.tmpdir");
        String uniqueDir = String.format("%d", TimeUnit.NANOSECONDS.toMillis(Instant.now().getNano()));
        filePath = Paths.get(tempDir, uniqueDir, fileName).toString();

        File dir = new File(filePath).getParentFile();
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw new RuntimeException("Failed to create directory " + dir.getAbsolutePath() + " for contract file.");
            }

        }
    }

    private boolean isStringCollectionEmpty(List<String> list) {
        if (null != list && !list.isEmpty()) {
            Optional<String> notEmptyEle = list.stream()
                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank).findFirst();
            return !notEmptyEle.isPresent();
        }

        return true;
    }

    private void initSigningUsers() {
        boolean needOwner = isStringCollectionEmpty(this.owners);
        boolean needOwnerLeader = isStringCollectionEmpty(this.ownerLeaders);
        boolean needSmallMicroOwner = isStringCollectionEmpty(this.smallMicroOwners);

        for (HcmsProject project : projects) {
            if (needOwner) {
                this.owners.add(project.getOwner().getUserName());
            }

            if (needOwnerLeader) {
                this.ownerLeaders.add(project.getOwnerLeader().getUserName());
            }

            if (needSmallMicroOwner) {
                this.smallMicroOwners.add(project.getOwnerDeptLeader().getUserName());
            }
        }
    }


    /**
     * equal to contract_stage_budget.go#generateReport
     */
    public void previewReport() {
        // 渲染模板
        try {
            // 乙方签字
            this.sign();
            // IT负责人签字
            this.sign();
            // 团队接口人签字
            this.sign();
            // 小微主签字
            this.sign();

            // 上传阿里云oss
            this.uploadToOss();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String getOssUri(String filePath) {
        String fileName = Paths.get(filePath).getFileName().toString();
        return String.format("reports/%s/%s/%s", name, stage, fileName);
    }


    /**
     * 上传到oss，并且添加转换pdf的任务
     */
    public void uploadToOss() {
        try {
            String ossUri = this.getOssUri(filePath);
            ossClient.uploadOssFile(ossUri, filePath);
            new File(filePath).delete();

            immClient.createOfficeConversionTask(ossClient.unifyOSSUri(ossUri), ContractOssClient.OSS_BUCKET_NAME);
        } catch (OSSException e) {
            throw new RuntimeException(e);
        }

    }


    public int signWithUsers(List<List<String>> users) throws IOException {
        List<XWPFTable> tables = this.document.getTables();

        int index = 0;
        int currentIndex = 0;

        for (XWPFTable table : tables) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        if (!CollectionUtils.isEmpty(runs)) {
                            for (int i = 0; i < runs.size(); i++) {
                                XWPFRun run = runs.get(i);
                                String text = run.getText(0);
                                if (null == text || !text.contains(SEARCH_TEXT)) {
                                    continue;
                                }

                                if (index >= users.size()) {
                                    continue;
                                }

                                List<String> roleUsers = users.get(index);
                                if (CollectionUtils.isEmpty(roleUsers)) {
                                    continue;
                                }

                                currentIndex = index;

                                // 分割文本，保持签名文字和图片插入
                                String beforeText = text.substring(0, text.indexOf(SEARCH_TEXT) + SEARCH_TEXT.length());
                                String afterText = text.substring(text.indexOf(SEARCH_TEXT) + SEARCH_TEXT.length());
                                // 设置“签名：”文本
                                run.setText(beforeText, 0);

                                // 插入图片
                                XWPFRun imageRun = paragraph.insertNewRun(i + 1);
                                this.addUserSignature(imageRun, roleUsers);

                                // 插入剩余文本
                                XWPFRun newRun = paragraph.insertNewRun(i + 2);
                                newRun.setText(afterText);

                                index++;
                            }
                        }
                    }
                }
            }
        }

        this.saveDocumentToFile(document, this.filePath);

        return currentIndex + 1;
    }


    /**
     * 签名
     * <p>
     * equal to contract.go#Sign
     */
    public void sign() throws IOException {
        int index = 0;

        List<XWPFTable> tables = this.document.getTables();
        outerLoop:
        for (XWPFTable table : tables) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        if (!CollectionUtils.isEmpty(runs)) {
                            for (int i = 0; i < runs.size(); i++) {
                                XWPFRun run = runs.get(i);
                                String text = run.getText(0);
                                if (null != text && text.contains(SEARCH_TEXT)) {
                                    if (this.currentSigningIndex == index) {
                                        // 分割文本，保持签名文字和图片插入
                                        String beforeText = text.substring(0, text.indexOf(SEARCH_TEXT) + SEARCH_TEXT.length());
                                        String afterText = text.substring(text.indexOf(SEARCH_TEXT) + SEARCH_TEXT.length());
                                        // 设置“签名：”文本
                                        run.setText(beforeText, 0);

                                        // 插入图片
                                        XWPFRun imageRun = paragraph.insertNewRun(i + 1);
                                        this.sign(imageRun);

                                        // 插入剩余文本
                                        XWPFRun newRun = paragraph.insertNewRun(i + 2);
                                        newRun.setText(afterText);

                                        break outerLoop;
                                    }

                                    index++;
                                }
                            }
                        }
                    }
                }
            }
        }

        saveDocumentToFile(document, this.filePath);
    }

    private void sign(XWPFRun imageRun) {
        switch (currentSigningIndex) {
            case 0:
                this.addUserSignature(imageRun, this.partBUserId);
                break;
            case 1:
                this.addUserSignature(imageRun, this.getOwners());
                break;
            case 2:
                this.addUserSignature(imageRun, this.getOwnerLeaders());
                break;
            case 3:
                this.addUserSignature(imageRun, this.getSmallMicroOwners());
                break;
        }
        this.currentSigningIndex++;
    }

    private void addUserSignature(XWPFRun imageRun, List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        List<String> validUserIds = userIds.stream()
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String, Boolean> signedMap = new HashMap<>();
        for (String userId : validUserIds) {
            if (Boolean.TRUE.equals(signedMap.get(userId))) {
                continue;
            }

            signedMap.put(userId, true);
            this.addUserSignature(imageRun, userId);
        }
    }

    private void addUserSignature(XWPFRun imageRun, String userId) {
        SignatureClient.SignInfo signature = getSignInfo(userId);
        byte[] imageBytes = Base64.getDecoder().decode(signature.getAutograph());
        String fileFormat = org.apache.commons.lang3.StringUtils.isNotBlank(signature.getFormat()) ? signature.getFormat() : "png";
        String fileName = "sign_" + signature.getUserCode() + "_" + Instant.now().toEpochMilli() + "." + fileFormat;


        // 将 byte[] 转换为 ByteArrayInputStream
        try (InputStream inputStream = new ByteArrayInputStream(imageBytes)) {
            // 添加图片到文档
            imageRun.addPicture(inputStream, Document.PICTURE_TYPE_PNG, fileName, Units.toEMU(50), Units.toEMU(40));
        } catch (IOException | InvalidFormatException e) {
            throw new RuntimeException(e);
        }

    }

    private SignatureClient.SignInfo getSignInfo(String userId) {

        return getUserSignatureFunc.apply(userId);
    }

    private void saveDocumentToFile(XWPFDocument document, String filePath) throws IOException {
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            document.write(out);
        }
    }


    /**
     * equal to contract.go#GenerateDocx
     */
    public void generateDoc() throws IOException {
        this.render();
        this.saveDocumentToFile(this.document, this.filePath);
    }

    public void render() {
        renderHeader(this.document);
        renderBody(this.document);
    }

    private void renderHeader(XWPFDocument doc) {
        List<XWPFHeader> headers = doc.getHeaderList();
        for (XWPFHeader header : headers) {
            List<XWPFParagraph> paragraphs = header.getParagraphs();
            if (!CollectionUtils.isEmpty(paragraphs)) {
                for (XWPFParagraph paragraph : paragraphs) {
                    renderParagraph(paragraph);
                }
            }
            List<XWPFTable> tables = header.getTables();
            if (!CollectionUtils.isEmpty(tables)) {
                for (XWPFTable table : tables) {
                    renderTable(table);
                }
            }
        }
    }

    private void renderBody(XWPFDocument doc) {
        List<IBodyElement> bodyElements = doc.getBodyElements();
        if (!CollectionUtils.isEmpty(bodyElements)) {
            for (IBodyElement bodyElement : bodyElements) {
                if (bodyElement instanceof XWPFParagraph) {
                    renderParagraph((XWPFParagraph) bodyElement);
                } else if (bodyElement instanceof XWPFTable) {
                    renderTable((XWPFTable) bodyElement);
                }
            }
        }
    }

    private void renderTable(XWPFTable table) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    renderParagraph(paragraph);
                }
            }
        }
    }

    private void renderParagraph(XWPFParagraph paragraph) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs != null) {
            for (XWPFRun run : runs) {
                renderRun(run);
            }
        }
    }

    private void renderRun(XWPFRun run) {
        contractConsumer.accept(run);
    }

    private @NotNull String resolvingNamespace() {
        String processedNamespace = null;
        if (namespace.equals(BUSINESS)) {
            processedNamespace = "数字化转型平台";
        } else if (namespace.equals(PSI)) {
            processedNamespace = "数字化转型平台";
        } else if (namespace.equals(UPLUS)) {
            processedNamespace = "业务领域IT";
        } else {
            processedNamespace = "数字化转型平台";
        }
        return processedNamespace;
    }

}