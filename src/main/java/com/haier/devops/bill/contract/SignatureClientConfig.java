package com.haier.devops.bill.contract;

import com.haier.devops.bill.sdks.haier.SignatureClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SignatureClientConfig {
    final SignatureClientConfigProperties properties;

    public SignatureClientConfig(SignatureClientConfigProperties properties) {
        this.properties = properties;
    }

    @Bean
    public SignatureClient signatureClient() {
        return new SignatureClient(properties.getUser(), properties.getPass(), properties.getPublicKey());
    }
}

