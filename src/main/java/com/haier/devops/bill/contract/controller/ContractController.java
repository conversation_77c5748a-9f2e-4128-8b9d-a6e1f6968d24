package com.haier.devops.bill.contract.controller;

import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.entity.Contract;
import com.haier.devops.bill.common.entity.ContractBudget;
import com.haier.devops.bill.common.entity.ContractStage;
import com.haier.devops.bill.common.service.ContractBudgetService;
import com.haier.devops.bill.common.service.ContractService;
import com.haier.devops.bill.common.service.ContractStageService;
import com.haier.devops.bill.contract.HcmsContract;
import com.haier.devops.bill.contract.SignDTO;
import com.haier.devops.bill.contract.SignatureNotFoundException;
import com.haier.devops.bill.sdks.aliyun.ContractOssClient;
import com.haier.devops.bill.sdks.aliyun.ImmClient;
import com.haier.devops.bill.sdks.haier.SignatureClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.controller.ResponseEnum.INTERNAL_ERR;
import static com.haier.devops.bill.common.controller.ResponseEnum.RESOURCE_NOT_FOUND;

/**
 * 合同验收
 */
@RestController
@Slf4j
@RequestMapping("/api/v1/hcms/bill/contract")
public class ContractController {
    private final ContractService contractService;
    private final ContractBudgetService budgetService;
    private final ContractStageService stageService;

    private final HdsOpenApi hdsOpenApi;
    private final HworkAuthorityApi hworkAuthorityApi;
    private final SignatureClient signatureClient;
    private final ContractOssClient ossClient;
    private final ImmClient immClient;

    private final SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");

    public ContractController(ContractBudgetService budgetService,
                              ContractService contractService,
                              ContractStageService stageService,
                              HdsOpenApi hdsOpenApi, HworkAuthorityApi hworkAuthorityApi,
                              SignatureClient signatureClient,
                              @Qualifier("contractOssClient") ContractOssClient ossClient,
                              ImmClient immClient) {
        this.budgetService = budgetService;
        this.contractService = contractService;
        this.stageService = stageService;
        this.hdsOpenApi = hdsOpenApi;
        this.hworkAuthorityApi = hworkAuthorityApi;
        this.signatureClient = signatureClient;
        this.ossClient = ossClient;
        this.immClient = immClient;
    }

    /**
     * 重置合同
     * @param budgetId
     * @return
     */
    @PostMapping("/reset")
    public ResponseEntityWrapper<String> resetContract(String budgetId) {
        ContractBudget budget = budgetService.getById(budgetId);
        HcmsContract hcmsContract = composeHcmsContract(budget);
        // 重置当前签署索引
        hcmsContract.setCurrentSigningIndex(0);
        try {
            hcmsContract.init();
            hcmsContract.generateDoc();
            hcmsContract.sign();
            hcmsContract.uploadToOss();
        } catch (IOException e) {
            log.error("Failed to reset contract", e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "", "");
        }

        return new ResponseEntityWrapper<>(hcmsContract.getFilePath());
    }

    /**
     * 签名
     * @param signDTO
     * @return
     */
    @PostMapping("/sign")
    public ResponseEntityWrapper<Integer> signWithUsers(@RequestBody SignDTO signDTO) {
        ContractBudget budget = budgetService.getById(signDTO.getBudgetId());
        HcmsContract hcmsContract = composeHcmsContract(budget);

        int currentIndex = 0;
        try {
            hcmsContract.init();
            hcmsContract.generateDoc();
            currentIndex = hcmsContract.signWithUsers(signDTO.getUsers());
            hcmsContract.uploadToOss();
        } catch (IOException e) {
            log.error("Failed to sign contract", e);
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "", currentIndex);
        }
        return new ResponseEntityWrapper<>(currentIndex);
    }

    /**
     * 生成验收阶段合同
     * @param budgetId
     * @return
     */
    @PostMapping("/generate")
    public ResponseEntityWrapper<String> previewReport(String budgetId) {
        ContractBudget budget = budgetService.getById(budgetId);
        HcmsContract hcmsContract = composeHcmsContract(budget);
        try {
            hcmsContract.init();
            hcmsContract.generateDoc();
            hcmsContract.previewReport();
        } catch (Exception e) {
            log.error("Failed to preview contract", e);
            if (e instanceof SignatureNotFoundException) {
                return new ResponseEntityWrapper<>(RESOURCE_NOT_FOUND, e.getMessage(), null);
            }
            return new ResponseEntityWrapper<>(INTERNAL_ERR, "", "");
        }

        return new ResponseEntityWrapper<>(hcmsContract.getFilePath());
    }

    private HcmsContract composeHcmsContract(ContractBudget budget) {
        Contract contract = contractService.getById(budget.getContractId());
        ContractStage stage = stageService.getById(budget.getStageId());

        String projects = budget.getContractProjects();

        List<String> owners = Arrays.stream(budget.getOwners().split(","))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<String> ownerLeaders = Arrays.stream(budget.getOwnerLeaders().split(","))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<String> smallMicroOwners = Arrays.stream(budget.getSmallMicroOwners().split(","))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        HcmsContract hcmsContract = HcmsContract.builder()
                .name(contract.getContractName())
                .vendor(contract.getContractVendor())
                .executionCycleStart(dateFormatter.format(contract.getStartTime()))
                .executionCycleEnd(dateFormatter.format(contract.getEndTime()))
                .partBUserId(contract.getPartyB())
                .owners(owners)
                .ownerLeaders(ownerLeaders)
                .smallMicroOwners(smallMicroOwners)
                .budget(budget.getBudget())
                .namespace(budget.getNamespace())
                .stage(stage.getStageName())
                .stageStart(dateFormatter.format(stage.getStartTime()))
                .stageEnd(dateFormatter.format(stage.getEndTime()))
                .acceptanceState(stage.getAcceptanceState())
                .acceptanceDate(dateFormatter.format(stage.getAcceptanceDate()))
                .cost(Double.toString(budget.getCost()))
               .currentSigningIndex(budget.getCurrentSignIndex())
                // 注入签名client
                .signatureClient(signatureClient)
                .ossClient(ossClient)
                .immClient(immClient)
                .contract(contract)
                .build();
        hcmsContract.initContractProjects(hdsOpenApi, hworkAuthorityApi, Arrays.asList(projects.split(",")));
        return hcmsContract;
    }
}
