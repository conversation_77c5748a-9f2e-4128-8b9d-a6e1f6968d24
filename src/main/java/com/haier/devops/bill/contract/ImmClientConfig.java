package com.haier.devops.bill.contract;

import com.haier.devops.bill.sdks.aliyun.ImmClient;
import com.haier.devops.bill.sdks.aliyun.ImmClientConfigProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ImmClientConfig {

    private final ImmClientConfigProperties immConfigProperties;

    public ImmClientConfig(ImmClientConfigProperties immClientConfigProperties) {
        this.immConfigProperties = immClientConfigProperties;
    }

    @Bean
    public ImmClient immClient() {
        return new ImmClient(immConfigProperties.getEndpoint(), immConfigProperties.getAccessKeyId(), immConfigProperties.getAccessKeySecret());
    }
}
