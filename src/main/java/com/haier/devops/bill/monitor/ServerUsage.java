package com.haier.devops.bill.monitor;

import com.haier.bigdata.automat.data.DataClient;
import com.haier.devops.bill.common.service.ServerUsageService;
import com.haier.devops.bill.common.vo.ServiceResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ServerUsage {
    private final DataClient dataClient;
    private final ServerUsageService serverUsageService;

    public ServerUsage(DataClient dataClient, ServerUsageService serverUsageService) {
        this.dataClient = dataClient;
        this.serverUsageService = serverUsageService;
    }

    /**
     * 同步服务器使用效能数据并持久化到数据库
     * @return 操作结果
     */
    public ServiceResult<Boolean> persistServerUsage() {
        log.info("开始同步服务器使用效能数据");
        ServiceResult<Boolean> result = serverUsageService.syncServerUsageData();
        if (result.isSuccess()) {
            log.info("服务器使用效能数据同步成功");
        } else {
            log.error("服务器使用效能数据同步失败: {}", result.getErrMsg());
        }
        return result;
    }
}
