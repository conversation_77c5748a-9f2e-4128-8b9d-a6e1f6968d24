package com.haier.devops.bill.bookmark.api;

import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;

/**
 * 用户授权数据服务接口
 * 提供获取用户授权数据的基础功能
 */
public interface AuthDataService {
    
    /**
     * 获取用户授权的应用数据
     * 
     * @param userCode 用户编码
     * @param domain 领域编码，可为空
     * @param productIds 产品ID列表，可为空
     * @return 用户授权的应用数据
     */
    AuthAppForItResponse getAuthAppForIt(String userCode, String domain, java.util.List<String> productIds);
}
