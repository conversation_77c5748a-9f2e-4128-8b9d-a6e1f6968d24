package com.haier.devops.bill.bookmark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户收藏信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserBookmark implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 书签唯一标识
     */
    private String id;
    
    /**
     * 用户编码
     */
    private String userCode;
    
    /**
     * 收藏类型
     */
    private BookmarkType bookmarkType;
    
    /**
     * 领域编码
     */
    private String domainCode;
    
    /**
     * 产品ID集合
     * 当收藏类型为PRODUCT或SUB_PRODUCT时使用
     */
    private Set<String> productIds;
    
    /**
     * 子产品编码集合
     * 当收藏类型为SUB_PRODUCT时使用
     */
    private Set<String> subProductCodes;
    
    /**
     * 添加产品ID到收藏
     */
    public void addProductId(String productId) {
        if (productIds == null) {
            productIds = new HashSet<>();
        }
        productIds.add(productId);
    }
    
    /**
     * 添加子产品编码到收藏
     */
    public void addSubProductCode(String subProductCode) {
        if (subProductCodes == null) {
            subProductCodes = new HashSet<>();
        }
        subProductCodes.add(subProductCode);
    }
    
    /**
     * 合并另一个收藏信息
     */
    public void merge(UserBookmark other) {
        if (other == null) {
            return;
        }
        
        // 如果收藏类型不同，保留当前类型
        
        // 合并产品ID
        if (other.getProductIds() != null && !other.getProductIds().isEmpty()) {
            if (this.productIds == null) {
                this.productIds = new HashSet<>();
            }
            this.productIds.addAll(other.getProductIds());
        }
        
        // 合并子产品编码
        if (other.getSubProductCodes() != null && !other.getSubProductCodes().isEmpty()) {
            if (this.subProductCodes == null) {
                this.subProductCodes = new HashSet<>();
            }
            this.subProductCodes.addAll(other.getSubProductCodes());
        }
    }
}
