package com.haier.devops.bill.bookmark.service;

import com.haier.devops.bill.bookmark.api.AuthDataService;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItRequest;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;
import com.haier.devops.bill.common.service.AuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户授权数据服务实现
 * 负责调用原始API获取用户授权数据
 */
@Service
@Slf4j
public class AuthDataServiceImpl implements AuthDataService {

    @Autowired
    private AuthorityService authorityService;
    
    @Override
    public AuthAppForItResponse getAuthAppForIt(String userCode, String domain, List<String> productIds) {
        try {
            // 构建请求参数
            AuthAppForItRequest request = new AuthAppForItRequest();
            request.setUserCode(userCode);
            request.setDomain(domain);
            request.setProductIds(productIds);
            
            // 调用原始API
            return authorityService.getAuthAppForIt(request);
        } catch (Exception e) {
            log.error("获取用户授权数据失败: {}", e.getMessage(), e);
            return new AuthAppForItResponse();
        }
    }
}
