package com.haier.devops.bill.bookmark.service;

import com.haier.devops.bill.bookmark.api.AuthDataService;
import com.haier.devops.bill.bookmark.api.BookmarkService;
import com.haier.devops.bill.bookmark.model.BookmarkType;
import com.haier.devops.bill.bookmark.model.BookmarkedAuthResponse;
import com.haier.devops.bill.bookmark.model.UserBookmark;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;
import com.haier.devops.bill.common.api.HworkBillApi.Domain;
import com.haier.devops.bill.common.api.HworkBillApi.Product;
import com.haier.devops.bill.common.api.HworkBillApi.SubProduct;
import com.haier.devops.bill.util.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户收藏服务实现
 * 使用代理模式增强原有API，添加收藏功能
 */
@Slf4j
@Service
public class BookmarkServiceImpl implements BookmarkService {
    
    /** 收藏数据在Redis中的键前缀 */
    private static final String BOOKMARK_KEY_PREFIX = "hcms:bill:bookmark:";
    /** 收藏ID列表的键后缀 */
    private static final String BOOKMARK_IDS_KEY_SUFFIX = ":ids";
    /** 收藏数据的键后缀 */
    private static final String BOOKMARK_DATA_KEY_SUFFIX = ":data:";
    /** 收藏数据在Redis中的过期时间（秒） */
    private static final long BOOKMARK_EXPIRE_SECONDS = 10 * 365 * 24 * 60 * 60; // 10years
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private AuthDataService authDataService;
    

    @Override
    public List<UserBookmark> getUserBookmark(String userCode) {
        if (userCode == null) {
            return new ArrayList<>();
        }
        
        // 获取所有书签
        return getAllUserBookmarks(userCode);
    }
    
    /**
     * 获取用户所有收藏信息
     * 从Redis中获取用户所有的收藏信息
     * 
     * @param userCode 用户代码
     * @return 收藏列表
     */
    public List<UserBookmark> getAllUserBookmarks(String userCode) {
        if (userCode == null) {
            return new ArrayList<>();
        }
        
        List<UserBookmark> bookmarks = new ArrayList<>();
        
        try {
            // 获取该用户所有收藏的ID
            List<String> bookmarkIds = getUserBookmarkIds(userCode);
            if (bookmarkIds.isEmpty()) {
                return bookmarks;
            }
            
            // 根据ID获取每个收藏的详细信息
            for (String bookmarkId : bookmarkIds) {
                try {
                    String key = getBookmarkKey(userCode, bookmarkId);
                    Object bookmarkObj = redisUtils.get(key);
                    if (bookmarkObj != null && bookmarkObj instanceof UserBookmark) {
                        bookmarks.add((UserBookmark) bookmarkObj);
                    } else if (bookmarkObj != null) {
                        log.warn("收藏对象类型不匹配: userCode={}, bookmarkId={}, actualType={}", 
                                userCode, bookmarkId, bookmarkObj.getClass().getName());
                    } else {
                        log.warn("收藏对象不存在: userCode={}, bookmarkId={}", userCode, bookmarkId);
                    }
                } catch (Exception e) {
                    log.error("获取单个收藏详情失败: userCode={}, bookmarkId={}, error={}", 
                            userCode, bookmarkId, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("获取用户所有收藏失败: userCode={}, error={}", userCode, e.getMessage());
        }
        
        return bookmarks;
    }
    
    /**
     * 获取用户收藏ID列表
     * 
     * @param userCode 用户代码
     * @return 收藏ID列表
     */
    private List<String> getUserBookmarkIds(String userCode) {
        String idsKey = getBookmarkIdsKey(userCode);
        List<String> ids = new ArrayList<>();
        
        try {
            // 检查键是否存在
            if (redisUtils.exists(idsKey)) {
                List<Object> idsObj = redisUtils.lRange(idsKey, 0, -1);
                
                if (idsObj != null) {
                    for (Object id : idsObj) {
                        if (id != null) {
                            ids.add(id.toString());
                        }
                    }
                }
            } else {
                log.debug("用户没有收藏记录: userCode={}", userCode);
            }
        } catch (Exception e) {
            // 如果发生Redis数据类型错误，删除键并重新创建
            log.warn("获取用户收藏ID列表时发生错误，可能是数据类型不匹配: userCode={}, error={}", userCode, e.getMessage());
            
            try {
                // 删除可能导致类型错误的键
                redisUtils.remove(idsKey);
                log.info("已删除可能导致类型错误的键: {}", idsKey);
            } catch (Exception ex) {
                log.error("删除Redis键失败: {}, error={}", idsKey, ex.getMessage());
            }
        }
        
        return ids;
    }
    
    @Override
    public boolean bookmarkDomain(String userCode, String domainCode) {
        if (userCode == null || domainCode == null) {
            return false;
        }
        
        // 创建新的收藏
        UserBookmark bookmark = new UserBookmark();
        bookmark.setUserCode(userCode);
        bookmark.setBookmarkType(BookmarkType.DOMAIN);
        bookmark.setDomainCode(domainCode);
        
        // 保存收藏
        return saveBookmarkAsSeparateEntity(bookmark);
    }
    
    @Override
    public boolean bookmarkProducts(String userCode, String domainCode, Set<String> productIds) {
        if (userCode == null || domainCode == null || productIds == null || productIds.isEmpty()) {
            return false;
        }
        
        // 创建新的收藏
        UserBookmark bookmark = new UserBookmark();
        bookmark.setUserCode(userCode);
        bookmark.setBookmarkType(BookmarkType.PRODUCT);
        bookmark.setDomainCode(domainCode);
        bookmark.setProductIds(productIds);
        
        // 保存收藏
        return saveBookmarkAsSeparateEntity(bookmark);
    }
    
    @Override
    public boolean bookmarkSubProducts(String userCode, String domainCode, String productId, Set<String> subProductCodes) {
        if (userCode == null || domainCode == null || subProductCodes == null || subProductCodes.isEmpty()) {
            return false;
        }
        
        // 创建新的收藏
        UserBookmark bookmark = new UserBookmark();
        bookmark.setUserCode(userCode);
        bookmark.setBookmarkType(BookmarkType.SUB_PRODUCT);
        bookmark.setDomainCode(domainCode);
        
        // 添加产品ID
        Set<String> productIds = new HashSet<>();
        productIds.add(productId);
        bookmark.setProductIds(productIds);
        
        // 添加子产品编码
        bookmark.setSubProductCodes(subProductCodes);
        
        // 保存收藏
        return saveBookmarkAsSeparateEntity(bookmark);
    }
    
    /**
     * 保存书签作为独立实体
     * 每个书签保存为一个独立的Redis条目，同时维护用户的书签ID列表
     * 
     * @param bookmark 书签信息
     * @return 是否保存成功
     */
    private boolean saveBookmarkAsSeparateEntity(UserBookmark bookmark) {
        if (bookmark == null || bookmark.getUserCode() == null) {
            return false;
        }
        
        try {
            // 根据书签数据生成哈希ID，确保相同的书签只保存一次
            String bookmarkId = generateHashId(bookmark);
            bookmark.setId(bookmarkId);
            
            // 构建Redis键
            String userCode = bookmark.getUserCode();
            String key = getBookmarkKey(userCode, bookmarkId);
            
            // 检查是否已存在相同的书签
            if (redisUtils.exists(key)) {
                // 书签已存在，检查是否已在用户的书签ID列表中
                String idsKey = getBookmarkIdsKey(userCode);
                List<Object> existingIds = null;
                
                try {
                    // 尝试获取现有ID列表
                    if (redisUtils.exists(idsKey)) {
                        existingIds = redisUtils.lRange(idsKey, 0, -1);
                    }
                } catch (Exception e) {
                    // 如果发生异常，说明键存在但不是列表类型，删除它
                    log.warn("书签ID列表类型不匹配，删除旧数据: userCode={}, key={}, error={}", 
                            userCode, idsKey, e.getMessage());
                    redisUtils.remove(idsKey);
                    existingIds = null;
                }
                
                // 检查ID是否已在列表中
                boolean idExists = false;
                if (existingIds != null) {
                    for (Object id : existingIds) {
                        if (id != null && id.toString().equals(bookmarkId)) {
                            idExists = true;
                            break;
                        }
                    }
                }
                
                // 如果ID不在列表中，添加它
                if (!idExists) {
                    redisUtils.lPush(idsKey, bookmarkId);
                    // 设置过期时间
                    redisUtils.set(idsKey + ":expire", "1", BOOKMARK_EXPIRE_SECONDS, TimeUnit.SECONDS);
                }
                
                // 更新书签的过期时间
                redisUtils.set(key, bookmark, BOOKMARK_EXPIRE_SECONDS, TimeUnit.SECONDS);
                
                log.info("更新现有用户书签: userCode={}, bookmarkType={}, bookmarkId={}", 
                        userCode, bookmark.getBookmarkType(), bookmarkId);
                
                return true;
            }
            
            // 保存到Redis
            redisUtils.set(key, bookmark, BOOKMARK_EXPIRE_SECONDS, TimeUnit.SECONDS);
            
            // 将ID添加到用户的书签ID列表
            String idsKey = getBookmarkIdsKey(userCode);
            
            try {
                // 检查键是否存在
                if (redisUtils.exists(idsKey)) {
                    // 尝试获取列表，如果不是列表类型会抛出异常
                    redisUtils.lRange(idsKey, 0, 0);
                }
            } catch (Exception e) {
                // 如果发生异常，说明键存在但不是列表类型，删除它
                log.warn("书签ID列表类型不匹配，删除旧数据: userCode={}, key={}, error={}", 
                        userCode, idsKey, e.getMessage());
                redisUtils.remove(idsKey);
            }
            
            // 添加到列表
            redisUtils.lPush(idsKey, bookmarkId);
            
            // 设置过期时间
            redisUtils.set(idsKey + ":expire", "1", BOOKMARK_EXPIRE_SECONDS, TimeUnit.SECONDS);
            
            log.info("成功保存用户书签: userCode={}, bookmarkType={}, bookmarkId={}", 
                    userCode, bookmark.getBookmarkType(), bookmarkId);
            
            return true;
        } catch (Exception e) {
            log.error("保存用户书签失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 根据书签数据生成哈希ID
     * 使用书签的关键字段生成一个唯一的哈希值，确保相同的书签只保存一次
     * 
     * @param bookmark 书签信息
     * @return 哈希ID
     */
    private String generateHashId(UserBookmark bookmark) {
        StringBuilder sb = new StringBuilder();
        
        // 添加用户代码
        sb.append(bookmark.getUserCode()).append(":");
        
        // 添加书签类型
        sb.append(bookmark.getBookmarkType()).append(":");
        
        // 添加领域编码
        if (bookmark.getDomainCode() != null) {
            sb.append(bookmark.getDomainCode());
        }
        sb.append(":");
        
        // 添加产品ID（按字母顺序排序以确保一致性）
        if (bookmark.getProductIds() != null && !bookmark.getProductIds().isEmpty()) {
            List<String> sortedProductIds = new ArrayList<>(bookmark.getProductIds());
            Collections.sort(sortedProductIds);
            for (String productId : sortedProductIds) {
                sb.append(productId).append(",");
            }
        }
        sb.append(":");
        
        // 添加子产品编码（按字母顺序排序以确保一致性）
        if (bookmark.getSubProductCodes() != null && !bookmark.getSubProductCodes().isEmpty()) {
            List<String> sortedSubProductCodes = new ArrayList<>(bookmark.getSubProductCodes());
            Collections.sort(sortedSubProductCodes);
            for (String subProductCode : sortedSubProductCodes) {
                sb.append(subProductCode).append(",");
            }
        }
        
        // 生成MD5哈希
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5算法不可用，回退到简单的哈希码
            log.warn("MD5算法不可用，使用简单哈希: {}", e.getMessage());
            return String.valueOf(sb.toString().hashCode());
        }
    }
    
    /**
     * 过滤不存在的收藏条目
     * 清理用户收藏中已经不存在的领域、产品或子产品
     * 
     * @param userCode 用户代码
     * @param response 授权应用数据
     */
    private void cleanupNonExistentBookmarks(String userCode, AuthAppForItResponse response) {
        if (userCode == null || response == null) {
            return;
        }
        
        // 获取用户所有收藏
        List<UserBookmark> bookmarks = getAllUserBookmarks(userCode);
        if (bookmarks.isEmpty()) {
            return;
        }
        
        // 收集所有有效的领域编码
        Set<String> validDomainCodes = new HashSet<>();
        if (response.getDomains() != null) {
            for (Domain domain : response.getDomains()) {
                validDomainCodes.add(domain.getDomainCode());
            }
        }
        
        // 收集所有有效的产品ID
        Set<String> validProductIds = new HashSet<>();
        if (response.getProducts() != null) {
            for (Product product : response.getProducts()) {
                validProductIds.add(product.getId());
            }
        }
        
        // 收集所有有效的子产品编码
        Set<String> validSubProductCodes = new HashSet<>();
        if (response.getSubProducts() != null) {
            for (SubProduct subProduct : response.getSubProducts()) {
                validSubProductCodes.add(subProduct.getAppScode());
            }
        }
        
        // 检查每个收藏的有效性
        for (UserBookmark bookmark : bookmarks) {
            boolean needsUpdate = false;
            
            // 验证领域
            if (bookmark.getDomainCode() != null && !validDomainCodes.contains(bookmark.getDomainCode())) {
                log.info("清理不存在的领域收藏: userCode={}, domainCode={}", userCode, bookmark.getDomainCode());
                // 如果是领域收藏，直接删除
                if (bookmark.getBookmarkType() == BookmarkType.DOMAIN) {
                    String key = getBookmarkKey(userCode, bookmark.getId());
                    redisUtils.remove(key);
                    continue;
                }
                needsUpdate = true;
            }
            
            // 验证并清理产品ID
            if (bookmark.getProductIds() != null && !bookmark.getProductIds().isEmpty()) {
                Set<String> invalidProductIds = new HashSet<>();
                for (String productId : bookmark.getProductIds()) {
                    if (!validProductIds.contains(productId)) {
                        invalidProductIds.add(productId);
                    }
                }
                
                if (!invalidProductIds.isEmpty()) {
                    log.info("清理不存在的产品收藏: userCode={}, productIds={}", userCode, invalidProductIds);
                    bookmark.getProductIds().removeAll(invalidProductIds);
                    needsUpdate = true;
                    
                    // 如果是产品收藏，且所有产品都无效，直接删除
                    if (bookmark.getBookmarkType() == BookmarkType.PRODUCT && bookmark.getProductIds().isEmpty()) {
                        String key = getBookmarkKey(userCode, bookmark.getId());
                        redisUtils.remove(key);
                        continue;
                    }
                }
            }
            
            // 验证并清理子产品编码
            if (bookmark.getSubProductCodes() != null && !bookmark.getSubProductCodes().isEmpty()) {
                Set<String> invalidSubProductCodes = new HashSet<>();
                for (String subProductCode : bookmark.getSubProductCodes()) {
                    if (!validSubProductCodes.contains(subProductCode)) {
                        invalidSubProductCodes.add(subProductCode);
                    }
                }
                
                if (!invalidSubProductCodes.isEmpty()) {
                    log.info("清理不存在的子产品收藏: userCode={}, subProductCodes={}", userCode, invalidSubProductCodes);
                    bookmark.getSubProductCodes().removeAll(invalidSubProductCodes);
                    needsUpdate = true;
                    
                    // 如果是子产品收藏，且所有子产品都无效，直接删除
                    if (bookmark.getBookmarkType() == BookmarkType.SUB_PRODUCT && bookmark.getSubProductCodes().isEmpty()) {
                        String key = getBookmarkKey(userCode, bookmark.getId());
                        redisUtils.remove(key);
                        continue;
                    }
                }
            }
            
            // 如果需要更新收藏
            if (needsUpdate) {
                String key = getBookmarkKey(userCode, bookmark.getId());
                redisUtils.set(key, bookmark, BOOKMARK_EXPIRE_SECONDS, TimeUnit.SECONDS);
            }
        }
    }

    @Data
    @AllArgsConstructor
    class Bookmark {
        String subProductCode;
        String productId;
        String domainCode;
    }
    
    /**
     * 获取用户授权的应用数据，带有收藏标记
     * 合并了用户收藏的数据和授权的应用数据
     * 
     * @param userCode 用户代码
     * @return 用户授权的应用数据，带有收藏标记
     */
    public AuthAppForItResponse getBookmarkedAuthApp(String userCode) {
        Objects.requireNonNull(userCode);

        // 获取用户所有收藏信息
        List<UserBookmark> bookmarks = getAllUserBookmarks(userCode);
        // 如果没有收藏，直接返回授权数据
        if (CollectionUtils.isEmpty(bookmarks)) {
            // 获取所有授权的应用（基础数据）
            AuthAppForItResponse authentications = authDataService.getAuthAppForIt(userCode, null, null);
            for (SubProduct subProduct : authentications.getSubProducts()) {
                subProduct.setChecked(true);
            }

            return authentications;
        }

        String domainCode = null;

        // 根据收藏类型分组
        Map<BookmarkType, List<UserBookmark>> bookmarkMap = bookmarks.stream()
                .collect(Collectors
                        .toMap(UserBookmark::getBookmarkType,
                                Collections::singletonList,
                                (existingList, newList) -> {
                                    existingList.addAll(newList);
                                    return existingList;
                                }
                        )
                );

        List<UserBookmark> subProductBookmarks = bookmarkMap.get(BookmarkType.SUB_PRODUCT);
        List<UserBookmark> productBookmarks = bookmarkMap.get(BookmarkType.PRODUCT);
        List<UserBookmark> domainBookmarks = bookmarkMap.get(BookmarkType.DOMAIN);

        List<String> bookmarkedDomains = new ArrayList<>();
        List<String> bookmarkedProductIds = new ArrayList<>();
        List<String> bookmarkedSubProductCodes = new ArrayList<>();
        AuthAppForItResponse checkedSubProducts = null;

        /* 1、子产品标签（这种类型的标签productIds都只有一个）这种类型的子产品bookmarked = true */
        if (!CollectionUtils.isEmpty(subProductBookmarks)) {
            domainCode = subProductBookmarks.get(0).getDomainCode();
            for (UserBookmark bookmark : subProductBookmarks) {
                bookmarkedSubProductCodes.addAll(bookmark.getSubProductCodes());
            }
        }

        /* 2、(排除了上边1中子产品的）产品标签，也就是说这些产品下的子产品都要选中 */
        List<UserBookmark> excludedSubProductBookmarks = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productBookmarks)) {
            if (StringUtils.isBlank(domainCode)) {
                domainCode = productBookmarks.get(0).getDomainCode();
            }
            for (UserBookmark productBookmark : productBookmarks) {
                Set<String> productIds = productBookmark.getProductIds();
                if (!CollectionUtils.isEmpty(subProductBookmarks)) {
                    Optional<UserBookmark> first = subProductBookmarks.stream()
                            .filter(sbm -> productIds.contains(sbm.getProductIds().iterator().next()))
                            .findFirst();
                    if (first.isPresent()) {
                        continue;
                    }
                }

                bookmarkedProductIds.addAll(productBookmark.getProductIds());
                excludedSubProductBookmarks.add(productBookmark);
            }

            // 根据excludedSubProductBookmarks查询子产品，这些子产品bookmarked = true
            checkedSubProducts = authDataService.getAuthAppForIt(userCode, domainCode, bookmarkedProductIds);
        }


        /* 3、如果1和2都没有查询到，那么bookmark只能是到领域的，那么根据领域标签 */
        boolean bookmarkAll = false;
        if (CollectionUtils.isEmpty(subProductBookmarks) && CollectionUtils.isEmpty(productBookmarks) && !CollectionUtils.isEmpty(domainBookmarks)) {
            // 根据domain查询到的都标记为bookmarked = true
            bookmarkAll = true;
            if (StringUtils.isBlank(domainCode)) {
                domainCode = domainBookmarks.get(0).getDomainCode();
            }
            bookmarkedDomains.add(domainCode);
        }

        // 并发调用getAuthAppForIt接口
        final String finalDomainCode = domainCode;
        CompletableFuture<AuthAppForItResponse> authenticationsWithDomainFuture = CompletableFuture.supplyAsync(() -> 
            authDataService.getAuthAppForIt(userCode, finalDomainCode, null)
        );
        
        CompletableFuture<AuthAppForItResponse> allDomainsFuture = CompletableFuture.supplyAsync(() -> 
            authDataService.getAuthAppForIt(userCode, null, null)
        );
        
        // 等待两个异步调用完成
        AuthAppForItResponse authentications;
        AuthAppForItResponse allDomainsResponse;
        try {
            authentications = authenticationsWithDomainFuture.get();
            allDomainsResponse = allDomainsFuture.get();
            
            // 将不传domainCode调用结果的domains合并到authentications中
            if (authentications.getDomains() == null || authentications.getDomains().isEmpty()) {
                authentications.setDomains(allDomainsResponse.getDomains());
            }
        } catch (InterruptedException | ExecutionException e) {
            // 如果并发调用失败，则回退到串行调用
            authentications = authDataService.getAuthAppForIt(userCode, domainCode, null);
            log.error("并发调用getAuthAppForIt接口失败", e);
        }
        
        handleBookmarks(authentications, bookmarkedSubProductCodes, bookmarkedProductIds, bookmarkedDomains, bookmarkAll, checkedSubProducts);

        return authentications;
    }

    private void handleBookmarks(AuthAppForItResponse authentications,
                                 List<String> bookmarkedSubProductCodes,
                                 List<String> bookmarkedProductIds,
                                 List<String> bookmarkedDomains,
                                 boolean bookmarkAll, AuthAppForItResponse checkedSubProducts) {
        if (bookmarkAll) {
            for (Domain domain : authentications.getDomains()) {
                if (bookmarkedDomains.contains(domain.getDomainCode())) {
                    domain.setBookmarked(true);
                }
            }
            for (SubProduct subProduct : authentications.getSubProducts()) {
                subProduct.setChecked(true);
            }
            return;
        }

        if (!CollectionUtils.isEmpty(bookmarkedSubProductCodes)) {
            for (SubProduct subProduct : authentications.getSubProducts()) {
                if (bookmarkedSubProductCodes.contains(subProduct.getAppScode())) {
                    subProduct.setBookmarked(true);
                    subProduct.setChecked(true);
                }
            }
        }

        if (!CollectionUtils.isEmpty(bookmarkedProductIds)) {
            for (Product product : authentications.getProducts()) {
                if (bookmarkedProductIds.contains(product.getId())) {
                    product.setBookmarked(true);
                }
            }

            if (null != checkedSubProducts) {
                List<String> scodes = checkedSubProducts.getSubProducts().stream()
                        .map(SubProduct::getAppScode)
                        .collect(Collectors.toList());
                for (SubProduct subProduct : authentications.getSubProducts()) {
                    if (scodes.contains(subProduct.getAppScode())) {
                        subProduct.setChecked(true);
                    }
                }
            }
        }
    }

    /**
     * 将原始Domain列表转换为BookmarkedDomain列表
     */
    private List<BookmarkedAuthResponse.BookmarkedDomain> convertDomains(List<Domain> domains) {
        List<BookmarkedAuthResponse.BookmarkedDomain> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(domains)) {
            for (Domain domain : domains) {
                result.add(new BookmarkedAuthResponse.BookmarkedDomain(
                        domain.getDomainCode(),
                        domain.getDomainName(),
                        domain.isDefaultChecked(),
                        domain.isBookmarked()
                ));
            }
        }
        return result;
    }
    
    /**
     * 将原始Product列表转换为BookmarkedProduct列表
     */
    private List<BookmarkedAuthResponse.BookmarkedProduct> convertProducts(List<Product> products) {
        List<BookmarkedAuthResponse.BookmarkedProduct> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(products)) {
            for (Product product : products) {
                result.add(new BookmarkedAuthResponse.BookmarkedProduct(
                        product.getId(),
                        product.getProductName(),
                        product.getProductNameShort(),
                        product.isBookmarked()
                ));
            }
        }
        return result;
    }
    
    /**
     * 将原始SubProduct列表转换为BookmarkedSubProduct列表
     */
    private List<BookmarkedAuthResponse.BookmarkedSubProduct> convertSubProducts(List<SubProduct> subProducts) {
        List<BookmarkedAuthResponse.BookmarkedSubProduct> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(subProducts)) {
            for (SubProduct subProduct : subProducts) {
                result.add(new BookmarkedAuthResponse.BookmarkedSubProduct(
                        subProduct.getAppScode(),
                        subProduct.getAppShortName(),
                        subProduct.getAppName(),
                        subProduct.isBookmarked()
                ));
            }
        }
        return result;
    }
    
    /**
     * 获取用户授权的应用数据，带有收藏标记
     * 合并了用户收藏的数据和授权的应用数据
     * 
     * @param userCode 用户代码
     * @return 用户授权的应用数据，带有收藏标记
     */
    @Override
    public AuthAppForItResponse getAuthAppWithBookmark(String userCode) {
        // 获取带有收藏标记的认证应用数据
        return getBookmarkedAuthApp(userCode);
    }
    

    /**
     * 获取收藏在Redis中的键
     * 
     * @param userCode 用户代码
     * @param bookmarkId 收藏ID
     * @return Redis键
     */
    private String getBookmarkKey(String userCode, String bookmarkId) {
        return BOOKMARK_KEY_PREFIX + userCode + BOOKMARK_DATA_KEY_SUFFIX + bookmarkId;
    }
    
    /**
     * 获取收藏ID列表在Redis中的键
     * 
     * @param userCode 用户代码
     * @return Redis键
     */
    private String getBookmarkIdsKey(String userCode) {
        return BOOKMARK_KEY_PREFIX + userCode + BOOKMARK_IDS_KEY_SUFFIX;
    }
    
    @Override
    public boolean clearBookmark(String userCode) {
        // 获取用户所有收藏ID
        List<String> bookmarkIds = getUserBookmarkIds(userCode);
        
        // 删除所有收藏
        for (String bookmarkId : bookmarkIds) {
            String key = getBookmarkKey(userCode, bookmarkId);
            redisUtils.remove(key);
        }
        
        // 删除收藏ID列表
        String idsKey = getBookmarkIdsKey(userCode);
        redisUtils.remove(idsKey);
        
        return true;
    }
    
    @Override
    public boolean unbookmarkDomain(String userCode, String domainCode) {
        if (userCode == null || domainCode == null) {
            return false;
        }
        
        // 获取用户所有收藏
        List<UserBookmark> bookmarks = getAllUserBookmarks(userCode);
        List<String> idsToRemove = new ArrayList<>();
        
        // 找出要删除的领域收藏
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getBookmarkType() == BookmarkType.DOMAIN && 
                domainCode.equals(bookmark.getDomainCode())) {
                idsToRemove.add(bookmark.getId());
            }
        }
        
        // 删除收藏
        return removeBookmarks(userCode, idsToRemove);
    }
    
    @Override
    public boolean unbookmarkProducts(String userCode, String domainCode, Set<String> productIds) {
        if (userCode == null || domainCode == null || productIds == null || productIds.isEmpty()) {
            return false;
        }
        
        // 获取用户所有收藏
        List<UserBookmark> bookmarks = getAllUserBookmarks(userCode);
        List<String> idsToRemove = new ArrayList<>();
        
        // 找出要删除的产品收藏
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getBookmarkType() == BookmarkType.PRODUCT && 
                domainCode.equals(bookmark.getDomainCode()) && 
                bookmark.getProductIds() != null) {
                
                // 检查是否包含要取消收藏的产品ID
                Set<String> bookmarkProductIds = new HashSet<>(bookmark.getProductIds());
                bookmarkProductIds.retainAll(productIds);
                
                if (!bookmarkProductIds.isEmpty()) {
                    idsToRemove.add(bookmark.getId());
                }
            }
        }
        
        // 删除收藏
        return removeBookmarks(userCode, idsToRemove);
    }
    
    @Override
    public boolean unbookmarkSubProducts(String userCode, String domainCode, String productId, Set<String> subProductCodes) {
        if (userCode == null || domainCode == null || subProductCodes == null || subProductCodes.isEmpty()) {
            return false;
        }
        
        // 获取用户所有收藏
        List<UserBookmark> bookmarks = getAllUserBookmarks(userCode);
        List<String> idsToRemove = new ArrayList<>();
        
        // 找出要删除的子产品收藏
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getBookmarkType() == BookmarkType.SUB_PRODUCT && 
                domainCode.equals(bookmark.getDomainCode()) && 
                bookmark.getSubProductCodes() != null) {

                // 如果指定了产品ID，则需要匹配
/*
                if (productId != null && bookmark.getProductIds() != null) {
                    if (!bookmark.getProductIds().contains(productId)) {
                        continue;
                    }
                }
*/

                // 检查是否包含要取消收藏的子产品编码
                Set<String> bookmarkSubProductCodes = new HashSet<>(bookmark.getSubProductCodes());
                bookmarkSubProductCodes.retainAll(subProductCodes);
                
                if (!bookmarkSubProductCodes.isEmpty()) {
                    idsToRemove.add(bookmark.getId());
                }
            }
        }
        
        // 删除收藏
        return removeBookmarks(userCode, idsToRemove);
    }
    
    /**
     * 删除指定的收藏
     * 
     * @param userCode 用户编码
     * @param bookmarkIds 要删除的收藏ID列表
     * @return 是否删除成功
     */
    private boolean removeBookmarks(String userCode, List<String> bookmarkIds) {
        if (bookmarkIds.isEmpty()) {
            return true;
        }
        
        try {
            String idsKey = getBookmarkIdsKey(userCode);
            
            // 删除每个收藏
            for (String bookmarkId : bookmarkIds) {
                // 删除收藏数据
                String key = getBookmarkKey(userCode, bookmarkId);
                redisUtils.remove(key);
                
                // 从ID列表中删除
                if (redisUtils.exists(idsKey)) {
                    redisUtils.lRem(idsKey, 0, bookmarkId);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("删除收藏失败: userCode={}, error={}", userCode, e.getMessage());
            return false;
        }
    }
}
