package com.haier.devops.bill.bookmark.api;

import com.haier.devops.bill.bookmark.model.UserBookmark;
import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;

import java.util.List;

/**
 * 用户收藏服务接口
 * 提供用户收藏领域、产品、子产品的功能
 */
public interface BookmarkService {
    
    /**
     * 收藏领域
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @return 是否收藏成功
     */
    boolean bookmarkDomain(String userCode, String domainCode);
    
    /**
     * 收藏产品
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @param productIds 产品ID列表
     * @return 是否收藏成功
     */
    boolean bookmarkProducts(String userCode, String domainCode, java.util.Set<String> productIds);
    
    /**
     * 收藏子产品
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @param productId 产品ID
     * @param subProductCodes 子产品编码列表
     * @return 是否收藏成功
     */
    boolean bookmarkSubProducts(String userCode, String domainCode, String productId, java.util.Set<String> subProductCodes);
    
    /**
     * 获取用户收藏信息
     * 
     * @param userCode 用户编码
     * @return 用户收藏信息列表
     */
    List<UserBookmark> getUserBookmark(String userCode);
    
    /**
     * 获取用户授权的应用数据，并标记收藏状态
     * 
     * @param userCode 用户编码
     * @return 用户授权的应用数据，带有收藏标记
     */
    AuthAppForItResponse getAuthAppWithBookmark(String userCode);
    
    /**
     * 清除用户收藏
     * 
     * @param userCode 用户编码
     * @return 是否清除成功
     */
    boolean clearBookmark(String userCode);
    
    /**
     * 取消收藏领域
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @return 是否取消成功
     */
    boolean unbookmarkDomain(String userCode, String domainCode);
    
    /**
     * 取消收藏产品
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @param productIds 产品ID列表
     * @return 是否取消成功
     */
    boolean unbookmarkProducts(String userCode, String domainCode, java.util.Set<String> productIds);
    
    /**
     * 取消收藏子产品
     * 
     * @param userCode 用户编码
     * @param domainCode 领域编码
     * @param productId 产品ID
     * @param subProductCodes 子产品编码列表
     * @return 是否取消成功
     */
    boolean unbookmarkSubProducts(String userCode, String domainCode, String productId, java.util.Set<String> subProductCodes);
}
