package com.haier.devops.bill.bookmark.model;

import com.haier.devops.bill.common.api.HworkBillApi.AuthAppForItResponse;
import com.haier.devops.bill.common.api.HworkBillApi.Domain;
import com.haier.devops.bill.common.api.HworkBillApi.Product;
import com.haier.devops.bill.common.api.HworkBillApi.SubProduct;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 带有收藏标记的授权数据响应
 * 扩展原始API响应，增加收藏标记
 */
@Data
@Slf4j
public class BookmarkedAuthResponse {
    
    private List<BookmarkedDomain> domains = new ArrayList<>();
    private List<BookmarkedProduct> products = new ArrayList<>();
    private List<BookmarkedSubProduct> subProducts = new ArrayList<>();
    private List<UserBookmark> bookmarks = new ArrayList<>();
    
    /**
     * 从原始响应和用户收藏信息创建带有收藏标记的响应
     * @deprecated 使用 fromMultiple 替代，以支持多个收藏
     */
    public static BookmarkedAuthResponse from(AuthAppForItResponse response, UserBookmark bookmark) {
        if (response == null) {
            return null;
        }
        
        BookmarkedAuthResponse result = new BookmarkedAuthResponse();
        
        // 处理领域
        if (response.getDomains() != null) {
            for (Domain domain : response.getDomains()) {
                boolean bookmarked = false;
                
                // 检查是否已经在原始响应中标记为收藏
                if (domain.isBookmarked()) {
                    bookmarked = true;
                } else if (bookmark != null && domain.getDomainCode().equals(bookmark.getDomainCode())) {
                    // 根据用户收藏信息标记
                    bookmarked = true;
                    // 更新原始响应中的收藏标记
                    domain.setBookmarked(true);
                }
                
                result.getDomains().add(new BookmarkedDomain(
                        domain.getDomainCode(),
                        domain.getDomainName(),
                        domain.isDefaultChecked(),
                        bookmarked
                ));
            }
        }
        
        // 处理产品
        if (response.getProducts() != null) {
            Set<String> bookmarkedProductIds = bookmark != null && 
                    bookmark.getBookmarkType() != BookmarkType.DOMAIN && 
                    bookmark.getProductIds() != null ? 
                    bookmark.getProductIds() : null;
            
            for (Product product : response.getProducts()) {
                boolean bookmarked = false;
                
                // 检查是否已经在原始响应中标记为收藏
                if (product.isBookmarked()) {
                    bookmarked = true;
                } else if (bookmark != null && 
                        (bookmark.getBookmarkType() == BookmarkType.DOMAIN || 
                         (bookmarkedProductIds != null && bookmarkedProductIds.contains(product.getId())))) {
                    // 根据用户收藏信息标记
                    bookmarked = true;
                    // 更新原始响应中的收藏标记
                    product.setBookmarked(true);
                }
                
                result.getProducts().add(new BookmarkedProduct(
                        product.getId(),
                        product.getProductName(),
                        product.getProductNameShort(),
                        bookmarked
                ));
            }
        }
        
        // 处理子产品
        if (response.getSubProducts() != null) {
            Set<String> bookmarkedSubProductCodes = bookmark != null && 
                    bookmark.getBookmarkType() == BookmarkType.SUB_PRODUCT && 
                    bookmark.getSubProductCodes() != null ? 
                    bookmark.getSubProductCodes() : null;
            
            for (SubProduct subProduct : response.getSubProducts()) {
                boolean bookmarked = false;
                
                // 检查是否已经在原始响应中标记为收藏
                if (subProduct.isBookmarked()) {
                    bookmarked = true;
                } else if (bookmark != null && 
                        (bookmark.getBookmarkType() == BookmarkType.DOMAIN || 
                         (bookmark.getBookmarkType() == BookmarkType.PRODUCT && 
                          bookmark.getProductIds() != null && 
                          !bookmark.getProductIds().isEmpty()) ||
                         (bookmarkedSubProductCodes != null && 
                          bookmarkedSubProductCodes.contains(subProduct.getAppScode())))) {
                    // 根据用户收藏信息标记
                    bookmarked = true;
                    // 更新原始响应中的收藏标记
                    subProduct.setBookmarked(true);
                }
                
                result.getSubProducts().add(new BookmarkedSubProduct(
                        subProduct.getAppScode(),
                        subProduct.getAppShortName(),
                        subProduct.getAppName(),
                        bookmarked
                ));
            }
        }
        
        // 添加书签信息
        if (bookmark != null) {
            List<UserBookmark> bookmarks = new ArrayList<>();
            bookmarks.add(bookmark);
            result.setBookmarks(bookmarks);
        }
        
        return result;
    }
    
    /**
     * 从原始响应和多个用户收藏信息创建带有收藏标记的响应
     * 支持多个收藏，以数组形式返回
     * 
     * 注意：此方法已被弃用，主要逻辑已移至BookmarkServiceImpl.getBookmarkedAuthApp方法
     * 保留此方法仅为了向后兼容
     * 
     * @param response 原始响应
     * @param bookmarks 用户收藏信息列表
     * @return 带有收藏标记的响应
     */
    public static BookmarkedAuthResponse fromMultiple(AuthAppForItResponse response, List<UserBookmark> bookmarks) {
        if (response == null) {
            return null;
        }
        
        BookmarkedAuthResponse result = new BookmarkedAuthResponse();
        
        // 处理领域
        if (response.getDomains() != null) {
            for (Domain domain : response.getDomains()) {
                result.getDomains().add(new BookmarkedDomain(
                        domain.getDomainCode(),
                        domain.getDomainName(),
                        domain.isDefaultChecked(),
                        domain.isBookmarked()
                ));
            }
        }
        
        // 处理产品
        if (response.getProducts() != null) {
            for (Product product : response.getProducts()) {
                result.getProducts().add(new BookmarkedProduct(
                        product.getId(),
                        product.getProductName(),
                        product.getProductNameShort(),
                        product.isBookmarked()
                ));
            }
        }
        
        // 处理子产品
        if (response.getSubProducts() != null) {
            for (SubProduct subProduct : response.getSubProducts()) {
                result.getSubProducts().add(new BookmarkedSubProduct(
                        subProduct.getAppScode(),
                        subProduct.getAppShortName(),
                        subProduct.getAppName(),
                        subProduct.isBookmarked()
                ));
            }
        }
        
        // 添加所有书签信息
        if (bookmarks != null && !bookmarks.isEmpty()) {
            result.setBookmarks(bookmarks);
        }
        
        return result;
    }
    
    /**
     * 带有收藏标记的领域
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class BookmarkedDomain extends Domain {
        private boolean bookmarked;
        
        public BookmarkedDomain(String domainCode, String domainName, boolean defaultChecked, boolean bookmarked) {
            super(domainCode, domainName, defaultChecked);
            this.bookmarked = bookmarked;
        }
    }
    
    /**
     * 带有收藏标记的产品
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class BookmarkedProduct extends Product {
        private boolean bookmarked;
        
        public BookmarkedProduct(String id, String productName, String productNameShort, boolean bookmarked) {
            super(id, productName, productNameShort);
            this.bookmarked = bookmarked;
        }
    }
    
    /**
     * 带有收藏标记的子产品
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class BookmarkedSubProduct extends SubProduct {
        private boolean bookmarked;
        
        public BookmarkedSubProduct(String appScode, String appShortName, String appName, boolean bookmarked) {
            super(appScode, appShortName, appName);
            this.bookmarked = bookmarked;
        }
    }
}
