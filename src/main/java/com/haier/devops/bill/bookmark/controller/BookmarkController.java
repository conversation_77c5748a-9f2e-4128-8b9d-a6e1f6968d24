package com.haier.devops.bill.bookmark.controller;

import com.haier.devops.bill.bookmark.api.BookmarkService;
import com.haier.devops.bill.bookmark.model.UserBookmark;
import com.haier.devops.bill.bookmark.service.BookmarkServiceImpl;
import com.haier.devops.bill.common.api.HworkBillApi;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.haier.devops.bill.common.controller.ResponseEnum.*;

/**
 * 用户收藏控制器
 * 提供用户收藏领域、产品、子产品的REST接口
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/bookmarks")
@Slf4j
public class BookmarkController {

    @Autowired
    private BookmarkService bookmarkService;
    
    @Autowired
    private BookmarkServiceImpl bookmarkServiceImpl;
    
    /**
     * 获取当前用户授权的应用数据，带有收藏标记
     * 
     * @return 用户授权的应用数据，带有收藏标记
     */
    @GetMapping("/auth-app")
    public ResponseEntityWrapper<HworkBillApi.AuthAppForItResponse> getBookmarkedAuthApp() {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        return new ResponseEntityWrapper<>(bookmarkServiceImpl.getBookmarkedAuthApp(currentUser.getUserCode()));
    }
    
    /**
     * 获取当前用户收藏信息
     * 
     * @return 用户收藏信息
     */
    @GetMapping
    public ResponseEntityWrapper<List<UserBookmark>> getUserBookmark() {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        List<UserBookmark> bookmarks = bookmarkServiceImpl.getAllUserBookmarks(currentUser.getUserCode());
        return new ResponseEntityWrapper<>(bookmarks);
    }


    /**
     * 获取当前用户收藏信息（合并）
     *
     * @return 用户收藏信息
     */
    @GetMapping("/getMergedBookmark")
    public ResponseEntityWrapper<UserBookmark> getMergedBookmark() {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }

        List<UserBookmark> bookmarks = bookmarkServiceImpl.getAllUserBookmarks(currentUser.getUserCode());
        
        // 如果没有书签，返回空
        if (bookmarks == null || bookmarks.isEmpty()) {
            return new ResponseEntityWrapper<>(null);
        }
        
        // 合并所有书签
        UserBookmark mergedBookmark = new UserBookmark();
        mergedBookmark.setUserCode(currentUser.getUserCode());
        
        // 由于只允许同时收藏一个领域的书签，所以所有书签的domainCode应该相同
        // 使用第一个书签的domainCode
        String domainCode = bookmarks.get(0).getDomainCode();
        mergedBookmark.setDomainCode(domainCode);
        
        // 合并所有productIds
        Set<String> allProductIds = new HashSet<>();
        
        // 合并所有subProductCodes
        Set<String> allSubProductCodes = new HashSet<>();
        
        // 遍历所有书签，合并productIds和subProductCodes
        for (UserBookmark bookmark : bookmarks) {
            // 合并productIds
            if (bookmark.getProductIds() != null) {
                allProductIds.addAll(bookmark.getProductIds());
            }
            
            // 合并subProductCodes
            if (bookmark.getSubProductCodes() != null) {
                allSubProductCodes.addAll(bookmark.getSubProductCodes());
            }
        }
        
        // 设置合并后的productIds和subProductCodes
        mergedBookmark.setProductIds(allProductIds);
        mergedBookmark.setSubProductCodes(allSubProductCodes);
        
        return new ResponseEntityWrapper<>(mergedBookmark);
    }

    /**
     * 获取当前用户所有收藏信息
     * 
     * @return 用户所有收藏信息列表
     */
    @GetMapping("/all")
    public ResponseEntityWrapper<List<UserBookmark>> getAllUserBookmarks() {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        List<UserBookmark> bookmarks = bookmarkServiceImpl.getAllUserBookmarks(currentUser.getUserCode());
        return new ResponseEntityWrapper<>(bookmarks);
    }
    
    /**
     * 收藏领域
     * 
     * @param request 收藏领域请求
     * @return 是否收藏成功
     */
    @PostMapping("/domain")
    public ResponseEntityWrapper<Boolean> bookmarkDomain(@RequestBody DomainBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码不能为空", false);
        }
        
        // 检查是否存在其他领域的书签
        String userCode = currentUser.getUserCode();
        String newDomainCode = request.getDomainCode();
        Boolean forced = request.getForced() != null ? request.getForced() : false;
        
        // 获取所有书签，检查是否有不同领域的书签
        List<UserBookmark> bookmarks = bookmarkService.getUserBookmark(userCode);
        boolean hasDifferentDomain = false;
        String existingDomain = null;
        
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getDomainCode() != null && !bookmark.getDomainCode().equals(newDomainCode)) {
                hasDifferentDomain = true;
                existingDomain = bookmark.getDomainCode();
                break;
            }
        }
        
        if (hasDifferentDomain) {
            if (forced) {
                // 如果存在不同领域的书签且强制收藏，则清除所有书签
                bookmarkService.clearBookmark(userCode);
            } else {
                // 如果存在不同领域的书签且非强制收藏，则返回提示信息
                return new ResponseEntityWrapper<>(REQUEST_CONFLICTS, "已存在其他领域的书签（" + existingDomain + "），请先删除或使用强制收藏", false);
            }
        }
        
        boolean success = bookmarkService.bookmarkDomain(userCode, newDomainCode);
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 收藏产品
     * 
     * @param request 收藏产品请求
     * @return 是否收藏成功
     */
    @PostMapping("/products")
    public ResponseEntityWrapper<Boolean> bookmarkProducts(@RequestBody ProductsBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null || request.getProductIds() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码或产品ID不能为空", false);
        }
        
        // 检查是否存在其他领域的书签
        String userCode = currentUser.getUserCode();
        String newDomainCode = request.getDomainCode();
        Boolean forced = request.getForced() != null ? request.getForced() : false;
        
        // 获取所有书签，检查是否有不同领域的书签
        List<UserBookmark> bookmarks = bookmarkService.getUserBookmark(userCode);
        boolean hasDifferentDomain = false;
        String existingDomain = null;
        
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getDomainCode() != null && !bookmark.getDomainCode().equals(newDomainCode)) {
                hasDifferentDomain = true;
                existingDomain = bookmark.getDomainCode();
                break;
            }
        }
        
        if (hasDifferentDomain) {
            if (forced) {
                // 如果存在不同领域的书签且强制收藏，则清除所有书签
                bookmarkService.clearBookmark(userCode);
            } else {
                // 如果存在不同领域的书签且非强制收藏，则返回提示信息
                return new ResponseEntityWrapper<>(REQUEST_CONFLICTS, "已存在其他领域的书签（" + existingDomain + "），请先删除或使用强制收藏", false);
            }
        }
        
        boolean success = bookmarkService.bookmarkProducts(userCode, newDomainCode, request.getProductIds());
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 收藏子产品
     * 
     * @param request 收藏子产品请求
     * @return 是否收藏成功
     */
    @PostMapping("/sub-products")
    public ResponseEntityWrapper<Boolean> bookmarkSubProducts(@RequestBody SubProductsBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null || request.getSubProductCodes() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码或子产品编码不能为空", false);
        }
        
        // 检查是否存在其他领域的书签
        String userCode = currentUser.getUserCode();
        String newDomainCode = request.getDomainCode();
        Boolean forced = request.getForced() != null ? request.getForced() : false;
        
        // 获取所有书签，检查是否有不同领域的书签
        List<UserBookmark> bookmarks = bookmarkService.getUserBookmark(userCode);
        boolean hasDifferentDomain = false;
        String existingDomain = null;
        
        for (UserBookmark bookmark : bookmarks) {
            if (bookmark.getDomainCode() != null && !bookmark.getDomainCode().equals(newDomainCode)) {
                hasDifferentDomain = true;
                existingDomain = bookmark.getDomainCode();
                break;
            }
        }
        
        if (hasDifferentDomain) {
            if (forced) {
                // 如果存在不同领域的书签且强制收藏，则清除所有书签
                bookmarkService.clearBookmark(userCode);
            } else {
                // 如果存在不同领域的书签且非强制收藏，则返回提示信息
                return new ResponseEntityWrapper<>(REQUEST_CONFLICTS, "已存在其他领域的书签（" + existingDomain + "），请先删除或使用强制收藏", false);
            }
        }
        
        boolean success = bookmarkService.bookmarkSubProducts(
                userCode, 
                newDomainCode, 
                request.getProductId(), 
                request.getSubProductCodes());
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 取消收藏领域
     * 
     * @param request 取消收藏领域请求
     * @return 是否取消成功
     */
    @DeleteMapping("/domain")
    public ResponseEntityWrapper<Boolean> unbookmarkDomain(@RequestBody DomainBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码不能为空", false);
        }
        
        String userCode = currentUser.getUserCode();
        String domainCode = request.getDomainCode();
        
        boolean success = bookmarkService.unbookmarkDomain(userCode, domainCode);
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 取消收藏产品
     * 
     * @param request 取消收藏产品请求
     * @return 是否取消成功
     */
    @DeleteMapping("/products")
    public ResponseEntityWrapper<Boolean> unbookmarkProducts(@RequestBody ProductsBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null || request.getProductIds() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码或产品ID不能为空", false);
        }
        
        String userCode = currentUser.getUserCode();
        String domainCode = request.getDomainCode();
        
        boolean success = bookmarkService.unbookmarkProducts(userCode, domainCode, request.getProductIds());
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 取消收藏子产品
     * 
     * @param request 取消收藏子产品请求
     * @return 是否取消成功
     */
    @DeleteMapping("/sub-products")
    public ResponseEntityWrapper<Boolean> unbookmarkSubProducts(@RequestBody SubProductsBookmarkRequest request) {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        if (request == null || request.getDomainCode() == null || request.getSubProductCodes() == null) {
            return new ResponseEntityWrapper<>(REQUEST_PARAM_LOST, "领域编码或子产品编码不能为空", false);
        }
        
        String userCode = currentUser.getUserCode();
        String domainCode = request.getDomainCode();
        
        boolean success = bookmarkService.unbookmarkSubProducts(
                userCode, 
                domainCode, 
                request.getProductId(), 
                request.getSubProductCodes());
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 清除当前用户收藏
     * 
     * @return 是否清除成功
     */
    @DeleteMapping
    public ResponseEntityWrapper<Boolean> clearBookmark() {
        User currentUser = LoginContextHolder.getCurrentUser();
        if (currentUser == null) {
            return new ResponseEntityWrapper<>(REQUEST_AUTH_FAILED, "用户未登录", null);
        }
        
        boolean success = bookmarkService.clearBookmark(currentUser.getUserCode());
        return new ResponseEntityWrapper<>(success);
    }
    
    /**
     * 收藏领域请求
     */
    @Data
    public static class DomainBookmarkRequest {
        private String domainCode;
        private Boolean forced = false;
    }
    
    /**
     * 收藏产品请求
     */
    @Data
    public static class ProductsBookmarkRequest {
        private String domainCode;
        private Set<String> productIds;
        private Boolean forced = false;
    }
    
    /**
     * 收藏子产品请求
     */
    @Data
    public static class SubProductsBookmarkRequest {
        private String domainCode;
        private String productId;
        private Set<String> subProductCodes;
        private Boolean forced = false;
    }
}
