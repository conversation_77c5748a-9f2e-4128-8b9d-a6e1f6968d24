package com.haier.devops.bill.sdks.feishu;

import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.export.util.SheetUtil;
import com.haier.devops.bill.sdks.feishu.sheet.SpreadsheetResp;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.core.token.AccessTokenType;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.sheets.v3.model.GridProperties;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetReq;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetResp;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class FeishuClient {
    private final Client client;

    public FeishuClient(Client client) {
        this.client = client;
    }

    public Sheet[] getSheetsFromToken(String token) throws Exception {
        QuerySpreadsheetSheetResp sheetResp =
                client.sheets().spreadsheetSheet().query(
                        QuerySpreadsheetSheetReq.newBuilder().spreadsheetToken(token).build());
        if (!sheetResp.success()) {
            throw new FeishuClientException(
                    sheetResp.getCode(), sheetResp.getMsg(),
                    String.format("query spreadsheet sheet failed, code %s message %s", sheetResp.getCode(), sheetResp.getMsg()));
        }

        return sheetResp.getData().getSheets();
    }

    public List<Object> getSheetContents(Sheet sheet, String token) throws Exception {
        GridProperties gridProp = sheet.getGridProperties();
        int rowCnt = gridProp.getRowCount();
        int colCnt = gridProp.getColumnCount();

        String valueRange = sheet.getSheetId() +
                "!A1:" +
                convertToExcelColumn(colCnt - 1) +
                rowCnt;

        RawResponse rawResponse =
                client.get(String.format("/open-apis/sheets/v2/spreadsheets/%s/values/%s", token, valueRange),
                        null,
                        AccessTokenType.App);

        SpreadsheetResp readResp = Jsons.DEFAULT.fromJson(new String(rawResponse.getBody()), SpreadsheetResp.class);
        if (!readResp.success()) {
            log.error("read spreadsheet failed, code: {} message: {}", readResp.getCode(), readResp.getMsg());
            throw new FeishuClientException(
                    readResp.getCode(),
                    readResp.getMsg(),
                    String.format("read spreadsheet failed, code %s message %s", readResp.getCode(), readResp.getMsg()));
        }

        return readResp.getData().getValueRange().getValues();
    }


    private String convertToExcelColumn(long num) {
        StringBuilder result = new StringBuilder();
        while (num >= 0) {
            long remainder = num % 26;
            result.insert(0, (char) ('A' + remainder));
            num = (num - remainder) / 26 - 1;
        }
        return result.toString();
    }

    public static void main(String[] args) throws Exception {
        Client client = Client.newBuilder("cli_a480637b2577500c", "s9rpPIQ7bUSGw2RyEtwsWefiSPhmyQ2K").build();

        String sheetToken = "QxXGs1StOhMKY6tQhXpcYBCHnCg";
/*
        Sheet[] sheets = feishuClient.getSheetsFromToken(sheetToken);
        List<Object> sheetContents = feishuClient.getSheetContents(sheets[0], sheetToken);
        System.out.println(JSON.toJSONString(sheetContents));
*/
        FeishuClient feishuClient = new FeishuClient(client);
        ServiceResult<List<Object>> sheetContents = SheetUtil.getSheetContents(feishuClient, sheetToken);
        if (!sheetContents.isSuccess()) {
            return;
        }

    }
}
