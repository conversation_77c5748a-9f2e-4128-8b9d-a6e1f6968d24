package com.haier.devops.bill.sdks.haier;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SignatureClient {
    private static final String SIGN_DOMAIN = "http://sign.hrit.haier.net";
    private static final String SIGN_API_GET_SIGN = "/api/get";

    private final String user;
    private final String pass;
    private final String publicKey;
    private final CloseableHttpClient httpClient;

    public SignatureClient(String user, String pass, String publicKey) {
        this.user = user;
        this.pass = pass;
        this.publicKey = publicKey;
        this.httpClient = HttpClients.createDefault();
    }

    public List<SignInfo> get(String... userId) throws IOException, URISyntaxException, ParseException {
        Map<String, Object> params = new HashMap<>();
        params.put("userCode", String.join(",", userId));
        params.put("system", this.user);
        params.put("timestamp", Instant.now().toEpochMilli());

        sign(params);

        HttpPost request = new HttpPost(new URI(SIGN_DOMAIN + SIGN_API_GET_SIGN));
        request.setHeader("Content-Type", "application/json");
        String auth = user + ":" + pass;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        request.setHeader("Authorization", "Basic " + encodedAuth);

        String jsonParams = new Gson().toJson(params);
        request.setEntity(new StringEntity(jsonParams, StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String jsonResponse = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            GetSignResponse data = new Gson().fromJson(jsonResponse, GetSignResponse.class);

            switch (data.getCode()) {
                case "0000":
                    Type listType = new TypeToken<List<SignInfo>>() {}.getType();
                    return new Gson().fromJson(data.getResult(), listType);
                default:
                    throw new IOException(data.getMessage());
            }
        }
    }

    private void sign(Map<String, Object> params) {
        String sign = SignUtil.encode(SignUtil.sortMapString(params), publicKey);
        params.put("token", sign);
        params.put("publicKey", publicKey);
    }

    @Getter
    @Setter
    static class GetSignResponse {
        private String code;
        private String message;
        private String result;
    }

    @Data
    public static class SignInfo {
        private String format;
        private String autograph;
        private String userCode;
        private String status;

        public String save(String dir) throws IOException {
            byte[] decodeBytes = Base64.getDecoder().decode(this.autograph);
            String fileFormat = this.format != null && !this.format.isEmpty() ? this.format : "png";
            String fileName = "sign_" + this.userCode + "_" + Instant.now().toEpochMilli() + "." + fileFormat;
            File file = new File(Paths.get(dir, fileName).toString());

            // Create the directories if they do not exist
            Files.createDirectories(Paths.get(dir));

            // Write to the file
            try (FileOutputStream fos = new FileOutputStream(file)) {
                fos.write(decodeBytes);
            }

            return file.getAbsolutePath();
        }
    }


}