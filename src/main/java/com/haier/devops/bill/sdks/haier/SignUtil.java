package com.haier.devops.bill.sdks.haier;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

public class SignUtil {
    private static final int ENCODE_LENGTH = 117;

    public static String sortMapString(Map<String, Object> map) {
        List<KeyValue> sorted = sortKey(map);
        StringBuilder sb = new StringBuilder();
        for (KeyValue kv : sorted) {
            sb.append(String.format("%s=%s&", kv.getKey(), kv.getValue()));
        }
        // 删除最后一个 & 字符
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }

    private static List<KeyValue> sortKey(Map<String, Object> map) {
        List<KeyValue> sorted = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sorted.add(new KeyValue(entry.getKey(), entry.getValue()));
        }
        Collections.sort(sorted);
        return sorted;
    }

    // Inner class to represent key-value pairs
    private static class KeyValue implements Comparable<KeyValue> {
        private final String key;
        private final Object value;

        public KeyValue(String key, Object value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public Object getValue() {
            return value;
        }

        @Override
        public int compareTo(KeyValue other) {
            return this.key.compareTo(other.key);
        }
    }

    public static String encode(String data, String publicKey) {
        try {
            if (data.length() > ENCODE_LENGTH) {
                List<byte[]> encryptedChunks = new ArrayList<>();
                for (int i = 0; i < data.length(); i += ENCODE_LENGTH) {
                    int end = Math.min(data.length(), i + ENCODE_LENGTH);
                    String subData = data.substring(i, end);
                    byte[] encryptedData = encodeData(subData, publicKey);
                    encryptedChunks.add(encryptedData);
                }

                // 合并所有加密块
                byte[] combined = concatenate(encryptedChunks);
                return Base64.getEncoder().encodeToString(combined);
            } else {
                byte[] encryptedData = encodeData(data, publicKey);
                return Base64.getEncoder().encodeToString(encryptedData);
            }
        } catch (GeneralSecurityException e) {
            throw new RuntimeException("Error encoding data", e);
        }
    }

    private static byte[] encodeData(String data, String publicKey) throws GeneralSecurityException {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKey);
        PublicKey pubKey = rsaPublicKeyFromBytes(publicKeyBytes);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey, new SecureRandom());
        return cipher.doFinal(data.getBytes());
    }

    private static PublicKey rsaPublicKeyFromBytes(byte[] publicKeyBytes) throws NoSuchAlgorithmException, InvalidKeySpecException {
        X509EncodedKeySpec spec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    private static byte[] concatenate(List<byte[]> chunks) {
        int length = 0;
        for (byte[] chunk : chunks) {
            length += chunk.length;
        }

        byte[] combined = new byte[length];
        int pos = 0;
        for (byte[] chunk : chunks) {
            System.arraycopy(chunk, 0, combined, pos, chunk.length);
            pos += chunk.length;
        }
        return combined;
    }

    public static String generateHash(List<String> data) throws NoSuchAlgorithmException {
        // Concatenate all strings in the list
        StringBuilder concatenatedData = new StringBuilder();
        for (String str : data) {
            concatenatedData.append(str);
        }

        // Get bytes of the concatenated string
        byte[] bytes = concatenatedData.toString().getBytes(StandardCharsets.UTF_8);

        // Use SHA-256 hash function
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hashBytes = digest.digest(bytes);

        // Convert hash bytes to hex
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        // Return the first 6 characters of the hex string
        return hexString.toString().substring(0, 6);
    }

}