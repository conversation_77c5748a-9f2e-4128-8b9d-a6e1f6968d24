package com.haier.devops.bill.sdks.aliyun;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.GetObjectRequest;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Paths;
import java.util.UUID;

@Component("contractOssClient")
public class ContractOssClient {
    private OSS client;

    public static final String OSS_BUCKET_NAME = "haier-hdshcms-report";

    public ContractOssClient(OSS client) {
        this.client = client;
    }


    public Bucket getBucket(String bucketName) throws IllegalArgumentException, OSSException {
        if (bucketName == null || bucketName.isEmpty()) {
            throw new IllegalArgumentException("bucketName is empty");
        }
        return client.getBucketInfo(bucketName).getBucket();
    }


    public String unifyOSSUri(String uri) {
        uri = uri.startsWith("/") ? uri.substring(1) : uri;
        if (uri.startsWith(OSS_BUCKET_NAME)) {
            uri = uri.substring(OSS_BUCKET_NAME.length() + 1);
        }
        return uri;
    }

    public File getOssFile(String uri) throws OSSException {
        uri = unifyOSSUri(uri);

        String tmpFileName = UUID.randomUUID().toString() + Paths.get(uri).getFileName().toString();
        File tmpFile = new File(System.getProperty("java.io.tmpdir"), tmpFileName);
        client.getObject(new GetObjectRequest(OSS_BUCKET_NAME, uri), tmpFile);

        return tmpFile;
    }

    public void uploadOssFile(String uri, String filePath) throws OSSException {
        uri = unifyOSSUri(uri);
        client.putObject(OSS_BUCKET_NAME, uri, new File(filePath));
    }

}
