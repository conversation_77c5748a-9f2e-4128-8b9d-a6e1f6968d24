package com.haier.devops.bill.sdks.aliyun;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "aliyun.imm")
@Data
public class ImmClientConfigProperties {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
}
