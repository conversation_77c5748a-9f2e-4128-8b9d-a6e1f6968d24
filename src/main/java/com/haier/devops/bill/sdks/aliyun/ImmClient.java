package com.haier.devops.bill.sdks.aliyun;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.imm20200930.AsyncClient;
import com.aliyun.sdk.service.imm20200930.models.CreateOfficeConversionTaskRequest;
import com.aliyun.sdk.service.imm20200930.models.CreateOfficeConversionTaskResponse;
import darabonba.core.client.ClientOverrideConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

public class ImmClient {
    private static final Logger log = LoggerFactory.getLogger(ImmClient.class);
    private AsyncClient client;

    public ImmClient(String endpoint, String accessKeyId, String accessKeySecret) {
        // Configure Credentials authentication information, including ak, secret, token
        StaticCredentialProvider provider = StaticCredentialProvider.create(
                Credential.builder()
                        .accessKeyId(accessKeyId)
                        .accessKeySecret(accessKeySecret)
                        .build());

        // Configure the Client
        client = AsyncClient.builder()
                // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                //.httpClient(httpClient)
                .credentialsProvider(provider)
                // Service-level configuration
                //.serviceConfiguration(Configuration.create())
                // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/imm
//                                .setEndpointOverride("imm.cn-hangzhou.aliyuncs.com")
                                .setEndpointOverride(endpoint)
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();
    }

    public void createOfficeConversionTask(String srcUri, String bucketName) {
        String projectName = "hdshcms-S02166";
        srcUri = "oss://" + bucketName + "/" + srcUri;

        CreateOfficeConversionTaskRequest request = CreateOfficeConversionTaskRequest.builder()
                .projectName(projectName)
                .sourceURI(srcUri)
                .targetURI(convertToPdfUri(srcUri))
                .targetType("pdf")
                .build();
        CompletableFuture<CreateOfficeConversionTaskResponse> convertFuture = client.createOfficeConversionTask(request);
        convertFuture.whenComplete((response, exception) -> {
            if (exception != null) {
                log.error("Failed to create office conversion task", exception);
            } else {
                log.info("Successfully created office conversion task. Response is {}", response);
            }
        });
    }

    private static String convertToPdfUri(String srcUri) {
        Path path = Paths.get(srcUri);
        return convertDocxToPdf(srcUri);
    }

    private static String removeFileExtension(String fileName) {
        int lastIndexOfDot = fileName.lastIndexOf('.');
        if (lastIndexOfDot == -1) {
            return fileName; // No extension found
        }
        return fileName.substring(0, lastIndexOfDot);
    }

    private static String convertDocxToPdf(String filePath) {
        if (filePath == null || !filePath.endsWith(".docx")) {
            throw new IllegalArgumentException("Invalid file path. The file must have a .docx extension.");
        }

        // 替换后缀
        return filePath.substring(0, filePath.length() - 5) + ".pdf";
    }

}
