package com.haier.devops.bill.sdks.feishu;

import com.lark.oapi.Client;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class FeishuClientConfig {
    private final FeishuConfigProperties feishuConfigProperties;


    public FeishuClientConfig(FeishuConfigProperties feishuConfigProperties) {
        this.feishuConfigProperties = feishuConfigProperties;
    }

    public Client client(FeishuConfigProperties feishuConfigProperties) {
        return Client.newBuilder(feishuConfigProperties.getAppId(), feishuConfigProperties.getAppSecret())
                .build();
    }

    @Bean
    public FeishuClient feishuClient() {
        return new FeishuClient(client(feishuConfigProperties));
    }
}

/**
 * <AUTHOR>
 */

@Configuration
@ConfigurationProperties(prefix = "feishu")
@Data
class FeishuConfigProperties {
    private String appId;
    private String appSecret;
}
