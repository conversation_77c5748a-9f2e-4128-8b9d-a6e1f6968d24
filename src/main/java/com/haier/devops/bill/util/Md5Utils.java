package com.haier.devops.bill.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
public class Md5Utils {
    /**
     * MD5加密方法
     * @explain springboot自带MD5加密
     * @param str
     */
    public static String encryptToMd5(String str) {
        log.debug("MD5待加密字符串：\n"+str);
        String md5 = "";
        if (StringUtils.isEmpty(str)) {
            return md5;
        }
        try {
            md5 = DigestUtils.md5DigestAsHex(str.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return md5;
    }

    public static void main(String[] args) {
        String appId = "wjXlDuC96C0ePtpG"; //能力发布平台签发appid
        String appSecret = "I9RuVxMIUCG48j6CKQb0FlcH"; //能力发布平台签发appSecret
        long time = System.currentTimeMillis();
        System.out.println(time);
        String uuid = String.valueOf(UUID.randomUUID());
        System.out.println(uuid);
        String signature = Md5Utils.encryptToMd5(appId + "," + appSecret + "," + time + "," + uuid);
        System.out.println(signature);
    }
}
