package com.haier.devops.bill.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;

/**
 * sql 拼装
 *
 * <AUTHOR>
 */
public class QueryWrapperUtil {
    public static <T> QueryWrapper<T> compose(List<Filter> params) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (null == params) {
            return queryWrapper;
        }
        for (Filter param : params) {
            String column = camelToUnderscore(param.getColumn());
            Object value = param.getValues().get(0);
            switch (OperatorEnum.operatorMap.get(param.getOperator())) {
                case EQ:
                    queryWrapper.eq(column, value);
                    break;
                case NE:
                    queryWrapper.ne(column, value);
                    break;
                case GT:
                    queryWrapper.gt(column, value);
                    break;
                case LT:
                    queryWrapper.lt(column, value);
                    break;
                case GE:
                    queryWrapper.ge(column, value);
                    break;
                case LE:
                    queryWrapper.le(column, value);
                    break;
                case LIKE:
                    queryWrapper.like(column, value);
                    break;
                case NOT_LIKE:
                    queryWrapper.notLike(column, value);
                    break;
                case IN:
                    if ("all".equals(String.valueOf(value))) {
                        break;
                    }
                    queryWrapper.in(column, param.getValues());
                    break;
                case NOT_IN:
                    queryWrapper.notIn(column, param.getValues());
                    break;
                case IS_NULL:
                    queryWrapper.isNull(column);
                    break;
                case IS_NOT_NULL:
                    queryWrapper.isNotNull(column);
                    break;
                default:
                    break;
            }
        }
        return queryWrapper;
    }

    public static String camelToUnderscore(String param) {
        return param.replaceAll("([A-Z])", "_$1").toLowerCase();
    }

    @Getter
    @Setter
    public static class Filter {
        @NotNull
        private String column;
        @NotNull
        private String operator;
        @NotEmpty
        private List<Object> values;
    }
}


enum OperatorEnum {
    EQ("="),
    NE("<>"),
    GT(">"),
    LT("<"),
    GE(">="),
    LE("<="),
    LIKE("like"),
    NOT_LIKE("not like"),
    IN("in"),
    NOT_IN("not in"),
    IS_NULL("is null"),
    IS_NOT_NULL("is not null");

    private String operator;

    public static HashMap<String, OperatorEnum> operatorMap = new HashMap<>();

    static {
        for (OperatorEnum value : OperatorEnum.values()) {
            operatorMap.put(value.getOperator(), value);
        }
    }

    OperatorEnum(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

}



