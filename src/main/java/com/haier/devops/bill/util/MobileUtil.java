package com.haier.devops.bill.util;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2024年02月01日 18:50
 */
public class MobileUtil {
    /**
     * @Description: 手机号脱敏
     * @author: 张爱苹
     * @date: 2024/2/1 13:51
     * @param phoneNum:
     * @Return: java.lang.String
     */
    public static String blurPhoneNum(String phoneNum){
        if(phoneNum == null || phoneNum.length() < 11){
            return phoneNum;
        }
        StringBuilder sb = new StringBuilder(phoneNum);
        for(int i = 3; i < 7; i++){
            sb.setCharAt(i, '*');
        }
        return sb.toString();
    }
}
