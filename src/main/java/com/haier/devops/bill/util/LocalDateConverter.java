package com.haier.devops.bill.util;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.StringUtils;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description Date转换器
 * @date 2023/12/21
 */
public class LocalDateConverter implements Converter<LocalDate> {
    private final static String DATE_FORMAT = "yyyy-MM-dd";

    @Override
    public Class<LocalDate> supportJavaTypeKey() {
        return LocalDate.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDate convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) throws ParseException {
        String value = cellData.getStringValue();
        if (StringUtils.isNotBlank(value)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
            return LocalDate.parse(value, formatter);
        }
        return null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(LocalDate localDate, ExcelContentProperty excelContentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (localDate != null) {
            return new WriteCellData<>(DateTimeFormatter.ofPattern(DATE_FORMAT).format(localDate));
        }
        return null;
    }
}
