package com.haier.devops.bill.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
public class IpUtil {
    private static final String POD_IP = "POD_IP";
    private static final String LOCALHOST = "127.0.0.1";

    /**
     * check if ip is reachable
     *
     * @param ip
     * @return
     */
    public static boolean isIpReachable(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            // Timeout in milliseconds
            return inetAddress.isReachable(3000);
        } catch (UnknownHostException e) {
            log.error("Invalid IP address: {}", ip);
        } catch (Exception e) {
            log.error("An error occurred", e);
        }

        return false;
    }

    /**
     * get pod ip
     *
     * @return
     */
    public static String getPodIp() {
        String podIP = System.getenv(POD_IP);
        if (StringUtils.isNotBlank(podIP)) {
            return podIP;
        }
        InetAddress host = null;
        try {
            host = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            log.error("Failed to get localhost", e);
        }
        return null == host ? LOCALHOST : host.getHostAddress();
    }

    public static void main(String[] args) {
        System.out.println(isIpReachable("*********"));
        System.out.println(isIpReachable("************"));
        System.out.println(isIpReachable("*************"));
    }
}
