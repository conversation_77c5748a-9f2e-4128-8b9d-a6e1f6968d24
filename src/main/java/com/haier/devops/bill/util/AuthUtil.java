package com.haier.devops.bill.util;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.config.SpringContextAware;
import com.haier.devops.bill.common.enums.NamespaceEnum;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.service.AuthorityService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AuthUtil {
    /**
     * 提取授权的子产品
     * @param commaSeparatedScodes
     * @return
     */
    public static List<String> extractAuthedScodes(String commaSeparatedScodes) {
        List<String> scodeList = new ArrayList<>();
        if (Strings.isNotBlank(commaSeparatedScodes)) {
            scodeList = new ArrayList<>(Arrays.asList(commaSeparatedScodes.split(",")));
        }

        String namespace = ScopeUtil.getNamespaceScope();
        if (NamespaceEnum.SINGLE_NAMESPACE.getNamespace().equals(namespace)) {
            // 如果是命名空间范围，不校验其他权限
            return scodeList;
        } else if (NamespaceEnum.HWORK_MULTI_NAMESPACE.getNamespace().equals(namespace)) {
//            User user = getCurrentUser();
//            AuthorityService authorityService = AuthorityServiceHolder.INSTANCE.getAuthorityService();
//            AuthAppForItResponse authAppForIt = authorityService.getAuthAppForIt(new AuthAppForItRequest(null, null, user.getUserCode()));
//            List<String> authedScodes = authAppForIt.getSubProducts().stream().map(HworkBillApi.SubProduct::getAppScode).collect(Collectors.toList());
//            List<String> finalScodes = scodeList.stream().filter(authedScodes::contains).collect(Collectors.toList());
//            return finalScodes;
        	return scodeList;
        } else {
            // 默认使用枚举获取 authorityService
/*
            AuthorityService authorityService = AuthorityServiceHolder.INSTANCE.getAuthorityService();
            List<HdsSubProducts> products = authorityService.getAuthedSubProducts(null);
            List<String> authedScodes = new ArrayList<>();
            if (CollectionUtils.isEmpty(products)) {
                throw new RuntimeException("此用户没有子产品权限");
            }
            products.forEach(product -> {
                authedScodes.add(product.getAppScode());
            });

            return scodeList.stream().filter(authedScodes::contains).collect(Collectors.toList());
*/
            // 放飞自我，不再校验权限
            return scodeList;
        }

    }
    
    /**
     * 判断当前用户是否为管理员。
     * @return 当前用户是否为管理员
     */
    public static User getCurrentUser() {
        return LoginContextHolder.getCurrentUser();
    }

    public static boolean isAdmin(User user) {
        boolean isAdministrator = false;
        List<HworkAuthorityApi.RolesDTO> roles = user.getRoles();
        if (!CollectionUtils.isEmpty(roles)){
            for (HworkAuthorityApi.RolesDTO role : roles) {
                if (role.getRoleCode().equals("HCMS_Admin")){
                    isAdministrator = true;
                    break;
                }
            }
        }

        return isAdministrator;
    }

    /**
     * 	•	线程安全：enum 的实例在类加载时被 JVM 保证为线程安全，且是单例的。
     * 	•	懒加载：enum 的实例会在第一次被访问时加载，避免了不必要的对象创建。
     */
    public enum AuthorityServiceHolder {
        INSTANCE;

        private final AuthorityService authorityService;

        AuthorityServiceHolder() {
            this.authorityService = SpringContextAware.getBean(AuthorityService.class);
        }

        public AuthorityService getAuthorityService() {
            return authorityService;
        }
    }
}
