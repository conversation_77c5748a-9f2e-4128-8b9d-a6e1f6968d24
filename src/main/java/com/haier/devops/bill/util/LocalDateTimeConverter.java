package com.haier.devops.bill.util;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description LocalDateTime时间转换器
 * @date 2023/12/21
 */
public class LocalDateTimeConverter implements Converter<LocalDateTime> {
    private final static String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public Class<LocalDateTime> supportJavaTypeKey() {
        return LocalDateTime.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public LocalDateTime convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty excelContentProperty,
                                           GlobalConfiguration globalConfiguration) {
        String value = cellData.getStringValue();
        if (StringUtils.isNotBlank(value)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
            return LocalDateTime.parse(value, formatter);
        }
        return null;
    }

    @Override
    public WriteCellData<String> convertToExcelData(LocalDateTime localDateTime,
                                                    ExcelContentProperty excelContentProperty
            , GlobalConfiguration globalConfiguration) {
        if (localDateTime != null) {
            return new WriteCellData<>(DateTimeFormatter.ofPattern(DATE_FORMAT).format(localDateTime));
        }
        return null;
    }
}
