package com.haier.devops.bill.util;
import java.util.LinkedHashMap;
import java.util.Map;

public class DeepCopyUtil {


    public static Map<String, Object> deepCopy(Map<String, Object> original) {
        Map<String, Object> copy = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : original.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            Object copiedValue;
            if (value instanceof SomeClass) {
                copiedValue = new SomeClass((SomeClass) value);
            } else {
                copiedValue = value; // 默认情况，处理其他对象
            }

            copy.put(key, copiedValue);
        }
        return copy;
    }

    public static void main(String[] args) {
        Map<String, Object> originalMap = new LinkedHashMap<>();
        originalMap.put("key1", new SomeClass("value1"));
        originalMap.put("key2", new SomeClass("value2"));

        Map<String, Object> copiedMap = deepCopy(originalMap);
        originalMap.put("key3", "value3");
        System.out.println("Original Map: " + originalMap);
        System.out.println("Copied Map: " + copiedMap);
    }
}
