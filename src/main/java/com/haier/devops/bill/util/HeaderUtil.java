package com.haier.devops.bill.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;

/**
 * @Description: 请求头工具类
 * @Author: A0018437
 * @Date：2024-01-15
 */
public class HeaderUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HeaderUtil.class);
    public static final String DEF_CHATSET = "UTF-8";
    public static final int DEF_CONN_TIMEOUT = 30000;
    public static final int DEF_READ_TIMEOUT = 30000;
    public static String userAgent = "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.66 Safari/537.36";

    public void AjaxUrl() {
    }


    public static String urlencode(Map<String, Object> data) {
        StringBuilder sb = new StringBuilder();
        Iterator var2 = data.entrySet().iterator();

        while(var2.hasNext()) {
            Map.Entry i = (Map.Entry)var2.next();

            try {
                sb.append(i.getKey()).append("=").append(URLEncoder.encode(i.getValue() + "", "UTF-8")).append("&");
            } catch (UnsupportedEncodingException var5) {
                var5.printStackTrace();
            }
        }

        return sb.toString();
    }

    public static HttpServletRequest getServletRequest() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if(null != servletRequestAttributes) {
            return servletRequestAttributes.getRequest();
        }else{
            System.out.println("获取request失败");
            return null;
        }
    }

    public static String getToken(){
        if(null != getServletRequest()) {
            LOGGER.info("解析请求头信息！");
            LOGGER.info("==============>>>>>>>>>>>>> header " + getServletRequest().toString());
            LOGGER.info("header SDK_USER_ACCOUNT X-USER-ID:{};", new Object[]{getServletRequest().getHeaders("X-USER")});
            if (!StringUtils.isEmpty(getServletRequest().getHeader("X-USER-ID"))) {
                LOGGER.info("==============>>>>>>>>>>>>> header SDK_USER_ACCOUNT 结束");
                return getServletRequest().getHeader("X-USER-ID");
            } else if(!StringUtils.isEmpty(getServletRequest().getHeader("X-USER"))){
                return getServletRequest().getHeader("X-USER");
            } else {
                return null;
            }
        }
        return null;
    }

    public static String getJwtToken(){
        if(null != getServletRequest()) {
            LOGGER.info("解析请求头信息！");
            LOGGER.info("==============>>>>>>>>>>>>> header " + getServletRequest().toString());
            LOGGER.info("header SDK_USER_ACCOUNT Authorization:{};", new Object[]{getServletRequest().getHeaders("Authorization")});
            if (!StringUtils.isEmpty(getServletRequest().getHeader("Authorization"))) {
                LOGGER.info("==============>>>>>>>>>>>>> header SDK_USER_ACCOUNT 结束");
                String bearToken = getServletRequest().getHeader("Authorization");
                return bearToken.replace("Bearer ", "");
            } else {
                return null;
            }
        }
        return null;
    }

    /**
     * @Description: 获取当前登录用户工号，未查询到则抛出异常
     * @author: 0018437
     * @param:
     * @return:
     * @date: 2020/11/24 16:00
     */
    public static String getUserId() {
        String userId = getToken();
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(userId)) {
            throw new RuntimeException("无登录用户信息");
        }
        return userId;
    }
}
