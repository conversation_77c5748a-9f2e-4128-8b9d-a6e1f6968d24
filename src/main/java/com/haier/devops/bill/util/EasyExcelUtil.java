package com.haier.devops.bill.util;

import com.alibaba.excel.EasyExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EasyExcelUtil {
    /**
     * 无填充方式导出并下载xlsx
     */
    public static void download(String excelName,
                                HttpServletResponse response,
                                Class clazz,
                                List data,
                                Collection<String> excludedColumnNames) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(excelName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel
                .write(response.getOutputStream(), clazz)
                .excludeColumnFieldNames(excludedColumnNames)
                .sheet(excelName)
                .doWrite(data);
    }
}
