package com.haier.devops.bill.util;

import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

/**
 * @Description: Aes加解密工具类
 * @Author: A0018437
 * @Date：2023-12-07
 */
@Component
public class AesUtil {
    private static String DEF_KEY = "haier690";

    /**
     * 使用MD5算法对默认密钥DEF_KEY进行哈希处理，将得到的哈希值转换为十六进制字符串并返回
     * @return
     */
    public static String getDefaultKey() {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashedBytes = md.digest(DEF_KEY.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (int i = 0; i < hashedBytes.length && i < 16; i++) {
                String hex = Integer.toHexString(0xff & hashedBytes[i]);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString().substring(0, 16);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
    /**
     * AES-CBC解密
     * @param cal 待解密内容
     * @return
     */
    public static String decryptCBC(String cal) {
        try {
            // 获取秘钥
            String defaultKey = getDefaultKey();
            SecretKeySpec secretKey = new SecretKeySpec(defaultKey.getBytes(), "AES");
            // "算法/模式/补码方式"
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] iv = adjustByteArray(defaultKey.getBytes());
            //偏移
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            // 选择解密
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(cal);
            // 将内容解密
            byte[] decryptedBytes = cipher.doFinal(decodedBytes);

            return new String(decryptedBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * AES-CBC加密
     * @param cal 待加密内容
     * @return
     */
    public static String encryptCBC(String cal) {
        try {
            // 获取秘钥
            String defaultKey = getDefaultKey();
            SecretKeySpec secretKey = new SecretKeySpec(defaultKey.getBytes(), "AES");
            // "算法/模式/补码方式"
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] iv = adjustByteArray(defaultKey.getBytes());
            //偏移
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            // 选择加密
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);

            // 将内容加密
            byte[] decryptedBytes = cipher.doFinal(cal.getBytes());

            return Base64Utils.encodeToString(decryptedBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将输入的字节数组调整为长度为16的字节数组。如果输入的字节数组长度小于16，填充0扩展至长度为16；
     * 如果字节数组长度大于等于16，则直接复制前16个字节返回
     * @param bytes
     * @return
     */
    public static byte[] adjustByteArray(byte[] bytes) {
        if (bytes.length < 16) {
            byte[] paddedBytes = new byte[16];
            System.arraycopy(bytes, 0, paddedBytes, 0, bytes.length);
            return paddedBytes;
        } else {
            return Arrays.copyOf(bytes, 16);
        }
    }

}
