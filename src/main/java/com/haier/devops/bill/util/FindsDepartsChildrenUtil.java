package com.haier.devops.bill.util;


import com.haier.devops.bill.common.entity.SysDepart;
import com.haier.devops.bill.common.vo.DepartIdVo;
import com.haier.devops.bill.common.vo.SysDepartTreeVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <P>
 * 对应部门的表,处理并查找树级数据
 * <P>
 * 
 * @Author: Steve
 * @Date: 2019-01-22
 */
public class FindsDepartsChildrenUtil {

	//部门树信息-树结构
	//private static List<SysDepartTreeVo> sysDepartTreeList = new ArrayList<SysDepartTreeVo>();
	
	//部门树id-树结构
    //private static List<DepartIdVo> idList = new ArrayList<>();


    /**
     * queryTreeList的子方法 ====1=====
     * 该方法是s将SysDepart类型的list集合转换成SysDepartTreeVo类型的集合
     */
    public static List<SysDepartTreeVo> wrapTreeDataToTreeList(List<SysDepart> recordList) {
        // 在该方法每请求一次,都要对全局list集合进行一次清理
        //idList.clear();
    	List<DepartIdVo> idList = new ArrayList<DepartIdVo>();
        List<SysDepartTreeVo> records = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            SysDepart depart = recordList.get(i);
            records.add(new SysDepartTreeVo(depart));
        }
        List<SysDepartTreeVo> tree = findChildren(records, idList);
        setEmptyChildrenAsNull(tree);
        return tree;
    }

    /**
     * 获取 DepartIdVo
     * @param recordList
     * @return
     */
    public static List<DepartIdVo> wrapTreeDataToDepartIdTreeList(List<SysDepart> recordList) {
        // 在该方法每请求一次,都要对全局list集合进行一次清理
        //idList.clear();
        List<DepartIdVo> idList = new ArrayList<DepartIdVo>();
        List<SysDepartTreeVo> records = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            SysDepart depart = recordList.get(i);
            records.add(new SysDepartTreeVo(depart));
        }
        findChildren(records, idList);
        return idList;
    }

    /**
     * queryTreeList的子方法 ====2=====
     * 该方法是找到并封装顶级父类的节点到TreeList集合
     */
    private static List<SysDepartTreeVo> findChildren(List<SysDepartTreeVo> recordList,
                                                         List<DepartIdVo> departIdList) {

        List<SysDepartTreeVo> treeList = new ArrayList<>();
        for (int i = 0; i < recordList.size(); i++) {
            SysDepartTreeVo branch = recordList.get(i);
            if (oConvertUtils.isEmpty(branch.getParentId())) {
                treeList.add(branch);
                DepartIdVo DepartIdVo = new DepartIdVo().convert(branch);
                departIdList.add(DepartIdVo);
            }
        }
        getGrandChildren(treeList,recordList,departIdList);
        
        //idList = departIdList;
        return treeList;
    }

    /**
     * queryTreeList的子方法====3====
     *该方法是找到顶级父类下的所有子节点集合并封装到TreeList集合
     */
    private static void getGrandChildren(List<SysDepartTreeVo> treeList,List<SysDepartTreeVo> recordList,List<DepartIdVo> idList) {

        for (int i = 0; i < treeList.size(); i++) {
            SysDepartTreeVo model = treeList.get(i);
            DepartIdVo idModel = idList.get(i);
            for (int i1 = 0; i1 < recordList.size(); i1++) {
                SysDepartTreeVo m = recordList.get(i1);
                if (m.getParentId()!=null && m.getParentId().equals(model.getId())) {
                    model.getChildren().add(m);
                    DepartIdVo dim = new DepartIdVo().convert(m);
                    idModel.getChildren().add(dim);
                }
            }
            getGrandChildren(treeList.get(i).getChildren(), recordList, idList.get(i).getChildren());
        }

    }
    

    /**
     * queryTreeList的子方法 ====4====
     * 该方法是将子节点为空的List集合设置为Null值
     */
    private static void setEmptyChildrenAsNull(List<SysDepartTreeVo> treeList) {

        for (int i = 0; i < treeList.size(); i++) {
            SysDepartTreeVo model = treeList.get(i);
            if (model.getChildren().size() == 0) {
                model.setChildren(null);
                model.setIsLeaf(true);
            }else{
                setEmptyChildrenAsNull(model.getChildren());
                model.setIsLeaf(false);
            }
        }
        // sysDepartTreeList = treeList;
    }
}
