package com.haier.devops.bill.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class HworkSignUtil {
    /**
     * 创建签名
     *
     * @param appId     应用ID
     * @param params    请求参数
     * @param secretKey 密钥
     * @param body      请求体
     * @return 签名
     */
    public static String generateSign(String appId, Map<String, String> params, String secretKey, String body) {
        // 处理默认值
        appId = StringUtils.isNotBlank(appId) ? appId : StringUtils.EMPTY;
        params = CollectionUtils.isEmpty(params) ? Collections.emptyMap() : params;
        secretKey = StringUtils.isNotBlank(secretKey) ? secretKey : StringUtils.EMPTY;
        body = StringUtils.isNotBlank(body) ? body : StringUtils.EMPTY;
        // 生成签名
        StringBuilder sb = new StringBuilder(appId);
        List<String> queryParamKeys = params.keySet().stream().sorted().collect(Collectors.toList());
        for (String queryParamKey : queryParamKeys) {
            sb.append(queryParamKey).append(params.getOrDefault(queryParamKey, StringUtils.EMPTY));
        }
        String trimBody = body.replaceAll("[\t\r\n ]", "").trim();
        sb.append(secretKey).append(trimBody);
        String cheapSign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8));
        return DigestUtils.md5DigestAsHex(cheapSign.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }

    public static String generateSign(String appId, String secretKey, long time, String uuid) {
        String signature = Md5Utils.encryptToMd5(appId + "," + secretKey + "," + time + "," + uuid);
        return signature;
    }
}
