package com.haier.devops.bill.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 正则表达式
 * @Author: A0018437
 * @Date：2023-12-14
 */
public class RegularUtil {

    /**
     * S码验证
     */
    public static boolean isSCode(String val){
        // 编译正则表达式
        String scode_regex = "^[ZWSPG][0-9]{5}$";
        Pattern pattern = Pattern.compile(scode_regex);
        Matcher matcher = pattern.matcher(val);
        // 字符串是否与正则表达式相匹配
        return matcher.matches();
    }

    /**
    * @Description: 手机号验证
    * @author: 张爱苹
    * @date: 2024/2/4 09:39
    * @param val:
    * @Return: boolean
    */
    public static boolean isMobile(String val){
        // 编译正则表达式
        String scode_regex = "^1[0-9]{10}$";
        Pattern pattern = Pattern.compile(scode_regex);
        Matcher matcher = pattern.matcher(val);
        // 字符串是否与正则表达式相匹配
        return matcher.matches();
    }

    public static boolean isTmMobile(String val){
        // 编译正则表达式
        String scode_regex = "^1[0-9]{2}+[*]{4}+[0-9]{4}$";
        Pattern pattern = Pattern.compile(scode_regex);
        Matcher matcher = pattern.matcher(val);
        // 字符串是否与正则表达式相匹配
        return matcher.matches();
    }

    /**
    * @Description: 邮箱验证
    * @author: 张爱苹
    * @date: 2024/2/4 09:39
    * @param val:
    * @Return: boolean
    */
    public static boolean isEmail(String val){
        // 编译正则表达式
        String scode_regex = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(.[a-zA-Z0-9_-]+)+$";
        Pattern pattern = Pattern.compile(scode_regex);
        Matcher matcher = pattern.matcher(val);
        // 字符串是否与正则表达式相匹配
        return matcher.matches();
    }
}
