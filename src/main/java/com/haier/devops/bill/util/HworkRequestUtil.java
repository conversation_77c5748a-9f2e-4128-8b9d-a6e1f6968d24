package com.haier.devops.bill.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @Description: TODO
 * @author: scott
 * @date: 2024年12月10日 10:10
 */
public class HworkRequestUtil {
    private static final Logger log = LoggerFactory.getLogger(HworkRequestUtil.class);

    public HworkRequestUtil() {
    }

    public static HttpServletRequest getRequest() {
        return (HttpServletRequest) Objects.requireNonNull(getRequestOrNull());
    }

    public static HttpServletRequest getRequestOrNull() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return servletRequestAttributes != null ? servletRequestAttributes.getRequest() : null;
    }
}
