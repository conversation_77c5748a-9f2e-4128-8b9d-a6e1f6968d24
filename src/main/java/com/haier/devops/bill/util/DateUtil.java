package com.haier.devops.bill.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DateUtil {
    private static final int BILLING_DAY_OF_MONTH = 15;

    private static final DateTimeFormatter YEAR_FORMATTER = DateTimeFormatter.ofPattern("yyyy");
    private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final DateTimeFormatter YEAR_MONTH_DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * 获取前一天
     *
     * @param day
     * @return
     */
    public static String getTheDayBefore(LocalDate day) {
        LocalDate previousDay = day.minusDays(1);
        return previousDay.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取指定月份的第一天
     * @param yearMonth
     * @return
     */
    public static String getFirstDayOfMonth(String yearMonth) {
        YearMonth ym = YearMonth.parse(yearMonth, YEAR_MONTH_FORMATTER);
        LocalDate firstDay = ym.atDay(1);
        return firstDay.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取指定月份的最后一天
     * @param yearMonth
     * @return
     */
    public static String getLastDayOfMonth(String yearMonth) {
        YearMonth ym = YearMonth.parse(yearMonth, YEAR_MONTH_FORMATTER);
        LocalDate lastDay = ym.atEndOfMonth();
        return lastDay.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取最新账单月
     *
     * @return
     */
    public static String getLatestBillingMonth() {
        LocalDate now = LocalDate.now();
        LocalDate latestMonth;

        if (now.getDayOfMonth() <= BILLING_DAY_OF_MONTH) {
            // If the current day is before the 15th, get the month before last
            latestMonth = now.minusMonths(2);
        } else {
            // If the current day is the 15th or later, get the last month
            latestMonth = now.minusMonths(1);
        }

        return latestMonth.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取去年第一天
     * @return
     */
    public static String getFirstDayOfLastYearStr() {
        LocalDate firstDayOfLastYear = LocalDate.now().minusYears(1).withDayOfYear(1);
        return firstDayOfLastYear.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取去年本月第一天
     * @return
     */
    public static String getFirstDayOfCurrentMonthLastYear() {
        LocalDate firstDayOfCurrentMonthLastYear = LocalDate.now().minusYears(1).withDayOfMonth(1);
        return firstDayOfCurrentMonthLastYear.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取去年的今天
     * @return
     */
    public static String getTodayLastYear() {
        LocalDate todayLastYear = LocalDate.now().minusYears(1);
        return todayLastYear.format(YEAR_MONTH_DAY_FORMATTER);
    }

    /**
     * 获取指定月份的上一个月
     *
     * @param yearMonthStr
     * @return
     */
    public static String getPreviousMonth(String yearMonthStr) {
        LocalDate date = LocalDate.parse(yearMonthStr + "-01");
        LocalDate lastMonth = date.minusMonths(1);
        return lastMonth.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取上一个季度的第一个月
     *
     * @param yearMonthStr
     * @return
     */
    public static String getFirstMonthOfPreviousQuarter(String yearMonthStr) {
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, YEAR_MONTH_FORMATTER);
        int month = yearMonth.getMonthValue();

        // 确定上一个季度的起始月份
        int firstMonthOfCurrentQuarter = ((month - 1) / 3) * 3 + 1;
        int firstMonthOfPreviousQuarter;
        if (firstMonthOfCurrentQuarter == 1) {
            // 如果当前是第一季度，则上一个季度是上一年的第四季度
            // Q4 starts in October
            firstMonthOfPreviousQuarter = 10;
            // 减去一年
            yearMonth = yearMonth.minusYears(1);
        } else {
            // 否则上一个季度在同一年内
            firstMonthOfPreviousQuarter = firstMonthOfCurrentQuarter - 3;
        }

        return YearMonth.of(yearMonth.getYear(), firstMonthOfPreviousQuarter)
                .format(YEAR_MONTH_FORMATTER);
    }

    public static String getQuarter(String yearMonthStr) {
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, YEAR_MONTH_FORMATTER);
        int year = yearMonth.getYear();
        int month = yearMonth.getMonthValue();

        if (month >= 1 && month <= 3) {
            return String.format("%s-Q%s", year, 1);
        } else if (month >= 4 && month <= 6) {
            return String.format("%s-Q%s", year, 2);
        } else if (month >= 7 && month <= 9) {
            return String.format("%s-Q%s", year, 3);
        } else {
            return String.format("%s-Q%s", year, 4);
        }
    }


    /**
     * 获取上一年的第一个月
     *
     * @param yearMonthStr
     * @return
     */
    public static String getFirstMonthOfPreviousYear(String yearMonthStr) {
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, YEAR_MONTH_FORMATTER);
        // 获取上一年的第一个月
        YearMonth firstMonthOfPreviousYear = yearMonth.minusYears(1).withMonth(1);
        return firstMonthOfPreviousYear.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取年
     * @param yearMonthStr
     * @return
     */
    public static String getYear(String yearMonthStr) {
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, YEAR_MONTH_FORMATTER);
        return yearMonth.format(YEAR_FORMATTER);
    }

    public static String getDayOf(int n) {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();

        // 将日期减去一天
        calendar.add(Calendar.DAY_OF_MONTH, n);

        // 获取减去一天后的日期
        int year = calendar.get(Calendar.YEAR);
        // 月份从0开始，需要加1
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        return String.format("%d-%02d-%02d", year, month, day);
    }


    /**
     * 获取指定月份的下一个月
     *
     * @param month
     * @return
     */
    public static String getNextMonthOf(String month) {
        LocalDate date = LocalDate.parse(month + "-01");
        LocalDate lastMonth = date.plusMonths(1);
        return lastMonth.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取当前年份
     */
    public static String getCurrentYear() {
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        return now.format(formatter);
    }

    /**
     * 获取当前月份
     *
     * @return
     */
    public static String getCurrentMonth() {
        // 获取今天的日期
        LocalDateTime today = LocalDateTime.now();
        // 格式化输出
        return today.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取上一个月份
     *
     * @return
     */
    public static String getLastMonth() {
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1);
        return lastMonth.format(YEAR_MONTH_FORMATTER);
    }

    /**
     * 获取指定年份的上一年
     *
     * @param year
     * @return
     */
    public static String getLastYearOf(String year) {
        LocalDate date = LocalDate.parse(year + "-01-01");
        LocalDate lastYear = date.minusYears(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        return lastYear.format(formatter);
    }

    public static boolean isValidFormat(String format, String value) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            YearMonth.parse(value, formatter);
        } catch (DateTimeParseException e) {
            return false;
        }
        return true;
    }

    public static LocalDateTime convertYearMonthToLocalDateTime(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // Parse the string as a YearMonth
        YearMonth yearMonth = YearMonth.parse(dateStr, formatter);
        // Convert YearMonth to LocalDateTime
        return yearMonth.atDay(1).atStartOfDay();
    }


    public static String getCurrentDay() {
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return now.format(formatter);
    }


    /**
     * 获取当前月份剩余的天数（包括今天）
     *
     * @return
     */
    public static int getRemainingDaysCountOfCurrentMonth() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取当前年月
        YearMonth currentYearMonth = YearMonth.from(today);

        // 获取当前月份的天数
        int totalDaysInMonth = currentYearMonth.lengthOfMonth();

        // 计算今天是这个月的第几天
        int dayOfMonth = today.getDayOfMonth();

        // 计算剩余的天数，包括今天
        return totalDaysInMonth - dayOfMonth + 1;
    }


    /**
     * 返回包含的年-月列表
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> getYearMonthRange(String start, String end) {
        List<String> result = new ArrayList<>();

        LocalDate startDate = LocalDate.parse(start + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(end + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        while (!startDate.isAfter(endDate)) {
            result.add(startDate.format(YEAR_MONTH_FORMATTER));
            startDate = startDate.plusMonths(1);
        }

        return result;
    }

    /**
     * 返回包含的年-季度列表
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> getYearQuarterRange(String start, String end) {
        List<String> result = new ArrayList<>();

        LocalDate startDate = LocalDate.parse(start + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(end + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        while (!startDate.isAfter(endDate)) {
            int quarter = (startDate.getMonthValue() - 1) / 3 + 1;
            result.add(startDate.getYear() + "-Q" + quarter);
            startDate = startDate.plusMonths(3 - (startDate.getMonthValue() - 1) % 3);
        }

        return result;
    }

    /**
     * 获取当年的第一天
     * @return
     */
    public static LocalDateTime getFirstDayOfCurrentYear() {

        // 获取当前年份的 1 月 1 日
        return LocalDateTime.of(LocalDateTime.now().getYear(), 1, 1, 0, 0, 0);
    }

    /**
     * 获取去年的第一天
     * @return
     */
    public static LocalDateTime getFirstDayOfLastYear() {
        // 获取去年的 1 月 1 日
        return LocalDateTime.of(LocalDateTime.now().getYear() - 1, 1, 1, 0, 0, 0);
    }

    /**
     * 返回包含的年列表
     *
     * @param start
     * @param end
     * @return
     */
    public static List<String> getYearRange(String start, String end) {
        List<String> result = new ArrayList<>();

        int startYear = Integer.parseInt(start.substring(0, 4));
        int endYear = Integer.parseInt(end.substring(0, 4));

        for (int year = startYear; year <= endYear; year++) {
            result.add(String.valueOf(year));
        }

        return result;
    }

    public static void main(String[] args) {
        String date1 = "2025-01";
        String date2 = "2025-08";
        System.out.println(calculateMonthDifference(date1,date2));
    }

    //获取指定日期的后一天
    public static String getNextDayOf(String dateString) {
        LocalDate date = LocalDate.parse(dateString);
        LocalDate nextDay = date.plusDays(1);
        return nextDay.toString();
    }

    public static String getCurrentDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(new Date());
    }

    public static List<String> getMonthsBetween(String startCycle, String endCycle) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startCycle, formatter);
        LocalDate endDate = LocalDate.parse(endCycle, formatter);

        YearMonth startMonth = YearMonth.from(startDate);
        YearMonth endMonth = YearMonth.from(endDate);

        List<String> months = new ArrayList<>();
        while (!startMonth.isAfter(endMonth)) {
            months.add(startMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            startMonth = startMonth.plusMonths(1);
        }

        return months;
    }


    public static String formatDateToMonth(String startDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(startDate, formatter);
            return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        } catch (DateTimeParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static int calculateMonthDifference(String date1, String date2) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth start = YearMonth.parse(date1, formatter);
        YearMonth end = YearMonth.parse(date2, formatter);
        int monthsBetween = (int) ChronoUnit.MONTHS.between(start, end);
        return monthsBetween;
    }
}
