package com.haier.devops.bill.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @description EasyExcel工具类
 * @date 2024/03/05
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * excel导出方法
     * @param response  http请求
     * @param filename  文件名
     * @param sheetName Excel sheet名
     * @param head      表头
     * @param data      数据
     */
    public static <T> void writeExcel(HttpServletResponse response, String filename, String sheetName,
                                      List<List<String>> head, List<T> data) {
        try {
            response.setHeader("Content-disposition", "attachment;filename*=" + URLEncoder.encode(filename,
                    StandardCharsets.UTF_8.name()) + ".xlsx");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.ms-excel");
            // 在此处开放Content-Disposition权限，前端代码才能获取到表名
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(Boolean.FALSE)
                    .sheet(sheetName)
                    .head(head)
                    // 自适应列宽
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(getStyleStrategy())
                    .registerConverter(new LocalDateConverter()).registerConverter(new LocalDateTimeConverter())
                    .doWrite(data);
        } catch (Exception e) {
            log.error("导出信息失败: [{}]", e.getLocalizedMessage());
            e.printStackTrace();
            throw new RuntimeException("导出信息失败");
        }
    }

    public static HorizontalCellStyleStrategy getStyleStrategy() {

        // 头的策略  样式调整
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 水平对齐方式
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直对齐方式
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        WriteFont font =  new WriteFont();
        font.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(font);
        // 内容的策略
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 设置 水平居中
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentStyle.setWriteFont(font); // 设置字体
        contentStyle.setFillBackgroundColor(IndexedColors.WHITE.getIndex());
        // 设置细边框
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setBorderTop(BorderStyle.THIN);
        // 设置边框颜色 25级灰度
        contentStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentStyle);
    }

}
