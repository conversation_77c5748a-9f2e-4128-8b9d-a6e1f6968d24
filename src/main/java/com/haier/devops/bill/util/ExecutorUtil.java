package com.haier.devops.bill.util;

import org.slf4j.Logger;

import java.util.concurrent.Callable;
import java.util.concurrent.CompletionService;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ExecutorUtil {

    public static <T> Future<T> analysisTask(CompletionService<T> service,
                                             Callable<T> task,
                                             AtomicInteger count, CountDownLatch latch, Logger log) {
        Future<T> future = null;
        try {
            future = service.submit(task);
        } catch (Exception e) {
            log.error("Failed to submit task", e);
        } finally {
            count.incrementAndGet();
            latch.countDown();
        }
        return future;
    }
}
