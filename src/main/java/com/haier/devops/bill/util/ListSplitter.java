package com.haier.devops.bill.util;

import java.util.ArrayList;
import java.util.List;

/**
* @ClassName: ListSplitter
* @Description:  列表拆分工具类
* @author: 张爱苹
* @date: 2024/3/15 15:23
*/
public class ListSplitter {

    public static <T> List<List<T>> splitList(List<T> list,int batchSize) {
        List<List<T>> subLists = new ArrayList<>();
        int listSize = list.size();
        for (int i = 0; i < listSize; i += batchSize) {
            subLists.add(new ArrayList<>(list.subList(i, Math.min(i + batchSize, listSize))));
        }
        return subLists;
    }
}


