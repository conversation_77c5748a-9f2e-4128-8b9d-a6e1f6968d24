package com.haier.devops.bill.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TimeRangeParser {
    public static Map parseTimeRange(Integer time,String start,String end) {
        Map map = new HashMap();
        //获取上个月的最后一天
        LocalDate now = LocalDate.now().withDayOfMonth(1).minus(1, ChronoUnit.DAYS);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(null != end && !"".equals(end) && null != start &&  !"".equals(start)){
            map.put("endDate", end);
            map.put("startDate", start);
        }else{
            map.put("endDate", formatter.format(now));
            LocalDate startDate = now.minus(time-1, ChronoUnit.MONTHS).withDayOfMonth(1);
            map.put("startDate", formatter.format(startDate));
        }
        end =  map.get("endDate").toString();
        map.put("currentMonthDay",end.substring(0,8)+"01");
        //获取startCycle-endCycle之间所有的月份，放入list
        List<String> months = DateUtil.getMonthsBetween(map.get("startDate").toString(), map.get("endDate").toString());
        map.put("months", months);
        return map;
    }
}
