package com.haier.devops.bill.orgnization;

import com.haier.devops.bill.common.entity.IdmOrg;
import com.haier.devops.bill.common.service.IdmOrgService;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
//@Component
@Slf4j
public class OrganizationCache {
    private final JedisPool jedisPool;
    private final Jedis redisClient;
    private final IdmOrgService orgService;

    public OrganizationCache(JedisPool jedisPool, IdmOrgService orgService) {
        this.jedisPool = jedisPool;
        this.orgService = orgService;
        this.redisClient = jedisPool.getResource();
    }

    public void preloadCache() {
        List<IdmOrg> orgs = orgService.getAllOrgs();
/*
        Map<String, IdmOrg> orgMap = new HashMap<>();
        orgs.stream().forEach(org -> orgMap.put(org.getOrgId(), org));
        orgMap.forEach((orgId, org) -> cacheOrg(org));
*/

        log.info("开始预热部门信息到缓存...");
        orgs.forEach(this::cacheOrg);
        log.info("预热部门信息到缓存结束...");
    }

    public IdmOrg getTopLevelOrg(List<String> orgIds) {
        IdmOrg topLevelOrg = null;

        for (String orgId : orgIds) {
            IdmOrg currentOrg = findTopLevelOrg(orgId);
            if (topLevelOrg == null || isHigherLevel(currentOrg, topLevelOrg)) {
                topLevelOrg = currentOrg;
            }
        }

        return topLevelOrg;
    }

    private IdmOrg findTopLevelOrg(String orgId) {
        IdmOrg org = getOrgFromCache(orgId);

        while (org.getUpId() != null) {
            IdmOrg parentOrg = getOrgFromCache(org.getUpId());
            if (parentOrg == null) {
                break;
            }
            org = parentOrg;
        }

        return org;
    }

    public List<IdmOrg> getAllSubOrgs(String orgId) {
        List<IdmOrg> subOrgs = new ArrayList<>();
        Queue<String> queue = new LinkedList<>();
        queue.add(orgId);

        while (!queue.isEmpty()) {
            String currentOrgId = queue.poll();
            List<String> childOrgIds = redisClient.lrange("orgChildren:" + currentOrgId, 0, -1);
            for (String childOrgId : childOrgIds) {
                IdmOrg childOrg = getOrgFromCache(childOrgId);
                subOrgs.add(childOrg);
                queue.add(childOrgId);
            }
        }

        return subOrgs;
    }

    private boolean isHigherLevel(IdmOrg currentOrg, IdmOrg topLevelOrg) {
        int currentDepth = getDepth(currentOrg);
        int topLevelDepth = getDepth(topLevelOrg);

        // 层级越小，表示级别越高
        return currentDepth < topLevelDepth;
    }

    private int getDepth(IdmOrg org) {
        int depth = 0;
        while (org.getUpId() != null) {
            org = getOrgFromCache(org.getUpId());
            depth++;
        }
        return depth;
    }

    private IdmOrg getOrgFromCache(String orgId) {
        Map<String, String> cachedOrg = redisClient.hgetAll("org:" + orgId);
        if (cachedOrg == null || cachedOrg.isEmpty()) {
            return null;
        }

        IdmOrg org = new IdmOrg();
        org.setOrgId(cachedOrg.get("orgId"));
        org.setOrgName(cachedOrg.get("orgName"));
        org.setUpId(cachedOrg.get("upId"));
        org.setEndTime(Timestamp.valueOf(cachedOrg.get("endTime")));

        return org;
    }

    private void cacheOrg(IdmOrg org) {
        redisClient.hset("org:" + org.getOrgId(), "orgId", org.getOrgId());
        redisClient.hset("org:" + org.getOrgId(), "orgName", org.getOrgName());
        redisClient.hset("org:" + org.getOrgId(), "endTime", org.getEndTime().toString());

        if (org.getUpId() != null) {
            redisClient.hset("org:" + org.getOrgId(), "upId", org.getUpId());
            redisClient.rpush("orgChildren:" + org.getUpId(), org.getOrgId());
        }
    }
}
