package com.haier.devops.bill.substitution.controller;

import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.entity.ExchangeRate;
import com.haier.devops.bill.common.interceptor.LoginContextHolder;
import com.haier.devops.bill.common.interceptor.User;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.ExchangeRateService;
import com.haier.devops.bill.substitution.vo.PassBillVo;
import com.haier.devops.bill.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.enums.CurrencyEnum.CNY;
import static com.haier.devops.bill.common.enums.CurrencyEnum.USD;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/hcms/bill/paas")
@Slf4j
public class PassBillController {
    private final AggregatedBillService aggregatedBillService;


    @Autowired
    private ExchangeRateService exchangeRateService;


    public PassBillController(AggregatedBillService aggregatedBillService) {
        this.aggregatedBillService = aggregatedBillService;
    }

    /**
     * 查询6月内容器云账单
     * @param passParm
     * @return
     */
    @PostMapping("/getAgentRecentHalfYearPaasBill")
    public ResponseEntityWrapper<List<PassBillVo>> getAgentRecentHalfYearPaasBill(@Valid @RequestBody PassParm passParm) {
        List<String> appCodeList = null;
        if(CollectionUtils.isNotEmpty(passParm.getAppList())){
            appCodeList = passParm.getAppList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            //数组转字符串用逗号隔开
            String scodes = StringUtils.join(appCodeList, ",");
            passParm.setScodes(scodes);
        }else{
            List<String> domainCodeList = null;
            if(!CollectionUtils.isEmpty(passParm.getDomainList())){
                domainCodeList = passParm.getDomainList().stream().map(item -> item.get("code")).collect(Collectors.toList());
            }
            User currentUser = LoginContextHolder.getCurrentUser();
            String userCode = currentUser.getUserCode();
            boolean isAdmin = AuthUtil.isAdmin(currentUser);
            passParm.setScodes(aggregatedBillService.getDomainSubProducts(domainCodeList,isAdmin,userCode));
        }
        if(StringUtils.isEmpty(passParm.getScodes())){
            return new ResponseEntityWrapper(new ArrayList<>());
        }
        if("CNY".equals(passParm.getCurrency())){
            return getRecentHalfYearPassBill(passParm);
        }else if("USD".equals(passParm.getCurrency())){
            return getRecentHalfYearPassBillWithUsd(passParm);
        }else{
            return getRecentHalfYearPassBillAll(passParm);
        }
    }

    private ResponseEntityWrapper<List<PassBillVo>> getRecentHalfYearPassBillAll(PassParm passParm) {
        String date = DateUtils.format(new Date(), "yyyy-MM-dd");

        ExchangeRate exchangeRate = null;
        try {
            exchangeRate = exchangeRateService.getExchangeRate("CNY", "USD", DateUtils.parseDate(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return new ResponseEntityWrapper<>(
                aggregatedBillService.getRecentHalfYearPassBill(passParm.getVendor(),
                        Arrays.asList(passParm.getScodes().split(",")),
                        passParm.getStartDate(),
                        passParm.getEndDate(), exchangeRate.getExchangeRate())
        );
    }

    /**
     * 查询6月内容器云账单
     * @param passParm
     * @return
     */
    @PostMapping("/recentHalfYearPaasBill")
    public ResponseEntityWrapper<List<PassBillVo>> getRecentHalfYearPassBill(@Valid @RequestBody PassParm passParm) {
        return new ResponseEntityWrapper<>(
                aggregatedBillService.getRecentHalfYearPassBill(passParm.getVendor(),
                        Arrays.asList(passParm.getScodes().split(",")),
                        passParm.getStartDate(),
                        passParm.getEndDate(), CNY.getCurrency())
        );
    }

    /**
     * 查询6月内容器云账单
     * @param passParm
     * @return
     */
    @PostMapping("/recentHalfYearPaasBill/usd")
    public ResponseEntityWrapper<List<PassBillVo>> getRecentHalfYearPassBillWithUsd(@Valid @RequestBody PassParm passParm) {
        return new ResponseEntityWrapper<>(
                aggregatedBillService.getRecentHalfYearPassBill(passParm.getVendor(),
                        Arrays.asList(passParm.getScodes().split(",")),
                        passParm.getStartDate(),
                        passParm.getEndDate(),
                        USD.getCurrency())
        );
    }

}
