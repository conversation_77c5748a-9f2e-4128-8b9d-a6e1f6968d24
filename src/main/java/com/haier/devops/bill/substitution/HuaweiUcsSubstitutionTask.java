package com.haier.devops.bill.substitution;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.BillSubstitutionTask;
import com.haier.devops.bill.common.service.*;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Objects;

@Component
@Slf4j
public class HuaweiUcsSubstitutionTask extends SubstitutionTask implements PendingSubstitutedBillQuery {
    private final AggregatedBillService aggregatedBillService;
    private final CmdbProductOverviewService productOverviewService;
    private final BillSubstitutionTaskService substitutionTaskService;
    private final MigratedBillService migratedBillService;
    private final PendingSubstitutionBillService pendingSubstitutionBillService;

    private final PlatformTransactionManager transactionManager;


    private final PendingSubstitutingBillQuery pendingSubstitutingBillQuery;
    public HuaweiUcsSubstitutionTask(AggregatedBillService aggregatedBillService,
                                      CmdbProductOverviewService productOverviewService,
                                      BillSubstitutionTaskService substitutionTaskService,
                                      MigratedBillService migratedBillService,
                                      PendingSubstitutionBillService pendingSubstitutionBillService,
                                      PlatformTransactionManager transactionManager,
                                      @Qualifier("huaweiUcsPendingSubstitutingBillQuery")
                                      PendingSubstitutingBillQuery pendingSubstitutingBillQuery) {
        this.aggregatedBillService = aggregatedBillService;
        this.productOverviewService = productOverviewService;
        this.substitutionTaskService = substitutionTaskService;
        this.migratedBillService = migratedBillService;
        this.pendingSubstitutionBillService = pendingSubstitutionBillService;
        this.transactionManager = transactionManager;
        this.pendingSubstitutingBillQuery = pendingSubstitutingBillQuery;
    }

    @Override
    public PageInfo<AggregatedBill> query(String billingCycle, int page, int perPage) {
        return aggregatedBillService.getHuaweiUcsPendingSubstitutionBills(billingCycle, page, perPage);
    }

    @Override
    void migrate(BillSubstitutionTask task) {
        doMigrate(this, task.getBillingCycle(), task.getSubTaskId());
    }

    @Override
    void clean(BillSubstitutionTask task, CleanToken token) {
        Objects.requireNonNull(token);
        String billingCycle = task.getBillingCycle();
        int cnt = aggregatedBillService.cleanHuaweiUcsBills(billingCycle);
        log.info("cleanHuaweiPassBills, billingCycle: {}, cnt: {}", billingCycle, cnt);

        PageInfo<AggregatedBill> pager = aggregatedBillService.getHuaweiUcsPendingSubstitutionBills(billingCycle, 1, perPage);
        if (pager.getTotal() == 0) {
        }
    }

    @Override
    void calculate(BillSubstitutionTask task) {
        doCalculate(task);
    }

    @Override
    void substitute(BillSubstitutionTask task) {
        doSubstitute(task);
    }

    @Override
    public PendingSubstitutingBillQuery getPendingSubstitutingBillQuery() {
        return pendingSubstitutingBillQuery;
    }

    @Override
    public PlatformTransactionManager getTransactionManager() {
        return transactionManager;
    }

    @Override
    public PendingSubstitutionBillService getPendingSubstitutionBillService() {
        return pendingSubstitutionBillService;
    }

    @Override
    public MigratedBillService getMigratedBillService() {
        return migratedBillService;
    }

    @Override
    public BillSubstitutionTaskService getSubstitutionTaskService() {
        return substitutionTaskService;
    }

    @Override
    public CmdbProductOverviewService getProductOverviewService() {
        return productOverviewService;
    }

    @Override
    public AggregatedBillService getAggregatedBillService() {
        return aggregatedBillService;
    }

}
