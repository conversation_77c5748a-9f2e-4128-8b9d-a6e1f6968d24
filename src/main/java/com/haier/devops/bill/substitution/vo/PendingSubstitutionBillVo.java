package com.haier.devops.bill.substitution.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class PendingSubstitutionBillVo {
    private LocalDateTime billingCycle;
    private BigDecimal summer;
    private String vendor;
    private String scode;
    private String accountName;
    private String projectCode;
    private String projectName;
    private String productName;
    private String remark;

    private String aggregatedId;
    private String productCode = "paas";
    private String instanceId;
}
