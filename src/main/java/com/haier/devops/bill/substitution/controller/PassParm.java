package com.haier.devops.bill.substitution.controller;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PassParm {

    private String scodes;

    @NotNull(message = "开始日期不能为空")
    private String startDate;

    @NotNull(message = "结束日期不能为空")
    private String endDate;

    private String vendor;

    private List<Map<String,String>> appList;

    private List<Map<String,String>> domainList;

    private List<Map<String,String>> productList;

    private String currency;
}
