package com.haier.devops.bill.substitution;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.aws.commitment.vo.AggregatedBillCommitmentVo;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.vo.ServiceResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component("awsCommitmentPendingSubstitutingBillQuery")
@Slf4j
public class AwsCommitmentPendingSubstitutingBillQuery implements PendingSubstitutingBillQuery {
    private static final int SCALE_CALCULATION = 10;
    private static final int SCALE_FINANCIAL = 2;
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;
    private static final DateTimeFormatter YEAR_MONTH_DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final AggregatedBillService aggregatedBillService;
    public AwsCommitmentPendingSubstitutingBillQuery(AggregatedBillService aggregatedBillService) {
        this.aggregatedBillService = aggregatedBillService;
    }

    /**
     * 将到天的金额根据签约情况重新分配
     * @param billingCycle String in "YYYY-MM-DD" format
     * @param subTaskId
     * @return
     */
    @Override
    public ServiceResult<List<PendingSubstitutionBill>> query(String billingCycle, String subTaskId) {
        int page = 1;
        int pageSize = 500;

        List<AggregatedBillCommitmentVo> bills = new ArrayList<>();

        PageInfo<AggregatedBillCommitmentVo> billsPage = aggregatedBillService.getAwsPendingSubstitutionBills(billingCycle, page, pageSize);
        while (billsPage.hasContent()) {
            bills.addAll(billsPage.getList());

            page++;
            billsPage = aggregatedBillService.getAwsPendingSubstitutionBills(billingCycle, page, pageSize);
        }

        if (bills.isEmpty()) {
            log.info("No bills found for AWS commitment pending substitution. BillingCycle: {}, SubTaskId: {}", billingCycle, subTaskId);
            return new ServiceResult<>(new ArrayList<>());
        }

        List<PendingSubstitutionBill> resultPendingBills = new ArrayList<>();
        LocalDateTime localDateTimeBillingCycle = LocalDate.parse(billingCycle, YEAR_MONTH_DAY_FORMATTER).atStartOfDay();

        // Group bills by account name to handle each account's invoice separately.
        Map<String, List<AggregatedBillCommitmentVo>> billsByAccount = bills.stream()
                .filter(b -> b.getAccountName() != null)
                .collect(Collectors.groupingBy(AggregatedBillCommitmentVo::getAccountName));

        // 每个账号单独分摊
        for (Map.Entry<String, List<AggregatedBillCommitmentVo>> accountEntry : billsByAccount.entrySet()) {
            String accountName = accountEntry.getKey();
            List<AggregatedBillCommitmentVo> accountBills = accountEntry.getValue();

            BigDecimal summerForAccount = accountBills.stream()
                    .map(AggregatedBillCommitmentVo::getPayableSum)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal invoiceSummerForAccount = accountBills.stream()
                    .map(AggregatedBillCommitmentVo::getOriginalSum)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // The difference is due to AWS automatically applying Savings Plans, RIs, etc.
            BigDecimal diffForAccount = invoiceSummerForAccount.subtract(summerForAccount);

            // Distribute diff amount for the current account
            if (diffForAccount.compareTo(BigDecimal.ZERO) != 0) {
                if (summerForAccount.compareTo(BigDecimal.ZERO) == 0) {
                    log.warn("Total payableSum for account '{}' is zero, but diff ({}) is non-zero. Cannot distribute proportionally. SubTaskId: {}", accountName, diffForAccount, subTaskId);
                    continue;
                }

                Map<GroupKey, BigDecimal> scodePayableSumMap = accountBills.stream()
                        .filter(b -> b.getScode() != null && b.getPayableSum() != null)
                        .collect(Collectors.groupingBy(
                                a -> new GroupKey(a.getVendor(), a.getAccountName(), a.getScode()),
                                Collectors.reducing(BigDecimal.ZERO, AggregatedBill::getPayableSum, BigDecimal::add)
                        ));

                List<PendingSubstitutionBill> accountResultBills = new ArrayList<>();
                BigDecimal accumulatedDiff = BigDecimal.ZERO;

                for (Map.Entry<GroupKey, BigDecimal> entry : scodePayableSumMap.entrySet()) {
                    GroupKey groupKey = entry.getKey();
                    BigDecimal currentScodePayableSum = entry.getValue();

                    if (currentScodePayableSum.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }

                    BigDecimal proportion = currentScodePayableSum.divide(summerForAccount, SCALE_CALCULATION, ROUNDING_MODE);
                    BigDecimal diffForScode = diffForAccount.multiply(proportion).setScale(SCALE_FINANCIAL, ROUNDING_MODE);
                    accumulatedDiff = accumulatedDiff.add(diffForScode);

                    if (diffForScode.compareTo(BigDecimal.ZERO) != 0) {
                        PendingSubstitutionBill diffPendingBill = PendingSubstitutionBill.builder()
                                .billingCycle(localDateTimeBillingCycle)
                                .vendor(groupKey.getVendor())
                                .accountName(groupKey.getAccountName())
                                .scode(groupKey.getScode())
                                .summer(diffForScode)
                                .productName("签约优惠")
                                .subTaskId(subTaskId)
                                .build();
                        accountResultBills.add(diffPendingBill);
                    }
                }

                // Adjust for rounding errors to ensure the sum of distributed diffs equals the total diff for the account.
                if (!accountResultBills.isEmpty() && diffForAccount.compareTo(accumulatedDiff) != 0) {
                    PendingSubstitutionBill firstBill = accountResultBills.get(0);
                    firstBill.setSummer(firstBill.getSummer().add(diffForAccount.subtract(accumulatedDiff)));
                }
                resultPendingBills.addAll(accountResultBills);
            }
        }

        return new ServiceResult<>(resultPendingBills);
    }

    @Data
    @AllArgsConstructor
    public static class GroupKey {
        private String vendor;
        private String accountName;
        private String scode;
    }
}
