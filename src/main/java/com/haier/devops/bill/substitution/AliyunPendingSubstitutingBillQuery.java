package com.haier.devops.bill.substitution;

import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.DocConfig;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;
import com.haier.devops.bill.common.service.DocConfigService;
import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.export.util.SheetUtil;
import com.haier.devops.bill.sdks.feishu.FeishuClient;
import com.haier.devops.bill.substitution.vo.PendingSubstitutionBillVo;
import com.haier.devops.bill.util.DateUtil;
import com.haier.devops.bill.util.Md5Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询阿里容器云替换账单
 * <AUTHOR>
 */
@Component("aliyunPendingSubstitutingBillQuery")
@Slf4j
public class AliyunPendingSubstitutingBillQuery implements PendingSubstitutingBillQuery {
    private static final String ADDITION = "增量";
    private final FeishuClient feishuClient;
    private final DocConfigService docConfigService;

    public AliyunPendingSubstitutingBillQuery(FeishuClient feishuClient, DocConfigService docConfigService) {
        this.feishuClient = feishuClient;
        this.docConfigService = docConfigService;
    }

    @Override
    public ServiceResult<List<PendingSubstitutionBill>> query(String billingCycle, String subTaskId) {
        DocConfig sheetConfig = docConfigService.getDocConfig("sheet", "substitution:aliyun:paas", billingCycle);
        if (null == sheetConfig || StringUtils.isBlank(sheetConfig.getToken())) {
            String err = "未找到容器云分摊sheet token配置";
            log.error(err);
            return new ServiceResult<>(err, null);
        }

        ServiceResult<List<Object>> sheetContentsResult =
                SheetUtil.getSheetContents(feishuClient, sheetConfig.getToken());
        if (!sheetContentsResult.isSuccess()) {
            String err = "获取容器云分摊sheet数据出错";
            log.error("{}： {}", err, sheetContentsResult.getErrMsg());
            return new ServiceResult<>(err, null);
        }
        String[] nodePropertiesSheetColumnsMapping = new String[]{
                "billingCycle", "vendor", "accountName", "scode", "projectCode", "projectName",
                "", "", "", "", "", "", "productName", "", "summer", "remark"
        };
        ServiceResult<List<PendingSubstitutionBillVo>> containerNodesResult =
                SheetUtil.getObjectsFromSheetData(PendingSubstitutionBillVo.class,
                        nodePropertiesSheetColumnsMapping, sheetContentsResult.getData());

        if (!containerNodesResult.isSuccess()) {
            String err = "解析容器云分摊sheet数据出错";
            log.error("{}： {}", err, containerNodesResult.getErrMsg());
            return new ServiceResult<>(err, null);
        }

        LocalDateTime firstDayOfCurrentYear = DateUtil.getFirstDayOfCurrentYear();
        LocalDateTime firstDayOfLastYear = DateUtil.getFirstDayOfLastYear();

        LocalDateTime billingCycleDate = DateUtil.convertYearMonthToLocalDateTime(billingCycle);
        List<PendingSubstitutionBill> pendingSubstitutionBills = new ArrayList<>();
        for (PendingSubstitutionBillVo billVo : containerNodesResult.getData()) {
            if (!billingCycleDate.equals(billVo.getBillingCycle())) {
                continue;
            }

            String supplementId = AliyunPaasProductEnum.getProductCode(billVo.getProductName());
            if (StringUtils.isBlank(supplementId)) {
                log.warn("未知的容器云分账类型：{}", billVo.getProductName());
            }

            String aggregateId = Md5Util.Sum(billVo.getVendor() + billVo.getAccountName() + Constant.PAAS_PRODUCT_CODE + supplementId + billVo.getScode());

            boolean isAddition = false;
            String remark = billVo.getRemark();
            if (StringUtils.isBlank(remark) || remark.contains(ADDITION)) {
                isAddition = true;
            }

            PendingSubstitutionBill pendingSubstitutionBill = PendingSubstitutionBill.builder()
                    .vendor(billVo.getVendor())
                    .accountName(billVo.getAccountName())
                    .scode(billVo.getScode())
                    .granularity(AggregatedBill.Granularity.MONTH.getValue())
                    .aggregatedId(aggregateId)
                    .instanceId("c-" + billVo.getScode())
                    .supplementId(supplementId)
                    .productCode(Constant.PAAS_PRODUCT_CODE)
                    .productName(billVo.getProductName())
                    .projectCode(billVo.getProjectCode())
                    .projectName(billVo.getProjectName())
                    .subTaskId(subTaskId)
                    .subscriptionType(Constant.PAY_AS_YOU_GO)
                    .billingCycle(billVo.getBillingCycle())
                    .summer(billVo.getSummer())
                    .creationTime(isAddition ? firstDayOfCurrentYear : firstDayOfLastYear)
                    .build();
            pendingSubstitutionBills.add(pendingSubstitutionBill);
        }
        return new ServiceResult<>(pendingSubstitutionBills);
    }

    public enum AliyunPaasProductEnum {
        GPU("容器云GPU（整卡调度、显存隔离）", "gpu"),
        CPU_MEMORY("容器云应用（cpu、内存）", "cpu-memory");

        @Getter
        private String productName;
        @Getter
        private String productCode;

        private static final Map<String, String> PRODUCT_MAP = new HashMap<>();

        static {
            for (AliyunPaasProductEnum product: AliyunPaasProductEnum.values()) {
                PRODUCT_MAP.put(product.getProductName(), product.getProductCode());
            }
        }

        AliyunPaasProductEnum(String productName, String productCode) {
            this.productName = productName;
            this.productCode = productCode;
        }

        public static String getProductCode(String productName) {
            return PRODUCT_MAP.get(productName);
        }
    }
}
