package com.haier.devops.bill.substitution;

import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.DocConfig;
import com.haier.devops.bill.common.entity.PendingSubstitutionBill;
import com.haier.devops.bill.common.service.DocConfigService;
import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.export.util.SheetUtil;
import com.haier.devops.bill.sdks.feishu.FeishuClient;
import com.haier.devops.bill.substitution.vo.PendingSubstitutionBillVo;
import com.haier.devops.bill.util.DateUtil;
import com.haier.devops.bill.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component("huaweiPendingSubstitutingBillQuery")
@Slf4j
public class HuaweiPendingSubstitutingBillQuery implements PendingSubstitutingBillQuery {
    private static final String ADDITION = "增量";
    private final FeishuClient feishuClient;
    private final DocConfigService docConfigService;

    public HuaweiPendingSubstitutingBillQuery(FeishuClient feishuClient,
                                              DocConfigService docConfigService) {
        this.feishuClient = feishuClient;
        this.docConfigService = docConfigService;
    }

    @Override
    public ServiceResult<List<PendingSubstitutionBill>> query(String billingCycle, String subTaskId) {
        DocConfig sheetConfig = docConfigService.getDocConfig("sheet", "substitution:huawei:paas", billingCycle);
        if (null == sheetConfig || StringUtils.isBlank(sheetConfig.getToken())) {
            String err = "未找到华为容器云分摊sheet token配置";
            log.error(err);
            return new ServiceResult<>(err, null);
        }

        ServiceResult<List<Object>> sheetContentsResult =
                SheetUtil.getSheetContents(feishuClient, sheetConfig.getToken());
        if (!sheetContentsResult.isSuccess()) {
            String err = "获取华为容器云分摊sheet数据出错";
            log.error("{}： {}", err, sheetContentsResult.getErrMsg());
            return new ServiceResult<>(err, null);
        }
        String[] nodePropertiesSheetColumnsMapping = new String[]{
                "billingCycle", "vendor", "accountName", "scode", "projectCode", "projectName",
                "", "", "", "", "", "", "productName", "", "summer", "remark"
        };
        ServiceResult<List<PendingSubstitutionBillVo>> containerNodesResult =
                SheetUtil.getObjectsFromSheetData(PendingSubstitutionBillVo.class,
                        nodePropertiesSheetColumnsMapping, sheetContentsResult.getData());

        if (!containerNodesResult.isSuccess()) {
            String err = "解析华为容器云分摊sheet数据出错";
            log.error("{}： {}", err, containerNodesResult.getErrMsg());
            return new ServiceResult<>(err, null);
        }


        LocalDateTime firstDayOfCurrentYear = DateUtil.getFirstDayOfCurrentYear();
        LocalDateTime firstDayOfLastYear = DateUtil.getFirstDayOfLastYear();

        List<PendingSubstitutionBill> pendingSubstitutionBills = new ArrayList<>();
        LocalDateTime billingCycleDate = DateUtil.convertYearMonthToLocalDateTime(billingCycle);
        for (PendingSubstitutionBillVo billVo : containerNodesResult.getData()) {
            if (!billingCycleDate.equals(billVo.getBillingCycle())) {
                continue;
            }
            String aggregateId = Md5Util.Sum(billVo.getVendor() + billVo.getAccountName() + Constant.PAAS_PRODUCT_CODE + billVo.getScode());

            boolean isAddition = false;
            String remark = billVo.getRemark();
            if (StringUtils.isBlank(remark) || remark.contains(ADDITION)) {
                isAddition = true;
            }

            PendingSubstitutionBill pendingSubstitutionBill = PendingSubstitutionBill.builder()
                    .vendor(billVo.getVendor())
                    .accountName(billVo.getAccountName())
                    .scode(billVo.getScode())
                    .granularity(AggregatedBill.Granularity.MONTH.getValue())
                    .aggregatedId(aggregateId)
                    .instanceId("c-" + billVo.getScode())
                    .productCode(Constant.PAAS_PRODUCT_CODE)
                    .productName(billVo.getProductName())
                    .projectCode(billVo.getProjectCode())
                    .projectName(billVo.getProjectName())
                    .subTaskId(subTaskId)
                    .subscriptionType(Constant.PAY_AS_YOU_GO)
                    .billingCycle(billVo.getBillingCycle())
                    .summer(billVo.getSummer())
                    .creationTime(isAddition ? firstDayOfCurrentYear : firstDayOfLastYear)
                    .build();
            pendingSubstitutionBills.add(pendingSubstitutionBill);
        }
        return new ServiceResult<>(pendingSubstitutionBills);
    }

}
