package com.haier.devops.bill.substitution;

import com.haier.devops.bill.common.entity.BillSubstitutionTask;
import com.haier.devops.bill.common.service.BillSubstitutionTaskService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;

import static com.haier.devops.bill.substitution.Constant.*;

/**
 * <AUTHOR>
 */
@Component
public class SubstitutionTaskExecutor {
    private final BillSubstitutionTaskService substitutionTaskService;
    private final AliyunPaasSubstitutionTask aliyunPassSubstitutionTask;
    private final AliyunDedicatedPaasSubstitutionTask aliyunDedicatedPaasSubstitutionTask;
    private final BigDataStarrocksPaasSubstitutionTask starrocksPaasSubstitutionTask;
    private final BigDataHdopPaasSubstitutionTask hdopPaasSubstitutionTask;
    private final HuaweiPaasSubstitutionTask huaweiPaasSubstitutionTask;
    private final CloudMonitorSubstitutionTask cloudMonitorSubstitutionTask;
    private final HuaweiUcsSubstitutionTask huaweiUcsSubstitutionTask;
    private final AwsCommitmentSubstitutionTask awsCommitmentSubstitutionTask;


    public SubstitutionTaskExecutor(BillSubstitutionTaskService substitutionTaskService,
                                    AliyunPaasSubstitutionTask aliyunPassSubstitutionTask,
                                    AliyunDedicatedPaasSubstitutionTask aliyunDedicatedPaasSubstitutionTask,
                                    BigDataStarrocksPaasSubstitutionTask starrocksPaasSubstitutionTask,
                                    BigDataHdopPaasSubstitutionTask hdopPaasSubstitutionTask,
                                    HuaweiPaasSubstitutionTask huaweiPaasSubstitutionTask,
                                    CloudMonitorSubstitutionTask cloudMonitorSubstitutionTask,
                                    HuaweiUcsSubstitutionTask huaweiUcsSubstitutionTask,
                                    AwsCommitmentSubstitutionTask awsCommitmentSubstitutionTask) {
        this.substitutionTaskService = substitutionTaskService;
        this.aliyunPassSubstitutionTask = aliyunPassSubstitutionTask;
        this.aliyunDedicatedPaasSubstitutionTask = aliyunDedicatedPaasSubstitutionTask;
        this.starrocksPaasSubstitutionTask = starrocksPaasSubstitutionTask;
        this.hdopPaasSubstitutionTask = hdopPaasSubstitutionTask;
        this.huaweiPaasSubstitutionTask = huaweiPaasSubstitutionTask;
        this.cloudMonitorSubstitutionTask = cloudMonitorSubstitutionTask;
        this.huaweiUcsSubstitutionTask = huaweiUcsSubstitutionTask;
        this.awsCommitmentSubstitutionTask = awsCommitmentSubstitutionTask;
    }

    public void create(String type, String billingCycle) {
        List<BillSubstitutionTask> tasks = substitutionTaskService.getTaskByTypeAndBillingCycle(type, billingCycle);
        if (!CollectionUtils.isEmpty(tasks)) {
            return;
        }

        switch (type) {
            case ALIYUN_PAAS:
                // 创建阿里云账单替换任务
                BillSubstitutionTask substitutionTask = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(ALIYUN_PAAS)
                        .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                        .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(substitutionTask);
                break;
            case ALIYUN_DEDICATED_PAAS:
                // 创建阿里云专属云账单替换任务
                BillSubstitutionTask dedicatedSubstitutionTask = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(ALIYUN_DEDICATED_PAAS)
                        .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                        .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(dedicatedSubstitutionTask);
                break;
            case HUAWEI_PAAS:
        // 创建华为云账单替换任务
        BillSubstitutionTask huaweiSubstitutionTask =
            BillSubstitutionTask.builder()
                .subTaskId(UUID.randomUUID().toString())
                .subType(HUAWEI_PAAS)
                .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                .billingCycle(billingCycle)
                .build();
                substitutionTaskService.createTask(huaweiSubstitutionTask);
                break;
            case UCS:
                // 创建华为UCS云账单替换任务
                BillSubstitutionTask huaweiUcsSubstitutionTask =
                        BillSubstitutionTask.builder()
                                .subTaskId(UUID.randomUUID().toString())
                                .subType(UCS)
                                .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                                .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                                .billingCycle(billingCycle)
                                .build();
                substitutionTaskService.createTask(huaweiUcsSubstitutionTask);
                break;
            case BIGDATA_STARROCKS_CODE:
                // 创建大数据Starrocks账单替换任务
                BillSubstitutionTask starRocksSubstitutionTask = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(BIGDATA_STARROCKS_CODE)
                        .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                        .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(starRocksSubstitutionTask);
                break;
            case BIGDATA_HDOP_CODE:
                // 创建大数据HDOP账单替换任务
                BillSubstitutionTask hdopSubstitutionTask = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(BIGDATA_HDOP_CODE)
                        .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                        .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(hdopSubstitutionTask);
                break;
            case CLOUD_MONITOR:
                // 创建云监控账单替换任务
                BillSubstitutionTask cloudMonitorSubstitutionTaskObj = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(CLOUD_MONITOR)
                        .stage(SubstitutionTask.TaskEnum.MIGRATION.getStage())
                        .process(SubstitutionTask.TaskEnum.MIGRATION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(cloudMonitorSubstitutionTaskObj);
                break;
            case AWS_REDISTRIBUTION:
                // 创建aws二次分账任务
                BillSubstitutionTask awsSubstitutionTaskObj = BillSubstitutionTask.builder()
                        .subTaskId(UUID.randomUUID().toString())
                        .subType(AWS_REDISTRIBUTION)
                        .stage(SubstitutionTask.TaskEnum.SUBSTITUTION.getStage())
                        .process(SubstitutionTask.TaskEnum.SUBSTITUTION.getProcess())
                        .billingCycle(billingCycle)
                        .build();
                substitutionTaskService.createTask(awsSubstitutionTaskObj);
                break;
            default:
                break;
        }
    }

    /**
     * 迁移账单
     * @param type
     * @param billingCycle
     */
    public void migrate(String type, String billingCycle) {
        List<BillSubstitutionTask> tasks = substitutionTaskService.getPendingMigrationTaskByType(type, billingCycle);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (BillSubstitutionTask task : tasks) {
            migrate(task);
        }
    }

    /**
     * 清理账单
     * @param type
     * @param billingCycle
     */
    public void dryClean(String type, String billingCycle) {
        List<BillSubstitutionTask> tasks = substitutionTaskService.getPendingCleaningTaskByType(type, billingCycle);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (BillSubstitutionTask task : tasks) {
            dryClean(task);
        }
    }

    /**
     * 计算（准备）账单
     * @param type
     * @param billingCycle
     */
    public void calculate(String type, String billingCycle) {
        List<BillSubstitutionTask> tasks = substitutionTaskService.getPendingCalculationTaskByType(type, billingCycle);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (BillSubstitutionTask task : tasks) {
            calculate(task);
        }
    }


    /**
     * 替换账单
     * @param type
     * @param billingCycle
     */
    public void substitute(String type, String billingCycle) {
        List<BillSubstitutionTask> tasks = substitutionTaskService.getPendingSubstitutionTaskByType(type, billingCycle);
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        for (BillSubstitutionTask task : tasks) {
            substitute(task);
        }
    }

    private void substitute(BillSubstitutionTask task) {
        switch (task.getSubType()) {
            case ALIYUN_PAAS:
                // 替换阿里云账单
                aliyunPassSubstitutionTask.substitute(task);
                break;
            case ALIYUN_DEDICATED_PAAS:
                // 替换阿里云专属云账单
                aliyunDedicatedPaasSubstitutionTask.substitute(task);
                break;
            case HUAWEI_PAAS:
                // 替换华为云账单
                huaweiPaasSubstitutionTask.substitute(task);
                break;
            case UCS:
                huaweiUcsSubstitutionTask.substitute(task);
                break;
            case BIGDATA_STARROCKS_CODE:
                // 替换大数据Starrocks账单
                starrocksPaasSubstitutionTask.substitute(task);
                break;
            case BIGDATA_HDOP_CODE:
                // 替换大数据HDOP账单
                hdopPaasSubstitutionTask.substitute(task);
                break;
            case CLOUD_MONITOR:
                // 替换云监控账单
                cloudMonitorSubstitutionTask.substitute(task);
                break;
            case AWS_REDISTRIBUTION:
                awsCommitmentSubstitutionTask.substitute(task);
                break;
            default:
                break;
        }
    }


    private void dryClean(BillSubstitutionTask task) {
        switch (task.getSubType()) {
            case ALIYUN_PAAS:
                // 清理阿里云账单
                aliyunPassSubstitutionTask.dryClean(task);
                break;
            case ALIYUN_DEDICATED_PAAS:
                // 清理阿里云专属云账单
                aliyunDedicatedPaasSubstitutionTask.dryClean(task);
                break;
            case HUAWEI_PAAS:
                // 清理华为云账单
                huaweiPaasSubstitutionTask.dryClean(task);
                break;
            case UCS:
                huaweiUcsSubstitutionTask.dryClean(task);
                break;
            case BIGDATA_STARROCKS_CODE:
                // 清理大数据Starrocks账单
                starrocksPaasSubstitutionTask.dryClean(task);
                break;
            case BIGDATA_HDOP_CODE:
                // 清理大数据HDOP账单
                hdopPaasSubstitutionTask.dryClean(task);
                break;
                // 清理云监控账单
            case CLOUD_MONITOR:
                cloudMonitorSubstitutionTask.dryClean(task);
                break;
            default:
                break;
        }
    }


    private void calculate(BillSubstitutionTask task) {
        switch (task.getSubType()) {
            case ALIYUN_PAAS:
                // 计算阿里云账单
                aliyunPassSubstitutionTask.calculate(task);
                break;
            case ALIYUN_DEDICATED_PAAS:
                // 计算阿里云专属云账单
                aliyunDedicatedPaasSubstitutionTask.calculate(task);
                break;
            case HUAWEI_PAAS:
                // 计算华为云账单
                huaweiPaasSubstitutionTask.calculate(task);
                break;
            case UCS:
                huaweiUcsSubstitutionTask.calculate(task);
                break;
            case BIGDATA_STARROCKS_CODE:
                // 计算大数据Starrocks账单
                starrocksPaasSubstitutionTask.calculate(task);
                break;
            case BIGDATA_HDOP_CODE:
                // 计算大数据HDOP账单
                hdopPaasSubstitutionTask.calculate(task);
                break;
            case CLOUD_MONITOR:
                // 计算云监控账单
                cloudMonitorSubstitutionTask.calculate(task);
                break;
            default:
                break;
        }
    }

    private void migrate(BillSubstitutionTask task) {
        switch (task.getSubType()) {
            case ALIYUN_PAAS:
                // 迁移阿里云账单
                aliyunPassSubstitutionTask.migrate(task);
                break;
            case ALIYUN_DEDICATED_PAAS:
                // 迁移阿里云专属云账单
                aliyunDedicatedPaasSubstitutionTask.migrate(task);
                break;
            case HUAWEI_PAAS:
                // 迁移华为云账单
                huaweiPaasSubstitutionTask.migrate(task);
                break;
            case UCS:
                huaweiUcsSubstitutionTask.migrate(task);
                break;
            case BIGDATA_STARROCKS_CODE:
                // 迁移大数据Starrocks账单
                starrocksPaasSubstitutionTask.migrate(task);
                break;
            case BIGDATA_HDOP_CODE:
                // 迁移大数据HDOP账单
                hdopPaasSubstitutionTask.migrate(task);
                break;
            case CLOUD_MONITOR:
                cloudMonitorSubstitutionTask.migrate(task);
                break;
            default:
                break;
        }
    }
}

