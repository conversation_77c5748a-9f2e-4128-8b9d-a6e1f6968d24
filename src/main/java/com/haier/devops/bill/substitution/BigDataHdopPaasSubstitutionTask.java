package com.haier.devops.bill.substitution;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.BillSubstitutionTask;
import com.haier.devops.bill.common.service.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BigDataHdopPaasSubstitutionTask extends SubstitutionTask implements PendingSubstitutedBillQuery {
    @Getter
    private final AggregatedBillService aggregatedBillService;
    @Getter
    private final CmdbProductOverviewService productOverviewService;
    @Getter
    private final BillSubstitutionTaskService substitutionTaskService;
    @Getter
    private final MigratedBillService migratedBillService;
    @Getter
    private final PendingSubstitutionBillService pendingSubstitutionBillService;

    @Getter
    private final PlatformTransactionManager transactionManager;
    @Getter
    private final PendingSubstitutingBillQuery pendingSubstitutingBillQuery;

    public BigDataHdopPaasSubstitutionTask(AggregatedBillService aggregatedBillService,
                                      CmdbProductOverviewService productOverviewService,
                                      BillSubstitutionTaskService substitutionTaskService,
                                      MigratedBillService migratedBillService,
                                      PendingSubstitutionBillService pendingSubstitutionBillService,
                                      PlatformTransactionManager transactionManager,
                                      @Qualifier("bigDataHdopPendingSubstitutingBillQuery")
                                      PendingSubstitutingBillQuery pendingSubstitutingBillQuery) {
        this.aggregatedBillService = aggregatedBillService;
        this.productOverviewService = productOverviewService;
        this.substitutionTaskService = substitutionTaskService;
        this.migratedBillService = migratedBillService;
        this.pendingSubstitutionBillService = pendingSubstitutionBillService;
        this.transactionManager = transactionManager;
        this.pendingSubstitutingBillQuery = pendingSubstitutingBillQuery;
    }

    @Override
    void migrate(BillSubstitutionTask task) {
        doMigrate(this, task.getBillingCycle(), task.getSubTaskId());
    }

    @Override
    void clean(BillSubstitutionTask task, CleanToken token) {
        Objects.requireNonNull(token);
        String billingCycle = task.getBillingCycle();
        int cnt = aggregatedBillService.cleanBigDataHdopPassBills(billingCycle);
        log.info("cleanBigDataHdopPassBills, billingCycle: {}, cnt: {}", billingCycle, cnt);

        PageInfo<AggregatedBill> pager = aggregatedBillService.getBigDataHdopPaasPendingSubstitutionBills(billingCycle, 1, perPage);
        if (pager.getTotal() == 0) {
        }
    }

    @Override
    public PageInfo<AggregatedBill> query(String billingCycle, int page, int perPage) {
        return aggregatedBillService.getBigDataHdopPaasPendingSubstitutionBills(billingCycle, page, perPage);
    }

}
