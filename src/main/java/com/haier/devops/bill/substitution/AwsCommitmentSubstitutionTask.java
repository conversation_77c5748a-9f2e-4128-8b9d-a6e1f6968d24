package com.haier.devops.bill.substitution;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.service.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AwsCommitmentSubstitutionTask extends SubstitutionTask implements PendingSubstitutedBillQuery {
    private static final String AMAZON_EC2 = "AmazonEC2";
    private static final String AMAZON_RDS = "AmazonRDS";

    @Getter
    private final AggregatedBillService aggregatedBillService;
    @Getter
    private final CmdbProductOverviewService productOverviewService;
    @Getter
    private final BillSubstitutionTaskService substitutionTaskService;
    @Getter
    private final MigratedBillService migratedBillService;
    @Getter
    private final PendingSubstitutionBillService pendingSubstitutionBillService;

    private final BillTaskService billTaskService;

    @Getter
    private final PlatformTransactionManager transactionManager;
    @Getter
    private final PendingSubstitutingBillQuery pendingSubstitutingBillQuery;

    public AwsCommitmentSubstitutionTask(AggregatedBillService aggregatedBillService,
                                         CmdbProductOverviewService productOverviewService,
                                         BillSubstitutionTaskService substitutionTaskService,
                                         MigratedBillService migratedBillService,
                                         PendingSubstitutionBillService pendingSubstitutionBillService,
                                         RawBillSyncLogService syncLogService, BillTaskService billTaskService,
                                         PlatformTransactionManager transactionManager,
                                         @Qualifier("awsCommitmentPendingSubstitutingBillQuery")
                                         PendingSubstitutingBillQuery pendingSubstitutingBillQuery) {
        this.aggregatedBillService = aggregatedBillService;
        this.productOverviewService = productOverviewService;
        this.substitutionTaskService = substitutionTaskService;
        this.migratedBillService = migratedBillService;
        this.pendingSubstitutionBillService = pendingSubstitutionBillService;
        this.billTaskService = billTaskService;
        this.transactionManager = transactionManager;
        this.pendingSubstitutingBillQuery = pendingSubstitutingBillQuery;
    }


    @Override
    public PageInfo<AggregatedBill> query(String billingCycle, int page, int perPage) {
        // 将ec2、rds的账单查询出来
        return aggregatedBillService.getAwsCommitmentPendingSubstitutionBills(billingCycle, page, perPage);
    }

    @Override
    void migrate(BillSubstitutionTask task) {
        // do absolute nothing
    }

    @Override
    void clean(BillSubstitutionTask task, CleanToken token) {
        Objects.requireNonNull(token);
        // do absolute nothing
    }

    @Override
    void substitute(BillSubstitutionTask task) {
        getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                TaskEnum.SUBSTITUTION.getStage(),
                TaskEnum.SUBSTITUTION.getProcess());
        getAggregatedBillService().clearDirtyData(task.getSubTaskId());


        // 从准备替换的账单数据表中获取要替换的数据
        List<PendingSubstitutionBill> pendingSubstitutionBills = getPendingSubstitutionBillService().selectBySubTaskId(task.getSubTaskId());
        if (CollectionUtils.isEmpty(pendingSubstitutionBills)) {
            return;
        }

        List<BillTask> syncLogs = billTaskService.getBaseMapper().selectList(
                new LambdaQueryWrapper<BillTask>()
                        .eq(BillTask::getVendor, "aws")
                        .eq(BillTask::getBillingCycle, task.getBillingCycle())
                        .eq(BillTask::getStage, "aggregation")
                        .eq(BillTask::getStatus, "1")
        );
        if (CollectionUtils.isEmpty(syncLogs)) {
            return;
        }

        Map<GroupKey, BillTask> taskMap = syncLogs.stream()
                .collect(Collectors.toMap(t -> new GroupKey(t.getVendor(), t.getAccountName()), t -> t));


        // 拆分成两张表
        List<AggregatedBill> aggregatedBills = new ArrayList<>();
        List<CmdbProductOverview> productOverviews = new ArrayList<>();

        for (PendingSubstitutionBill pendingBill : pendingSubstitutionBills) {

            BillTask t = taskMap.get(new GroupKey(pendingBill.getVendor(), pendingBill.getAccountName()));

            aggregatedBills.add(AggregatedBill.builder()
                    .aggregatedId(pendingBill.getAggregatedId())
                    .summer(pendingBill.getSummer())
                    .billingCycle(pendingBill.getBillingCycle())
                    .scode(pendingBill.getScode())
                    .granularity(pendingBill.getGranularity())
                    .subTaskId(pendingBill.getSubTaskId())
                    // 获取到正常的任务id塞上
                    // todo 注意重新跑账单时，需要重新进行替换
                    // aws到天汇总完成后需要创建该任务（包括disable旧任务）
                    .taskId(t.getTaskId())
                    .build());
            productOverviews.add(CmdbProductOverview.builder()
                    .aggregatedId(pendingBill.getAggregatedId())
                    .accountName(pendingBill.getAccountName())
                    .accountId(pendingBill.getAccountName())
                    .productCode(pendingBill.getProductCode())
                    .productName(pendingBill.getProductName())
                    .creationTime(pendingBill.getCreationTime())
                    .instanceId(pendingBill.getInstanceId())
                    .supplementId(pendingBill.getSupplementId())
                    .scode(pendingBill.getScode())
                    .vendor(pendingBill.getVendor())
                    .subscriptionType(pendingBill.getSubscriptionType())
                    .build());
        }

        // 事务控制 由于新增的数据不会太多，所以可以使用事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = getTransactionManager().getTransaction(def);
        try {
            getAggregatedBillService().saveBatch(aggregatedBills);
            getProductOverviewService().saveOrUpdateBatchByParams(productOverviews);
            // 标记完成
            getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                    TaskEnum.SUBSTITUTION.getStage(),
                    DONE);
            getTransactionManager().commit(status);
        } catch (Exception e) {
            log.error("substitute error, task = {}", task, e);
            getSubstitutionTaskService().updateErrorInfo(task.getSubTaskId(), "保存账单替换结果失败：" + e.getMessage());
            getTransactionManager().rollback(status);
        }
    }

    public void create(String billingCycle) {

    }

    @Data
    @AllArgsConstructor
    public static class GroupKey {
        private String vendor;
        private String accountName;
    }
}
