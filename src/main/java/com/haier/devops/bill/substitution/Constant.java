package com.haier.devops.bill.substitution;

/**
 * <AUTHOR>
 */
public class Constant {
    public static final String ALIYUN_PAAS = "aliyun_paas";
    public static final String ALIYUN_DEDICATED_PAAS = "aliyun_dedicated_paas";
    public static final String HUAWEI_PAAS = "huawei_paas";
    public static final String ALIYUN_BIGDATA = "aliyun_bigdata";

    public static final String MIGRATION = "migration";
    public static final String CALCULATION = "calculation";

    /**
     * 容器云产品编码
     */
    public static final String PAAS_PRODUCT_CODE = "paas";
    public static final String PAAS_PRODUCT_NAME = "容器云平台";

    public static final String BIGDATA_HDOP_CODE = "bigdata_hdop";
    public static final String BIGDATA_STARROCKS_CODE = "bigdata_starrocks";
    /**
     * 云监控
     */
    public static final String CLOUD_MONITOR = "cloud_monitor";

    /**
     * 华为ucs
     */
    public static final String UCS = "ucs";
    /**
     * aws二次分摊
     */
    public static final String AWS_REDISTRIBUTION = "aws_redistribution";
    /**
     * 按量
     */
    public static final String PAY_AS_YOU_GO = "1";
    public static final String SUBSCRIPTION = "2";
}
