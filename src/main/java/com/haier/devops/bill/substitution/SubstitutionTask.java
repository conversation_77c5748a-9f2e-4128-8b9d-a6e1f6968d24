package com.haier.devops.bill.substitution;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.ServiceResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class SubstitutionTask {
    final static int DONE = 1;
    final int perPage = 200;

    /**
     * 迁移
     * @param task
     */
    abstract void migrate(BillSubstitutionTask task);

    /**
     * 清除
     * @param task
     */
    void dryClean(BillSubstitutionTask task) {
        // 更新任务阶段及状态
        getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                TaskEnum.CLEANING.getStage(),
                TaskEnum.CLEANING.getProcess());

        if (!couldClean(task)) {
            return;
        }

        // 只要数值能对起来就表示清理成功，真正的清理在替换时进行
        getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                TaskEnum.CLEANING.getStage(),
                DONE);
    }

    /**
     * 计算
     * @param task
     */
    void calculate(BillSubstitutionTask task) {
        doCalculate(task);
    }

    abstract void clean(BillSubstitutionTask task, CleanToken token);

    /**
     * 替换
     * @param task
     */
    void substitute(BillSubstitutionTask task) {
        doSubstitute(task);
    }

    void doCalculate(BillSubstitutionTask task) {
        // 更新任务阶段及状态
        getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(), TaskEnum.CALCULATION.getStage(), TaskEnum.CALCULATION.getProcess());
        // 清理脏数据
        getPendingSubstitutionBillService().clearDirtyData(task.getSubTaskId());

        ServiceResult<List<PendingSubstitutionBill>> pendingSubstitutionBillResult = getPendingSubstitutingBillQuery().query(task.getBillingCycle(), task.getSubTaskId());
        if (!pendingSubstitutionBillResult.isSuccess()) {
            getSubstitutionTaskService().updateErrorInfo(task.getSubTaskId(), pendingSubstitutionBillResult.getErrMsg());
        }

        if (CollectionUtils.isEmpty(pendingSubstitutionBillResult.getData())) {
            return;
        }

        // 事务控制 由于新增的数据不会太多，所以可以使用事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = getTransactionManager().getTransaction(def);
        try {
            getPendingSubstitutionBillService().saveBatch(pendingSubstitutionBillResult.getData());
            // 标记完成
            getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(), TaskEnum.CALCULATION.getStage(), DONE);
            getTransactionManager().commit(status);
        } catch (Exception e) {
            log.error("{} error, task = {}", task.getStage(), task, e);
            getTransactionManager().rollback(status);
        }
    }

    boolean couldClean(BillSubstitutionTask task) {
        // 校验金额总数 pending_substitution_bill.summer vs migrated_bill.summer
        String pendingSum = getPendingSubstitutionBillService().getSumBySubTaskId(task.getSubTaskId());
        String migratedSum = getMigratedBillService().getSumBySubTaskId(task.getSubTaskId());
        if (!pendingSum.equals(migratedSum)) {
            String err = String.format("substitute error, pendingSum = %s, migratedSum = %s", pendingSum, migratedSum);
            log.error(err);
            getSubstitutionTaskService().updateErrorInfo(task.getSubTaskId(), err);
            return false;
        }

        return true;
    }


    void doSubstitute(BillSubstitutionTask task) {
        // 更新任务阶段及状态
        getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                TaskEnum.SUBSTITUTION.getStage(),
                TaskEnum.SUBSTITUTION.getProcess());
        getAggregatedBillService().clearDirtyData(task.getSubTaskId());

        /* 再次校验，双重保障 */
        if (!couldClean(task)) {
            return;
        }

        // 从准备替换的账单数据表中获取要替换的数据
        List<PendingSubstitutionBill> pendingSubstitutionBills = getPendingSubstitutionBillService().selectBySubTaskId(task.getSubTaskId());

        // 拆分成两张表
        List<AggregatedBill> aggregatedBills = new ArrayList<>();
        List<CmdbProductOverview> productOverviews = new ArrayList<>();
        for (PendingSubstitutionBill pendingBill : pendingSubstitutionBills) {
            aggregatedBills.add(AggregatedBill.builder()
                    .aggregatedId(pendingBill.getAggregatedId())
                    .summer(pendingBill.getSummer())
                    .billingCycle(pendingBill.getBillingCycle())
                    .scode(pendingBill.getScode())
                    .granularity(pendingBill.getGranularity())
                    .subTaskId(pendingBill.getSubTaskId())
                    .build());
            productOverviews.add(CmdbProductOverview.builder()
                    .aggregatedId(pendingBill.getAggregatedId())
                    .accountName(pendingBill.getAccountName())
                    .accountId(pendingBill.getAccountName())
                    .productCode(pendingBill.getProductCode())
                    .productName(pendingBill.getProductName())
                    .projectCode(pendingBill.getProjectCode())
                    .projectName(pendingBill.getProjectName())
                    .creationTime(pendingBill.getCreationTime())
                    .instanceId(pendingBill.getInstanceId())
                    .supplementId(pendingBill.getSupplementId())
                    .scode(pendingBill.getScode())
                    .vendor(pendingBill.getVendor())
                    .subscriptionType(pendingBill.getSubscriptionType())
                    .build());
        }

        // 事务控制 由于新增的数据不会太多，所以可以使用事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        TransactionStatus status = getTransactionManager().getTransaction(def);
        try {
            clean(task, new CleanToken());
            getAggregatedBillService().saveBatch(aggregatedBills);
            getProductOverviewService().saveOrUpdateBatchByParams(productOverviews);
            // 标记完成
            getSubstitutionTaskService().updateStageAndProcessByTaskId(task.getSubTaskId(),
                    TaskEnum.SUBSTITUTION.getStage(),
                    DONE);
            getTransactionManager().commit(status);
        } catch (Exception e) {
            log.error("substitute error, task = {}", task, e);
            getSubstitutionTaskService().updateErrorInfo(task.getSubTaskId(), "保存账单替换结果失败：" + e.getMessage());
            getTransactionManager().rollback(status);
        }
    }

    void doMigrate(PendingSubstitutedBillQuery query,
                      String billingCycle,
                      String subTaskId) {
        // 清理脏数据
        getMigratedBillService().cleanDirtyData(subTaskId);

        int page = 1;
        PageInfo<AggregatedBill> firstPager = query.query(billingCycle, page, perPage);
        int pages = firstPager.getPages();

        // 使用CountDownLatch来等待所有线程完成
        CountDownLatch latch = new CountDownLatch(pages);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        ExecutorService executorService = Executors.newFixedThreadPool(10);
        for (; page <= pages; page++) {
            int finalPage = page;
            executorService.submit(() -> {
                try {
                    PageInfo<AggregatedBill> pager;
                    if (finalPage == 1) {
                        pager = firstPager;
                    } else {
                        pager = query.query(billingCycle, finalPage, perPage);
                    }
                    List<MigratedBill> migratedBills = new ArrayList<>();

                    for (AggregatedBill bill : pager.getList()) {
                        MigratedBill migratedBill = MigratedBill.builder()
                                .aggregatedId(bill.getAggregatedId())
                                .billingCycle(bill.getBillingCycle())
                                .scode(bill.getScode())
                                .summer(bill.getSummer())
                                .payableSum(bill.getPayableSum())
                                .voucherSum(bill.getVoucherSum())
                                .cashSum(bill.getCashSum())
                                .couponSum(bill.getCouponSum())
                                .granularity(bill.getGranularity())
                                .subTaskId(subTaskId).
                                build();
                        migratedBills.add(migratedBill);
                    }
                    // 插入到迁移表
                    getMigratedBillService().saveBatch(migratedBills);
                    successCount.getAndIncrement();
                } catch (Exception e) {
                    failureCount.getAndIncrement();

                    List<Runnable> tasksWillNotReach = executorService.shutdownNow();
                    for (Runnable r :tasksWillNotReach) {
                        if (r instanceof Future) {
                            ((Future)r).cancel(false);
                        }
                        latch.countDown();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
            executorService.shutdown();
        } catch (InterruptedException e) {
            log.error("doMigrate error", e);
        }

        if (failureCount.get() > 0) {
            log.error("doMigrate error, task = {}", subTaskId);
            return;
        }

        // 标记完成
        getSubstitutionTaskService().updateStageAndProcessByTaskId(subTaskId, TaskEnum.MIGRATION.getStage(), DONE);
    }

    @Getter
    enum TaskEnum {
        MIGRATION("migration", 2),
        CALCULATION("calculation", 2),
        CLEANING("cleaning", 2),
        SUBSTITUTION("substitution", 2);

        private String stage;
        private Integer process;

        TaskEnum(String stage, Integer process) {
            this.stage = stage;
            this.process = process;
        }
    }



    abstract AggregatedBillService getAggregatedBillService();
    abstract CmdbProductOverviewService getProductOverviewService();
    abstract BillSubstitutionTaskService getSubstitutionTaskService();
    abstract MigratedBillService getMigratedBillService();
    abstract PendingSubstitutionBillService getPendingSubstitutionBillService();
    abstract PlatformTransactionManager getTransactionManager();
    abstract PendingSubstitutingBillQuery getPendingSubstitutingBillQuery();

    protected static final class CleanToken {
        private CleanToken() {}
    }
}
