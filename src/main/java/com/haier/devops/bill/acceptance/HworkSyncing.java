package com.haier.devops.bill.acceptance;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.haier.devops.bill.common.api.HworkAcceptanceApi;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import com.haier.devops.bill.common.service.ApplicationAggregationService;
import com.haier.devops.bill.common.service.ResourceAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.haier.devops.bill.common.constant.CommonConstant.HWORK_SYNCING_STATUS_SYNCED;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HworkSyncing {
    private final ApplicationAggregationDump applicationAggregationDump;
    private final ResourceAggregationDump resourceAggregationDump;

    private final OrderIdDumpDecorator orderIdDumpDecorator;
    private final ReconciliationDumpDecorator reconciliationDumpDecorator;
    private final MilestoneDumpDecorator milestoneDumpDecorator;
    private final BudgetDumpDecorator budgetDumpDecorator;
    private final SmallBillReAllocationDumpDecorator smallBillReAllocationDumpDecorator;
    private final UserNameDumpDecorator userNameDumpDecorator;

    private final ApplicationAggregationService applicationAggregationService;
    private final ResourceAggregationService resourceAggregationService;

    private final HworkAcceptanceApi hworkAcceptanceApi;
    private PlatformTransactionManager transactionManager;

    public HworkSyncing(ApplicationAggregationDump applicationAggregationDump,
                        ReconciliationDumpDecorator reconciliationDumpDecorator,
                        MilestoneDumpDecorator milestoneDumpDecorator,
                        BudgetDumpDecorator budgetDumpDecorator,
                        ResourceAggregationDump resourceAggregationDump,
                        OrderIdDumpDecorator orderIdDumpDecorator,
                        SmallBillReAllocationDumpDecorator smallBillReAllocationDumpDecorator,
                        UserNameDumpDecorator userNameDumpDecorator,
                        ApplicationAggregationService applicationAggregationService,
                        ResourceAggregationService resourceAggregationService,
                        HworkAcceptanceApi hworkAcceptanceApi,
                        PlatformTransactionManager transactionManager) {
        this.applicationAggregationDump = applicationAggregationDump;
        this.reconciliationDumpDecorator = reconciliationDumpDecorator;
        this.milestoneDumpDecorator = milestoneDumpDecorator;
        this.budgetDumpDecorator = budgetDumpDecorator;
        this.resourceAggregationDump = resourceAggregationDump;
        this.orderIdDumpDecorator = orderIdDumpDecorator;
        this.smallBillReAllocationDumpDecorator = smallBillReAllocationDumpDecorator;
        this.userNameDumpDecorator = userNameDumpDecorator;
        this.applicationAggregationService = applicationAggregationService;
        this.resourceAggregationService = resourceAggregationService;
        this.hworkAcceptanceApi = hworkAcceptanceApi;
        this.transactionManager = transactionManager;
    }


    @Transactional(rollbackFor = Exception.class)
    public void persistApplicationAggregation(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        Objects.requireNonNull(applicationAggregationDump, "applicationAggregationDump is null");
        Objects.requireNonNull(budgetDumpDecorator, "budgetDumpDecorator is null");
        Objects.requireNonNull(milestoneDumpDecorator, "milestoneDumpDecorator is null");
//        Objects.requireNonNull(reconciliationDumpDecorator, "reconciliationDumpDecorator is null");
        Objects.requireNonNull(orderIdDumpDecorator, "orderIdDumpDecorator is null");
        Objects.requireNonNull(userNameDumpDecorator, "userNameDumpDecorator is null");

        orderIdDumpDecorator.setDump(applicationAggregationDump);
        userNameDumpDecorator.setDump(orderIdDumpDecorator);
        // 不再校验发票
//        reconciliationDumpDecorator.setDump(orderIdDumpDecorator);
        milestoneDumpDecorator.setDump(userNameDumpDecorator);
        budgetDumpDecorator.setDump(milestoneDumpDecorator);

        List<ApplicationAggregation> aggregations = null;
        try {
            aggregations = budgetDumpDecorator.dump(vendor, billingCycle, stage, account);
        } catch (SynchronizationException e) {
            log.error("syncApplicationAggregation failed", e);
            return;
        }

        if (CollectionUtils.isEmpty(aggregations)) {
            throw new SynchronizationException("结果为空");
        }

        for (ApplicationAggregation a : aggregations) {
            if (StringUtils.isBlank(a.getBudgetCode())) {
                log.error("empty budget code: {}", a.toString());
            }
        }

        // 使用新的批量保存方法，设置批次大小为500，并实现保存或更新逻辑
        batchSave(aggregations, 500, items -> applicationAggregationService.saveOrUpdateBatchByOrderId((List<ApplicationAggregation>) items));

    }

    @Transactional(rollbackFor = Exception.class)
    public void persistResourceAggregation(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        Objects.requireNonNull(resourceAggregationDump);
        Objects.requireNonNull(smallBillReAllocationDumpDecorator);

        smallBillReAllocationDumpDecorator.setDump(resourceAggregationDump);
        List<ResourceAggregation> aggregations = smallBillReAllocationDumpDecorator.dump(vendor, billingCycle, stage, account);
        // 删除已有数据，确保数据加载的幂等性或重新完整加载
        resourceAggregationService.deleteByVendorAndBillingCycleAndStageAndAccount(vendor, billingCycle, account);
        // 使用新的批量保存方法，设置批次大小为500
        batchSave(aggregations, 500, resourceAggregationService::saveBatch);
    }

    public void syncApplicationAggregation(String vendor, String billingCycle, String... account) {
        LambdaQueryWrapper<ApplicationAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApplicationAggregation::getFactory, vendor);
        queryWrapper.eq(ApplicationAggregation::getPayDate, billingCycle);
        queryWrapper.isNotNull(ApplicationAggregation::getBudgetCode);
        if (null != account && account.length > 0) {
            queryWrapper.in(ApplicationAggregation::getAccount, Arrays.asList(account));
        }

        List<ApplicationAggregation> aggregations =
                applicationAggregationService
                        .getBaseMapper()
                        .selectList(queryWrapper);

        // 计算 EstimateAmount 总和
        BigDecimal totalEstimateAmount = aggregations.stream()
                .map(ApplicationAggregation::getEstimateAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal actualTotalAmount = aggregations.stream()
                .map(ApplicationAggregation::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        HworkAcceptanceApi.ApplicationAggregationWrapper body =
                new HworkAcceptanceApi.ApplicationAggregationWrapper(aggregations.size(),
                        totalEstimateAmount,
                        actualTotalAmount,
                        aggregations);
        ResultWrapper<Boolean> booleanResultWrapper = hworkAcceptanceApi.syncApplicationAggregation(body);

        if (booleanResultWrapper.getData()) {
            // batch update aggregations to make status = 1
            applicationAggregationService.batchUpdateStatus(aggregations, HWORK_SYNCING_STATUS_SYNCED);
        }
    }

    public void syncResourceAggregation(String vendor, String billingCycle, String... account) {
        int concurrent = 5;
        int batch = 200;
        int pageSize = batch * 10;
        ExecutorService executorService = Executors.newFixedThreadPool(concurrent);

        try {
            // 定义状态常量
            final int STATUS_SYNCED = 1;        // 已同步
            final int STATUS_FAILED = 2;        // 同步失败
            final int STATUS_PROCESSING = 3;    // 正在处理中

            List<ResourceAggregation> resources = resourceAggregationService.selectPendingSyncingItems(vendor, billingCycle, account);
            if (CollectionUtils.isEmpty(resources)) {
                return;
            }

            // 先获取所有需要处理的记录的ID（排除已同步和正在处理中的记录）
            List<Integer> allIds = resources.stream().map(ResourceAggregation::getId).collect(Collectors.toList());

            // 按页处理这些固定的ID
            for (int i = 0; i < allIds.size(); i += pageSize) {
                List<Integer> pageIds = allIds.subList(i, Math.min(i + pageSize, allIds.size()));

                // 使用事务包装每一页的处理，确保状态更新的原子性
                TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);

                // 将页内数据分成小批次并发处理
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                for (int j = 0; j < pageIds.size(); j += batch) {
                    final int startIndex = j;
                    final int endIndex = Math.min(j + batch, pageIds.size());
                    List<Integer> batchIds = pageIds.subList(startIndex, endIndex);

                    futures.add(CompletableFuture.runAsync(() -> {
                        // 使用事务确保状态更新的原子性
                        transactionTemplate.execute(status -> {
                            try {
                                // 1. 首先查询并锁定要处理的记录
                                List<ResourceAggregation> batchData = resourceAggregationService.getBaseMapper()
                                        .selectList(new LambdaQueryWrapper<ResourceAggregation>()
                                                .in(ResourceAggregation::getId, batchIds)
                                                .notIn(ResourceAggregation::getStatus, Arrays.asList(STATUS_SYNCED, STATUS_PROCESSING))
                                                .last("FOR UPDATE"));

                                if (batchData.isEmpty()) {
                                    // 没有需要处理的记录，可能已被其他线程处理
                                    return null;
                                }

                                // 2. 标记为处理中
                                batchData.forEach(item -> item.setStatus(STATUS_PROCESSING));
                                resourceAggregationService.updateBatchById(batchData);

                                // 提交事务，释放锁
                                status.flush();

                                // 3. 执行同步操作（在事务外执行，避免长时间占用数据库连接）
                                HworkAcceptanceApi.ResourceAggregationResultWrapper result =
                                        callSyncResourceAggregationApi(batchData);

                                // 4. 开启新事务更新最终状态
                                return transactionTemplate.execute(innerStatus -> {
                                    int newStatus = Boolean.TRUE.equals(result.getData()) ? STATUS_SYNCED : STATUS_FAILED;

                                    // 重新查询记录，确保更新最新状态
                                    List<ResourceAggregation> updatedBatchData = resourceAggregationService.getBaseMapper()
                                            .selectList(new LambdaQueryWrapper<ResourceAggregation>()
                                                    .in(ResourceAggregation::getId, batchIds)
                                                    .eq(ResourceAggregation::getStatus, STATUS_PROCESSING));

                                    updatedBatchData.forEach(item -> item.setStatus(newStatus));
                                    resourceAggregationService.updateBatchById(updatedBatchData);
                                    return null;
                                });
                            } catch (Exception e) {
                                log.error("处理批次数据时发生错误", e);
                                // 发生异常时，将状态更新为失败
                                List<ResourceAggregation> failedBatchData = resourceAggregationService.getBaseMapper()
                                        .selectList(new LambdaQueryWrapper<ResourceAggregation>()
                                                .in(ResourceAggregation::getId, batchIds)
                                                .eq(ResourceAggregation::getStatus, STATUS_PROCESSING));

                                failedBatchData.forEach(item -> item.setStatus(STATUS_FAILED));
                                resourceAggregationService.updateBatchById(failedBatchData);
                                return null;
                            }
                        });
                    }, executorService));
                }

                // 等待当前页的所有批次处理完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
        } finally {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }


    private HworkAcceptanceApi.ResourceAggregationResultWrapper callSyncResourceAggregationApi(List<ResourceAggregation> aggregations) {
        BigDecimal detailTotalAmount = aggregations.stream()
                .map(ResourceAggregation::getCostAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        HworkAcceptanceApi.ResourceAggregationWrapper body = new HworkAcceptanceApi.ResourceAggregationWrapper(aggregations.size(), detailTotalAmount, aggregations);
        return hworkAcceptanceApi.syncResourceAggregation(body);
    }

    /**
     * 通用批量保存方法
     * @param collection 要保存的集合
     * @param batchSize 每批的大小
     * @param saveFunction 具体的保存函数
     * @param <T> 实体类型
     */
    private <T> void batchSave(Collection<T> collection, int batchSize,
            Function<Collection<T>, Boolean> saveFunction) {
        if (CollectionUtils.isEmpty(collection)) {
            log.warn("Collection is empty, nothing to save");
            return;
        }

        List<T> subList = new ArrayList<>(batchSize);
        int count = 0;

        for (T item : collection) {
            subList.add(item);
            count++;

            if (count % batchSize == 0 || count == collection.size()) {
                try {
                    boolean success = saveFunction.apply(subList);
                    if (!success) {
                        throw new SynchronizationException("Batch save failed");
                    }
                    subList.clear();
                } catch (Exception e) {
                    log.error("Error occurred while saving batch", e);
                    throw new SynchronizationException("Batch save failed", e);
                }
            }
        }
    }
}
