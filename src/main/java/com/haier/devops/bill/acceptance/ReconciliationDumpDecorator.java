package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.Invoice;
import com.haier.devops.bill.common.service.InvoiceService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.haier.devops.bill.acceptance.AcceptanceStageEnum.ESTIMATION;

/**
 * 账单数据dump时保留了两位小数位，重新求和之后会与发票有差异，这里对差异进行处理
 * <AUTHOR>
 */
@Component
@Slf4j
public class ReconciliationDumpDecorator implements DumpDecorator<ApplicationAggregation> {
    @Setter
    private Dump<ApplicationAggregation> dump;

    private final InvoiceService invoiceService;

    public ReconciliationDumpDecorator(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }


    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == this.dump) {
            return null;
        }

        List<ApplicationAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }

        List<ApplicationAggregation> reconciledBills = new ArrayList<>();
        List<Invoice> invoices = invoiceService.getInvoiceList(vendor, billingCycle, account);

        // 将Invoice按vendor和account分组，并计算每组的总发票金额
        Map<String, BigDecimal> invoiceTotals = invoices.stream()
                .collect(Collectors.groupingBy(
                        invoice -> invoice.getVendor() + "-" + invoice.getAccountName(),
                        Collectors.mapping(
                                invoice -> new BigDecimal(invoice.getAvailableInvoiceAmount()),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        // 将DumpedBillByScode按vendor和account分组，并计算每组的总应用金额
        Map<String, List<ApplicationAggregation>> billGroups = aggregations.stream()
                .collect(Collectors.groupingBy(appBill -> appBill.getFactory() + "-" + appBill.getAccount()));

        for (Map.Entry<String, List<ApplicationAggregation>> entry : billGroups.entrySet()) {
            String key = entry.getKey();
            List<ApplicationAggregation> bills = entry.getValue();

            for (ApplicationAggregation bill : bills) {
                if (stage.equals(ESTIMATION)) {
                    bill.setEstimateAmount(bill.getEstimateAmount());
                } else {
                    bill.setActualAmount(bill.getActualAmount());
                }
            }

            // 对发票，处理差异
            BigDecimal appTotal = bills.stream()
                    .map(bill -> {
                        if (bill == null) {
                            log.warn("Null bill encountered in reconciliation dump");
                            return BigDecimal.ZERO;
                        }

                        BigDecimal amount = stage.equals(ESTIMATION) ? bill.getEstimateAmount() : bill.getActualAmount();
                        if (amount == null) {
                            log.warn("Null estimated amount encountered for bill ID: {}", bill.getId());
                            return BigDecimal.ZERO;
                        }

                        return amount;
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal invoiceTotal = invoiceTotals.getOrDefault(key, BigDecimal.ZERO);
            if (BigDecimal.ZERO.equals(invoiceTotal)) {
                throw new SynchronizationException("没有维护" + key + "的发票");
            }

            BigDecimal difference = invoiceTotal.subtract(appTotal);

            // 如果有差异，将差异加到最后一个AppBill的金额上
            if (difference.compareTo(BigDecimal.ZERO) != 0) {
                ApplicationAggregation lastBill = bills.get(bills.size() - 1);
                if (stage.equals(ESTIMATION)) {
                    lastBill.setEstimateAmount(lastBill.getEstimateAmount().add(difference));
                } else {
                    lastBill.setActualAmount(lastBill.getActualAmount().add(difference));
                }
            }

            reconciledBills.addAll(bills);
        }

        return reconciledBills;
    }
}
