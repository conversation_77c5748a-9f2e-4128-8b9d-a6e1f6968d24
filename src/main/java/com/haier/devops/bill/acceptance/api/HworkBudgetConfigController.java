package com.haier.devops.bill.acceptance.api;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.controller.ResponseEnum;
import com.haier.devops.bill.common.entity.BudgetAppRel;
import com.haier.devops.bill.common.service.BudgetAppRelService;
import com.haier.devops.bill.common.service.BudgetConfigService;
import com.haier.devops.bill.common.vo.BudgetConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/hcms/bill/acceptance/budget")
@Slf4j
public class HworkBudgetConfigController {
    private final BudgetConfigService budgetConfigService;
    private final BudgetAppRelService budgetAppRelService;

    public HworkBudgetConfigController(BudgetConfigService budgetConfigService, BudgetAppRelService budgetAppRelService) {
        this.budgetConfigService = budgetConfigService;
        this.budgetAppRelService = budgetAppRelService;
    }

    /**
     * 应用预算配置列表
     * @param type
     * @param subProductId
     * @param budgetCode
     * @param userCode
     * @param vendor
     * @param page
     * @param pageSize
     * @return
     */
    @GetMapping
    public ResponseEntityWrapper<PageInfo<BudgetConfigVo>> list(@RequestParam(required = false) Integer type,
                                      @RequestParam(required = false) String subProductId,
                                      @RequestParam(required = false) String budgetCode,
                                      @RequestParam(required = false) String userCode,
                                      @RequestParam(required = false) String vendor,
                                      @RequestParam(defaultValue = "1") int page,
                                      @RequestParam(defaultValue = "10") int pageSize) {
        PageInfo<BudgetConfigVo> budgetConfigVoPageInfo;
        try {
            budgetConfigVoPageInfo = budgetConfigService.queryList(subProductId, budgetCode, userCode, vendor, page, pageSize);
        } catch (Exception e) {
            log.error("查询预算配置列表失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询预算配置列表失败", null);
        }
        return new ResponseEntityWrapper<>(budgetConfigVoPageInfo);
    }

    /**
     * 查询关联账期
     * @param id
     * @return
     */
    @GetMapping("/getBillingCycle")
    public ResponseEntityWrapper<List<String>> listBillingCycle(@RequestParam("id") Integer id) {
        List<String> billingCycles;
        try {
            billingCycles = budgetAppRelService.getBillingCycles(id);
        } catch (Exception e) {
            log.error("查询已关联账期失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询已关联账期失败", null);
        }

        return new ResponseEntityWrapper<>(billingCycles);
    }


    /**
     * 应用预算配置
     * @param file Excel文件，必须包含应用S码、云厂商、预算编码和账期字段
     * @return 处理结果
     */
    @PostMapping("/importing")
    public ResponseEntityWrapper<String> importing(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "文件不能为空", null);
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.endsWith(".xlsx")) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "只支持.xlsx格式的文件", null);
        }

        try {
            // 使用自定义监听器解析Excel文件
            BudgetAppRelImportListener listener = new BudgetAppRelImportListener();
            EasyExcel.read(file.getInputStream(), listener).sheet().doRead();

            // 获取解析结果
            List<String> errorMessages = listener.getErrorMessages();
            if (!errorMessages.isEmpty()) {
                // 如果有错误，返回第一个错误信息
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, errorMessages.get(0), null);
            }

            List<BudgetAppRel> budgetAppRels = listener.getBudgetAppRels();
            if (budgetAppRels.isEmpty()) {
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "Excel文件中没有有效数据", null);
            }

            // 保存数据到数据库
            boolean saveResult = budgetAppRelService.saveBatch(budgetAppRels);
            if (!saveResult) {
                return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "保存数据失败", null);
            }

            return new ResponseEntityWrapper<>("成功导入 " + budgetAppRels.size() + " 条预算配置数据");
        } catch (IOException e) {
            log.error("解析Excel文件失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "解析Excel文件失败: " + e.getMessage(), null);
        } catch (Exception e) {
            log.error("导入预算配置失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "导入预算配置失败: " + e.getMessage(), null);
        }
    }

    /**
     * Excel解析监听器，用于解析预算配置模板
     */
    private static class BudgetAppRelImportListener extends AnalysisEventListener<Map<Integer, String>> {
        private static final DateTimeFormatter BILLING_CYCLE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
        private final List<BudgetAppRel> budgetAppRels = new ArrayList<>();
        private final List<String> errorMessages = new ArrayList<>();
        private final Map<String, Integer> headerMap = new HashMap<>();
        private int rowIndex = 0;

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            // 解析表头，建立列索引与字段名的映射
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                String headerName = entry.getValue();
                if (headerName != null) {
                    headerMap.put(headerName.trim(), entry.getKey());
                }
            }

            // 检查必要的列是否存在
            String[] requiredHeaders = {"S码", "云厂商", "预算编码", "账期"};
            for (String header : requiredHeaders) {
                if (!headerMap.containsKey(header)) {
                    errorMessages.add("Excel模板缺少必要的列: " + header);
                }
            }
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            rowIndex++;
            
            // 如果表头检查已经发现错误，不再继续解析
            if (!errorMessages.isEmpty()) {
                return;
            }

            try {
                // 获取各列的值
                String scode = getCellValue(data, "S码");
                String vendor = getCellValue(data, "云厂商");
                String budgetCode = getCellValue(data, "预算编码");
                String billingCycle = getCellValue(data, "账期");

                // 验证所有字段都不为空
                if (StringUtils.isBlank(scode)) {
                    errorMessages.add("第 " + rowIndex + " 行: S码不能为空");
                    return;
                }
                if (StringUtils.isBlank(vendor)) {
                    errorMessages.add("第 " + rowIndex + " 行: 云厂商不能为空");
                    return;
                }
                if (StringUtils.isBlank(budgetCode)) {
                    errorMessages.add("第 " + rowIndex + " 行: 预算编码不能为空");
                    return;
                }
                if (StringUtils.isBlank(billingCycle)) {
                    errorMessages.add("第 " + rowIndex + " 行: 账期不能为空");
                    return;
                }

                // 验证账期格式是否符合 yyyy-MM
                try {
                    BILLING_CYCLE_FORMATTER.parse(billingCycle);
                } catch (DateTimeParseException e) {
                    errorMessages.add("第 " + rowIndex + " 行: 账期格式不正确，应为 yyyy-MM 格式");
                    return;
                }

                // 创建BudgetAppRel对象并添加到列表
                BudgetAppRel budgetAppRel = new BudgetAppRel();
                budgetAppRel.setScode(scode);
                budgetAppRel.setVendor(vendor);
                budgetAppRel.setBudgetCode(budgetCode);
                budgetAppRel.setBillingCycle(billingCycle);
                budgetAppRel.setCreateTime(LocalDateTime.now());
                budgetAppRel.setUpdateTime(LocalDateTime.now());
                budgetAppRel.setEnabled(1); // 默认启用

                budgetAppRels.add(budgetAppRel);
            } catch (Exception e) {
                errorMessages.add("第 " + rowIndex + " 行数据解析失败: " + e.getMessage());
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 所有数据解析完成后的操作
            log.info("Excel解析完成，共解析 {} 条有效数据", budgetAppRels.size());
        }

        /**
         * 获取单元格的值
         */
        private String getCellValue(Map<Integer, String> data, String headerName) {
            Integer columnIndex = headerMap.get(headerName);
            if (columnIndex == null) {
                return null;
            }
            String value = data.get(columnIndex);
            return value == null ? null : value.trim();
        }

        public List<BudgetAppRel> getBudgetAppRels() {
            return budgetAppRels;
        }

        public List<String> getErrorMessages() {
            return errorMessages;
        }
    }
}
