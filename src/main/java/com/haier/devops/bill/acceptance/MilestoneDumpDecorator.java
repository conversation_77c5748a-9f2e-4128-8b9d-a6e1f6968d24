package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.service.MilestoneService;
import com.haier.devops.bill.common.vo.MilestoneVo;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 补充里程碑信息
 */
@Component
@Slf4j
public class MilestoneDumpDecorator implements DumpDecorator<ApplicationAggregation> {
    private final MilestoneService milestoneService;
    @Setter
    private Dump<ApplicationAggregation> dump;

    public MilestoneDumpDecorator(MilestoneService milestoneService) {
        this.milestoneService = milestoneService;
    }

    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == dump) {
            return null;
        }

        List<ApplicationAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }

        // 根据厂商和账期查询里程碑信息
        List<MilestoneVo> milestoneVos = milestoneService.queryByVendorAndBillingCycle(vendor, billingCycle);
        Map<String, MilestoneVo> milestoneMap = milestoneVos.stream()
                .collect(Collectors.toMap(MilestoneVo::getVendor, Function.identity()));

        LocalDateTime now = LocalDateTime.now();
        String nowStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        for (ApplicationAggregation aggregation : aggregations) {
            MilestoneVo milestoneVo = milestoneMap.get(aggregation.getFactory());
            if (null == milestoneVo) {
                String err = String.format("milestone not found for vendor: %s of billing cycle: %s",
                        aggregation.getFactory(), billingCycle);
                log.error(err);
                throw new SynchronizationException(err);
            }

            aggregation.setAlmMilestoneId(milestoneVo.getMilestoneId());
            aggregation.setMilestoneName(milestoneVo.getMilestoneName());
            aggregation.setAlmProjectCode(milestoneVo.getProjectCode());
            aggregation.setAlmProjectName(milestoneVo.getProjectName());
            aggregation.setContractCode(milestoneVo.getContractCode());
            aggregation.setContractName(milestoneVo.getContractName());
            aggregation.setSupplierCode(milestoneVo.getSupplierCode());
            aggregation.setSupplierName(milestoneVo.getSupplierName());
            aggregation.setGenerationTime(nowStr);
        }

        return aggregations;
    }
}
