package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.entity.ApplicationAggregation;
import lombok.Setter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * 给系统负责人、领域负责人塞上姓名
 * <AUTHOR>
 */
@Component
public class UserNameDumpDecorator implements DumpDecorator<ApplicationAggregation> {
    private final ExecutorService executor = Executors.newFixedThreadPool(10);
    private HworkAuthorityApi hworkAuthorityApi;

    @Setter
    private Dump<ApplicationAggregation> dump;

    public UserNameDumpDecorator(HworkAuthorityApi hworkAuthorityApi) {
        this.hworkAuthorityApi = hworkAuthorityApi;
    }

    private void fillUpUserName(List<ApplicationAggregation> aggregations) {
        Set<String> userIds = aggregations.stream()
                .map(ApplicationAggregation::getSystemConfirmer)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        aggregations.stream()
                .map(ApplicationAggregation::getDomainConfirmer)
                .filter(StringUtils::isNotBlank)
                .forEach(userIds::add);

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        List<CompletableFuture<HworkAuthorityApi.User>> futures = userIds.stream()
                .map(userId -> CompletableFuture.supplyAsync(() -> {
                    try {
                        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> userDetailWrapper = hworkAuthorityApi.getUserDetail(userId, false);
                        if (userDetailWrapper != null && userDetailWrapper.getCode() == 200) {
                            return userDetailWrapper.getData();
                        }
                    } catch (Exception e) {
                        // log error
                    }
                    return null;
                }, executor))
                .collect(Collectors.toList());

        Map<String, String> userIdToNameMap = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(HworkAuthorityApi.User::getUserCode, HworkAuthorityApi.User::getUserName, (v1, v2) -> v1));

        aggregations.forEach(agg -> {
            agg.setSystemConfirmerName(userIdToNameMap.get(agg.getSystemConfirmer()));
            agg.setDomainConfirmerName(userIdToNameMap.get(agg.getDomainConfirmer()));
        });
    }

    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == dump) {
            return null;
        }

        List<ApplicationAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }


        /**
         * 遍历给系统负责人(systemConfirmer)、领域负责人塞上姓名(domainConfirmer)
         * 思路：将所有的系统负责人、领域负责人去重，然后并发调用API获取姓名，
         * 最后再遍历一次，塞上姓名
         * 查询用户信息api: HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> userDetailWrapper = hworkAuthorityApi.getUserDetail(userId, false);
         * 姓名为HworkAuthorityApi.User.getUserName()
         */
        this.fillUpUserName(aggregations);

        return aggregations;
    }
}
