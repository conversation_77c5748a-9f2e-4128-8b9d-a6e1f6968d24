package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.service.ApplicationAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.haier.devops.bill.acceptance.AcceptanceStageEnum.ESTIMATION;

@Component
@Slf4j
public class ApplicationAggregationDump implements Dump<ApplicationAggregation> {
    private final ApplicationAggregationService aggregationService;

    public ApplicationAggregationDump(ApplicationAggregationService aggregationService) {
        this.aggregationService = aggregationService;
    }

    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        List<ApplicationAggregation> aggregations = aggregationService.queryBillGroupByScode(vendor, billingCycle, stage.getStage(), account);
        aggregations.stream()
                .filter(a -> StringUtils.isBlank(a.getSysCode()))
                .findFirst()
                .ifPresent(a -> {
                    log.error("sysCode is blank: {}", a);
                    throw new SynchronizationException("sysCode is blank");
                });

        Predicate<ApplicationAggregation> valuablePredicate =
                aggregation -> stage.equals(ESTIMATION) ?
                        aggregation.getEstimateAmount().compareTo(BigDecimal.ZERO) != 0 :
                        aggregation.getActualAmount().compareTo(BigDecimal.ZERO) != 0;

        aggregations = aggregations.stream()
                .filter(valuablePredicate)
                .collect(Collectors.toList());
        return aggregations;
    }
}
