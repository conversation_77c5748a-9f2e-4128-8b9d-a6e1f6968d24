package com.haier.devops.bill.acceptance.api;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.api.HworkAcceptanceApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.controller.ResponseEnum;
import com.haier.devops.bill.common.entity.*;
import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.redis.Cache;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.vo.HworkPendingSyncBillVo;
import com.haier.devops.bill.acceptance.OrderIdDumpDecorator;
import com.haier.devops.bill.util.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/hcms/bill/acceptance")
@Slf4j
@Tag(name = "账单验收", description = "生成、推送hwork账单验收相关接口")
public class HworkAcceptanceController {
    private static final String ACCOUNT_NAME = "<EMAIL>";
    private static final int HWORK_SYNCING_STATUS_SYNCED = 1;
    private static final int HWORK_SYNCING_STATUS_BLACKLISTED = 2;
    private final List<String> validFields = new ArrayList<>(Arrays.asList("budgetCode", "almMilestoneId", "userCode"));

    private final ApplicationAggregationService applicationAggregationService;
    private final ResourceAggregationService resourceAggregationService;
    private final Cache<String, HworkAuthorityApi.User> hcmsUserCache;
    private final HworkAuthorityApi hworkAuthorityApi;
    private final HworkAcceptanceApi hworkAcceptanceApi;
    private final BudgetAppRelService budgetAppRelService;
    private final BudgetConfigService budgetConfigService;
    private final MilestoneService milestoneService;
    private final HdsSubProductsService subProductsService;
    private final PlatformTransactionManager transactionManager;
    private final AcceptanceBlacklistService acceptanceBlacklistService;

    public HworkAcceptanceController(ApplicationAggregationService applicationAggregationService,
                                     ResourceAggregationService resourceAggregationService,
                                     @Qualifier("hcmsUserCache") Cache<String, HworkAuthorityApi.User> hcmsUserCache,
                                     HworkAuthorityApi hworkAuthorityApi,
                                     HworkAcceptanceApi hworkAcceptanceApi,
                                     BudgetAppRelService budgetAppRelService,
                                     BudgetConfigService budgetConfigService,
                                     MilestoneService milestoneService,
                                     HdsSubProductsService subProductsService,
                                     PlatformTransactionManager transactionManager,
                                     AcceptanceBlacklistService acceptanceBlacklistService) {
        this.applicationAggregationService = applicationAggregationService;
        this.resourceAggregationService = resourceAggregationService;
        this.hcmsUserCache = hcmsUserCache;
        this.hworkAuthorityApi = hworkAuthorityApi;
        this.hworkAcceptanceApi = hworkAcceptanceApi;
        this.budgetAppRelService = budgetAppRelService;
        this.budgetConfigService = budgetConfigService;
        this.milestoneService = milestoneService;
        this.subProductsService = subProductsService;
        this.transactionManager = transactionManager;
        this.acceptanceBlacklistService = acceptanceBlacklistService;

    }

    @GetMapping("/order")
    public ResponseEntityWrapper<AggregationWrapper> get(@RequestParam String orderId) {
        ApplicationAggregation applicationAggregation = applicationAggregationService.getBaseMapper()
                .selectOne(new LambdaQueryWrapper<ApplicationAggregation>()
                        .eq(ApplicationAggregation::getOrderId, orderId));
        if (null == applicationAggregation) {
            log.error("没有找到该账单：{}", orderId);
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "没有找到该账单", null);
        }
        List<ResourceAggregation> resourceAggregations = resourceAggregationService.selectByApplication(applicationAggregation);
        if (CollectionUtils.isEmpty(resourceAggregations)) {
            log.error("没有找到该账单的明细：orderId = {}, vendor = {}, account = {}, billingCycle = {}, scode = {}",
                    orderId,
                    applicationAggregation.getFactory(),
                    applicationAggregation.getAccount(),
                    applicationAggregation.getPayDate(),
                    applicationAggregation.getSysCode());
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "没有找到该账单的明细", null);
        }

        AggregationWrapper aggregationWrapper = new AggregationWrapper();
        BeanUtils.copyProperties(applicationAggregation, aggregationWrapper);
        aggregationWrapper.setOrderDetailList(resourceAggregations);

        return new ResponseEntityWrapper<>(aggregationWrapper);
    }

    /**
     * 根据里程碑查询验收单
     * @param milestoneId
     * @return
     */
    @GetMapping("/getOrdersByMilestoneId")
    @Operation(summary = "查询已推送验收单", description = "根据里程碑id查询已经推送过的验收单信息")
    public ResponseEntityWrapper<List<ApplicationAggregation>> getByMilestoneId(@Valid @NotNull(message = "里程碑ID不能为空") @RequestParam @Parameter(description = "里程碑id") String milestoneId) {
        List<ApplicationAggregation> aggregations = null;
        try {
            aggregations = applicationAggregationService.getBaseMapper().selectList(new LambdaQueryWrapper<ApplicationAggregation>()
                    .eq(ApplicationAggregation::getAlmMilestoneId, milestoneId)
                    .eq(ApplicationAggregation::getStatus, HWORK_SYNCING_STATUS_SYNCED));
        } catch (Exception e) {
            log.error("根据里程碑查询验收单失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "根据里程碑查询验收单失败", new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(aggregations)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "没有找到该里程碑的账单", new ArrayList<>());
        }
        return new ResponseEntityWrapper<>(aggregations);
    }

    /**
     * 驳回验收单
     * @param orderIds
     * @return
     */
    @PostMapping("/orders/reject")
    public ResponseEntityWrapper<Boolean> rejectOrders(@RequestBody OrderIdWrapper orderIds) {
        if (null == orderIds || CollectionUtils.isEmpty(orderIds.getOrderIds())) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "需选择要驳回的订单", false);
        }

        int cnt;
        try {
            cnt = applicationAggregationService.batchRejectByOrderId(orderIds.getOrderIds());
        } catch (Exception e) {
            log.error("驳回失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "驳回失败", false);
        }

        return new ResponseEntityWrapper<>(ResponseEnum.SUCCESS, cnt + "条验收单被驳回", true);
    }

    @Data
    public static class OrderIdWrapper {
        private List<String> orderIds;
    }

    @PostMapping("/orders")
    public ResponseEntityWrapper<PageInfo<HworkPendingSyncBillVo>> getOrders(@Valid @RequestBody OrderRequest request) {
        PageInfo<HworkPendingSyncBillVo> pageInfo;
        try {
            pageInfo = applicationAggregationService.selectPendingSyncBill(request.getBillVo(),
                    request.getPage(), request.getPer_page(), request.getStartDate(), request.getEndDate());
        } catch (Exception e) {
            log.error("查询待推送账单列表失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询待推送账单列表失败", null);
        }
        return new ResponseEntityWrapper<>(pageInfo);
    }

    /**
     * 检查预算
     * @param request
     * @return
     */
    @PostMapping("/check-budget")
    public ResponseEntityWrapper<BudgetCheckResult> checkBudget(@RequestBody AggregationIdWrapper request) {
        if (CollectionUtils.isEmpty(request.getIds())) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "需选择要检查预算的订单", null);
        }

        try {
            // 1. 获取所有订单信息
            List<ApplicationAggregation> aggregations = applicationAggregationService.listByIds(request.getIds());
            if (CollectionUtils.isEmpty(aggregations)) {
                return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "未找到相关订单", null);
            }

            // 2. 获取所有不重复的预算编码
            Set<String> budgetCodes = aggregations.stream()
                    .filter(agg -> StringUtils.isNotBlank(agg.getBudgetCode()))
                    .map(ApplicationAggregation::getBudgetCode)
                    .collect(Collectors.toSet());

            if (budgetCodes.isEmpty()) {
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "所选订单没有关联预算编码", null);
            }

            // 3. 获取当前年份
            String currentYear = String.valueOf(java.time.Year.now().getValue());

            // 4. 查询当年所有使用这些预算编码的订单总金额
            List<Map<String, Object>> yearlyTotals = applicationAggregationService.getYearlyTotalByBudgetCodes(
                    new ArrayList<>(budgetCodes),
                    currentYear
            );

            Map<String, BigDecimal> budgetYearlyTotals = new HashMap<>();
            for (Map<String, Object> total : yearlyTotals) {
                String budgetCode = (String) total.get("budget_code");
                BigDecimal amount = (BigDecimal) total.get("total_amount");
                budgetYearlyTotals.put(budgetCode, amount);
            }

            // 5. 查询这些预算编码的可用预算
            List<BudgetConfig> budgetConfigs = budgetConfigService.list(
                    new LambdaQueryWrapper<BudgetConfig>()
                            .in(BudgetConfig::getBudgetCode, budgetCodes)
                            .eq(BudgetConfig::getYear, currentYear)
            );

            Map<String, BigDecimal> availableBudgets = budgetConfigs.stream()
                    .collect(Collectors.toMap(
                            BudgetConfig::getBudgetCode,
                            config -> new BigDecimal(config.getAvailableAmount()),
                            (a, b) -> a
                    ));

            // 6. 比较预算和已使用金额，生成结果
            List<BudgetCheckItem> checkItems = new ArrayList<>();
            boolean allPassed = true;

            for (String budgetCode : budgetCodes) {
                BigDecimal availableAmount = availableBudgets.getOrDefault(budgetCode, BigDecimal.ZERO);
                BigDecimal yearlyTotal = budgetYearlyTotals.getOrDefault(budgetCode, BigDecimal.ZERO);

                BudgetCheckItem item = new BudgetCheckItem();
                item.setBudgetCode(budgetCode);
                item.setAvailableAmount(availableAmount);
                item.setTotal(yearlyTotal);

                // 如果年度总额超过可用预算，则检查不通过
                if (yearlyTotal.compareTo(availableAmount) > 0) {
                    allPassed = false;
                    checkItems.add(item);
                }

            }

            BudgetCheckResult result = new BudgetCheckResult();
            result.setPassed(allPassed);
            result.setItems(checkItems);

            return new ResponseEntityWrapper<>(result);

        } catch (Exception e) {
            log.error("检查预算失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "检查预算失败: " + e.getMessage(), null);
        }
    }

    /**
     * 账单编辑
     * @param request
     * @return
     */
    @PutMapping("/order")
    public ResponseEntityWrapper<Boolean> update(@Valid @RequestBody UpdateOrderRequest request) {
        if (request.getId() == null) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "ID不能为空", null);
        }

        String userName = null;
        if (StringUtils.isNotBlank(request.getUserCode())) {
            HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> userDetail = hworkAuthorityApi.getUserDetail(request.getUserCode(), false);
            if (userDetail != null && userDetail.getData() != null) {
                userName = userDetail.getData().getUserName();
            }
        }

        // 开启事务
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 查询原记录
            ApplicationAggregation existingApp = applicationAggregationService.getById(request.getId());
            if (existingApp == null) {
                return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "未找到要更新的记录", null);
            }

            // 更新记录
            ApplicationAggregation application = new ApplicationAggregation();
            BeanUtils.copyProperties(request, application);
            application.setAlmMilestoneId(request.getMilestoneId());
            application.setId(request.getId());

//            BudgetConfig budgetConfig = budgetConfigService.getBaseMapper()
//                    .selectOne(new LambdaQueryWrapper<BudgetConfig>()
//                            .eq(BudgetConfig::getBudgetCode, application.getBudgetCode())
//                            .eq(BudgetConfig::getYear, DateUtil.getCurrentYear())
//                            .last("limit 1"));
            Milestone milestone = milestoneService.getBaseMapper()
                    .selectOne(new LambdaQueryWrapper<Milestone>()
                            .eq(Milestone::getMilestoneId, application.getAlmMilestoneId())
                            .last("limit 1"));
            application.setMilestoneName(milestone.getMilestoneName());
            application.setUserName(userName);


            // 如果修改了系统编码或支付日期，需要重新生成订单号
            if (!existingApp.getSysCode().equals(request.getSysCode()) ||
                !existingApp.getPayDate().equals(request.getPayDate())) {
                application.setOrderId(generateOrderId(application));
            } else {
                application.setOrderId(existingApp.getOrderId());
            }

            // 如果是云资源类型(type=1)且预算编码发生变化，更新预算应用关系表
            if (existingApp.getType() != null && existingApp.getType() == 1 &&
                StringUtils.isNotBlank(application.getBudgetCode()) &&
                !application.getBudgetCode().equals(existingApp.getBudgetCode())) {
                // 提取账期，格式为yyyy-MM
                String billingCycle = existingApp.getPayDate().substring(0, 7);
                // 检查并添加预算应用关系
                budgetAppRelService.checkAndAddBudgetAppRel(
                    existingApp.getSysCode(),
                    existingApp.getFactory(),
                    application.getBudgetCode(),
                    billingCycle
                );
            }

            applicationAggregationService.updateById(application);

            // 提交事务
            transactionManager.commit(status);
            return new ResponseEntityWrapper<>(true);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("更新失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "更新失败：" + e.getMessage(), false);
        }
    }

    /**
     * 账单编辑并推送
     * @param request
     * @return
     */
    @PostMapping("/updateAndPush")
    public ResponseEntityWrapper<Boolean> updateAndPush(@Valid @RequestBody UpdateOrderRequest request) {
        ResponseEntityWrapper<Boolean> response = update(request);
        if (response.getCode() != ResponseEnum.SUCCESS.getCode()) {
            return response;
        }

        return batchPush(new BatchPushWrapper(Collections.singletonList(request.getId())));
    }

    /**
     * 根据里程碑推送
     * @param mileStoneId
     * @return
     */
    @PostMapping("/push")
    public ResponseEntityWrapper<Boolean> pushByMileStone(@RequestParam String mileStoneId) {
        List<ApplicationAggregation> aggregations = applicationAggregationService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<ApplicationAggregation>()
                        .eq(ApplicationAggregation::getAlmMilestoneId, mileStoneId));
        if (CollectionUtils.isEmpty(aggregations) || null == aggregations.get(0)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "该里程碑没有要推送的账单", false);
        }

        return batchPush(aggregations);
    }

    /**
     * 批量推送（勾选要推送的id）
     * @param request
     * @return
     */
    @PostMapping("/batchPush")
    public ResponseEntityWrapper<Boolean> batchPush(@Valid @RequestBody BatchPushWrapper request) {
        List<ApplicationAggregation> aggregations = applicationAggregationService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<ApplicationAggregation>()
                        .in(ApplicationAggregation::getId, request.getIds()));
        if (CollectionUtils.isEmpty(aggregations)) {
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "没有找到要推送的账单", false);
        }

        return batchPush(aggregations);
    }

    @org.jetbrains.annotations.NotNull
    private ResponseEntityWrapper<Boolean> batchPush(List<ApplicationAggregation> aggregations) {
        // 过滤黑名单中的应用
        List<ApplicationAggregation> filteredAggregations = new ArrayList<>();
        List<ApplicationAggregation> blacklistedAggregations = new ArrayList<>();

        for (ApplicationAggregation app : aggregations) {
            // 检查是否在黑名单中
            if (Boolean.TRUE.equals(acceptanceBlacklistService.isInBlacklist(app.getFactory(), app.getSysCode()))) {
                blacklistedAggregations.add(app);
                log.info("应用被过滤（在黑名单中）: 云厂商={}, S码={}", app.getFactory(), app.getSysCode());
            } else {
                filteredAggregations.add(app);
            }
        }

        // 如果所有应用都在黑名单中，返回提示信息
        if (filteredAggregations.isEmpty()) {
            // 如果有黑名单项，更新它们的状态为已生成但不推送
            if (!blacklistedAggregations.isEmpty()) {
                applicationAggregationService.batchUpdateStatus(blacklistedAggregations, HWORK_SYNCING_STATUS_BLACKLISTED);
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR, "所有账单都在黑名单中，已标记为不推送", true);
            }
            return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "没有找到要推送的账单", false);
        }

        // 计算 EstimateAmount 总和
        BigDecimal totalActualAmount = filteredAggregations.stream()
                .map(app -> Optional.ofNullable(app.getActualAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalEstimateAmount = filteredAggregations.stream()
                .map(app -> Optional.ofNullable(app.getEstimateAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 如果有黑名单项，更新它们的状态为已生成但不推送
        if (!blacklistedAggregations.isEmpty()) {
            applicationAggregationService.batchUpdateStatus(blacklistedAggregations, HWORK_SYNCING_STATUS_BLACKLISTED);
        }

        HworkAcceptanceApi.ApplicationAggregationWrapper body =
                new HworkAcceptanceApi.ApplicationAggregationWrapper(filteredAggregations.size(), totalEstimateAmount, totalActualAmount, filteredAggregations);
        ResultWrapper<Boolean> applicationResultWrapper = hworkAcceptanceApi.syncApplicationAggregation(body);

        if (Boolean.TRUE.equals(applicationResultWrapper.getData())) {
            List<ResourceAggregation> resourceAggregations = resourceAggregationService.selectByApplications(filteredAggregations);
            // 没有明细数据直接标记推送成功（只有云桌面的账单有明细）
            if (CollectionUtils.isEmpty(resourceAggregations)) {
                applicationAggregationService.batchUpdateStatus(filteredAggregations, HWORK_SYNCING_STATUS_SYNCED);
                return new ResponseEntityWrapper<>(true);
            }

            HworkAcceptanceApi.ResourceAggregationResultWrapper resourceResultWrapper = batchPushResource(resourceAggregations);
            if (Boolean.TRUE.equals(resourceResultWrapper.getData())) {
                // 汇总和明细都成功了之后更新汇总条目推送状态 batch update aggregations to make status = 1
                applicationAggregationService.batchUpdateStatus(filteredAggregations, HWORK_SYNCING_STATUS_SYNCED);
                return new ResponseEntityWrapper<>(true);
            } else {
                log.error("推送明细失败：{}", resourceResultWrapper.getMsg());
            }
        } else {
            log.error("推送汇总失败：{}", applicationResultWrapper.getMsg());
        }

        return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "推送失败", false);
    }

    private HworkAcceptanceApi.ResourceAggregationResultWrapper batchPushResource(List<ResourceAggregation> aggregations) {
        BigDecimal detailTotalAmount = aggregations.stream()
                .map(res -> Optional.ofNullable(res.getCostAmount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        HworkAcceptanceApi.ResourceAggregationWrapper body = new HworkAcceptanceApi.ResourceAggregationWrapper(aggregations.size(), detailTotalAmount, aggregations);
        return hworkAcceptanceApi.syncResourceAggregation(body);
    }

    /**
     * 里程碑下拉框
     * @param milestoneName
     * @return
     */
    @GetMapping("/milestones")
    public ResponseEntityWrapper<List<Milestone>> milestones(String milestoneName) {
        LambdaQueryWrapper<Milestone> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(milestoneName)) {
            queryWrapper.like(Milestone::getMilestoneName, milestoneName);
        }
        List<Milestone> milestones = new ArrayList<>();
        try {
            milestones = milestoneService.getBaseMapper().selectList(queryWrapper);
        } catch (Exception e) {
            log.error("查询里程碑失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询里程碑失败", null);
        }
        return new ResponseEntityWrapper<>(milestones);
    }

    /**
     * 预算下拉框
     * @param budgetName
     * @return
     */
    @GetMapping("/budgets")
    public ResponseEntityWrapper<List<BudgetConfig>> budgets(String budgetName) {
        List<BudgetConfig> budgetConfigs = new ArrayList<>();
        try {

            LambdaQueryWrapper<BudgetConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BudgetConfig::getYear, DateUtil.getCurrentYear());
            if (StringUtils.isNotBlank(budgetName)) {
                queryWrapper.like(BudgetConfig::getBudgetName, budgetName);
            }
            budgetConfigs = budgetConfigService.getBaseMapper().selectList(queryWrapper);
        } catch (Exception e) {
            log.error("查询预算编码失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询预算编码失败", null);
        }
        return new ResponseEntityWrapper<>(budgetConfigs);
    }

    /**
     * 子产品下拉框
     * @param subProductName
     * @return
     */
    @GetMapping("/subProducts")
    public ResponseEntityWrapper<List<HdsSubProducts>> subProducts(String subProductName) {
        List<HdsSubProducts> subProducts = new ArrayList<>();
        LambdaQueryWrapper<HdsSubProducts> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(subProductName)) {
            queryWrapper.like(HdsSubProducts::getSubProductName, subProductName);
        }

        try {
            subProducts = subProductsService.getBaseMapper().selectList(queryWrapper);
        } catch (Exception e) {
            log.error("查询子产品失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "查询子产品失败", null);
        }

        return new ResponseEntityWrapper<>(subProducts);
    }

    /**
     * 领域下拉框
     * @param query
     * @return
     */
    @GetMapping("/domains")
    public ResponseEntityWrapper domains(String query) {
        List<HdsSubProducts> subProducts = new ArrayList<>();
        try {
            subProducts = subProductsService.searchDomain(query);
        } catch (Exception e) {
            log.error("查询领域失败", e);
            return new ResponseEntityWrapper(ResponseEnum.INTERNAL_ERR, "查询领域失败", null);
        }
        return new ResponseEntityWrapper(subProducts);
    }

    /**
     * 云桌面导入
     * @param file
     * @return
     */
    @PostMapping("/importing")
    public ResponseEntityWrapper<String> importing(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "请选择要上传的文件", null);
        }

        // 开启事务
        TransactionStatus status = null;
        try {
            // 读取汇总sheet
            ApplicationAggregationListener applicationListener = new ApplicationAggregationListener();
            // 读取明细sheet
            ResourceAggregationListener resourceListener = new ResourceAggregationListener();

            InputStream inputStream = file.getInputStream();
            EasyExcel.read(inputStream)
                    .sheet(0)
                    .head(ApplicationAggregation.class)
                    .registerReadListener(applicationListener)
                    .doRead();

            inputStream = file.getInputStream();
            EasyExcel.read(inputStream)
                    .sheet(1)
                    .head(ResourceAggregation.class)
                    .registerReadListener(resourceListener)
                    .doRead();

            // 获取解析的数据
            List<ApplicationAggregation> applications = applicationListener.getData();
            List<ResourceAggregation> resources = resourceListener.getData();

            // 过滤空行
            applications = applications.stream()
                    .filter(app -> null != app && StringUtils.isNotBlank(app.getSysCode()) && StringUtils.isNotBlank(app.getPayDate()))
                    .collect(Collectors.toList());

            // 获取所有里程碑ID
            List<String> milestoneIds = applications.stream()
                    .filter(app -> StringUtils.isNotBlank(app.getAlmMilestoneId()))
                    .map(ApplicationAggregation::getAlmMilestoneId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询里程碑信息
            if (!milestoneIds.isEmpty()) {
                List<Milestone> milestones = milestoneService.list(new LambdaQueryWrapper<Milestone>()
                        .in(Milestone::getMilestoneId, milestoneIds));

                // 创建里程碑ID到里程碑对象的映射
                Map<String, Milestone> milestoneMap = milestones.stream()
                        .collect(Collectors.toMap(Milestone::getMilestoneId, milestone -> milestone, (a, b) -> a));

                // 为每个应用填充里程碑信息
                for (ApplicationAggregation app : applications) {
                    if (StringUtils.isNotBlank(app.getAlmMilestoneId())) {
                        Milestone milestone = milestoneMap.get(app.getAlmMilestoneId());
                        if (milestone != null) {
                            // 填充里程碑相关字段
                            app.setMilestoneName(milestone.getMilestoneName());
                            app.setAlmProjectCode(milestone.getProjectCode());
                            app.setAlmProjectName(milestone.getProjectName());
                            app.setContractCode(milestone.getContractCode());
                            app.setContractName(milestone.getContractName());
                            app.setSupplierCode(milestone.getSupplierCode());
                            app.setSupplierName(milestone.getSupplierName());
                        }
                    }
                }
            }

            for (ApplicationAggregation application : applications) {
                application.setOrderId(generateOrderId(application));

                // 设置用户名
                String userName = getUserName(application.getUserCode());
                if (userName != null) {
                    application.setUserName(userName);
                }
            }

            for (ResourceAggregation resource : resources) {
                resource.setBillType(2);
            }

            resources = resources.stream()
                    .filter(res -> null != res && StringUtils.isNotBlank(res.getPayDate()))
                    .collect(Collectors.toList());

            if (applications.isEmpty() && resources.isEmpty()) {
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "Excel文件中没有有效数据", null);
            }

            status = transactionManager.getTransaction(new DefaultTransactionDefinition());
            // 批量保存汇总表数据
            if (!applications.isEmpty()) {
                for (ApplicationAggregation application : applications) {
                    // 根据orderId查询是否存在
                    ApplicationAggregation existingApp = applicationAggregationService.getOne(
                            new LambdaQueryWrapper<ApplicationAggregation>()
                                    .eq(ApplicationAggregation::getOrderId, application.getOrderId())
                    );
                    if (existingApp != null) {
                        // 存在则更新ID
                        application.setId(existingApp.getId());
                    }
                    applicationAggregationService.saveOrUpdate(application);
                }
            }

            // 批量保存明细表数据
            if (!resources.isEmpty()) {
                for (ResourceAggregation resource : resources) {
                    // 根据payDate和sysCode组合查询是否存在
                    ResourceAggregation existingResource = resourceAggregationService.getOne(
                            new LambdaQueryWrapper<ResourceAggregation>()
                                    .eq(ResourceAggregation::getPayDate, resource.getPayDate())
                                    .eq(ResourceAggregation::getSysCode, resource.getSysCode())
                                    .eq(ResourceAggregation::getServiceItem, resource.getServiceItem())
                    );
                    if (existingResource != null) {
                        // 存在则更新ID
                        resource.setId(existingResource.getId());
                    }
                    resourceAggregationService.saveOrUpdate(resource);
                }
            }

            // 提交事务
            transactionManager.commit(status);
            return new ResponseEntityWrapper<>("导入成功");
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("导入Excel失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "导入失败：" + e.getMessage(), null);
        }
    }

    private String getUserName(String userCode) {
        // 先从缓存中获取
        HworkAuthorityApi.User user = hcmsUserCache.get(userCode);
        if (user != null) {
            return user.getUserName();
        }

        // 缓存中没有，调用API获取
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> result = hworkAuthorityApi.getUserDetail(userCode, false);
        if (result != null && result.getData() != null) {
            // 将用户信息存入缓存，设置24小时过期
            hcmsUserCache.put(userCode, result.getData(), 24, TimeUnit.HOURS);
            return result.getData().getUserName();
        }

        return null;
    }

    private String generateOrderId(ApplicationAggregation aggregation) {
        StringBuilder sb = new StringBuilder(aggregation.getSysCode());
        sb.append(aggregation.getPayDate().replace("-", ""));
        sb.insert(0, OrderIdDumpDecorator.hashAccountTo8Chars(ACCOUNT_NAME));
        sb.insert(0, VendorEnum.CLOUD_DESKTOP.getAbbreviation());

        return sb.toString();
    }

    @Data
    private static class ApplicationAggregationListener extends AnalysisEventListener<ApplicationAggregation> {
        private final List<ApplicationAggregation> data = new ArrayList<>();

        @Override
        public void invoke(ApplicationAggregation applicationAggregation, AnalysisContext analysisContext) {
            data.add(applicationAggregation);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        }

        public List<ApplicationAggregation> getData() {
            return data;
        }
    }

    @Data
    private static class ResourceAggregationListener extends AnalysisEventListener<ResourceAggregation> {
        private final List<ResourceAggregation> data = new ArrayList<>();

        @Override
        public void invoke(ResourceAggregation resourceAggregation, AnalysisContext analysisContext) {
            data.add(resourceAggregation);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        }

        public List<ResourceAggregation> getData() {
            return data;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AggregationIdWrapper {
        private List<Integer> ids;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BatchPushWrapper {
        @NotEmpty(message = "需选择要推送的订单")
        private List<Integer> ids;
    }

    @Data
    public static class UpdateOrderRequest {
        @NotNull(message = "ID不能为空")
        private Integer id;

        @NotBlank(message = "系统编码不能为空")
        private String sysCode;

        @NotBlank(message = "支付日期不能为空")
        private String payDate;

        @NotBlank(message = "预算编码不能为空")
        private String budgetCode;

        @NotNull(message = "里程碑不能为空")
        private String milestoneId;

        @NotNull(message = "验收负责人不能为空")
        private String userCode;

        private Boolean push = false;
    }

    @Data
    public static class OrderRequest {
        HworkPendingSyncBillVo billVo;

        /**
         * 开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM")
        String startDate;

        /**
         * 结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM")
        String endDate;

        @Min(1)
        Integer page = 1;
        @Min(1)
        Integer per_page = 10;

    }

    @Data
    public static class BatchEditRequest {
        @NotEmpty(message = "需选择要编辑的订单")
        private List<Integer> ids;

        private String budgetCode;

        private String almMilestoneId;

        private String userCode;

        @NotEmpty(message = "需选择要更新的字段")
        private List<String> updateFields;
    }

    /**
     * 批量编辑ApplicationAggregation
     * 动态选择更新字段：budgetCode, almMilestoneId, userCode
     * @param request
     * @return
     */
    @PostMapping("/batchEdit")
    public ResponseEntityWrapper<Boolean> batchEdit(@Valid @RequestBody BatchEditRequest request) {
        if (CollectionUtils.isEmpty(request.getIds())) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "需选择要编辑的订单", false);
        }

        if (CollectionUtils.isEmpty(request.getUpdateFields())) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "需选择要更新的字段", false);
        }

        // 验证更新字段是否合法
        for (String field : request.getUpdateFields()) {
            if (!validFields.contains(field)) {
                return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_PARAM_ERR, "不支持更新字段: " + field, false);
            }
        }

        String userName = null;
        // 如果需要更新userCode，获取对应的userName
        if (request.getUpdateFields().contains("userCode") && StringUtils.isNotBlank(request.getUserCode())) {
            HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.User> userDetail = hworkAuthorityApi.getUserDetail(request.getUserCode(), false);
            if (userDetail != null && userDetail.getData() != null) {
                userName = userDetail.getData().getUserName();
            }
        }

        // 开启事务
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            // 使用UpdateWrapper动态设置要更新的字段
            UpdateWrapper<ApplicationAggregation> updateWrapper =
                new UpdateWrapper<>();

            // 设置更新条件：id在指定列表中
            updateWrapper.in("id", request.getIds());

            // 如果需要更新预算编码，需要同时更新预算应用关系表
            boolean updateBudgetCode = request.getUpdateFields().contains("budgetCode") &&
                                      StringUtils.isNotBlank(request.getBudgetCode());

            if (updateBudgetCode) {
                updateWrapper.set("budget_code", request.getBudgetCode());

                // 查询所有需要更新的记录
                List<ApplicationAggregation> applications = applicationAggregationService.list(
                    new LambdaQueryWrapper<ApplicationAggregation>()
                        .in(ApplicationAggregation::getId, request.getIds())
                );

                // 为每个云资源类型的应用更新预算应用关系
                for (ApplicationAggregation app : applications) {
                    // 只有当类型为云资源(type=1)且预算编码发生变化时才更新关系
                    if (app.getType() != null && app.getType() == 1 &&
                        !request.getBudgetCode().equals(app.getBudgetCode())) {
                        // 提取账期，格式为yyyy-MM
                        String billingCycle = app.getPayDate().substring(0, 7);
                        // 检查并添加预算应用关系
                        budgetAppRelService.checkAndAddBudgetAppRel(
                            app.getSysCode(),
                            app.getFactory(),
                            request.getBudgetCode(),
                            billingCycle
                        );
                    }
                }
            }

            if (request.getUpdateFields().contains("almMilestoneId") && StringUtils.isNotBlank(request.getAlmMilestoneId())) {
                // 获取里程碑名称
                Milestone milestone = milestoneService.getBaseMapper()
                        .selectOne(new LambdaQueryWrapper<Milestone>()
                                .eq(Milestone::getMilestoneId, request.getAlmMilestoneId())
                                .last("limit 1"));
                if (milestone != null) {
                    updateWrapper.set("alm_milestone_id", request.getAlmMilestoneId());
                    updateWrapper.set("milestone_name", milestone.getMilestoneName());
                } else {
                    return new ResponseEntityWrapper<>(ResponseEnum.RESOURCE_NOT_FOUND, "未找到对应的里程碑信息", false);
                }
            }

            if (request.getUpdateFields().contains("userCode") && StringUtils.isNotBlank(request.getUserCode())) {
                updateWrapper.set("user_code", request.getUserCode());
                if (userName != null) {
                    updateWrapper.set("user_name", userName);
                }
            }

            // 执行批量更新
            boolean success = applicationAggregationService.update(updateWrapper);

            // 提交事务
            transactionManager.commit(status);
            return new ResponseEntityWrapper<>(success);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            log.error("批量更新失败", e);
            return new ResponseEntityWrapper<>(ResponseEnum.INTERNAL_ERR, "批量更新失败：" + e.getMessage(), false);
        }
    }

    @Data
    public static class BudgetCheckResult {
        private boolean passed;
        private List<BudgetCheckItem> items;
    }

    @Data
    public static class BudgetCheckItem {
        private String budgetCode;
        private BigDecimal availableAmount;
        private BigDecimal total;
    }
}
