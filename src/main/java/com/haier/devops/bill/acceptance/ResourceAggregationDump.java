package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ResourceAggregation;
import com.haier.devops.bill.common.service.ResourceAggregationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 月度账单明细汇总（到资源）
 * <AUTHOR>
 */
@Component
@Slf4j
public class ResourceAggregationDump implements Dump<ResourceAggregation> {
    private final ResourceAggregationService resourceAggregationService;

    public ResourceAggregationDump(ResourceAggregationService resourceAggregationService) {
        this.resourceAggregationService = resourceAggregationService;
    }

    @Override
    public List<ResourceAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        return resourceAggregationService.queryBillByResource(vendor, billingCycle, account);
    }
}
