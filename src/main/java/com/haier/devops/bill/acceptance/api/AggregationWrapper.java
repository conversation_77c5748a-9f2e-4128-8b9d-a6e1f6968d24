package com.haier.devops.bill.acceptance.api;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.entity.ResourceAggregation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AggregationWrapper extends ApplicationAggregation {
    List<ResourceAggregation> orderDetailList;
}
