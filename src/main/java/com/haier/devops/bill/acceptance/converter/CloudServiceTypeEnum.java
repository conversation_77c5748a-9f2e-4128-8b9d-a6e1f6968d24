package com.haier.devops.bill.acceptance.converter;

public enum CloudServiceTypeEnum {
    CLOUD_RESOURCE(1, "云资源"),
    CLOUD_DESKTOP(2, "云桌面");

    private final int value;
    private final String description;

    CloudServiceTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static CloudServiceTypeEnum fromDescription(String description) {
        for (CloudServiceTypeEnum type : values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unsupported cloud service type: " + description);
    }

    public static CloudServiceTypeEnum fromValue(int value) {
        for (CloudServiceTypeEnum type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unsupported type value: " + value);
    }
}

