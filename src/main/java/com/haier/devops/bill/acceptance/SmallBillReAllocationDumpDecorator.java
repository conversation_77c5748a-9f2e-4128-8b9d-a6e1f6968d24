package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ResourceAggregation;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SmallBillReAllocationDumpDecorator implements DumpDecorator<ResourceAggregation> {
    
    private static final String SMALL_BILL_SYS_CODE = "S04076";
    private static final BigDecimal THRESHOLD = new BigDecimal(50);
    
    @Setter
    private Dump<ResourceAggregation> dump;

    @Override
    public List<ResourceAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == dump) {
            return null;
        }
        
        // Get the original dump results
        List<ResourceAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }
        
        // Group by factory, account, and sysCode and calculate sum of costAmount for each group
        Map<String, BigDecimal> groupSums = aggregations.stream()
                .collect(Collectors.groupingBy(
                    agg -> getGroupKey(agg.getFactory(), agg.getAccount(), agg.getSysCode()),
                    Collectors.mapping(
                        ResourceAggregation::getCostAmount,
                        Collectors.reducing(BigDecimal.ZERO, 
                            amount -> amount != null ? amount : BigDecimal.ZERO, 
                            BigDecimal::add)
                    )
                ));
        
        // Find group keys with sum less than threshold
        Set<String> smallBillGroups = groupSums.entrySet().stream()
                .filter(entry -> entry.getValue().compareTo(THRESHOLD) < 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        
        if (!smallBillGroups.isEmpty()) {
            log.debug("Found {} small bill groups with sum less than {}", smallBillGroups.size(), THRESHOLD);
        }
        
        // Process the aggregations, modifying sysCode for small bill groups
        aggregations.forEach(aggregation -> {
            String groupKey = getGroupKey(aggregation.getFactory(), aggregation.getAccount(), aggregation.getSysCode());
            if (smallBillGroups.contains(groupKey)) {
                // Log the change
                log.debug("Small bill reallocation: Changed sysCode from {} to {} for factory={}, account={}, costAmount={}",
                        aggregation.getSysCode(), SMALL_BILL_SYS_CODE, 
                        aggregation.getFactory(), aggregation.getAccount(), aggregation.getCostAmount());
                
                // Directly modify the original object
                aggregation.setSysCode(SMALL_BILL_SYS_CODE);
            }
        });
        
        return aggregations;
    }
    
    /**
     * Creates a composite key for grouping aggregations
     */
    private String getGroupKey(String factory, String account, String sysCode) {
        return String.format("%s:%s:%s", 
                factory != null ? factory : "", 
                account != null ? account : "", 
                sysCode != null ? sysCode : "");
    }
}
