package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.enums.VendorEnum;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.zip.CRC32;

@Component
@Slf4j
public class OrderIdDumpDecorator implements DumpDecorator<ApplicationAggregation> {
    @Setter
    private Dump<ApplicationAggregation> dump;

    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == dump) {
            return null;
        }
        List<ApplicationAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }

        for (ApplicationAggregation aggregation : aggregations) {
            String orderId = aggregation.getOrderId();
            String vendorAbbr = VendorEnum.getAbbreviation(vendor);
            if (StringUtils.isBlank(vendorAbbr)) {
                String err = String.format("vendor abbreviation not found for vendor: %s", vendor);
                log.error(err);
                throw new SynchronizationException(err);
            }
            String accountAbbr = hashAccountTo8Chars(aggregation.getAccount());
            aggregation.setOrderId(String.format("%s%s%s", vendorAbbr, accountAbbr, orderId));
        }

        return aggregations;
    }


    public static String hashAccountTo8Chars(String accountName) {
        CRC32 crc = new CRC32();
        crc.update(accountName.getBytes());
        long crcValue = crc.getValue();

        // 将CRC值转为定长的8位16进制字符串
        return String.format("%08X", crcValue);
    }

    public static void main(String[] args) {
        String accountName = "haier10";
        System.out.println(hashAccountTo8Chars(accountName));
    }
}
