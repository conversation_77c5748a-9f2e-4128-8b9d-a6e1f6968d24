package com.haier.devops.bill.acceptance;

import com.haier.devops.bill.common.entity.ApplicationAggregation;
import com.haier.devops.bill.common.service.BudgetConfigService;
import com.haier.devops.bill.common.vo.BudgetConfigVo;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 补充预算信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class BudgetDumpDecorator implements DumpDecorator<ApplicationAggregation> {
    private final BudgetConfigService budgetConfigService;
    @Setter
    private Dump<ApplicationAggregation> dump;

    public BudgetDumpDecorator(BudgetConfigService budgetConfigService) {
        this.budgetConfigService = budgetConfigService;
    }

    @Override
    public List<ApplicationAggregation> dump(String vendor, String billingCycle, AcceptanceStageEnum stage, String... account) {
        if (null == dump) {
            return null;
        }

        List<ApplicationAggregation> aggregations = this.dump.dump(vendor, billingCycle, stage, account);
        if (CollectionUtils.isEmpty(aggregations)) {
            return null;
        }

        Set<String> scodes = aggregations.stream().map(ApplicationAggregation::getSysCode).collect(Collectors.toSet());
        List<BudgetConfigVo> budgetConfigVos = budgetConfigService.queryByScodesAndBillingCycle(vendor, scodes);
        Map<String, BudgetConfigVo> budgetMap = budgetConfigVos.stream()
                .collect(Collectors.toMap(BudgetConfigVo::getScode, Function.identity()));

        List<String> budgetNotFoundScodes = new ArrayList<>();
        for (ApplicationAggregation aggregation : aggregations) {
            BudgetConfigVo budgetConfigVo = budgetMap.get(aggregation.getSysCode());
            if (null == budgetConfigVo) {
            	if (StringUtils.isNotBlank(aggregation.getSysCode())) {
            		budgetNotFoundScodes.add(aggregation.getSysCode());
            	}
            	continue;
            }
            aggregation.setBudgetCode(budgetConfigVo.getBudgetCode());
        }
        
/*
        if (budgetNotFoundScodes.size() > 0) {
        	String err = String.format("budget not found for scode: %s of billing cycle: %s",
                    budgetNotFoundScodes.stream().collect(Collectors.joining(",")), billingCycle.substring(0, 4));
            log.error(err);
            throw new SynchronizationException(err);
        }

*/
        return aggregations;
    }
}
