package com.haier.devops.bill.adjustment;

import com.haier.devops.bill.adjustment.vo.AdjustmentRange;
import com.haier.devops.bill.common.entity.BillAcceptanceResource;
import com.haier.devops.bill.common.service.BillAcceptanceResourceService;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
public class AdjustmentChecker {
    private final BillAcceptanceResourceService acceptanceResourceService;

    public AdjustmentChecker(BillAcceptanceResourceService acceptanceResourceService) {
        this.acceptanceResourceService = acceptanceResourceService;
    }

    /**
     * 判断是否可调账
     * @param aggregatedIds
     * @param range
     * @return
     */
    public boolean checkIfCanAdjust(List<String> aggregatedIds, AdjustmentRange... range) {
        if (CollectionUtils.isEmpty(aggregatedIds)) {
            return false;
        }

        // 判断range：如果不为空，则需要每个range的start和end都不能为空
        if (range != null && range.length > 0) {
            for (AdjustmentRange adjustmentRange : range) {
                if (adjustmentRange == null
                        || !StringUtils.hasText(adjustmentRange.getStart())
                        || !StringUtils.hasText(adjustmentRange.getEnd())) {
                    throw new RuntimeException("range is invalid");
                }
            }
        }

        List<BillAcceptanceResource> billAcceptanceResources = acceptanceResourceService.selectBillsNotReservedAmongIdBetweenDate(aggregatedIds, range);
        if (CollectionUtils.isEmpty(billAcceptanceResources)) {
            return true;
        }
        return false;
    }
}
