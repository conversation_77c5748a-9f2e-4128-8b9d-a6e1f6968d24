package com.haier.devops.bill.adjustment;

import com.haier.devops.bill.adjustment.vo.AdjustmentRange;
import com.haier.devops.bill.common.entity.BillAcceptanceResource;
import com.haier.devops.bill.common.service.BillAcceptanceResourceService;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class AdjustmentChecker {
    private final BillAcceptanceResourceService acceptanceResourceService;

    public AdjustmentChecker(BillAcceptanceResourceService acceptanceResourceService) {
        this.acceptanceResourceService = acceptanceResourceService;
    }

    /**
     * 判断是否可调账
     * @param aggregatedIds
     * @param range
     * @return
     */
    public boolean checkIfCanAdjust(List<String> aggregatedIds, AdjustmentRange... range) {
        if (CollectionUtils.isEmpty(aggregatedIds)) {
            return false;
        }
        // range
        List<BillAcceptanceResource> billAcceptanceResources = acceptanceResourceService.selectBillsNotReservedAmongIdBetweenDate(aggregatedIds, range);
        if (CollectionUtils.isEmpty(billAcceptanceResources)) {
            return true;
        }
        return false;
    }
}
