package com.haier.devops.bill.aliyun.clientImpl;

import com.haier.devops.bill.aliyun.client.RdsClient;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.ecs.v2.region.EcsRegion;
import com.huaweicloud.sdk.rds.v3.model.ListInstancesRequest;
import com.huaweicloud.sdk.rds.v3.model.ListInstancesResponse;

/**
 * @Description: 华为云Rds
 * @Author: A0018437
 * @Date：2024-03-02
 */
public class HuaweiRdsClient implements RdsClient {
    private final com.huaweicloud.sdk.rds.v3.RdsClient client;

    public HuaweiRdsClient(String projectId, String region, String accessKeyId, String accessKeySecret) throws Exception {
        ICredential auth = new BasicCredentials()
                .withProjectId(projectId)
                .withAk(accessKeyId)
                .withSk(accessKeySecret);

        this.client = com.huaweicloud.sdk.rds.v3.RdsClient.newBuilder()
                .withCredential(auth)
                .withRegion(EcsRegion.valueOf(region))
                .build();
    }
    @Override
    public ListInstancesResponse describeDBInstanceAttributeWithOptions(String instanceIds) {
        ListInstancesRequest request = new ListInstancesRequest();
        request.withId(instanceIds);
        try {
            ListInstancesResponse response = client.listInstances(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }
}
