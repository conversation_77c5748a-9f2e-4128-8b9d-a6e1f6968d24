package com.haier.devops.bill.aliyun.factory;

import com.haier.devops.bill.aliyun.client.EcsClient;
import com.haier.devops.bill.aliyun.client.ProjectsClient;
import com.haier.devops.bill.aliyun.client.RdsClient;
import com.haier.devops.bill.aliyun.clientImpl.HuaweiEcsClient;
import com.haier.devops.bill.aliyun.clientImpl.HuaweiProjectsClient;
import com.haier.devops.bill.aliyun.clientImpl.HuaweiRdsClient;
import com.haier.devops.bill.common.service.CloudAccountService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description: 华为云工厂
 * @Author: A0018437
 * @Date：2024-03-02
 */
@Component
public class HuaweiClientFactory {
    @Resource
    private CloudAccountService cloudAccountService;

    public ProjectsClient createProjectsClient(String region, String vendor, String accountName) throws Exception {
        Map<String, String> openApiKey = cloudAccountService.getOpenApiKey(vendor, accountName);
        return new HuaweiProjectsClient(region, openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"));
    }

    public EcsClient createEcsClient(String projectId, String region, String vendor, String accountName) throws Exception {
        Map<String, String> openApiKey = cloudAccountService.getOpenApiKey(vendor, accountName);
        return new HuaweiEcsClient(projectId, region, openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"));
    }

    public RdsClient createRdsClient(String projectId, String region, String vendor, String accountName) throws Exception {
        Map<String, String> openApiKey = cloudAccountService.getOpenApiKey(vendor, accountName);
        return new HuaweiRdsClient(projectId, region, openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"));
    }
}
