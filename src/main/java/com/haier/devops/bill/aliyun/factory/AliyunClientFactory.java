package com.haier.devops.bill.aliyun.factory;

import com.haier.devops.bill.aliyun.client.EcsClient;
import com.haier.devops.bill.aliyun.client.RdsClient;
import com.haier.devops.bill.aliyun.clientImpl.AliyunEcsClient;
import com.haier.devops.bill.aliyun.clientImpl.AliyunRdsClient;
import com.haier.devops.bill.common.service.CloudAccountService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Description: 阿里云工厂类
 * @Author: A0018437
 * @Date：2024-02-29
 */
@Component
public class AliyunClientFactory {
    @Resource
    private CloudAccountService cloudAccountService;
    public EcsClient createEcsClient(String region, String vendor, String accountName) throws Exception {
        Map<String, String> openApiKey = cloudAccountService.getOpenApiKey(vendor, accountName);
        return new AliyunEcsClient(region, openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"));
    }

    public RdsClient createRdsClient(String region, String vendor, String accountName) throws Exception {
        Map<String, String> openApiKey = cloudAccountService.getOpenApiKey(vendor, accountName);
        return new AliyunRdsClient(region, openApiKey.get("accessKey"), openApiKey.get("accessKeySecret"));
    }
}
