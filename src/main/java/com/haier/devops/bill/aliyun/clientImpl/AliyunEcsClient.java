package com.haier.devops.bill.aliyun.clientImpl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.ecs20140526.models.DescribeInstancesResponse;
import com.aliyun.ecs20140526.models.DescribeInstancesResponseBody;
import com.aliyun.tea.TeaException;
import com.haier.devops.bill.aliyun.client.EcsClient;
import com.haier.devops.bill.common.enums.RegionEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * @Description: 阿里云Ecs客户端
 * @Author: A0018437
 * @Date：2024-02-29
 */
public class AliyunEcsClient implements EcsClient {
    private final com.aliyun.ecs20140526.Client client;
    public AliyunEcsClient(String region, String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Ecs
        config.endpoint = "ecs"+ RegionEnum.getRegion(region).get(1);
        this.client = new com.aliyun.ecs20140526.Client(config);
    }
    public DescribeInstancesResponseBody describeInstances(String instanceIds, String region) {
        Set<String> instanceIdsSet = new HashSet<String>();
        instanceIdsSet.add(instanceIds);
        com.aliyun.ecs20140526.models.DescribeInstancesRequest describeInstancesRequest = new com.aliyun.ecs20140526.models.DescribeInstancesRequest()
                .setRegionId(RegionEnum.getRegion(region).get(0))
                .setInstanceIds(JSONObject.toJSONString(instanceIdsSet));
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeInstancesResponse describeInstancesResponse = client.describeInstancesWithOptions(describeInstancesRequest, runtime);
            if (CollectionUtils.isNotEmpty(describeInstancesResponse.getBody().getInstances().getInstance())){
                return describeInstancesResponse.getBody();
            }
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
//            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }
}
