package com.haier.devops.bill.aliyun.clientImpl;

import com.haier.devops.bill.aliyun.client.EcsClient;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.exception.ConnectionException;
import com.huaweicloud.sdk.core.exception.RequestTimeoutException;
import com.huaweicloud.sdk.core.exception.ServiceResponseException;
import com.huaweicloud.sdk.ecs.v2.model.ShowServerRequest;
import com.huaweicloud.sdk.ecs.v2.model.ShowServerResponse;
import com.huaweicloud.sdk.ecs.v2.region.EcsRegion;

/**
 * @Description: 华为云ECS
 * @Author: A0018437
 * @Date：2024-03-02
 */
public class HuaweiEcsClient implements EcsClient {
    private final com.huaweicloud.sdk.ecs.v2.EcsClient client;

    public HuaweiEcsClient(String projectId, String region, String accessKeyId, String accessKeySecret) throws Exception {
        ICredential auth = new BasicCredentials()
                .withProjectId(projectId)
                .withAk(accessKeyId)
                .withSk(accessKeySecret);

        this.client = com.huaweicloud.sdk.ecs.v2.EcsClient.newBuilder()
                .withCredential(auth)
                .withRegion(EcsRegion.valueOf(region))
                .build();
    }
    @Override
    public ShowServerResponse describeInstances(String instanceIds, String region) {
        ShowServerRequest request = new ShowServerRequest();
        request.withServerId(instanceIds);
        try {
            ShowServerResponse response = client.showServer(request);
            return response;
        } catch (ConnectionException e) {
            e.printStackTrace();
        } catch (RequestTimeoutException e) {
            e.printStackTrace();
        } catch (ServiceResponseException e) {
            e.printStackTrace();
            System.out.println(e.getHttpStatusCode());
            System.out.println(e.getRequestId());
            System.out.println(e.getErrorCode());
            System.out.println(e.getErrorMsg());
        }
        return null;
    }
}
