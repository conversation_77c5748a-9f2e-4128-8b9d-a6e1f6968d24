package com.haier.devops.bill.aliyun.clientImpl;

import com.aliyun.rds20140815.models.DescribeDBInstanceAttributeResponse;
import com.aliyun.rds20140815.models.DescribeDBInstanceAttributeResponseBody;
import com.aliyun.tea.TeaException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.haier.devops.bill.aliyun.client.RdsClient;

/**
 * @Description: 阿里云数据库
 * @Author: A0018437
 * @Date：2024-03-01
 */
public class AliyunRdsClient implements RdsClient {
    private final com.aliyun.rds20140815.Client client;
    public AliyunRdsClient(String region, String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Ecs
        config.endpoint = "rds.aliyuncs.com";
        this.client = new com.aliyun.rds20140815.Client(config);
    }
    @Override
    public DescribeDBInstanceAttributeResponseBody describeDBInstanceAttributeWithOptions(String instanceIds) {
        com.aliyun.rds20140815.models.DescribeDBInstanceAttributeRequest describeDBInstanceAttributeRequest = new com.aliyun.rds20140815.models.DescribeDBInstanceAttributeRequest()
                .setDBInstanceId(instanceIds);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeDBInstanceAttributeResponse describeDBInstanceAttributeResponse = client.describeDBInstanceAttributeWithOptions(describeDBInstanceAttributeRequest, runtime);
            if (CollectionUtils.isNotEmpty(describeDBInstanceAttributeResponse.getBody().getItems().getDBInstanceAttribute())){
                return describeDBInstanceAttributeResponse.body;
            }
        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }
}
