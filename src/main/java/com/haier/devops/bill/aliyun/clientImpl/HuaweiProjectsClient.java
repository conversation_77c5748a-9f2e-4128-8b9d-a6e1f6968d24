package com.haier.devops.bill.aliyun.clientImpl;

import com.haier.devops.bill.aliyun.client.ProjectsClient;
import com.huaweicloud.sdk.core.auth.GlobalCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectsResponse;
import com.huaweicloud.sdk.iam.v3.region.IamRegion;

/**
 * @Description: 华为云项目
 * @Author: A0018437
 * @Date：2024-03-02
 */
public class HuaweiProjectsClient implements ProjectsClient {
    private final IamClient client;

    public HuaweiProjectsClient(String region, String accessKeyId, String accessKeySecret) throws Exception {
        ICredential auth = new GlobalCredentials()
                .withAk(accessKeyId)
                .withSk(accessKeySecret);

        this.client = IamClient.newBuilder()
                .withCredential(auth)
                .withRegion(IamRegion.valueOf(region))
                .build();
    }

    @Override
    public KeystoneListProjectsResponse keystoneListProjects() {
        KeystoneListProjectsRequest request = new KeystoneListProjectsRequest();
        KeystoneListProjectsResponse response = client.keystoneListProjects(request);
        return response;
    }
}
