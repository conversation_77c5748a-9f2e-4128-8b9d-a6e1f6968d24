package com.haier.devops.bill.export.util;

import com.alibaba.fastjson.JSON;
import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.sdks.feishu.FeishuClient;
import com.haier.devops.bill.sdks.feishu.FeishuClientException;
import com.haier.devops.bill.util.DateUtil;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class SheetUtil {

    /**
     * 将sheet中的一行数据映射为对象
     *
     * @param clazz
     * @param propertiesInOrder
     * @param cells
     * @param <T>
     * @return
     */
    private static <T> ServiceResult<T> mapRowToObject(Class<T> clazz, String[] propertiesInOrder, List<Object> cells) {
        T t = null;
        int i = 0;
        try {
            Constructor<T> constructor = clazz.getDeclaredConstructor();
            t = constructor.newInstance();
            for (; i < propertiesInOrder.length; i++) {
                if (StringUtils.isBlank(propertiesInOrder[i])) {
                    continue;
                }
                Field field = clazz.getDeclaredField(propertiesInOrder[i]);
                field.setAccessible(true);

                Object value = cells.get(i);
                if (field.getType().equals(String.class)) {
                    field.set(t, value);
                } else if (field.getType().equals(LocalDateTime.class)) {
                    String strVal = String.valueOf(value);
                    if (DateUtil.isValidFormat("yyyy-MM", strVal)) {
                        field.set(t, DateUtil.convertYearMonthToLocalDateTime(strVal));
                    }
                } else if (field.getType().equals(BigDecimal.class)) {
                    field.set(t, new BigDecimal(String.valueOf(value)));
                } else {
                    throw new RuntimeException("不支持的数据类型");
                }
            }

            return new ServiceResult<>(t);
        } catch (Exception e) {
            log.error("解析在线表格出错 i = {}, val = {}", i, JSON.toJSONString(cells), e);
        }
        return new ServiceResult<>("解析在线表格出错", null);
    }

    /**
     * 将在线表格内容转换为对象列表
     * @param clazz
     * @param propertiesInOrder
     * @param data
     * @return
     * @param <T>
     */
    public static <T> ServiceResult<List<T>> getObjectsFromSheetData(Class<T> clazz,
                                                                     String[] propertiesInOrder,
                                                                     List<Object> data) {
        List<T> list = new ArrayList<>();

        if (!CollectionUtils.isEmpty(data) && data.size() > 1) {
            for (Object row : data.subList(1, data.size())) {
                if (!(row instanceof ArrayList)) {
                    continue;
                }

                List<Object> cells = (ArrayList<Object>) row;
                ServiceResult<T> result = mapRowToObject(clazz, propertiesInOrder, cells);
                if (!result.isSuccess()) {
                    return new ServiceResult<>(result.getErrMsg(), null);
                }

                list.add(result.getData());
            }
        }

        return new ServiceResult<>(list);
    }


    /**
     * 获取在线表格内容
     *
     * @param feishuClient
     * @param sheetToken
     * @return
     */
    public static ServiceResult<List<Object>> getSheetContents(FeishuClient feishuClient, String sheetToken) {
        List<Object> sheetContents = new ArrayList<>();

        try {
            Sheet[] sheets = feishuClient.getSheetsFromToken(sheetToken);
            if (sheets.length < 1) {
                return new ServiceResult<>("未找到表格", sheetContents);
            }
            sheetContents = feishuClient.getSheetContents(sheets[0], sheetToken);
        } catch (Exception e) {
            if (e instanceof FeishuClientException) {
                FeishuClientException fce = (FeishuClientException) e;
                log.error("error code: {}, error message: {}", fce.getCode(), fce.getDetail());
                return new ServiceResult<>(fce.getDetail(), sheetContents);
            }
            log.error("get sheet contents failed, error message: {}", e.getMessage());
            return new ServiceResult<>(e.getMessage(), sheetContents);
        }

        return new ServiceResult<>(sheetContents);
    }

}
