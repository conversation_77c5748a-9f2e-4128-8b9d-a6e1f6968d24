package com.haier.devops.bill.export.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ExportVo {
    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 流水号
     */
    private String serialNo;

    /**
     * 开始周期
     */
    private String startCycle;

    /**
     * 结束周期
     */
    private String endCycle;

    /**
     * 任务执行pod的ip
     */
    private String podIp;
}
