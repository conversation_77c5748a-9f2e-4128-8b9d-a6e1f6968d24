package com.haier.devops.bill.export.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class AliyunOssConfig {
    private AliyunOssConfigProperties ossConfigProperties;

    public AliyunOssConfig(AliyunOssConfigProperties ossConfigProperties) {
        this.ossConfigProperties = ossConfigProperties;
    }

    @Bean
     public OSS ossClient() {
         return new OSSClientBuilder()
                 .build(ossConfigProperties.getEndpoint(),
                 ossConfigProperties.getAccessKeyId(),
                 ossConfigProperties.getAccessKeySecret());
     }
}
