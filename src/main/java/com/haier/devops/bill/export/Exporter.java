package com.haier.devops.bill.export;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.*;
import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.export.vo.UploadResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 */
public interface Exporter<T> {
    String BUCKET_NAME = "haier-hdshcms-bill-center";
    Long EXPIRATION_TIME_SEVEN_DAY = 1000L * 60 * 60 * 24 * 7;

    Integer SUCCESS = 1;
    Integer FAILED = 2;
    Integer TIMEOUT = 3;

    Logger log = LoggerFactory.getLogger(Exporter.class);

    /**
     * 上传时设置存储类型和访问权限
     *
     * @return
     */
    default ObjectMetadata getMetadata() {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        metadata.setObjectAcl(CannedAccessControlList.Private);
        return metadata;
    }

    /**
     * 如果bucket不存在则创建
     *
     * @param ossClient
     */
    default void createBucketIfNotExisting(OSS ossClient) {
        if (!ossClient.doesBucketExist(BUCKET_NAME)) {
            ossClient.createBucket(BUCKET_NAME);
        }
    }

    /**
     * 获得一个一天有效期的url
     *
     * @param ossClient
     * @param key
     * @param expirationDate
     * @return
     */
    default URL getExpirationUrl(OSS ossClient, String key, Date expirationDate) {
        return ossClient.generatePresignedUrl(BUCKET_NAME, key, expirationDate);
    }

    /**
     * 将数据写入excel流
     *
     * @param t
     * @return
     */
    ServiceResult<ByteArrayOutputStream> writeToExcelStream(T t);

    /**
     * 获取文件名
     *
     * @param t
     * @return
     */
    String getFileName(T t);

    /**
     * 获取文件在oss路径
     *
     * @param t
     * @return
     */
    String getFilePath(T t);

    /**
     * 获取oss client实例
     *
     * @return
     */
    OSS getOssClient();

    /**
     * 保存oss上传日志
     *
     * @param t
     * @param result
     * @param url
     * @param isUploadingSuccess
     * @param remark
     */
    void updateLog(T t, PutObjectResult result, String url, boolean isUploadingSuccess, String... remark);

    /**
     * 导出日志并上传到oss
     *
     * @param t
     * @return
     */
    default UploadResult doExport(T t) {
        String fileName = getFileName(t);
        String filePath = getFilePath(t);

        ServiceResult<ByteArrayOutputStream> streamResult = writeToExcelStream(t);
        if (!streamResult.isSuccess()) {
            updateLog(t, null, null, false, streamResult.getErrMsg());
            return null;
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(streamResult.getData().toByteArray());

/*
        //////////////////////////////////////////
        // Create a ByteArrayInputStream from the byte array
        ByteArrayInputStream byteArrayInputStream = inputStream;

        // Specify the file path where you want to save the data
        filePath = "/Users/<USER>/Desktop/" + filePath;

        // Create a file object
        File outputFile = new File(filePath);

        try {
            // Create a FileOutputStream to write the data to the file
            FileOutputStream fileOutputStream = new FileOutputStream(outputFile);

            // Read from ByteArrayInputStream and write to FileOutputStream
            int bytesRead;
            byte[] buffer = new byte[1024];
            while ((bytesRead = byteArrayInputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }

            // Close streams
            byteArrayInputStream.close();
            fileOutputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (1 == 1) {
            return null;
        }
        //////////////////////////////////////////
*/

        log.info("开始上传文件：{}", fileName);

        PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, filePath, inputStream);
        // 设置文件隐私、超时时间等元数据
        putObjectRequest.setMetadata(getMetadata());

        OSS ossClient = getOssClient();
        // 创建bucket
        createBucketIfNotExisting(ossClient);

        boolean isUploadingSuccess = true;
        PutObjectResult result = null;
        URL url = null;
        try {
            result = ossClient.putObject(putObjectRequest);

            Date expiration = new Date(System.currentTimeMillis() + EXPIRATION_TIME_SEVEN_DAY);
            // 生成下载url，从前端可直接下载
            url = getExpirationUrl(ossClient, filePath, expiration);
        } catch (Exception e) {
            isUploadingSuccess = false;
            log.error("上传文件失败：{}", JSON.toJSONString(t), e);
        }

        String urlStr = null == url ? "" : url.toString();
        updateLog(t, result, urlStr, isUploadingSuccess);

        return new UploadResult(urlStr, result);
    }
}
