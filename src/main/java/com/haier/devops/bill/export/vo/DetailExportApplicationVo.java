package com.haier.devops.bill.export.vo;

import com.haier.devops.bill.util.QueryWrapperUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * 明细导出申请vo
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class DetailExportApplicationVo extends ExportVo implements Serializable {
    /**
     * s码
     */
    private String scode;

    /**
     * 过滤器
     */
    List<QueryWrapperUtil.Filter> params;
}
