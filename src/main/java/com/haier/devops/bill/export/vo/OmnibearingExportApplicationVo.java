package com.haier.devops.bill.export.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * 综合导出申请vo
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class OmnibearingExportApplicationVo extends ExportVo implements Serializable {
    /**
     * s码
     */
    private List<String> scodes;
    /**
     * 云厂商
     */
    private String vendor;

    /**
     * 账户名
     */
    private String accountName;
}
