package com.haier.devops.bill.export.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class DateRangeMerger {


    // 数据结构表示一个时间段
    static class DateRange {
        LocalDate start;
        LocalDate end;

        DateRange(LocalDate start, LocalDate end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return start + " to " + end;
        }
    }

    public static void main(String[] args) {
        // 日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 输入的时间段
        List<DateRange> ranges = Arrays.asList(
                new DateRange(LocalDate.parse("2024-04-01", formatter), LocalDate.parse("2024-08-19", formatter)),
                new DateRange(LocalDate.parse("2024-08-05", formatter), LocalDate.parse("2024-08-07", formatter)),
                new DateRange(LocalDate.parse("2024-05-01", formatter), LocalDate.parse("2024-08-20", formatter))
        );

        // 得到拆分后的不重叠时间段
        List<DateRange> splitRanges = getNonOverlappingRanges(ranges);

        // 输出结果
        for (DateRange range : splitRanges) {
            System.out.println(range);
        }
    }

    // 得到不重叠时间段的方法
    private static List<DateRange> getNonOverlappingRanges(List<DateRange> ranges) {
        if (ranges.isEmpty()) return new ArrayList<>();

        // 收集所有的时间点
        List<LocalDate> points = new ArrayList<>();
        for (DateRange range : ranges) {
            points.add(range.start);
            points.add(range.end.plusDays(1)); // 结束日期的下一天
        }

        // 排序并去重
        points = new ArrayList<>(new HashSet<>(points));
        Collections.sort(points);

        // 遍历所有的时间点，生成不重叠的时间段
        List<DateRange> result = new ArrayList<>();
        for (int i = 0; i < points.size() - 1; i++) {
            LocalDate start = points.get(i);
            LocalDate end = points.get(i + 1).minusDays(1); // 计算当前段的结束日期

            // 检查是否该时间段在原始时间段中
            boolean isWithinAnyRange = false;
            for (DateRange range : ranges) {
                if (!start.isAfter(range.end) && !end.isBefore(range.start)) {
                    isWithinAnyRange = true;
                    break;
                }
            }

            if (isWithinAnyRange) {
                result.add(new DateRange(start, end));
            }
        }

        return result;
    }
}

