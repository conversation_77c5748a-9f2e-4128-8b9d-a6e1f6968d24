package com.haier.devops.bill.export.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.*;
import com.haier.devops.bill.export.vo.UploadResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

@Component("ossUtil")
@Slf4j
public class OssUtil {

    String BUCKET_NAME = "haier-hdshcms-bill-center";

    Long EXPIRATION_TIME_SEVEN_DAY = 1000L * 60 * 60 * 24 * 7;


    @Resource
    private OSS ossClient;


    /**
     * 如果bucket不存在则创建
     *
     * @param ossClient
     */
    public void createBucketIfNotExisting(OSS ossClient) {
        if (!ossClient.doesBucketExist(BUCKET_NAME)) {
            ossClient.createBucket(BUCKET_NAME);
        }
    }

    /**
     * 上传时设置存储类型和访问权限
     *
     * @return
     */
    public ObjectMetadata getMetadata() {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        metadata.setObjectAcl(CannedAccessControlList.Private);
        return metadata;
    }

    public UploadResult upload(String filePath, InputStream inputStream) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, filePath, inputStream);
        // 设置文件隐私、超时时间等元数据
        putObjectRequest.setMetadata(getMetadata());
        // 创建bucket
        createBucketIfNotExisting(ossClient);

        PutObjectResult result = null;
        URL url = null;
        try {
            result = ossClient.putObject(putObjectRequest);

            Date expiration = new Date(System.currentTimeMillis() + EXPIRATION_TIME_SEVEN_DAY);
            // 生成下载url，从前端可直接下载
            url = ossClient.generatePresignedUrl(BUCKET_NAME, filePath, expiration);
        } catch (Exception e) {
            log.error("上传文件失败：", e);
        }
        String urlStr = null == url ? "" : url.toString();
        return new UploadResult(urlStr, result);
    }
}
