package com.haier.devops.bill.cmdb;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.haier.devops.bill.common.controller.ResponseEntityWrapper;
import com.haier.devops.bill.common.controller.ResponseEnum;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import groovy.util.logging.Slf4j;
import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/hcms/bill/instance")
@Tag(name = "实例信息", description = "实例信息的相关接口")
@Slf4j
public class InstanceController {
    private static final String PRODUCT_OSS = "oss";
    private final CmdbProductOverviewService cmdbProductOverviewService;

    public InstanceController(CmdbProductOverviewService cmdbProductOverviewService) {
        this.cmdbProductOverviewService = cmdbProductOverviewService;
    }

  /**
   * 查询cmdb总表的实例信息
   *
   * @param instanceId 实例id
   * @param product 产品类型
   * @return
   */
  @Operation(summary = "查询cmdb总表的实例信息", description = "根据实例id查询cmdb总表的实例信息")
  @GetMapping("/getInstanceInfo")
  public ResponseEntityWrapper<List<CmdbProductOverview>> getInstanceInfo(
      @RequestParam("instanceId") @Parameter(description = "实例id") String instanceId,
      @RequestParam(required = false, name = "product") @Parameter(description = "产品类型") String product) {
      LambdaQueryWrapper<CmdbProductOverview> queryWrapper = null;
      if (StringUtils.isNotBlank(product)) {
          switch (product) {
              case PRODUCT_OSS: {
                  queryWrapper = new LambdaQueryWrapper<CmdbProductOverview>()
                          .eq(CmdbProductOverview::getSupplementId, instanceId)
                          .eq(CmdbProductOverview::getProductCode, PRODUCT_OSS);
                  break;
              }
              default: {
                  queryWrapper = new LambdaQueryWrapper<CmdbProductOverview>()
                          .eq(CmdbProductOverview::getSupplementId, instanceId);
                  break;
              }
          }
      } else {
          queryWrapper = new LambdaQueryWrapper<CmdbProductOverview>()
                  .eq(CmdbProductOverview::getInstanceId, instanceId);
      }

      try {
            List<CmdbProductOverview> cmdbProductOverviews = cmdbProductOverviewService
                    .getBaseMapper()
                    .selectList(queryWrapper);

            return new ResponseEntityWrapper<>(cmdbProductOverviews);
        } catch (Exception e) {
            return new ResponseEntityWrapper<>(ResponseEnum.REQUEST_ERR.getCode(),e.getMessage(),null);
        }
    }
}
