package com.haier.devops.bill.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.entity.BillTask;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.entity.RawBillSyncLog;
import com.haier.devops.bill.common.enums.VendorEnum;
import com.haier.devops.bill.common.service.*;
import com.haier.devops.bill.common.tools.InstanceInfoTool;
import com.haier.devops.bill.common.tools.ProjectInfoTool;
import com.haier.devops.bill.common.tools.ScodeTool;
import com.haier.devops.bill.common.vo.BillingSummaryVo;
import com.haier.devops.bill.substitution.Constant;
import com.haier.devops.bill.substitution.SubstitutionTaskExecutor;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 账单汇总
 * @Author: A0018437
 * @Date：2024-02-27
 */
@Slf4j
@Component
public class BillSummaryTask {
    private final Logger logger = LoggerFactory.getLogger(BillSummaryTask.class);
    @Resource
    private RawBillSyncLogService rawBillSyncLogService;
    @Resource
    private BillTaskService billTaskService;
    @Resource
    private RefinedRawBillService refinedRawBillService;

    private Executor localBootAsyncExecutor;

    @Resource
    ProjectInfoTool projectInfoTool;
    @Resource
    ScodeTool scodeTool;
    @Resource
    InstanceInfoTool instanceInfoTool;
    @Resource
    CmdbProductOverviewService cmdbProductOverviewService;
    @Resource
    AggregatedBillService aggregatedBillService;
    private static Integer BATCH_SIZE = 30;
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;

    private SqlSessionFactory sqlSessionFactory;

    private SubstitutionTaskExecutor executor;
    private final TransactionTemplate transactionTemplate;

    public BillSummaryTask(SqlSessionFactory sqlSessionFactory,
                           @Qualifier("billSummaryAsyncExecutor") Executor localBootAsyncExecutor,
                           SubstitutionTaskExecutor executor,
                           PlatformTransactionManager transactionManager) {
        this.sqlSessionFactory = sqlSessionFactory;
        this.localBootAsyncExecutor = localBootAsyncExecutor;
        this.executor = executor;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    /**
     * 汇总账单
     */
    @XxlJob("billSummary")
    public void billSummary() {
        // 记录汇总账单开始时间
        log.info("开始汇总账单： " + LocalDateTime.now());
        String params = XxlJobHelper.getJobParam();
        log.info("xxl-job params: {}", params);
        JSONObject jsonObject = JSON.parseObject(params);
        String vendor = "";
        if (jsonObject != null) {
            vendor = jsonObject.getString("vendor");
            log.info("云厂商: {}", vendor);

        }

        summarize(vendor);

        log.info("汇总账单结束： " + LocalDateTime.now());

    }

    public void summarize(String vendor) {
        // 获取已处理完成的原始账单同步日志列表
        List<RawBillSyncLog> logList = rawBillSyncLogService.getPendingAggregatedBillSyncLog(vendor);

        // 使用 parallelStream 并发遍历原始账单同步日志
        logList.parallelStream().forEach(rawBillSyncLog -> {
            // 根据供应商、账期和状态查询任务
            BillTask billTask = new BillTask(rawBillSyncLog);
            billTaskService.save(billTask);
            log.info("--> saved bill task: {}", JSON.toJSONString(billTask));
            updateLogStatus(rawBillSyncLog, CommonConstant.LOG_STAGE_AGGREGATION, CommonConstant.LOG_STATUS_START);
            log.info("--> updated log status: {}", JSON.toJSONString(rawBillSyncLog));

            // 使用线程安全集合跟踪成功和失败的批次
            List<CompletableFuture<Boolean>> futures = new CopyOnWriteArrayList<>();
            // 用于跟踪总批次和成功批次
            AtomicInteger totalBatches = new AtomicInteger(0);
            AtomicInteger successfulBatches = new AtomicInteger(0);
            // 用于标记是否有任何批次失败
            AtomicBoolean hasFailed = new AtomicBoolean(false);
            // 保存失败的批次以便重试
            List<List<String>> failedBatches = new CopyOnWriteArrayList<>();

            // 尝试通过分页处理原始账单同步日志
            try {
                int page = 1;
                int pageSize = BATCH_SIZE;

                // 获取第一页数据
                PageInfo<String> aggregatedIdPage =
                        refinedRawBillService.getPendingAggregatedId(
                                rawBillSyncLog.getVendor(),
                                rawBillSyncLog.getAccountName(),
                                rawBillSyncLog.getBillingCycle(),
                                page,
                                pageSize);

                // 即使第一页为空也要继续检查，防止分页问题
                while (true) {
                    // 检查当前页是否有内容
                    if (aggregatedIdPage != null && aggregatedIdPage.hasContent()) {
                        log.info("--> has content, length: {}, page: {}", aggregatedIdPage.getList().size(), page);
                        totalBatches.incrementAndGet();
                        
                        PageInfo<String> finalAggregatedIdPage = aggregatedIdPage;
                        List<String> currentBatchIds = new ArrayList<>(finalAggregatedIdPage.getList());
                        
                        // 使用局部变量存储当前页码，解决Lambda表达式中的变量必须是final的问题
                        final int currentPage = page;
                        
                        // 处理当前批次
                        CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(
                                () -> {
                                    // 重试机制
                                    for (int retryCount = 0; retryCount <= MAX_RETRY_COUNT; retryCount++) {
                                        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
                                            List<BillingSummaryVo> billingSummaryData =
                                                    refinedRawBillService.getBillingSummaryDataWithInAggregatedId(
                                                            sqlSession,
                                                            rawBillSyncLog.getVendor(),
                                                            rawBillSyncLog.getAccountName(),
                                                            rawBillSyncLog.getBillingCycle(),
                                                            currentBatchIds);

                                            if (CollectionUtils.isNotEmpty(billingSummaryData)) {
                                                aggregated(sqlSession, billingSummaryData, billTask);
                                                return true; // 标记为成功处理
                                            }
                                            return true; // 即使没有数据也标记为成功，因为没有错误发生
                                        } catch (Exception e) {
                                            // 最后一次重试失败
                                            if (retryCount == MAX_RETRY_COUNT) {
                                                log.error("Failed to process batch after {} retries. Page: {}, Error: {}", 
                                                          MAX_RETRY_COUNT, currentPage, e.getMessage(), e);
                                                return false; // 处理失败
                                            }
                                            // 记录重试日志
                                            log.warn("Retry #{} for batch processing. Page: {}, Error: {}", 
                                                    retryCount + 1, currentPage, e.getMessage());
                                            try {
                                                // 简单的退避重试策略
                                                Thread.sleep((1 << retryCount) * 1000L);
                                            } catch (InterruptedException ie) {
                                                Thread.currentThread().interrupt();
                                                return false;
                                            }
                                        }
                                    }
                                    return false; // 不应该到达这里
                                }, localBootAsyncExecutor)
                                .whenComplete((success, ex) -> {
                                    if (Boolean.TRUE.equals(success)) {
                                        successfulBatches.incrementAndGet();
                                    } else {
                                        hasFailed.set(true);
                                        // 记录失败的批次，以便稍后重试
                                        failedBatches.add(currentBatchIds);
                                        log.error("Batch processing failed. Adding to failed batches list.");
                                    }
                                });
                        
                        futures.add(future);
                    }
                    
                    // 检查是否有下一页
                    if (aggregatedIdPage != null && aggregatedIdPage.isHasNextPage()) {
                        // 获取下一页
                        aggregatedIdPage = refinedRawBillService.getPendingAggregatedId(
                                rawBillSyncLog.getVendor(),
                                rawBillSyncLog.getAccountName(),
                                rawBillSyncLog.getBillingCycle(),
                                ++page,
                                pageSize);
                    } else {
                        // 如果没有下一页或当前页为空，结束循环
                        break;
                    }
                }

                log.info("--> waiting for all futures to complete. Total batches: {}", totalBatches.get());
                
                // 等待所有批次处理完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0]));
                
                // 等待所有任务完成
                allFutures.join();
                
                log.info("--> All batches processed. Successful: {}/{}, Failed: {}", 
                        successfulBatches.get(), totalBatches.get(), 
                        totalBatches.get() - successfulBatches.get());

                // 处理任务状态
                if (hasFailed.get()) {
                    // 任何批次失败都导致整个任务失败
                    log.warn("--> Some batches failed. Task marked as failed.");
                    
                    // 记录错误信息
                    String errorMsg = String.format("Processing failed. %d/%d batches successful", 
                            successfulBatches.get(), totalBatches.get());
                    rawBillSyncLog.setErrMsg(errorMsg);
                    
                    // 更新为失败状态
                    updateTaskStatus(billTask, CommonConstant.TASK_STATUS_FAILURE_DATA);
                    updateLogStatus(rawBillSyncLog, CommonConstant.LOG_STAGE_AGGREGATION, CommonConstant.LOG_STATUS_ERROR);
                } else {
                    transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                        protected void doInTransactionWithoutResult(TransactionStatus status) {
                            try {
                                // 全部成功，更新状态
                                // 查询任务状态为正式数据的任务
                                BillTask billTaskFormalStatus = billTaskService.getOne(
                                        new LambdaQueryWrapper<BillTask>()
                                                .eq(BillTask::getVendor, rawBillSyncLog.getVendor())
                                                .eq(BillTask::getBillingCycle, rawBillSyncLog.getBillingCycle())
                                                .eq(BillTask::getStatus, CommonConstant.TASK_STATUS_FORMAL_DATA)
                                                .eq(BillTask::getAccountName, rawBillSyncLog.getAccountName())
                                                .eq(BillTask::getStage, CommonConstant.LOG_STAGE_AGGREGATION));
                                if (billTaskFormalStatus != null) {
                                    billTaskFormalStatus.setStatus(CommonConstant.TASK_STATUS_HISTORICAL_DATA);
                                    billTaskService.updateById(billTaskFormalStatus);
                                }

                                updateTaskStatus(billTask, CommonConstant.TASK_STATUS_FORMAL_DATA);
                                updateLogStatus(rawBillSyncLog, CommonConstant.LOG_STAGE_AGGREGATION, CommonConstant.LOG_STATUS_FINISHED);

                                // 如果是aws则创建到日二次分账任务
                                if (billTask.getVendor().equals(VendorEnum.AWS.getVendor())) {
                                    executor.create(Constant.AWS_REDISTRIBUTION, billTask.getBillingCycle());
                                }
                            } catch (Exception e) {
                                status.setRollbackOnly();
                            }
                        }
                    });


                }
                
                // 清理资源
                futures.clear();
            } catch (Exception e) {
                // 处理主流程异常
                handleException(futures, rawBillSyncLog, billTask, e);
            }
        });
    }

    /**
     * 对账数据进行汇总
     *
     * @param billingSummaryData 对账数据列表
     * @param task               对账任务
     */
    public void aggregated(SqlSession sqlSession, List<BillingSummaryVo> billingSummaryData, BillTask task) {
        // 创建汇总账单列表
        List<AggregatedBill> aggregatedBillList = new ArrayList<>();
        // 创建CMDB产品概览列表
        List<CmdbProductOverview> cmdbProductOverviewList = new ArrayList<>();

        try {
            // 遍历对账数据列表
            for (BillingSummaryVo billingSummaryVo : billingSummaryData) {
                AggregatedBill aggregatedBill = new AggregatedBill(billingSummaryVo);
                aggregatedBill.setTaskId(task.getTaskId());
                aggregatedBillList.add(aggregatedBill);

                // 查询CMDB产品概览是否存在，如果不存在则调用实例信息工具进行实例信息处理
                List<CmdbProductOverview> list =
                        cmdbProductOverviewService.listByAggregatedId(sqlSession, billingSummaryVo.getAggregatedId());

                HdsOpenApi.Project projectInfo = projectInfoTool.getProjectInfo(billingSummaryVo.getCostUnit());
                if (CollectionUtils.isEmpty(list)) {
                    // 创建CMDB产品概览对象
                    CmdbProductOverview cmdbProductOverview = new CmdbProductOverview(billingSummaryVo);
                    cmdbProductOverview.setScode(aggregatedBill.getScode());
                    cmdbProductOverview.setSubscriptionType(billingSummaryVo.getSubscriptionType());
                    cmdbProductOverview.setProjectCode(projectInfo == null ? null : projectInfo.getId());
                    cmdbProductOverview.setProjectName(projectInfo == null ? null : projectInfo.getName());
                    cmdbProductOverview.setScodeUnverified(billingSummaryVo.getScodeUnverified());
                    cmdbProductOverview.setScodeRuleMatched(billingSummaryVo.getScodeRuleMatched());
                    cmdbProductOverview.setSupplementId(billingSummaryVo.getSupplementId());
                    cmdbProductOverview.setCurrency(billingSummaryVo.getCurrency());
                    cmdbProductOverview.setParentFound(billingSummaryVo.getParentFound());

                    instanceInfoTool.instanceInfo(cmdbProductOverview);
                    if (cmdbProductOverview.getCreationTime() == null) {
                        cmdbProductOverview.setCreationTime(billingSummaryVo.getBillingCycle());
                    }

                    cmdbProductOverviewList.add(cmdbProductOverview);
                } else {
                    // 更新部分字段：scode_unverified、supplement_id、subscription_type
                    for (CmdbProductOverview productOverview : list) {
                        productOverview.setSupplementId(billingSummaryVo.getSupplementId());
                        productOverview.setSubscriptionType(billingSummaryVo.getSubscriptionType());
                        productOverview.setScodeUnverified(billingSummaryVo.getScodeUnverified());
                        productOverview.setScode(billingSummaryVo.getCostUnit());
                        productOverview.setProjectCode(projectInfo == null ? null : projectInfo.getId());
                        productOverview.setProjectName(projectInfo == null ? null : projectInfo.getName());
                        productOverview.setCurrency(billingSummaryVo.getCurrency());

                        cmdbProductOverviewList.add(productOverview);
                    }
                }
            }

            // 保存汇总账单和CMDB产品概览列表
            if (!aggregatedBillList.isEmpty()) {
                aggregatedBillService.saveOrUpdateWhenAggregatingInDay(aggregatedBillList);
            }
            
            if (!cmdbProductOverviewList.isEmpty()) {
                try {
                    cmdbProductOverviewService.saveOrUpdateBatchByParams(cmdbProductOverviewList);
                } catch (org.springframework.dao.DataIntegrityViolationException e_dive) {
                    // Log the data integrity violation and allow the program to continue
                    log.warn("Task ID: {}. Data integrity violation (e.g., duplicate key) encountered while saving to bc_cmdb_product_overview. Some records may not have been saved. Program continues. Error: {}",
                             task.getTaskId(), e_dive.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error processing aggregated bill: {}", e.getMessage(), e);
            // 异常向上传播以便触发重试机制
            throw e;
        }
    }

    /**
     * 更新日志状态和任务状态。
     *
     * @param log       日志对象，需要更新阶段和状态。
     * @param newStage  新的日志阶段。
     * @param newStatus 新的日志状态。
     */
    private void updateLogStatus(RawBillSyncLog log, String newStage, String newStatus) {
        if (newStage != null) {
            log.setStage(newStage); // 设置新的日志阶段
        }
        log.setStatus(newStatus); // 设置新的日志状态
        log.setUpdateTime(LocalDateTime.now());
        rawBillSyncLogService.updateById(log); // 根据ID更新日志对象
    }

    /**
     * 更新任务状态。
     *
     * @param task      任务对象，需要更新状态。
     * @param newStatus 新的任务状态。
     */
    private void updateTaskStatus(BillTask task, Integer newStatus) {
        task.setStatus(newStatus); // 设置新的任务状态
        billTaskService.updateById(task); // 根据ID更新任务对象
    }

    /**
     * 处理异常情况，取消未完成的任务，更新日志和任务状态为错误，并记录异常信息。
     *
     * @param futures 未完成的Future列表，将被取消。
     * @param log     相关的日志对象，需要更新为错误状态。
     * @param task    相关的任务对象，需要更新为失败状态。
     * @param e       异常对象，将被记录到日志中。
     */
    private void handleException(List<CompletableFuture<Boolean>> futures, RawBillSyncLog log, BillTask task, Exception e) {
        // 取消未完成的Future任务
        futures.stream()
                .filter(f -> !f.isDone())
                .forEach(f -> f.cancel(true));

        // 设置错误信息并更新状态
        log.setErrMsg(e.getMessage());
        updateLogStatus(log, CommonConstant.LOG_STAGE_AGGREGATION, CommonConstant.LOG_STATUS_ERROR);
        updateTaskStatus(task, CommonConstant.TASK_STATUS_FAILURE_DATA);

        // 记录详细错误日志，增加更多上下文信息
        logger.error("Critical error during bill aggregation. Vendor: {}, Account: {}, Cycle: {}, Error: {}",
                log.getVendor(), log.getAccountName(), log.getBillingCycle(), e.getMessage(), e);
    }
}