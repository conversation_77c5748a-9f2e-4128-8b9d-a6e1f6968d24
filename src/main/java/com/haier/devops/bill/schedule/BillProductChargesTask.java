package com.haier.devops.bill.schedule;

import com.haier.devops.bill.common.service.BcRawBillChargesService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Description: 账单产品收费项任务
 * @Author: ********
 * @Date：2024-01-12
 */
@Component
@Slf4j
public class BillProductChargesTask {

    @Resource
    BcRawBillChargesService bcRawBillChargesService;

    @XxlJob("billProductCharges")
    public void billProductCharges() {
        log.info("账单产品收费项补充任务开始：" + LocalDateTime.now());
        bcRawBillChargesService.insertBillCharges();
        log.info("账单产品收费项补充任务结束：" + LocalDateTime.now());
    }
}
