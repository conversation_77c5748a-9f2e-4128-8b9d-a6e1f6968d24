package com.haier.devops.bill.schedule;

import com.haier.devops.bill.common.api.HworkAcceptanceApi;
import com.haier.devops.bill.common.api.entity.ResultWrapper;
import com.haier.devops.bill.common.entity.Milestone;
import com.haier.devops.bill.common.service.MilestoneService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 同步里程碑信息
 * <AUTHOR>
 */
@Component
@Slf4j
public class MilestoneSyncingTask {
    private final HworkAcceptanceApi hworkAcceptanceApi;
    private final MilestoneService milestoneService;

    public MilestoneSyncingTask(HworkAcceptanceApi hworkAcceptanceApi, MilestoneService milestoneService) {
        this.hworkAcceptanceApi = hworkAcceptanceApi;
        this.milestoneService = milestoneService;
    }

    @XxlJob("syncMilestone")
    public void sync() {
        ResultWrapper<List<Milestone>> milestoneListWrapper = hworkAcceptanceApi.getAlmMilestoneList();
        if (milestoneListWrapper.getCode() != HttpStatus.OK.value()) {
            log.error("同步里程碑失败：{}", milestoneListWrapper.getMsg());
            return;
        }

        List<Milestone> milestones = milestoneListWrapper.getData();
        if (CollectionUtils.isEmpty(milestones)) {
            return;
        }
        boolean saveOrUpdateDone = milestoneService.batchSaveOrUpdateByParams(milestones);
        if (!saveOrUpdateDone) {
            log.error("保存活更新里程碑失败");
        }
    }
}
