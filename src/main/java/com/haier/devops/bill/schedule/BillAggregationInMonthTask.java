package com.haier.devops.bill.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.common.entity.AggregatedBill;
import com.haier.devops.bill.common.service.AggregatedResolveService;
import com.haier.devops.bill.util.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 默认汇总上月到本月的账单
 * 可配置参数:
 * vendor: 云厂商
 * start: 开始时间 格式: yyyy-MM 默认上个月
 * end: 结束时间 格式: yyyy-MM 默认下个月
 * 区间为左闭右开 [start, end)
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BillAggregationInMonthTask {
    private static final String YEAR_MONTH_FORMAT = "yyyy-MM";

    @Resource
    private AggregatedResolveService aggregatedResolveService;

    @XxlJob("billAggregationInMonthTask")
    public void aggregate() {
        // 记录汇总账单开始时间
        log.info("开始按月汇总账单： {}", LocalDateTime.now());
        String params = XxlJobHelper.getJobParam();
        log.info("xxl-job params: {}", params);
        JSONObject jsonObject = JSON.parseObject(params);

        String vendor = null;
        String start = null;
        String end = null;
        if (jsonObject != null) {
            vendor = jsonObject.getString("vendor");
            start = jsonObject.getString("start");
            end = jsonObject.getString("end");
        }

        triggerAggregationTask(vendor, start, end);

        log.info("结束按月汇总账单 ： {}", LocalDateTime.now());
    }

    /**
     * 触发按月汇总任务 [start, end]
     *
     * @param vendor
     * @param start        格式：yyyy-MM 如果为空或者格式不对，默认上个月
     * @param end          格式：yyyy-MM 如果为空或者格式不对，默认下个月
     * @param aggregatedId
     */
    public void triggerAggregationTask(String vendor, String start, String end, String... aggregatedId) {
        String currentMonth = DateUtil.getCurrentMonth();
        if (StringUtils.isBlank(start) || !DateUtil.isValidFormat(YEAR_MONTH_FORMAT, start)) {
            start = currentMonth;
        }

        if (StringUtils.isBlank(end) || !DateUtil.isValidFormat(YEAR_MONTH_FORMAT, end)) {
            end = currentMonth;
        }

        log.info("按月汇总账单： vendor = {}, start = {}, end = {}", vendor, start, end);

        // 清除旧的汇总账单
        aggregatedResolveService.clearOldAggregatedBill(vendor, start, end);

        // 按月汇总的账单
        List<AggregatedBill> groupedAggregatedBills = aggregatedResolveService.getGroupedAggregatedBillsBetweenMonths(start, end, vendor, aggregatedId);

        aggregatedResolveService.saveOrUpdateWhenAggregatingInMonth(groupedAggregatedBills);
    }

}
