package com.haier.devops.bill.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.haier.devops.bill.common.constant.CommonConstant;
import com.haier.devops.bill.common.entity.DataSupplementConfig;
import com.haier.devops.bill.common.redis.Cache;
import com.haier.devops.bill.common.service.DataSupplementConfigService;
import com.haier.devops.bill.common.service.RawBillDailySyncLogService;
import com.haier.devops.bill.common.service.RawBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 账单数据补全定时任务
 * @Author: A0018437
 * @Date：2023-12-07
 */
@Component
@Slf4j
public class BillDataCompletionTask {
    @Resource
    private RawBillService rawBillService;
    @Resource
    private DataSupplementConfigService dataSupplementConfigService;
    @Resource
    RawBillDailySyncLogService rawBillDailySyncLogService;
    @Resource
    Executor localBootAsyncExecutor;
    @Value("${query.batch.size}")
    private Integer batchSize;

    @Resource
    @Qualifier("dataSupplementTemplate")
    private Cache<String, List<DataSupplementConfig>> cloudAccountCache;


    /**
     * 获取配置信息
     *
     * @param vendor      云厂商
     * @param productCode 产品编码
     * @return
     */
    public List<DataSupplementConfig> getConfig(String vendor, String productCode) {
        // 根据云厂商+产品编码从redis获取数据
        List<DataSupplementConfig> dataSupplementConfigList = cloudAccountCache.get(vendor + productCode);
        // 如果为空则从数据库获取数据并填充到redis
        if (CollectionUtils.isEmpty(dataSupplementConfigList)) {
            dataSupplementConfigList = dataSupplementConfigService.list(new LambdaQueryWrapper<DataSupplementConfig>().eq(DataSupplementConfig::getVendor, vendor)
                    .eq(DataSupplementConfig::getProductCode, productCode).eq(DataSupplementConfig::getDelFlag, CommonConstant.DEL_FLAG_FALSE).orderByAsc(DataSupplementConfig::getSort));

            if (CollectionUtils.isEmpty(dataSupplementConfigList)) {
                dataSupplementConfigList = dataSupplementConfigService.list(new LambdaQueryWrapper<DataSupplementConfig>().eq(DataSupplementConfig::getVendor, vendor)
                        .eq(DataSupplementConfig::getProductCode, "default").eq(DataSupplementConfig::getDelFlag, CommonConstant.DEL_FLAG_FALSE).orderByAsc(DataSupplementConfig::getSort));
            }

            cloudAccountCache.put(vendor + productCode, dataSupplementConfigList, CommonConstant.ONE_DAY_OF_MILLI_SECONDS, TimeUnit.MILLISECONDS);
        }
        return dataSupplementConfigList;
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return new ArrayList<>();
    }
}
