package com.haier.devops.bill.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.haier.devops.bill.common.api.AlmBudgetApi;
import com.haier.devops.bill.common.entity.BudgetConfig;
import com.haier.devops.bill.common.service.BudgetConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预算配置同步定时任务
 * 从ALM系统同步预算配置信息
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BudgetConfigSyncTask {

    private final AlmBudgetApi almBudgetApi;
    private final BudgetConfigService budgetConfigService;

    /**
     * 每天凌晨2点执行同步
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void syncBudgetConfigs() {
        log.info("开始同步预算配置信息");
        
        try {
            // 获取当前年份
            String currentYear = String.valueOf(java.time.Year.now().getValue());
            
            // 调用ALM API获取预算信息
            List<AlmBudgetApi.BudgetItem> budgetItems = almBudgetApi.getBudgetItems(currentYear);
            
            if (budgetItems == null || budgetItems.isEmpty()) {
                log.warn("未获取到{}年的预算信息", currentYear);
                return;
            }
            
            log.info("获取到{}条预算信息", budgetItems.size());
            
            // 逐条更新预算配置
            for (AlmBudgetApi.BudgetItem item : budgetItems) {
                updateBudgetConfig(item);
            }
            
            log.info("预算配置同步完成");
        } catch (Exception e) {
            log.error("同步预算配置信息失败", e);
            throw e;
        }
    }
    
    /**
     * 更新单条预算配置
     *
     * @param item ALM预算项
     */
    private void updateBudgetConfig(AlmBudgetApi.BudgetItem item) {
        if (item == null || StringUtils.isBlank(item.getBudgetCode())) {
            return;
        }
        
        try {
            // 查询是否已存在该预算编码的配置
            BudgetConfig existingConfig = budgetConfigService.getOne(
                    new LambdaQueryWrapper<BudgetConfig>()
                            .eq(BudgetConfig::getBudgetCode, item.getBudgetCode())
                            .eq(BudgetConfig::getYear, item.getYear())
                            .last("LIMIT 1")
            );
            
            if (existingConfig != null) {
                // 更新已有记录
                updateExistingBudgetConfig(existingConfig, item);
            } else {
                // 创建新记录
                createNewBudgetConfig(item);
            }
        } catch (Exception e) {
            log.error("更新预算配置失败，预算编码：{}", item.getBudgetCode(), e);
        }
    }
    
    /**
     * 更新已有预算配置
     *
     * @param existingConfig 已有配置
     * @param item ALM预算项
     */
    private void updateExistingBudgetConfig(BudgetConfig existingConfig, AlmBudgetApi.BudgetItem item) {
        // 计算金额
        BigDecimal totalAmount = calculateTotalAmount(item.getYearPaymentAmount());
        BigDecimal availableAmount = calculateAvailableAmount(item.getBudgetBalance());
        
        // 确定分类
        String category = determineCategory(item.getExistProject());
        
        // 更新记录
        LambdaUpdateWrapper<BudgetConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BudgetConfig::getId, existingConfig.getId())
                .set(BudgetConfig::getBudgetName, item.getBudgetName())
                .set(BudgetConfig::getTotalAmount, totalAmount.toString())
                .set(BudgetConfig::getAvailableAmount, availableAmount.toString())
                .set(BudgetConfig::getCategory, category)
                .set(BudgetConfig::getCloudType, item.getBudgetCategoryName())
                .set(BudgetConfig::getUpdateTime, LocalDateTime.now());
        
        budgetConfigService.update(updateWrapper);
        log.info("更新预算配置成功，预算编码：{}", item.getBudgetCode());
    }
    
    /**
     * 创建新预算配置
     *
     * @param item ALM预算项
     */
    private void createNewBudgetConfig(AlmBudgetApi.BudgetItem item) {
        // 计算金额
        BigDecimal totalAmount = calculateTotalAmount(item.getYearPaymentAmount());
        BigDecimal availableAmount = calculateAvailableAmount(item.getBudgetBalance());
        
        // 确定分类
        String category = determineCategory(item.getExistProject());
        
        // 创建新记录
        BudgetConfig newConfig = new BudgetConfig();
        newConfig.setBudgetCode(item.getBudgetCode());
        newConfig.setBudgetName(item.getBudgetName());
        newConfig.setTotalAmount(totalAmount.toString());
        newConfig.setAvailableAmount(availableAmount.toString());
        newConfig.setYear(item.getYear());
        newConfig.setCategory(category);
        newConfig.setCloudType(item.getBudgetCategoryName());
        newConfig.setCreateTime(LocalDateTime.now());
        newConfig.setUpdateTime(LocalDateTime.now());
        
        budgetConfigService.save(newConfig);
        log.info("创建预算配置成功，预算编码：{}", item.getBudgetCode());
    }
    
    /**
     * 计算总金额
     * 年度金额 * 10000
     *
     * @param yearPaymentAmount 年度金额
     * @return 总金额
     */
    private BigDecimal calculateTotalAmount(String yearPaymentAmount) {
        if (StringUtils.isBlank(yearPaymentAmount)) {
            return BigDecimal.ZERO;
        }
        
        try {
            return new BigDecimal(yearPaymentAmount).multiply(new BigDecimal("10000"));
        } catch (NumberFormatException e) {
            log.error("转换总金额失败：{}", yearPaymentAmount, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 计算可用余额
     * 预算余额 * 10000
     *
     * @param budgetBalance 预算余额
     * @return 可用余额
     */
    private BigDecimal calculateAvailableAmount(String budgetBalance) {
        if (StringUtils.isBlank(budgetBalance)) {
            return BigDecimal.ZERO;
        }
        
        try {
            return new BigDecimal(budgetBalance).multiply(new BigDecimal("10000"));
        } catch (NumberFormatException e) {
            log.error("转换可用余额失败：{}", budgetBalance, e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 确定分类
     * "new" if item["exist_project"] == 0 else "renew"
     *
     * @param existProject 是否存在项目
     * @return 分类
     */
    private String determineCategory(String existProject) {
        return "0".equals(existProject) ? "new" : "renew";
    }
}
