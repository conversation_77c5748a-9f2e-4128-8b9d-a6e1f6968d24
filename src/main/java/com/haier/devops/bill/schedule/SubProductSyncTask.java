package com.haier.devops.bill.schedule;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.haier.devops.bill.common.api.HdsOpenApi;
import com.haier.devops.bill.common.api.HworkAuthorityApi;
import com.haier.devops.bill.common.dto.SubProductsPageDto;
import com.haier.devops.bill.common.entity.HdsSubProducts;
import com.haier.devops.bill.common.service.HdsSubProductsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 子产品同步定时任务
 * @Author: ********
 * @Date：2024-03-13
 */
@Slf4j
@Component
public class SubProductSyncTask {
    @Resource
    HdsSubProductsService hdsSubProductsService;
    @Resource
    HworkAuthorityApi hworkAuthorityApi;
    @Resource
    HdsOpenApi hdsOpenApi;

    @XxlJob("subProductsSync")
    /**
     * 同步子产品信息。
     * 此方法通过调用外部接口，分页获取子产品信息，并将获取到的信息处理后存储。
     */
    public void subProductsSync() {
        log.info("子产品信息同步开始：{}", LocalDateTime.now());

        // 初始化分页参数
        SubProductsPageDto sub = new SubProductsPageDto();
        sub.setPageIndex(0);
        sub.setPageSize(100);

        // 获取所有子产品列表
//        List<HdsSubProducts> list = hdsSubProductsService.list();

        boolean hasMoreData;
        do {
            // 递增页码以获取下一页数据
            sub.setPageIndex(sub.getPageIndex() + 1);
            // 调用接口获取子产品数据
            HdsOpenApi.ResultSubProduct subProducts = hdsOpenApi.getSubProducts(sub);
            // 检查是否有更多数据需要处理
            hasMoreData = CollectionUtils.isNotEmpty(subProducts.getData().getData());

            if (hasMoreData) {
                // 处理并存储子产品数据
                processSubProductData(subProducts.getData().getData()/*,list*/);
            }
        } while (hasMoreData);

        log.info("子产品信息同步结束：{}", LocalDateTime.now());
    }

    /**
     * 处理子产品数据。
     * 此方法将接口返回的子产品数据进行转换、过滤，并存储。
     *
     * @param dataList 接口返回的子产品数据列表
     * @param list     已存在的子产品列表，用于过滤重复数据
     */
    private void processSubProductData(List<HdsOpenApi.SubProduct> dataList/*, List<HdsSubProducts> list*/) {
        // 转换数据类型并过滤掉appScode为空的项
        List<HdsSubProducts> convertedData = dataList.stream()
                .map(HdsSubProducts::new)
                .filter(s -> StringUtils.isNotBlank(s.getAppScode()))
                .collect(Collectors.toList());

        // 过滤出不在已有列表中的数据，即新数据
/*
        List<HdsSubProducts> filteredData = convertedData.stream().filter(item -> list.stream().allMatch(each -> !(item.getAppScode() != null && item.getAppScode().equals(each.getAppScode()) && item.getOwner() != null && item.getOwner().equals(each.getOwner())))).collect(Collectors.toList());
*/

        // 如果有新数据，补充组织信息并批量保存
        if (CollectionUtils.isNotEmpty(convertedData)) {
            convertedData.forEach(this::populateOrganizationInfo);
            hdsSubProductsService.saveOrUpdateBatchByParams(convertedData);
        }
    }

    /**
     * 补充子产品信息中的组织信息。
     * 此方法通过调用接口，根据子产品的owner补充其组织信息。
     *
     * @param item 需要补充组织信息的子产品项
     */
    private void populateOrganizationInfo(HdsSubProducts item) {
        // 调用接口获取组织信息
        HworkAuthorityApi.ResultWrapper<HworkAuthorityApi.OrgInfoDTO> userOrgInfo = hworkAuthorityApi.getUserOrgInfo(item.getOwner());
        if (userOrgInfo.getData() != null) {
            // 处理组织ID，将其转换为适用于存储的格式
            String orgId = Optional.ofNullable(userOrgInfo.getData().getDeptPath())
                    .orElse("")
                    .replaceAll("/", ",");
            item.setOrgId(removeSpecificCharacters(orgId));
            item.setOrgName(userOrgInfo.getData().getDeptPathName());
        }
    }

    /**
     * 移除字符串中的特定字符。
     * 此方法用于移除字符串开头和结尾的多个特定字符（例如逗号）。
     *
     * @param str 需要处理的字符串
     * @return 移除特定字符后的字符串
     */
    public static String removeSpecificCharacters(String str) {
        return str.replaceAll("^[\\s,]*", "").replaceAll(",+$", "");
    }
}
