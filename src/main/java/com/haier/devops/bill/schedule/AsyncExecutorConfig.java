package com.haier.devops.bill.schedule;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class AsyncExecutorConfig {
    @Bean(name = "billSummaryAsyncExecutor")
    public Executor localBootAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);  // 核心线程数，可以根据CPU核心数和业务需要进行调整
        executor.setMaxPoolSize(20);   // 最大线程数，根据服务器资源和任务量设定
        executor.setQueueCapacity(100); // 队列容量，任务超过该数量将由maxPoolSize处理
        executor.setKeepAliveSeconds(300); // 非核心线程空闲的超时时间
        executor.setThreadNamePrefix("BillSummaryAsyncTask-");

        // Rejection策略：当线程和队列都满了时，如何处理新任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化
        executor.initialize();
        return executor;
    }
}
