package com.haier.devops.bill.schedule;

import com.haier.devops.bill.common.vo.ServiceResult;
import com.haier.devops.bill.monitor.ServerUsage;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 同步服务器能效数据
 */
@Component
@Slf4j
public class ServerUsageSyncingTask {
    private final ServerUsage serverUsage;

    public ServerUsageSyncingTask(ServerUsage serverUsage) {
        this.serverUsage = serverUsage;
    }

    @XxlJob("syncServerUsage")
    public void sync() {
        ServiceResult<Boolean> booleanServiceResult = serverUsage.persistServerUsage();
        if (!booleanServiceResult.isSuccess()) {
            log.error("syncing server usage data failed. {}", booleanServiceResult.getErrMsg());
        }
    }
}
