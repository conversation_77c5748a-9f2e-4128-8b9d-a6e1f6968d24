package com.haier.devops.bill.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.substitution.SubstitutionTaskEnum;
import com.haier.devops.bill.substitution.SubstitutionTaskExecutor;
import com.haier.devops.bill.util.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 账单替换任务创建任务
 * <AUTHOR>
 */

@Component
@Slf4j
public class SubstitutionTask {
    private final SubstitutionTaskExecutor taskExecutor;

    public SubstitutionTask(SubstitutionTaskExecutor taskCreator) {
        this.taskExecutor = taskCreator;
    }

    @XxlJob("substitutionCreatingTask")
    public void create() {
        SubstitutionTaskEnum[] taskEnums = SubstitutionTaskEnum.values();
        String type = null;
        String billingCycle = null;

        String params = XxlJobHelper.getJobParam();
        log.info("{} 开始创建账单替换任务 xxl-job params: {}", LocalDateTime.now(), params);
        JSONObject jsonObject = JSON.parseObject(params);
        if (null != jsonObject) {
            type = jsonObject.getString("type");
            billingCycle = jsonObject.getString("billingCycle");
        }

        // 如果账期为空，则默认取当前月份
        if (StringUtils.isBlank(billingCycle)) {
            billingCycle = DateUtil.getCurrentMonth();
        }

        // 如果不指定类型，则创建所有类型的任务
        if (StringUtils.isBlank(type)) {
            for (SubstitutionTaskEnum taskEnum : taskEnums) {
                create(taskEnum.getType(), billingCycle);
            }
        } else {
            create(type, billingCycle);
        }
    }

    public void create(String type, String billingCycle) {
        taskExecutor.create(type, billingCycle);
    }

    /**
     * 账单替换任务迁移任务
     *
     */
    @XxlJob("substitutionMigratingTask")
    public void migrate() {
        // type 可为空，为空时迁移所有类型的任务
        String type = null;
        // billingCycle 可为空，为空时从最早需要迁移的月份开始
        String billingCycle = null;

        String params = XxlJobHelper.getJobParam();
        log.info("{} 开始账单迁移任务 xxl-job params: {}", LocalDateTime.now(), params);
        JSONObject jsonObject = JSON.parseObject(params);

        if (null != jsonObject) {
            type = jsonObject.getString("type");
            billingCycle = jsonObject.getString("billingCycle");
        }

        taskExecutor.migrate(type, billingCycle);
    }

    /**
     * 账单替换任务清理任务
     */
    @XxlJob("substitutionCleaningTask")
    public void clean() {
        // type 可为空，为空时清理所有类型的任务
        String type = null;
        // billingCycle 可为空，为空时从最早需要清理的月份开始
        String billingCycle = null;

        String params = XxlJobHelper.getJobParam();
        log.info("{} 开始账单清理任务 xxl-job params: {}", LocalDateTime.now(), params);
        JSONObject jsonObject = JSON.parseObject(params);

        if (null != jsonObject) {
            type = jsonObject.getString("type");
            billingCycle = jsonObject.getString("billingCycle");
        }

        taskExecutor.dryClean(type, billingCycle);
    }


    @XxlJob("substitutionCalculatingTask")
    public void calculate() {
        // type 可为空，为空时迁移所有类型的任务
        String type = null;
        // billingCycle 可为空，为空时从最早需要迁移的月份开始
        String billingCycle = null;

        String params = XxlJobHelper.getJobParam();
        log.info("{} 开始计算（准备）任务 xxl-job params: {}", LocalDateTime.now(), params);
        JSONObject jsonObject = JSON.parseObject(params);

        if (null != jsonObject) {
            type = jsonObject.getString("type");
            billingCycle = jsonObject.getString("billingCycle");
        }

        taskExecutor.calculate(type, billingCycle);
    }

    @XxlJob("substitutionSubstitutingTask")
    public void substitute() {
        // type 可为空，为空时迁移所有类型的任务
        String type = null;
        // billingCycle 可为空，为空时从最早需要迁移的月份开始
        String billingCycle = null;

        String params = XxlJobHelper.getJobParam();
        log.info("{} 开始替换任务 xxl-job params: {}", LocalDateTime.now(), params);
        JSONObject jsonObject = JSON.parseObject(params);

        if (null != jsonObject) {
            type = jsonObject.getString("type");
            billingCycle = jsonObject.getString("billingCycle");
        }

        taskExecutor.substitute(type, billingCycle);
    }
}
