package com.haier.devops.bill.schedule;

import com.github.pagehelper.PageInfo;
import com.haier.devops.bill.common.entity.CmdbProductOverview;
import com.haier.devops.bill.common.service.AggregatedBillService;
import com.haier.devops.bill.common.service.CmdbProductOverviewService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 找父资源任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ParentResolvingTask {

    private final CmdbProductOverviewService productOverviewService;
    private final AggregatedBillService aggregatedBillService;

    public ParentResolvingTask(CmdbProductOverviewService productOverviewService,
                               AggregatedBillService aggregatedBillService) {
        this.productOverviewService = productOverviewService;
        this.aggregatedBillService = aggregatedBillService;
    }

    @XxlJob("parentResolvingTask")
    public void resolve() {
        log.info("开始查找替换父资源： {}", LocalDateTime.now());
//        String params = XxlJobHelper.getJobParam();
//        log.info("xxl-job params: {}", params);
//        JSONObject jsonObject = JSON.parseObject(params);

        // todo 指定时间范围

        int page = 1;
        int perPage = 200;

        PageInfo<CmdbProductOverview> orphans = productOverviewService.listParentNotFoundItems(page, perPage);
        while (orphans.hasContent()) {
            List<CmdbProductOverview> orphanList = orphans.getList();

            for (CmdbProductOverview orphan : orphanList) {
                CmdbProductOverview parent = productOverviewService.getParentInstanceByInstanceId(orphan.getSupplementId());
                if (null == parent) {
                    continue;
                }
                aggregatedBillService.replaceOrphanWithParent(orphan, parent);
            }

            page++;
            orphans = productOverviewService.listParentNotFoundItems(page, perPage);
        }

        log.info("结束查找替换父资源 ： {}", LocalDateTime.now());
    }

}
