package com.haier.devops.bill.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haier.devops.bill.acceptance.AcceptanceStageEnum;
import com.haier.devops.bill.acceptance.HworkSyncing;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@Slf4j
public class HworkSynchronizationTask {
    private final HworkSyncing hworkSyncing;

    public HworkSynchronizationTask(HworkSyncing hworkSyncing) {
        this.hworkSyncing = hworkSyncing;
    }

    /**
     * 生成月度验收汇总
     */
    @XxlJob("persist_application_aggregation_task")
    public void persistApplicationAggregation() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("persist_application_aggregation_task started with params: {}", jobParam);
        try {
            JSONObject params = JSON.parseObject(jobParam);
            String vendor = params != null ? params.getString("vendor") : null;
            String billingCycle = params != null ? params.getString("billingCycle") : null;
            String accountStr = params != null ? params.getString("account") : null;

            if (StringUtils.isAnyBlank(vendor, billingCycle)) {
                log.error("Vendor and BillingCycle are required.");
                XxlJobHelper.handleFail("Vendor and BillingCycle are required.");
                return;
            }

            String[] accounts = StringUtils.isNotBlank(accountStr) ? accountStr.split(",") : new String[0];
            log.info("Calling hworkSyncing.persistApplicationAggregation with vendor: {}, billingCycle: {}, accounts: {}", vendor, billingCycle, Arrays.toString(accounts));
            hworkSyncing.persistApplicationAggregation(vendor, billingCycle, AcceptanceStageEnum.ACTUAL, accounts);
            XxlJobHelper.handleSuccess();
            log.info("persist_application_aggregation_task completed successfully.");
        } catch (Exception e) {
            log.error("Error in persist_application_aggregation_task", e);
            XxlJobHelper.handleFail(e.getMessage());
        }
    }

    /**
     * 生成月度验收明细
     */
    @XxlJob("persist_resource_aggregation_task")
    public void persistResourceAggregation() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("persist_resource_aggregation_task started with params: {}", jobParam);
        try {
            JSONObject params = JSON.parseObject(jobParam);
            String vendor = params != null ? params.getString("vendor") : null;
            String billingCycle = params != null ? params.getString("billingCycle") : null;
            String accountStr = params != null ? params.getString("account") : null;

            if (StringUtils.isAnyBlank(vendor, billingCycle)) {
                log.error("Vendor and BillingCycle are required.");
                XxlJobHelper.handleFail("Vendor and BillingCycle are required.");
                return;
            }

            String[] accounts = StringUtils.isNotBlank(accountStr) ? accountStr.split(",") : new String[0];
            log.info("Calling hworkSyncing.persistResourceAggregation with vendor: {}, billingCycle: {}, accounts: {}", vendor, billingCycle, Arrays.toString(accounts));
            // Assuming AcceptanceStageEnum.ACTUAL or determine from params if needed
            hworkSyncing.persistResourceAggregation(vendor, billingCycle, AcceptanceStageEnum.ACTUAL, accounts);
            XxlJobHelper.handleSuccess();
            log.info("persist_resource_aggregation_task completed successfully.");
        } catch (Exception e) {
            log.error("Error in persist_resource_aggregation_task", e);
            XxlJobHelper.handleFail(e.getMessage());
        }
    }

    /**
     * 同步月度验收汇总
     */
    @XxlJob("sync_application_aggregation_task")
    public void syncApplicationAggregation() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("sync_application_aggregation_task started with params: {}", jobParam);
        try {
            JSONObject params = JSON.parseObject(jobParam);
            String vendor = params != null ? params.getString("vendor") : null;
            String billingCycle = params != null ? params.getString("billingCycle") : null;
            String accountStr = params != null ? params.getString("account") : null;

            if (StringUtils.isAnyBlank(vendor, billingCycle)) {
                log.error("Vendor and BillingCycle are required.");
                XxlJobHelper.handleFail("Vendor and BillingCycle are required.");
                return;
            }

            String[] accounts = StringUtils.isNotBlank(accountStr) ? accountStr.split(",") : new String[0];
            log.info("Calling hworkSyncing.syncApplicationAggregation with vendor: {}, billingCycle: {}, accounts: {}", vendor, billingCycle, Arrays.toString(accounts));
            hworkSyncing.syncApplicationAggregation(vendor, billingCycle, accounts);
            XxlJobHelper.handleSuccess();
            log.info("sync_application_aggregation_task completed successfully.");
        } catch (Exception e) {
            log.error("Error in sync_application_aggregation_task", e);
            XxlJobHelper.handleFail(e.getMessage());
        }
    }

    /**
     * 同步月度验收明细
     */
    @XxlJob("sync_resource_aggregation_task")
    public void syncResourceAggregation() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("sync_resource_aggregation_task started with params: {}", jobParam);
        try {
            JSONObject params = JSON.parseObject(jobParam);
            String vendor = params != null ? params.getString("vendor") : null;
            String billingCycle = params != null ? params.getString("billingCycle") : null;
            String accountStr = params != null ? params.getString("account") : null;

            if (StringUtils.isAnyBlank(vendor, billingCycle)) {
                log.error("Vendor and BillingCycle are required.");
                XxlJobHelper.handleFail("Vendor and BillingCycle are required.");
                return;
            }

            String[] accounts = StringUtils.isNotBlank(accountStr) ? accountStr.split(",") : new String[0];
            log.info("Calling hworkSyncing.syncResourceAggregation with vendor: {}, billingCycle: {}, accounts: {}", vendor, billingCycle, Arrays.toString(accounts));
            hworkSyncing.syncResourceAggregation(vendor, billingCycle, accounts);
            XxlJobHelper.handleSuccess();
            log.info("sync_resource_aggregation_task completed successfully.");
        } catch (Exception e) {
            log.error("Error in sync_resource_aggregation_task", e);
            XxlJobHelper.handleFail(e.getMessage());
        }
    }
}
