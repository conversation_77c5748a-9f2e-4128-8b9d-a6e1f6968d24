server.max-http-header-size=16384
spring.profiles.active=@pom.spring.profiles.active@

# AWS Configuration
aws.savings-plans.region=eu-central-1
aws.savings-plans.endpoint=https://savingsplans.eu-central-1.amazonaws.com
aws.savings-plans.cloud-account-id=1

# mybatis-plus
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.global-config.banner=false
mybatis.type-aliases-package=com.haier.devops.bill.common.entity,com.haier.devops.bill.aws.commitment.entity
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl


# logback
logback.logDir=${user.home}/log
logback.appName=bill-center
logback.fileType=log

# druid
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# \u521D\u59CB\u8FDE\u63A5\u6570
spring.datasource.dynamic.druid.initialSize=5
# \u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5\u6570
spring.datasource.dynamic.druid.minIdle=5
# \u6700\u5927\u6D3B\u8DC3\u8FDE\u63A5\u6570\uFF08\u5E76\u53D1\u8FDE\u63A5\u6570\uFF09
spring.datasource.dynamic.druid.maxActive=201

# \u83B7\u53D6\u8FDE\u63A5\u7684\u6700\u5927\u7B49\u5F85\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
# \u5F53\u5E94\u7528\u7A0B\u5E8F\u5411\u8FDE\u63A5\u6C60\u8BF7\u6C42\u4E00\u4E2A\u8FDE\u63A5\u65F6\uFF0C\u82E5\u5F53\u524D\u6C60\u4E2D\u6CA1\u6709\u53EF\u7528\u7684\u8FDE\u63A5\uFF0C\u7CFB\u7EDF\u4F1A\u7B49\u5F85\u4E00\u5B9A\u7684\u65F6\u95F4\u3002\u5982\u679C\u5728\u8FD9\u6BB5\u65F6\u95F4\u5185\u8FDE\u63A5\u6C60\u4F9D\u7136\u6CA1\u6709\u53EF\u7528\u8FDE\u63A5\uFF0C\u7CFB\u7EDF\u4F1A\u629B\u51FA\u8D85\u65F6\u5F02\u5E38
spring.datasource.dynamic.druid.maxWait=60000

# \u7528\u4E8E\u63A7\u5236\u5BA2\u6237\u7AEF\u5C1D\u8BD5\u8FDE\u63A5\u6570\u636E\u5E93\u7684\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.datasource.dynamic.druid.connect-timeout=180000


# \u7528\u4E8E\u63A7\u5236\u5BA2\u6237\u7AEF\u548C\u6570\u636E\u5E93\u4E4B\u95F4\u7684\u8BFB\u5199\u64CD\u4F5C\u7684\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.datasource.dynamic.druid.socket-timeout=180000

# \u8BBE\u7F6E SQL \u67E5\u8BE2\u7684\u8D85\u65F6\u65F6\u95F4\uFF0C\u5355\u4F4D\u4E3A\u79D2\u3002\u5B83\u5B9A\u4E49\u4E86\u5F53\u67E5\u8BE2\u64CD\u4F5C\u8D85\u8FC7\u6307\u5B9A\u65F6\u95F4\u6CA1\u6709\u8FD4\u56DE\u7ED3\u679C\u65F6\uFF0C\u7CFB\u7EDF\u4F1A\u629B\u51FA\u4E00\u4E2A\u8D85\u65F6\u5F02\u5E38
spring.datasource.dynamic.druid.query-timeout=600


# keep-alive=true \u7684\u529F\u80FD\u662F\u4FDD\u6301\u8FDE\u63A5\u6C60\u4E2D\u7684\u7A7A\u95F2\u8FDE\u63A5\u6D3B\u8DC3\uFF0C\u9632\u6B62\u6570\u636E\u5E93\u670D\u52A1\u5660\u56E0\u4E3A\u7A7A\u95F2\u8D85\u65F6\u800C\u5173\u95ED\u8FD9\u4E9B\u8FDE\u63A5\u3002
# \u542F\u7528\u540E\uFF0CDruid \u4F1A\u5B9A\u671F\u53D1\u9001\u5FC3\u8DF3\uFF08\u5982\u6267\u884C SELECT 1 \u8FD9\u6837\u7684\u7B80\u5355 SQL\uFF09\u5230\u6570\u636E\u5E93\uFF0C\u4EE5\u786E\u4FDD\u8FDE\u63A5\u4E0D\u4F1A\u88AB\u6570\u636E\u5E93\u7AEF\u8D85\u65F6\u5173\u95ED\u3002

####################################################################

spring.datasource.dynamic.druid.keep-alive=true
# \u68C0\u67E5\u7A7A\u95F2\u8FDE\u63A5\u7684\u95F4\u9694\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
# \u2022 \u4F5C\u7528\uFF1AtimeBetweenEvictionRunsMillis \u7528\u4E8E\u63A7\u5236 \u7A7A\u95F2\u8FDE\u63A5\u6E05\u7406\u7EBF\u7A0B\uFF08EvictionThread\uFF09\u4E4B\u95F4\u7684\u95F4\u9694\u65F6\u95F4\u3002Druid \u5B9A\u671F\u8FD0\u884C\u6E05\u7406\u7EBF\u7A0B\u6765\u68C0\u67E5\u8FDE\u63A5\u6C60\u4E2D\u7684\u8FDE\u63A5\uFF0C\u5173\u95ED\u90A3\u4E9B\u4E0D\u518D\u9700\u8981\u7684\u7A7A\u95F2\u8FDE\u63A5\u3002
# \u2022 \u5DE5\u4F5C\u673A\u5236\uFF1A\u5728\u8BBE\u5B9A\u7684\u65F6\u95F4\u95F4\u9694\u5185\uFF08timeBetweenEvictionRunsMillis \u6307\u5B9A\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2\uFF09\uFF0CDruid \u4F1A\u68C0\u67E5\u8FDE\u63A5\u6C60\u4E2D\u8FDE\u63A5\u7684\u72B6\u6001\uFF0C\u5173\u95ED\u8D85\u65F6\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u91CA\u653E\u8D44\u6E90\u3002
spring.datasource.dynamic.druid.timeBetweenEvictionRunsMillis=60000

# \u4E24\u8005\u7684\u533A\u522B
#
# \u2022 keep-alive \u7684\u4F5C\u7528\uFF1A\u4FDD\u6301\u8FDE\u63A5\u6C60\u4E2D\u7684\u7A7A\u95F2\u8FDE\u63A5\u4E0D\u88AB\u6570\u636E\u5E93\u4E3B\u52A8\u5173\u95ED\u3002\u5B83\u7684\u6838\u5FC3\u662F\u9632\u6B62\u6570\u636E\u5E93\u5173\u95ED\u8FDE\u63A5\uFF0C\u4FDD\u8BC1\u7A7A\u95F2\u8FDE\u63A5\u5728\u88AB\u518D\u6B21\u4F7F\u7528\u65F6\u4ECD\u7136\u53EF\u7528\u3002
# \u2022 timeBetweenEvictionRunsMillis \u7684\u4F5C\u7528\uFF1A\u63A7\u5236\u8FDE\u63A5\u6C60\u4E2D\u7A7A\u95F2\u8FDE\u63A5\u7684\u6E05\u7406\u95F4\u9694\uFF0C\u786E\u4FDD\u8FDE\u63A5\u6C60\u4E2D\u6CA1\u6709\u4E0D\u5FC5\u8981\u7684\u7A7A\u95F2\u8FDE\u63A5\u88AB\u957F\u671F\u5360\u7528\u8D44\u6E90\u3002\u5B83\u7684\u6838\u5FC3\u662F\u7BA1\u7406\u548C\u91CA\u653E\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u901A\u8FC7\u6E05\u7406\u7EBF\u7A0B\u81EA\u52A8\u9500\u6BC1\u4E0D\u518D\u9700\u8981\u7684\u8FDE\u63A5\u3002
#
# \u4E24\u8005\u7684\u5DE5\u4F5C\u914D\u5408
#
# \u2022 keep-alive \u662F\u4E3A\u4E86\u786E\u4FDD\u8FDE\u63A5\u4E0D\u4F1A\u56E0\u4E3A\u6570\u636E\u5E93\u8D85\u65F6\u88AB\u5173\u95ED\uFF0C\u7279\u522B\u9002\u5408\u90A3\u4E9B\u7A7A\u95F2\u65F6\u95F4\u8F83\u957F\u4F46\u9700\u8981\u4FDD\u6301\u53EF\u7528\u7684\u8FDE\u63A5\u3002
# \u2022 timeBetweenEvictionRunsMillis \u63A7\u5236\u8FDE\u63A5\u6C60\u7684\u5065\u5EB7\u68C0\u67E5\u548C\u7A7A\u95F2\u8FDE\u63A5\u6E05\u7406\u9891\u7387\u3002\u53EF\u4EE5\u770B\u4F5C\u662F\u8FDE\u63A5\u6C60\u81EA\u6211\u7EF4\u62A4\u7684\u673A\u5236\uFF0C\u5E2E\u52A9\u91CA\u653E\u4E0D\u5FC5\u8981\u7684\u8FDE\u63A5\u8D44\u6E90\u3002

####################################################################

# minEvictableIdleTimeMillis \u9009\u9879\u7684\u4F5C\u7528\u662F\u63A7\u5236\u8FDE\u63A5\u6C60\u4E2D\u7A7A\u95F2\u8FDE\u63A5\u7684\u6700\u77ED\u5B58\u6D3B\u65F6\u95F4\u3002\u5B83\u51B3\u5B9A\u4E86\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u4FDD\u6301\u7A7A\u95F2\u72B6\u6001\u7684\u6700\u77ED\u65F6\u95F4\uFF0C\u8D85\u8FC7\u8FD9\u4E2A\u65F6\u95F4\u540E\uFF0C\u8FDE\u63A5\u53EF\u80FD\u4F1A\u88AB\u6E05\u7406\uFF08\u56DE\u6536\uFF09\u4EE5\u91CA\u653E\u8D44\u6E90
# \u5728\u751F\u4EA7\u73AF\u5883\u4E2D\uFF0CminEvictableIdleTimeMillis\uFF08\u8FDE\u63A5\u6C60\u4E2D\u8FDE\u63A5\u7684\u6700\u5C0F\u7A7A\u95F2\u65F6\u95F4\uFF09\u63A8\u8350\u503C\u5E94\u6839\u636E\u7CFB\u7EDF\u8D1F\u8F7D\u3001\u6570\u636E\u5E93\u8FDE\u63A5\u7684\u9891\u7387\u548C\u8D44\u6E90\u60C5\u51B5\u8FDB\u884C\u8C03\u6574
# \u2022 \u9ED8\u8BA4\u63A8\u8350\u503C\uFF1A300,000 \u6BEB\u79D2\uFF08\u5373 5 \u5206\u949F\uFF09
# \u2022 \u66F4\u9AD8\u8D1F\u8F7D\u7684\u573A\u666F\uFF1A\u53EF\u4EE5\u8BBE\u7F6E\u4E3A 180,000 \u6BEB\u79D2\uFF083 \u5206\u949F\uFF09\uFF0C\u4EE5\u4FBF\u66F4\u9891\u7E41\u5730\u6E05\u7406\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u91CA\u653E\u8D44\u6E90\u3002
# \u2022 \u66F4\u4F4E\u8D1F\u8F7D\u6216\u9700\u8981\u66F4\u957F\u4FDD\u6301\u65F6\u95F4\u7684\u573A\u666F\uFF1A\u53EF\u4EE5\u8BBE\u7F6E\u4E3A 600,000 \u6BEB\u79D2\uFF0810 \u5206\u949F\uFF09\u6216\u66F4\u9AD8\uFF0C\u51CF\u5C11\u9891\u7E41\u7684\u8FDE\u63A5\u521B\u5EFA\u548C\u9500\u6BC1\u3002
# \u8C03\u6574\u5EFA\u8BAE\uFF1A
# \u2022 \u9AD8\u5E76\u53D1\u5E94\u7528\uFF1A\u5982\u679C\u5E94\u7528\u7684\u5E76\u53D1\u8FDE\u63A5\u9891\u7387\u8F83\u9AD8\uFF0C\u51CF\u5C11\u7A7A\u95F2\u65F6\u95F4\uFF083-5 \u5206\u949F\uFF09\u53EF\u4EE5\u907F\u514D\u8FC7\u591A\u7684\u957F\u65F6\u95F4\u7A7A\u95F2\u8FDE\u63A5\u79EF\u7D2F\u3002
# \u2022 \u4F4E\u9891\u8C03\u7528\uFF1A\u5982\u679C\u6570\u636E\u5E93\u8BBF\u95EE\u9891\u7387\u4F4E\uFF0C\u53EF\u4EE5\u589E\u52A0\u7A7A\u95F2\u65F6\u95F4\uFF085-10 \u5206\u949F\uFF09\uFF0C\u907F\u514D\u9891\u7E41\u5173\u95ED\u548C\u91CD\u5EFA\u8FDE\u63A5\u5E26\u6765\u7684\u6027\u80FD\u5F00\u9500\u3002
spring.datasource.dynamic.druid.minEvictableIdleTimeMillis=300000

# \u7528\u4E8E\u68C0\u6D4B\u8FDE\u63A5\u662F\u5426\u6709\u6548\u7684SQL\u67E5\u8BE2
spring.datasource.dynamic.druid.validationQuery=SELECT 1
# \u9A8C\u8BC1\u8FDE\u63A5\u6709\u6548\u6027\u7684\u67E5\u8BE2\u8D85\u65F6\u65F6\u95F4\uFF0C\u5355\u4F4D\u4E3A\u79D2
# \u8BBE\u7F6E\u8FDE\u63A5\u6C60\u5728\u6267\u884C\u9A8C\u8BC1 SQL \u67E5\u8BE2\u65F6\u7684\u8D85\u65F6\u65F6\u95F4\uFF0C\u786E\u4FDD\u5728\u5F52\u8FD8\u6216\u83B7\u53D6\u8FDE\u63A5\u65F6\uFF0C\u68C0\u6D4B\u5176\u6709\u6548\u6027\u4E0D\u4F1A\u82B1\u8D39\u8FC7\u591A\u65F6\u95F4\u3002
spring.datasource.druid.validationQueryTimeout=3000

# \u914D\u7F6E removeAbandoned \u9009\u9879\u6765\u68C0\u6D4B\u548C\u6E05\u7406\u957F\u65F6\u95F4\u672A\u4F7F\u7528\u7684\u8FDE\u63A5\uFF0C\u907F\u514D\u8FDE\u63A5\u6CC4\u9732\uFF1A
spring.datasource.dynamic.druid.removeAbandoned=true
# 30 \u5206\u949F
spring.datasource.dynamic.druid.remove-abandoned-timeout-millis=1800000
# \u5173\u95EDabandoned\u8FDE\u63A5\u65F6\u8F93\u51FA\u9519\u8BEF\u65E5\u5FD7
spring.datasource.dynamic.druid.log-abandoned=true

# \u662F\u5426\u5728\u7A7A\u95F2\u65F6\u68C0\u6D4B\u8FDE\u63A5\u6709\u6548\u6027
spring.datasource.dynamic.druid.testWhileIdle=true
# \u662F\u5426\u5728\u4ECE\u6C60\u4E2D\u53D6\u51FA\u8FDE\u63A5\u65F6\u68C0\u6D4B\u5176\u6709\u6548\u6027
spring.datasource.dynamic.druid.testOnBorrow=false
# \u662F\u5426\u5728\u5F52\u8FD8\u8FDE\u63A5\u65F6\u68C0\u6D4B\u5176\u6709\u6548\u6027
spring.datasource.dynamic.druid.testOnReturn=false


# Druid \u6570\u636E\u5E93\u8FDE\u63A5\u6C60\u4E2D\u7684\u4E00\u4E2A\u914D\u7F6E\u9009\u9879\uFF0C\u5B83\u7684\u4F5C\u7528\u662F\u542F\u7528 \u9884\u7F16\u8BD1 SQL \u8BED\u53E5\uFF08PreparedStatement\uFF09\u6C60\u3002\u5177\u4F53\u6765\u8BF4\uFF0C\u8BE5\u9009\u9879\u5141\u8BB8\u5C06 PreparedStatement \u5BF9\u8C61\u7F13\u5B58\u5230\u8FDE\u63A5\u6C60\u4E2D\uFF0C\u4EE5\u63D0\u9AD8\u6570\u636E\u5E93\u7684\u6027\u80FD
spring.datasource.dynamic.druid.poolPreparedStatements=true
# \u6BCF\u4E2A\u8FDE\u63A5\u7F13\u5B58\u7684\u6700\u5927 PreparedStatement \u6570\u91CF
spring.datasource.dynamic.druid.maxPoolPreparedStatementPerConnectionSize=20
# Druid \u6570\u636E\u5E93\u8FDE\u63A5\u6C60\u4E2D\u7684\u4E00\u4E2A\u914D\u7F6E\u9009\u9879\uFF0C\u5B83\u7684\u4F5C\u7528\u662F\u542F\u7528\u5168\u5C40\u7EDF\u8BA1\uFF0C\u6536\u96C4\u6240\u6709\u6570\u636E\u6E90\u7684\u7EDF\u8BA1\u4FE1\u606F
# spring.datasource.dynamic.druid.useGlobalDataSourceStat=true

# \u542F\u7528\u76D1\u63A7\u548C\u9632\u706B\u5899\uFF08stat, wall \u8FC7\u6EE4\u5668\uFF09
spring.datasource.dynamic.druid.filters=slf4j

# feign
feign.compression.request.enabled=true
feign.compression.request.mime-types=text/xml,application/xml,application/json
feign.compression.request.min-request-size=2048
feign.compression.response.enabled=true

feign.client.config.default.connectTimeout=2000
feign.client.config.default.readTimeout=5000

feign.client.config.hds.connectTimeout=1000
feign.client.config.hds.readTimeout=2000
#feign.client.config.hds.request-interceptors[0]=com.haier.devops.aru.config.HdsAuthenticationConfig.HdsAuthenticationInterceptor

feign.okhttp.follow-redirects=true
feign.okhttp.connect-timeout=5000
feign.okhttp.retry-on-connection-failure=true
feign.okhttp.read-timeout=5000
feign.okhttp.write-timeout=5000
feign.okhttp.max-idle-connections=5
feign.okhttp.keep-alive-duration=15000


# hds\u67E5\u8BE2\u9879\u76EE\u5217\u8868
api.hds.token=06QOwwXEumtFLfQnbaYj4TO7dypQcQcP
api.hds.url=https://gw-qd-aliyun.haier.net

# hcms\u67E5\u8BE2\u7528\u6237\u4FE1\u606F
api.hcms.user.token=9ebaa700-22c5-4eaa-a7d4-00f53313b035
api.hcms.user.url=https://hcms.qd-aliyun-test-internal.haier.net

# \u6839\u636EuserID\u67E5\u8BE2\u9879\u76EE\u4FE1\u606F
api.hds.user.token=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
api.hds.user.url=https://gw-qd-aliyun.haier.net

# \u6279\u91CF\u4FDD\u5B58
persist.batch.size=5000
# \u6279\u91CF\u67E5\u8BE2
query.batch.size=2000

feishu.app-id=cli_a480637b2577500c
feishu.app-secret=s9rpPIQ7bUSGw2RyEtwsWefiSPhmyQ2K

# substitution configuration
substitution.request.substitution-requests[0].vendor=aliyun
substitution.request.substitution-requests[0].account-name=<EMAIL>
substitution.request.substitution-requests[0].scode=\u5171\u4EAB\u5E26\u5BBD

substitution.request.substitution-requests[1].vendor=aliyun
substitution.request.substitution-requests[1].account-name=
substitution.request.substitution-requests[1].scode=S03055

substitution.request.substitution-requests[2].vendor=aliyun
substitution.request.substitution-requests[2].account-name=<EMAIL>
substitution.request.substitution-requests[2].scode=\u5BB9\u5668\u4E91\u5206\u644A

substitution.request.substitution-requests[3].vendor=aliyun
substitution.request.substitution-requests[3].account-name=hr690n
substitution.request.substitution-requests[3].scode=\u5BB9\u5668\u4E91\u5206\u644A

# \u4E2A\u4EBA\u4FE1\u606F\u7F13\u5B58\u5931\u6548\u65F6\u95F4(minutes)
cache.personal-info.expire-time=5
# \u7BA1\u7406\u5458\u6743\u9650\u7684\u7EC4\u7EC7\u7F16\u7801
hcms_admin_orgids=********

domain.code=T94594906

signature.client.user=HCMS
signature.client.pass=gqODmGap
signature.client.public-key=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBBZdhwL1qgabjtRD5a1XfaMQfhQDMlyj7Ou5h8bilhZsv9G7jSCS8abpEh7xjb65jwYadifeGybjXL52fg8EYNr94Zoetvg0Ey7nTvySptrfwpY2YTGs7kVKYBahly5te2j0orYM637jGDB+YkMoRZvaj/8vSUCG3LEGdXkCocQIDAQAB

# ALM Budget API URL
alm.budget.api.url=https://alm.haier.net


# bigdata
automat.api.app-id=2c9f8d3895d743a80195ea3924500003
automat.api.app-key=050b39dc-437e-4b7c-a87f-ec94cf1da9f0
automat.api.gateway-key=1pV5RKHXE1HOXFXTpvMtEIUKN5mRFmao
automat.api.url=https://gw-qd-aliyun.haier.net/dmc/data-service
automat.api.pool.max-per-route=5
automat.api.pool.max-total=10
# minutes
automat.api.pool.keep-live-mins=2
# milliseconds
automat.api.pool.connect-timeout-millis=3000
# milliseconds
automat.api.pool.socket-timeout-millis=3000


springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/swagger/v3/api-docs

statusCallBack=https://dtest.haiersmarthomes.com/adp/test/SendOperateLog

# Exchange Rate API Configuration
exchange.rate.api.url=https://v6.exchangerate-api.com/v6/************************/latest
exchange.rate.api.timeout=10000
exchange.rate.sync.vendor=SYSTEM

