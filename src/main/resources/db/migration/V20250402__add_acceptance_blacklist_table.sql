-- 创建验收推送黑名单表
CREATE TABLE IF NOT EXISTS `bc_acceptance_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vendor` varchar(32) NOT NULL COMMENT '云厂商',
  `sys_code` varchar(32) NOT NULL COMMENT '系统编码（S码）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vendor_sys_code` (`vendor`, `sys_code`) COMMENT '云厂商和系统编码唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验收推送黑名单，配置的云厂商和S码不会被推送到hwork';
