-- 创建汇率表
CREATE TABLE IF NOT EXISTS `bc_exchange_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `base_currency` varchar(8) NOT NULL DEFAULT 'CNY' COMMENT '基础货币代码',
  `target_currency` varchar(8) NOT NULL COMMENT '目标货币代码',
  `exchange_rate` decimal(20,8) NOT NULL COMMENT '汇率值（1基础货币=多少目标货币）',
  `rate_date` date NOT NULL COMMENT '汇率日期',
  `api_update_time` datetime DEFAULT NULL COMMENT 'API最后更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-有效；0-无效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_currency_date` (`base_currency`, `target_currency`, `rate_date`) COMMENT '货币对和日期唯一索引',
  KEY `idx_target_currency_date` (`target_currency`, `rate_date`) COMMENT '目标货币和日期索引',
  KEY `idx_rate_date` (`rate_date`) COMMENT '汇率日期索引',
  KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汇率表，存储每日汇率信息';
