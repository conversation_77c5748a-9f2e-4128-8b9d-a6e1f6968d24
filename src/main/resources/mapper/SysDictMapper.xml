<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.SysDictMapper">
    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.SysDictDTO" resultType="com.haier.devops.bill.common.entity.SysDict">
        SELECT * FROM sys_dict
        where del_flag = 0
        <if test="dto.dictName != null and dto.dictName != ''">
            AND dict_name LIKE CONCAT('%',#{dto.dictName},'%')
        </if>
        <if test="dto.dictCode != null and dto.dictCode != ''">
            AND dict_code LIKE CONCAT('%',#{dto.dictCode},'%')
        </if>
        ORDER BY create_time desc
    </select>
</mapper>
