<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.NodeMapper">

    <select id="forecastForTheMonth" resultType="double">
        select IFNULL(
                       ROUND(sum(_a.summer) + (SELECT sum(a.summer) * (DATEDIFF(LAST_DAY(NOW()), CURDATE()) + 1)
                                               from bc_bill_task t
                                                        inner join bc_aggregated_bill a on t.task_id = a.task_id
                                                        inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
                                               where t.status = 1
                                                 and t.stage = 'aggregation'
                                                 and o.subscription_type = '1'
                                                <if test="scodes != null and scodes.size() > 0">
                                                    and a.scode in
                                                    <foreach collection="scode" item="scode" separator="," open="(" close=")">
                                                        #{scode}
                                                    </foreach>
                                                </if>
                                                 ),
                             2),
                       0) AS amount

        FROM bc_bill_task _t
                 inner join bc_aggregated_bill _a on _t.task_id = _a.task_id
            where _t.status = 1 and _t.stage = 'aggregation'
                <![CDATA[
                    and _t.billing_cycle >= DATE_FORMAT(CURRENT_DATE, '%Y-%m-01')
                    and _t.billing_cycle < DATE_ADD(DATE_FORMAT(CURRENT_DATE, '%Y-%m-01'), INTERVAL 1 MONTH);
                ]]>
                <if test="scodes != null and scodes.size() > 0">
                    and a.scode in
                    <foreach collection="scode" item="scode" separator="," open="(" close=")">
                        #{scode}
                    </foreach>
                </if>
    </select>

    <select id="getConsumptionAnalysis" resultType="double">
        SELECT
            IFNULL(ROUND(sum(n.summer),2),0) AS amount
        FROM
            bc_aggregated_bill
        WHERE granularity = 'month'
        <![CDATA[
            AND billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
            AND billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        <if test="scodes != null and scodes.size() > 0">
            AND scode IN
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
    </select>

    <select id="getConsumerTrends" resultType="map">
        SELECT
            <if test="type == 1">
                DATE_FORMAT(d.date_value, '%Y-%m') AS date,
            </if>
            <if test="type == 2">
                concat(YEAR(d.date_value), '-Q',
                CASE
                    WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                    WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                    WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                    WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                END) AS date,
            </if>
            <if test="type == 3">
                YEAR(d.date_value) as date,
            </if>
            IFNULL(round(sum(a.summer), 2), 0) AS value
        FROM bc_date_range d LEFT JOIN bc_aggregated_bill a ON  d.date_value = a.billing_cycle AND a.granularity = 'month'
        <foreach collection="currencies" item="currency" separator=" " open="" close="">
            and a.currency = #{currency}
        </foreach>
        <if test="scodes != null and scodes.size() > 0">
            AND a.scode in
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
        <if test="scodes == null or scodes.size() == 0">
            <!-- 临时方案 -->
            and 1 = 2
        </if>
        inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        <if test="vendors != null and vendors.size() > 0">
            and p.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        WHERE d.date_type = 'month'
        <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        GROUP BY date
        ORDER BY date ASC
    </select>

    <select id="getConsumerTrendsAll" resultType="map">
        SELECT
        <if test="type == 1">
            DATE_FORMAT(d.date_value, '%Y-%m') AS date,
        </if>
        <if test="type == 2">
            concat(YEAR(d.date_value), '-Q',
            CASE
            WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
            WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
            WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
            WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
            END) AS date,
        </if>
        <if test="type == 3">
            YEAR(d.date_value) as date,
        </if>
        IFNULL(round(sum(
        case
        when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
        else summer
        end
        ), 2), 0) AS value
        FROM bc_date_range d LEFT JOIN bc_aggregated_bill a ON  d.date_value = a.billing_cycle AND a.granularity = 'month'
        <if test="scodes != null and scodes.size() > 0">
            AND a.scode in
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
        <if test="scodes == null or scodes.size() == 0">
            <!-- 临时方案 -->
            and 1 = 2
        </if>
        inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        <if test="vendors != null and vendors.size() > 0">
            and p.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        WHERE d.date_type = 'month'
        <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        GROUP BY date
        ORDER BY date ASC
    </select>

    <select id="getMainAssets" parameterType="String" resultType="map">
        SELECT
            a.ecsCount,
            b.dbCount,
            c.mwCount
        FROM (
            ( SELECT COUNT(1) AS ecsCount FROM rc_host_info h inner join
                (select vendor, account_name, currency from cloud_account where is_enabled = 1 group by vendor, account_name, currency) a
                on h.vendor = a.vendor and h.account_name = a.account_name
                <foreach collection="currencies" item="currency" separator=" " open="" close="">
                    and a.currency = #{currency}
                </foreach>
                where h.is_deleted != 1 and h.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
                <if test="vendors != null and vendors.size() > 0">
                    and h.vendor in
                    <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                        #{vendor}
                    </foreach>
                </if>
            ) a,
            ( SELECT COUNT(1) AS dbCount FROM rc_database_info r inner join
                (select vendor, account_name, currency from cloud_account where is_enabled = 1 group by vendor, account_name, currency) a
                on r.vendor = a.vendor and r.account_name = a.account_name
                <foreach collection="currencies" item="currency" separator=" " open="" close="">
                    and a.currency = #{currency}
                </foreach>
                where r.is_deleted != 1 and r.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
                <if test="vendors != null and vendors.size() > 0">
                    and r.vendor in
                    <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                        #{vendor}
                    </foreach>
                </if>
            ) b,
            ( SELECT COUNT(*) AS mwCount FROM rc_middleware_info m  inner join
                (select vendor, account_name, currency from cloud_account where is_enabled = 1 group by vendor, account_name, currency) a
                on m.vendor = a.vendor and m.account_name = a.account_name
                <foreach collection="currencies" item="currency" separator=" " open="" close="">
                    and a.currency = #{currency}
                </foreach>
                where m.is_deleted != 1 and m.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
                <if test="vendors != null and vendors.size() > 0">
                    and m.vendor in
                    <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                        #{vendor}
                    </foreach>
                </if>
            ) c
        )
    </select>

    <select id="getEcsDetail" parameterType="String" resultType="map">
        SELECT * FROM rc_host_info h
        where h.is_deleted != 1
        and h.creation_time between #{startDate} and #{endDate}
        and h.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and h.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        order by h.creation_time desc
    </select>

    <select id="getDbDetail" parameterType="String" resultType="map">
        SELECT * FROM rc_database_info r
        where r.is_deleted != 1 and r.creation_time between #{startDate} and #{endDate} and r.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and r.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        order by r.creation_time desc
    </select>

    <select id="getMwDetail" parameterType="String" resultType="map">
        SELECT * FROM rc_middleware_info m
        where m.is_deleted != 1 and m.creation_time between #{startDate} and #{endDate} and m.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and m.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        order by m.creation_time desc
    </select>

    <select id="getMainAssetsAll" parameterType="String" resultType="map">
        SELECT
        a.ecsCount,
        b.dbCount,
        c.mwCount
        FROM (
        ( SELECT COUNT(1) AS ecsCount FROM rc_host_info h
        where h.is_deleted != 1 and h.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and h.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        ) a,
        ( SELECT COUNT(1) AS dbCount FROM rc_database_info r
        where r.is_deleted != 1 and r.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and r.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        ) b,
        ( SELECT COUNT(*) AS mwCount FROM rc_middleware_info m
        where m.is_deleted != 1 and m.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendors != null and vendors.size() > 0">
            and m.vendor in
            <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                #{vendor}
            </foreach>
        </if>
        ) c
        )
    </select>

    <select id="getConsumptionStepByStep" parameterType="string" resultType="map">
        <if test="filter != null and filter == 'vendor'"> <!-- 按照云厂商 -->
            select
                   <if test="type == 1">
                       DATE_FORMAT(d.date_value, '%Y-%m') AS date,
                   </if>
                   <if test="type == 2">
                       concat(YEAR(d.date_value), '-Q',
                       CASE
                           WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                           WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                           WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                           WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                       END) AS date,
                   </if>
                   <if test="type == 3">
                       YEAR(d.date_value) as date,
                   </if>
                   ifnull(round(sum(a.summer), 2), 0) as value,
                    a.vendor as vendor
            from bc_date_range d
                     inner join
                 (select
                      t1.scode,
                      t1.billing_cycle,
                      t1.summer,
                      t1.payable_sum,
                      t1.voucher_sum,
                      t1.cash_sum,
                      t1.aggregated_id,
                      t1.granularity,
                      t2.vendor
                  from bc_aggregated_bill t1
                           inner join bc_cmdb_product_overview t2 on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
            <foreach collection="currencies" item="currency" separator=" " open="" close="">
                and t1.currency = #{currency}
            </foreach>
            <if test="vendors != null and vendors.size() > 0">
                and t2.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
                 on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>
            where d.date_type = 'month'
            <![CDATA[
                and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            group by a.vendor, date
            order by a.vendor, date asc
        </if>
        <if test="filter != null and filter == 'productType'"> <!-- 按照产品类型 -->
            select
                <if test="type == 1">
                    DATE_FORMAT(d.date_value, '%Y-%m') AS date,
                </if>
                <if test="type == 2">
                    concat(YEAR(d.date_value), '-Q',
                    CASE
                    WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                    WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                    WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                    WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                    END) AS date,
                </if>
                <if test="type == 3">
                    YEAR(d.date_value) as date,
                </if>
                   ifnull(round(sum(a.summer), 2), 0) as value,
                    a.product_code as productCode,
                    a.product_name as productName
            from bc_date_range d
                     inner join
                 (select
                      t1.scode,
                      t1.billing_cycle,
                      t1.summer,
                      t1.payable_sum,
                      t1.voucher_sum,
                      t1.cash_sum,
                      t1.aggregated_id,
                      t1.granularity,
                      t2.vendor,
                      t3.alias_code as product_code,
                      t3.alias_name as product_name
                  from bc_aggregated_bill t1
                           inner join bc_cmdb_product_overview t2 on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
                           inner join bc_raw_bill_products t3 on t2.vendor = t3.vendor and t2.product_code = t3.product_code and t3.del_flag != '1'
            <foreach collection="currencies" item="currency" separator=" " open="" close="">
                and t1.currency = #{currency}
            </foreach>
            <if test="vendors != null and vendors.size() > 0">
                and t2.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
                 on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>
            where d.date_type = 'month'
            <![CDATA[
                and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            group by a.product_code, date
            order by a.product_code, date asc
        </if>
        <if test="filter != null and filter == 'scode'"> <!-- 按照子产品 -->
            select
                <if test="type == 1">
                    DATE_FORMAT(d.date_value, '%Y-%m') AS date,
                </if>
                <if test="type == 2">
                    concat(YEAR(d.date_value), '-Q',
                    CASE
                    WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                    WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                    WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                    WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                    END) AS date,
                </if>
                <if test="type == 3">
                    YEAR(d.date_value) as date,
                </if>
                ifnull(round(sum(a.summer), 2), 0) as value,
                a.scode as scode,
                a.sub_product_name as subProductName
            from bc_date_range d
                     inner join
                 (select
                         t1.scode,
                         t1.billing_cycle,
                         t1.summer,
                         t1.payable_sum,
                         t1.voucher_sum,
                         t1.cash_sum,
                         t1.aggregated_id,
                         t1.granularity,
                         t2.app_scode,
                         t2.sub_product_id,
                         t2.sub_product_name,
                         t2.product_id
                  from bc_aggregated_bill t1
                           inner join bc_hds_sub_products t2 on t1.scode = t2.app_scode and t1.granularity = 'month'
                           inner join bc_cmdb_product_overview o on t1.aggregated_id = o.aggregated_id
            <foreach collection="currencies" item="currency" separator=" " open="" close="">
                and t1.currency = #{currency}
            </foreach>
            <if test="vendors != null and vendors.size() > 0">
                and o.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
                 on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>

            where d.date_type = 'month'
            <![CDATA[
                and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            group by a.sub_product_id, date
            order by a.sub_product_id, date asc
        </if>
        <if test="filter != null and filter == 'product'"> <!-- 按照产品 -->
            select
                <if test="type == 1">
                    DATE_FORMAT(d.date_value, '%Y-%m') AS date,
                </if>
                <if test="type == 2">
                    concat(YEAR(d.date_value), '-Q',
                    CASE
                    WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                    WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                    WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                    WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                    END) AS date,
                </if>
                <if test="type == 3">
                    YEAR(d.date_value) as date,
                </if>
                ifnull(round(sum(a.summer), 2), 0) as value,
                a.product_id as productId,
                a.product_name as productName
            from bc_date_range d
                     inner join
                 (select
                         t1.scode,
                         t1.billing_cycle,
                         t1.summer,
                         t1.payable_sum,
                         t1.voucher_sum,
                         t1.cash_sum,
                         t1.aggregated_id,
                         t1.granularity,
                         t2.app_scode,
                         t2.sub_product_id,
                         t2.sub_product_name,
                         t2.product_id,
                         t2.product_name
                  from bc_aggregated_bill t1
                           inner join bc_hds_sub_products t2 on t1.scode = t2.app_scode and t1.granularity = 'month'
                           inner join bc_cmdb_product_overview o on t1.aggregated_id = o.aggregated_id
                <foreach collection="currencies" item="currency" separator=" " open="" close="">
                    and t1.currency = #{currency}
                </foreach>
                <if test="vendors != null and vendors.size() > 0">
                    and o.vendor in
                    <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                        #{vendor}
                    </foreach>
                </if>
            ) a
                 on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>

            where d.date_type = 'month'
            <![CDATA[
                and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            group by a.product_id, date
            order by a.product_id, date asc
        </if>
    </select>

    <select id="getConsumptionStepByStepAll" parameterType="string" resultType="map">
        <if test="filter != null and filter == 'vendor'"> <!-- 按照云厂商 -->
            select
            <if test="type == 1">
                DATE_FORMAT(d.date_value, '%Y-%m') AS date,
            </if>
            <if test="type == 2">
                concat(YEAR(d.date_value), '-Q',
                CASE
                WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                END) AS date,
            </if>
            <if test="type == 3">
                YEAR(d.date_value) as date,
            </if>
            ifnull(round(sum(case
            when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
            else summer
            end), 2), 0) as value,
            a.vendor as vendor
            from bc_date_range d
            inner join
            (select
            t1.scode,
            t1.billing_cycle,
            t1.summer,
            t1.payable_sum,
            t1.voucher_sum,
            t1.cash_sum,
            t1.aggregated_id,
            t1.granularity,
            t2.vendor,
            t1.currency
            from bc_aggregated_bill t1
            inner join bc_cmdb_product_overview t2 on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
            <if test="vendors != null and vendors.size() > 0">
                and t2.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
            on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>
            where d.date_type = 'month'
            <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
            group by a.vendor, date
            order by a.vendor, date asc
        </if>
        <if test="filter != null and filter == 'productType'"> <!-- 按照产品类型 -->
            select
            <if test="type == 1">
                DATE_FORMAT(d.date_value, '%Y-%m') AS date,
            </if>
            <if test="type == 2">
                concat(YEAR(d.date_value), '-Q',
                CASE
                WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                END) AS date,
            </if>
            <if test="type == 3">
                YEAR(d.date_value) as date,
            </if>
            ifnull(round(sum(case
            when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
            else summer
            end), 2), 0) as value,
            a.product_code as productCode,
            a.product_name as productName
            from bc_date_range d
            inner join
            (select
            t1.scode,
            t1.billing_cycle,
            t1.summer,
            t1.payable_sum,
            t1.voucher_sum,
            t1.cash_sum,
            t1.aggregated_id,
            t1.granularity,
            t2.vendor,
            t3.alias_code as product_code,
            t3.alias_name as product_name,
            t1.currency
            from bc_aggregated_bill t1
            inner join bc_cmdb_product_overview t2 on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
            inner join bc_raw_bill_products t3 on t2.vendor = t3.vendor and t2.product_code = t3.product_code and t3.del_flag != '1'
            <if test="vendors != null and vendors.size() > 0">
                and t2.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
            on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>
            where d.date_type = 'month'
            <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
            group by a.product_code, date
            order by a.product_code, date asc
        </if>
        <if test="filter != null and filter == 'scode'"> <!-- 按照子产品 -->
            select
            <if test="type == 1">
                DATE_FORMAT(d.date_value, '%Y-%m') AS date,
            </if>
            <if test="type == 2">
                concat(YEAR(d.date_value), '-Q',
                CASE
                WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                END) AS date,
            </if>
            <if test="type == 3">
                YEAR(d.date_value) as date,
            </if>
            ifnull(round(sum(case
            when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
            else summer
            end), 2), 0) as value,
            a.scode as scode,
            a.sub_product_name as subProductName
            from bc_date_range d
            inner join
            (select
            t1.scode,
            t1.billing_cycle,
            t1.summer,
            t1.payable_sum,
            t1.voucher_sum,
            t1.cash_sum,
            t1.aggregated_id,
            t1.granularity,
            t2.app_scode,
            t2.sub_product_id,
            t2.sub_product_name,
            t2.product_id,
            t1.currency
            from bc_aggregated_bill t1
            inner join bc_hds_sub_products t2 on t1.scode = t2.app_scode and t1.granularity = 'month'
            inner join bc_cmdb_product_overview o on t1.aggregated_id = o.aggregated_id
            <if test="vendors != null and vendors.size() > 0">
                and o.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
            on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>

            where d.date_type = 'month'
            <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
            group by a.sub_product_id, date
            order by a.sub_product_id, date asc
        </if>
        <if test="filter != null and filter == 'product'"> <!-- 按照产品 -->
            select
            <if test="type == 1">
                DATE_FORMAT(d.date_value, '%Y-%m') AS date,
            </if>
            <if test="type == 2">
                concat(YEAR(d.date_value), '-Q',
                CASE
                WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
                WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
                WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
                WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
                END) AS date,
            </if>
            <if test="type == 3">
                YEAR(d.date_value) as date,
            </if>
            ifnull(round(sum(case
            when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
            else summer
            end), 2), 0) as value,
            a.product_id as productId,
            a.product_name as productName
            from bc_date_range d
            inner join
            (select
            t1.scode,
            t1.billing_cycle,
            t1.summer,
            t1.payable_sum,
            t1.voucher_sum,
            t1.cash_sum,
            t1.aggregated_id,
            t1.granularity,
            t2.app_scode,
            t2.sub_product_id,
            t2.sub_product_name,
            t2.product_id,
            t2.product_name,
            t1.currency
            from bc_aggregated_bill t1
            inner join bc_hds_sub_products t2 on t1.scode = t2.app_scode and t1.granularity = 'month'
            inner join bc_cmdb_product_overview o on t1.aggregated_id = o.aggregated_id
            <if test="vendors != null and vendors.size() > 0">
                and o.vendor in
                <foreach collection="vendors" item="vendor" separator="," open="(" close=")">
                    #{vendor}
                </foreach>
            </if>
            ) a
            on d.date_value = a.billing_cycle
            <if test="scodes != null and scodes.size() > 0">
                AND a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="scodes == null or scodes.size() == 0">
                <!-- 临时方案 -->
                and 1 = 2
            </if>

            where d.date_type = 'month'
            <![CDATA[
            and d.date_value >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
            and d.date_value < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
            group by a.product_id, date
            order by a.product_id, date asc
        </if>
    </select>
    <select id="getLastYearTotalAmount" resultType="java.lang.Double">
        select sum(summer) from bc_aggregated_bill where granularity = 'month'
        <![CDATA[
            and billing_cycle >= DATE_FORMAT(CURRENT_DATE - INTERVAL 1 YEAR, '%Y-01-01')
            and billing_cycle < DATE_FORMAT(CURRENT_DATE, '%Y-01-01');
        ]]>
        and scode in
        <if test="scodes != null and scodes.size() > 0">
            AND a.scode in
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
    </select>


</mapper>
