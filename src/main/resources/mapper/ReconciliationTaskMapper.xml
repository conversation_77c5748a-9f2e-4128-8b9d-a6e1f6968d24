<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ReconciliationTaskMapper">
    <select id="queryList" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.entity.ReconciliationTask">
        select
        a.*
        from
        bc_reconciliation_task a,
        (
        select
        aggregated_id,
        max(create_time) create_time
        from
        bc_reconciliation_task
        where
        (type = #{type}
        and status in (0, 2)
        and error_num &lt; 3)
        group by
        aggregated_id ) b
        where
        a.aggregated_id = b.aggregated_id
        and a.create_time = b.create_time
    </select>
</mapper>
