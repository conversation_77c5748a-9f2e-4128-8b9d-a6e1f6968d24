<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ExchangeRateMapper">

    <!-- 根据货币对和日期查询汇率 -->
    <select id="selectByDateAndCurrency" resultType="com.haier.devops.bill.common.entity.ExchangeRate">
        SELECT id, base_currency, target_currency, exchange_rate, rate_date, 
               api_update_time, create_time, update_time, status
        FROM bc_exchange_rate
        WHERE base_currency = #{baseCurrency}
          AND target_currency = #{targetCurrency}
          AND rate_date = #{rateDate}
          AND status = 1
        LIMIT 1
    </select>

    <!-- 查询指定日期的所有汇率 -->
    <select id="selectByDate" resultType="com.haier.devops.bill.common.entity.ExchangeRate">
        SELECT id, base_currency, target_currency, exchange_rate, rate_date, 
               api_update_time, create_time, update_time, status
        FROM bc_exchange_rate
        WHERE rate_date = #{rateDate}
          AND status = 1
        ORDER BY target_currency
    </select>

    <!-- 批量插入或更新汇率数据 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO bc_exchange_rate 
        (base_currency, target_currency, exchange_rate, rate_date, api_update_time, create_time, update_time, status)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.baseCurrency}, #{item.targetCurrency}, #{item.exchangeRate}, #{item.rateDate}, 
             #{item.apiUpdateTime}, #{item.createTime}, #{item.updateTime}, #{item.status})
        </foreach>
        ON DUPLICATE KEY UPDATE
        exchange_rate = VALUES(exchange_rate),
        api_update_time = VALUES(api_update_time),
        update_time = VALUES(update_time),
        status = VALUES(status)
    </insert>

    <!-- 删除指定日期的汇率数据 -->
    <delete id="deleteByDate">
        DELETE FROM bc_exchange_rate
        WHERE rate_date = #{rateDate}
    </delete>

</mapper>
