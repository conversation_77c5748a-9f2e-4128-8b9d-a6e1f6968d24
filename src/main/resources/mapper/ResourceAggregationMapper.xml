<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ResourceAggregationMapper">

    <select id="queryBillByResource" resultType="com.haier.devops.bill.common.entity.ResourceAggregation">
        select #{billingCycle} as pay_date, 1 as bill_type, o.vendor as factory, o.account_name as account, a.scode as sys_code,
               p.sub_product_name as sys_name,
            o.instance_id, o.supplement_id as split_item, o.private_ip as address, o.product_name,
            round(sum(a.summer), 2) as cost_amount,
            case o.subscription_type
                when 2 then 'Subscription'
                when 1 then 'PayAsYouGo'
            end as cost_type
        from bc_aggregated_bill a
        inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
                                                     and o.vendor = #{vendor}
                                                     and a.granularity = 'month'
        <![CDATA[
            and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
            and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        <if test="account != null and account.length > 0">
            and o.account_name in
            <foreach collection="account" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        left join bc_hds_sub_products p on a.scode = p.app_scode
        group by a.aggregated_id
    </select>
    <select id="selectByApplications" resultType="com.haier.devops.bill.common.entity.ResourceAggregation">
        select r.*
        from bc_application_aggregation a
                 inner join bc_resource_aggregation r on a.sys_code = r.sys_code and a.pay_date = r.pay_date
                 and a.`type` = 2
        <if test="aggregations != null and aggregations.size() > 0">
            where
                <foreach collection="aggregations" separator="or" item="item" open="(" close=")">
                    a.sys_code = #{item.sysCode} and a.pay_date = #{item.payDate}
                </foreach>
        </if>
        <if test="aggregations == null">
            where 1 != 1
        </if>
    </select>
    <select id="selectPendingSyncingItems" resultType="com.haier.devops.bill.common.entity.ResourceAggregation">
        select r.*
        from bc_resource_aggregation r
        where r.factory = #{vendor}
            and r.pay_date = #{billingCycle}
            <if test="account != null and account.length > 0">
                and r.account in
                <foreach collection="account" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and r.status not in (1, 3)
            and not exists(select 1
                        from bc_application_aggregation a
                        where a.budget_code is null
                        and a.factory = r.factory
                        and a.account = r.account
                        and a.sys_code = r.sys_code)
    </select>
    <delete id="deleteByVendorAndBillingCycleAndStageAndAccount">
        DELETE FROM bc_resource_aggregation
        WHERE factory = #{vendor}
          AND pay_date = #{billingCycle}
          <if test="account != null and account.length > 0">
            AND account IN
            <foreach item="item" index="index" collection="account" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
    </delete>
</mapper>
