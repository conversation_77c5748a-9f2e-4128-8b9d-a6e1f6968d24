<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BcRawBillChargesMapper">

    <resultMap type="com.haier.devops.bill.common.entity.BcRawBillCharges" id="BcRawBillChargesMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="billingItem" column="billing_item" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getProductChargeInfo" resultType="com.haier.devops.bill.common.vo.BillChargesProductsVo">
        select bc.id,bc.billing_item,bc.product_id,bp.vendor,
               bp.product_code,bp.product_name,
               bp.create_time,bp.create_by,
               bp.update_time,bp.update_by,bp.del_flag
        from bc_raw_bill_charges bc
            inner join bc_raw_bill_products bp
        on bc.product_id = bp.id
            ${ew.customSqlSegment}
    </select>

    <select id="getBillCharges" resultType="com.haier.devops.bill.common.vo.BillChargesVo">
        SELECT
            p.vendor,
            p.product_code,
            p.product_name,
            c.billing_item
        FROM
            bc_raw_bill_products p
            LEFT JOIN bc_raw_bill_charges c ON p.id = c.product_id
        where p.del_flag = 0 and c.del_flag = 0
    </select>

</mapper>

