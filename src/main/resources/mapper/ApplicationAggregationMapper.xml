<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ApplicationAggregationMapper">
    <update id="batchUpdateStatus">
        <foreach collection="aggregations" item="item" separator=";">
            update bc_application_aggregation
            set status = #{status}
            where id = #{item.id}
        </foreach>
    </update>
    <update id="batchRejectByOrderId">
        update bc_application_aggregation
        set status = 3
        where order_id in
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </update>

    <select id="queryBillGroupByScode" resultType="com.haier.devops.bill.common.entity.ApplicationAggregation">
        select _t.order_id,
               _t.type,
               _t.factory,
               _t.account,
               _t.sys_code,
               _t.pay_date,
               <if test="stage != null and stage == 'estimation'">
                   round(sum(_t.amount), 2) as estimate_amount,
               </if>
               <if test="stage != null and stage == 'actual'">
                   round(sum(_t.amount), 2) as actual_amount,
               </if>
               0 as status,
               p.sub_product_name as sys_name,
               p.owner as user_code,
               p.owner_name as user_name,
               _t.confirmation_serial_no,
               _t.system_confirmer,
               _t.domain_confirmer
        from (select t.order_id
                   , t.type
                   , t.factory
                   , t.account
                   <![CDATA[
                       , case when abs(t.amount) < 50 and t.factory = 'huawei' then 'S04076' else t.sys_code end as sys_code
                   ]]>
                   , t.pay_date
                   , t.amount
                   , t.confirmation_serial_no
                   , t.system_confirmer
                   , t.domain_confirmer
              from (
                    select
                        REPLACE(CONCAT(r.scode, r.billing_cycle), '-', '') as order_id,
                        1 as type,
                        o.vendor as factory,
                        o.account_name as account,
                        r.scode as sys_code,
                        r.billing_cycle as pay_date,
                        sum(consume_amount) as amount,
                        b.task_id as confirmation_serial_no,
                        r.system_owner as system_confirmer,
                        r.domain_owner as domain_confirmer
                    from bc_bill_acceptance_batch b
                    inner join bc_bill_acceptance_resource r on b.batch_id = r.domain_batch_id
                    inner join bc_cmdb_product_overview o on r.aggregated_id = o.aggregated_id
                    where b.billing_cycle = CONCAT(#{billingCycle}, '-01') and b.batch_type = 'domain'
                        <!-- 待办完成 -->
                        and b.batch_status = 'done'
                        <!-- 没有存疑 -->
                        and r.check_status = ''
                        and o.vendor = #{vendor}
                        <if test="accounts != null and accounts.length > 0">
                            and o.account_name in
                            <foreach item="acc" collection="accounts" open="(" close=")" separator=",">
                                #{acc}
                            </foreach>
                        </if>
                    group by o.vendor, o.account_name, r.scode, r.system_owner, r.domain_owner, r.billing_cycle, b.task_id
              ) t) _t
              left join bc_hds_sub_products p on _t.sys_code = p.app_scode
        group by _t.factory, _t.account, _t.sys_code, _t.pay_date
    </select>
    <select id="selectPendingSyncBill" resultType="com.haier.devops.bill.common.vo.HworkPendingSyncBillVo">
        select
            a.id,
            a.order_id,
            a.`type`,
            p.sub_product_id,
            p.sub_product_name,
            p.business_domain_ids,
            p.business_domains,
            a.factory,
            a.account,
            a.pay_date,
            a.estimate_amount,
            a.actual_amount,
            a.sys_code,
            a.sys_name,
            a.budget_code,
            b.budget_name,
            a.user_code,
            a.user_name,
            a.alm_project_code,
            a.alm_project_name,
            a.alm_milestone_id,
            a.milestone_name,
            a.supplier_code,
            a.supplier_name,
            a.status
        from bc_application_aggregation a left join bc_hds_sub_products p on a.sys_code = p.app_scode
                                          left join bc_budget_config b on a.budget_code = b.budget_code
        <where>
            1 = 1
            <if test="billVo.factory != null and billVo.factory != ''">
                and a.factory = #{billVo.factory}
            </if>
            <if test="billVo.orderId != null and billVo.orderId != ''">
                and a.order_id = #{billVo.orderId}
            </if>
            <if test="billVo.subProductId != null and billVo.subProductId != '' and billVo.subProductId != 'none'">
                and p.sub_product_id = #{billVo.subProductId}
            </if>
            <if test="billVo.subProductId == 'none'">
                and p.sub_product_id is null
            </if>
            <if test="billVo.businessDomainIds != null and billVo.businessDomainIds != ''">
                and p.business_domain_ids = #{billVo.businessDomainIds}
            </if>
            <if test="billVo.budgetCode != null and billVo.budgetCode != '' and billVo.budgetCode != 'none'">
                and a.budget_code = #{billVo.budgetCode}
            </if>
            <if test="billVo.budgetCode == 'none'">
                and a.budget_code is null
            </if>
            <if test="billVo.almMilestoneId != null and billVo.almMilestoneId != ''">
                and a.alm_milestone_id = #{billVo.almMilestoneId}
            </if>
            <if test="billVo.status != null">
                and a.status = #{billVo.status}
            </if>
            <if test="billVo.userCode != null and billVo.userCode != '' and billVo.userCode != 'none'">
                and a.user_code = #{billVo.userCode}
            </if>
            <if test="billVo.userCode == 'none'">
                and a.user_code is null or a.user_code = ''
            </if>
            <if test="billVo.type != null">
                and a.`type` = #{billVo.type}
            </if>
            <if test="startDate != null and startDate != ''">
                <![CDATA[
                    and a.pay_date >= #{startDate}
                ]]>
            </if>
            <if test="endDate != null and endDate != ''">
                <![CDATA[
                    and a.pay_date <= #{endDate} 
                ]]>
            </if>
        </where>
    </select>

    <select id="getYearlyTotalByBudgetCodes" resultType="java.util.Map">
        SELECT 
            budget_code, 
            SUM(actual_amount) as total_amount
        FROM 
            bc_application_aggregation
        WHERE 
            budget_code IN 
            <foreach collection="budgetCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
            AND pay_date LIKE CONCAT(#{year}, '%')
        GROUP BY 
            budget_code
    </select>

    <insert id="upsertBatchByOrderId" parameterType="java.util.List">
        INSERT INTO bc_application_aggregation (
            order_id, factory, account, type, sys_code, sys_name, pay_date,
            estimate_amount, actual_amount, budget_code, user_code, user_name,
            alm_milestone_id, milestone_name, alm_project_code, alm_project_name,
            contract_code, contract_name, supplier_code, supplier_name,
            generation_time, status, confirmation_serial_no, system_confirmer, system_confirmer_name,
            domain_confirmer, domain_confirmer_name,
            create_time, update_time
        )
        VALUES
        <foreach collection="aggregations" item="item" separator=",">
            (
                #{item.orderId}, #{item.factory}, #{item.account}, #{item.type}, #{item.sysCode}, #{item.sysName}, #{item.payDate},
                #{item.estimateAmount}, #{item.actualAmount}, #{item.budgetCode}, #{item.userCode}, #{item.userName},
                #{item.almMilestoneId}, #{item.milestoneName}, #{item.almProjectCode}, #{item.almProjectName},
                #{item.contractCode}, #{item.contractName}, #{item.supplierCode}, #{item.supplierName},
                #{item.generationTime}, #{item.status}, #{item.confirmationSerialNo}, #{item.systemConfirmer}, #{item.systemConfirmerName},
                #{item.domainConfirmer}, #{item.domainConfirmerName},
                <choose>
                    <when test="item.createTime != null">#{item.createTime},</when>
                    <otherwise>NOW(),</otherwise>
                </choose>
                <choose>
                    <when test="item.updateTime != null">#{item.updateTime}</when>
                    <otherwise>NOW()</otherwise>
                </choose>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            factory = IF(VALUES(factory) IS NOT NULL AND VALUES(factory) != '', VALUES(factory), factory),
            account = IF(VALUES(account) IS NOT NULL AND VALUES(account) != '', VALUES(account), account),
            type = IF(VALUES(type) IS NOT NULL, VALUES(type), type),
            sys_code = IF(VALUES(sys_code) IS NOT NULL AND VALUES(sys_code) != '', VALUES(sys_code), sys_code),
            sys_name = IF(VALUES(sys_name) IS NOT NULL AND VALUES(sys_name) != '', VALUES(sys_name), sys_name),
            pay_date = IF(VALUES(pay_date) IS NOT NULL AND VALUES(pay_date) != '', VALUES(pay_date), pay_date),
            estimate_amount = IF(VALUES(estimate_amount) IS NOT NULL, VALUES(estimate_amount), estimate_amount),
            actual_amount = IF(VALUES(actual_amount) IS NOT NULL, VALUES(actual_amount), actual_amount),
            budget_code = IF(VALUES(budget_code) IS NOT NULL AND VALUES(budget_code) != '', VALUES(budget_code), budget_code),
            user_code = IF(VALUES(user_code) IS NOT NULL AND VALUES(user_code) != '', VALUES(user_code), user_code),
            user_name = IF(VALUES(user_name) IS NOT NULL AND VALUES(user_name) != '', VALUES(user_name), user_name),
            alm_milestone_id = IF(VALUES(alm_milestone_id) IS NOT NULL AND VALUES(alm_milestone_id) != '', VALUES(alm_milestone_id), alm_milestone_id),
            milestone_name = IF(VALUES(milestone_name) IS NOT NULL AND VALUES(milestone_name) != '', VALUES(milestone_name), milestone_name),
            alm_project_code = IF(VALUES(alm_project_code) IS NOT NULL AND VALUES(alm_project_code) != '', VALUES(alm_project_code), alm_project_code),
            alm_project_name = IF(VALUES(alm_project_name) IS NOT NULL AND VALUES(alm_project_name) != '', VALUES(alm_project_name), alm_project_name),
            contract_code = IF(VALUES(contract_code) IS NOT NULL AND VALUES(contract_code) != '', VALUES(contract_code), contract_code),
            contract_name = IF(VALUES(contract_name) IS NOT NULL AND VALUES(contract_name) != '', VALUES(contract_name), contract_name),
            supplier_code = IF(VALUES(supplier_code) IS NOT NULL AND VALUES(supplier_code) != '', VALUES(supplier_code), supplier_code),
            supplier_name = IF(VALUES(supplier_name) IS NOT NULL AND VALUES(supplier_name) != '', VALUES(supplier_name), supplier_name),
            generation_time = IF(VALUES(generation_time) IS NOT NULL AND VALUES(generation_time) != '', VALUES(generation_time), generation_time),
            confirmation_serial_no = IF(VALUES(confirmation_serial_no) IS NOT NULL AND VALUES(confirmation_serial_no) != '', VALUES(confirmation_serial_no), confirmation_serial_no),
            system_confirmer = IF(VALUES(system_confirmer) IS NOT NULL AND VALUES(system_confirmer) != '', VALUES(system_confirmer), system_confirmer),
            system_confirmer_name = IF(VALUES(system_confirmer_name) IS NOT NULL AND VALUES(system_confirmer_name) != '', VALUES(system_confirmer_name), system_confirmer_name),
            domain_confirmer = IF(VALUES(domain_confirmer) IS NOT NULL AND VALUES(domain_confirmer) != '', VALUES(domain_confirmer), domain_confirmer),
            domain_confirmer_name = IF(VALUES(domain_confirmer_name) IS NOT NULL AND VALUES(domain_confirmer_name) != '', VALUES(domain_confirmer_name), domain_confirmer_name),
            status = IF(VALUES(status) IS NOT NULL, VALUES(status), status),
            update_time = NOW()
    </insert>

</mapper>
