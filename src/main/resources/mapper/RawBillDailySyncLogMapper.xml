<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RawBillDailySyncLogMapper">

    <resultMap type="com.haier.devops.bill.common.entity.RawBillDailySyncLog" id="BcRawBillDailySyncLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="errMsg" column="err_msg" jdbcType="VARCHAR"/>
        <result property="stage" column="stage" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

