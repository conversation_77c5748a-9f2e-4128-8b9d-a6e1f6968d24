<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ResourceInstanceMapper">

    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.ResourceInstanceDTO" resultType="com.haier.devops.bill.common.entity.CmdbProductOverview">
        select
        bcpo.id,
        bcpo.aggregated_id,
        bcpo.vendor,
        bcpo.account_id,
        bcpo.account_name,
        bcpo.product_code,
        bcpo.product_name,
        bcpo.instance_id,
        bcpo.supplement_id,
        bcpo.scode,
        bcpo.project_code,
        bcpo.project_name,
        bcpo.subscription_type,
        bcpo.creation_time,
        bcpo.releasing_time,
        bcpo.create_time,
        bcpo.update_time,
        ri.cost_unit,
        ifnull(bar.reconciliation_status, 'done') reconciliation_status,
        bhsp.app_name,
        bhsp.app_short_name
        from
        bc_cmdb_product_overview bcpo
        inner join bc_aggregated_bill a on bcpo.aggregated_id = a.aggregated_id
        left join bc_hds_sub_products bhsp on bcpo.scode = bhsp.app_scode
        left join resource_instance ri on bcpo.instance_id = ri.resource_id
        and bcpo.product_code = ri.product_code
        and (
        ifnull(bcpo.supplement_id, 1) = ifnull(ri.apportion_code, 1)
        or (
        length(ifnull(bcpo.supplement_id, '')) = 0
        and length(ri.apportion_code) = 0
        )
        )
        left join (
        select distinct
        bar2.aggregated_id,
        bar2.reconciliation_status
        from
        bc_adjustment_record bar2
        join (
        select
        bar.aggregated_id,
        max(create_time) create_time
        from
        bc_adjustment_record bar
        group by
        bar.aggregated_id
        ) bar on bar2.aggregated_id = bar.aggregated_id
        and bar.create_time = bar2.create_time
        ) bar on bcpo.aggregated_id = bar.aggregated_id
        where
            <choose>
                <when test="dto.ruleField == 'resource_id'">
                    ri.resource_id like concat('%', #{dto.ruleFieldValue}, '%')
                </when>
                <when test="dto.ruleField == 'resource_tag'">
                    ri.resource_tag = #{dto.ruleFieldValue}
                </when>
                <when test="dto.ruleField == 'resource_group'">
                    ri.resource_group = #{dto.ruleFieldValue}
                </when>
                <otherwise>
                  1=1
                </otherwise>
            </choose>
        and (releasing_time is null
        or releasing_time >= DATE(now() - interval 90 day))
        and a.granularity = 'month'
        and bcpo.product_code not in(
        'paas',
        'bigdata_hdop',
        'bigdata_starrocks',
        'cloud_monitor'
        )
          and ri.resource_user_name = #{dto.accountName}
          and ri.commodity_code = #{dto.commodityCode}
          and bcpo.vendor = 'aliyun'
        order by bcpo.scode,bcpo.id
    </select>

    <select id="queryResourceInstanceList" parameterType="com.haier.devops.bill.common.entity.CmdbProductOverview" resultType="com.haier.devops.bill.common.vo.ResourceInstanceVo">
        select
               ri.id,
        ri.resource_user_id ,
        ri.resource_user_name ,
        ri.resource_id ,
        ri.apportion_code,
        ca.access_key ,
        ca.access_key_secret,
        ca.encrypted ,
        ri.cost_unit,
        ri.cost_unit_id ,
        ri.commodity_code,
        ri.resource_type
        from resource_instance ri
                 , cloud_account ca
        where
        ri.resource_user_id = ca.account_identify and
        ca.is_enabled = 1 and
        ca.vendor like 'aliyun%' and (ca.purpose_type = 'MAIN' or ca.purpose_type = 'DEDICATED')
        and ri.resource_id = #{item.resourceId} and ri.product_code = #{item.productCode}
        and ri.resource_user_id = #{item.accountId}
        <choose>
            <when test="item.apportionCode == null || item.apportionCode == ''">
                and (ri.apportion_code is null or ri.apportion_code = '')
            </when>
            <otherwise>
                and ri.apportion_code = #{item.apportionCode}
            </otherwise>
        </choose>

    </select>

    <select id="queryInfoByAccountId" parameterType="java.lang.String" resultType="java.util.Map">
        select
            m.id,concat(m.commodity_code, m.apportion_code, m.resource_id) resourceId
        from
            (
                select
                    ri.id, ri.commodity_code,
                             case
                                 when ri.apportion_code = ''
                                     or ri.apportion_code is null
                                     then ''
                                 else ri.apportion_code
                                 end as apportion_code,
                             ri.resource_id
                from
                    resource_instance ri
                where
                    ri.resource_user_id = #{accountIdentify}) m
    </select>
</mapper>
