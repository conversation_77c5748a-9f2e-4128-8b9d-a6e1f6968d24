<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RcHostInfoMapper">

    <resultMap type="com.haier.devops.bill.common.entity.RcHostInfo" id="RcHostInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="instanceId" column="instance_id" jdbcType="VARCHAR"/>
        <result property="instanceName" column="instance_name" jdbcType="VARCHAR"/>
        <result property="creationTime" column="creation_time" jdbcType="TIMESTAMP"/>
        <result property="expiredTime" column="expired_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
        <result property="privateIp" column="private_ip" jdbcType="VARCHAR"/>
        <result property="publicIp" column="public_ip" jdbcType="VARCHAR"/>
        <result property="scode" column="scode" jdbcType="VARCHAR"/>
        <result property="project" column="project" jdbcType="VARCHAR"/>
        <result property="env" column="env" jdbcType="VARCHAR"/>
        <result property="resourceGroup" column="resource_group" jdbcType="VARCHAR"/>
        <result property="osType" column="os_type" jdbcType="VARCHAR"/>
        <result property="osName" column="os_name" jdbcType="VARCHAR"/>
        <result property="osArch" column="os_arch" jdbcType="VARCHAR"/>
        <result property="imageId" column="image_id" jdbcType="VARCHAR"/>
        <result property="cpu" column="cpu" jdbcType="INTEGER"/>
        <result property="numa" column="numa" jdbcType="VARCHAR"/>
        <result property="memory" column="memory" jdbcType="INTEGER"/>
        <result property="diskSize" column="disk_size" jdbcType="INTEGER"/>
        <result property="diskType" column="disk_type" jdbcType="VARCHAR"/>
        <result property="hostStatus" column="host_status" jdbcType="VARCHAR"/>
        <result property="hostType" column="host_type" jdbcType="VARCHAR"/>
        <result property="hostInsId" column="host_ins_id" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="zone" column="zone" jdbcType="VARCHAR"/>
        <result property="networkType" column="network_type" jdbcType="VARCHAR"/>
        <result property="vpcId" column="vpc_id" jdbcType="VARCHAR"/>
        <result property="subnetId" column="subnet_id" jdbcType="VARCHAR"/>
        <result property="classCode" column="class_code" jdbcType="VARCHAR"/>
        <result property="chargeType" column="charge_type" jdbcType="VARCHAR"/>
        <result property="rack" column="rack" jdbcType="VARCHAR"/>
        <result property="providerName" column="provider_name" jdbcType="VARCHAR"/>
        <result property="brandName" column="brand_name" jdbcType="VARCHAR"/>
        <result property="model" column="model" jdbcType="VARCHAR"/>
        <result property="sn" column="sn" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
        <result property="uniRegionId" column="uni_region_id" jdbcType="VARCHAR"/>
        <result property="idracIp" column="idrac_ip" jdbcType="VARCHAR"/>
        <result property="domain" column="domain" jdbcType="VARCHAR"/>
        <result property="team" column="team" jdbcType="VARCHAR"/>
        <result property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <result property="ownerName" column="owner_name" jdbcType="VARCHAR"/>
        <result property="maintainer" column="maintainer" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

