<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BudgetAppRelMapper">

    <select id="getBillingCycles" resultType="java.lang.String">
        select a.pay_date
        from bc_budget_app_rel r
                 inner join bc_application_aggregation a
                            on r.vendor = a.factory and r.scode = a.sys_code and r.budget_code = a.budget_code
        where r.id = #{id}
        group by a.pay_date
        order by a.pay_date desc
    </select>
</mapper>
