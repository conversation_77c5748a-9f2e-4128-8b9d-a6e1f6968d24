<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AlarmNoticeGroupMapper">
    <sql id="Base_Column_List">
     id, group_name, create_time, create_by, update_time, update_by, del_flag
    </sql>

    <!-- 分页查询云产品告警组列表 -->
    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.AlarmNoticeGroupDTO" resultType="com.haier.devops.bill.common.vo.AlarmNoticeGroupVo">
        select * from bc_alarm_notice_group where del_flag = '0'
        <if test="dto.groupName != null and dto.groupName != ''">
            and group_name like concat('%',#{dto.groupName},'%')
        </if>
    </select>

    <select id="getAlarmNoticeGroupByIdList" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AlarmNoticeObjVo">
        select
            bang.id notice_obj_id,
            bang.group_name notice_obj_name,
               null email,
               null mobile
        from
            bc_alarm_notice_group bang
        where bang.del_flag = '0' and
        id in
        <foreach collection="array" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
