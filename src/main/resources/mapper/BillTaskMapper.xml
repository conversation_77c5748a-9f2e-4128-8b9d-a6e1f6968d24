<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BillTaskMapper">

    <resultMap type="com.haier.devops.bill.common.entity.BillTask" id="BcBillTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="billingCycle" column="billing_cycle" jdbcType="DATE"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

