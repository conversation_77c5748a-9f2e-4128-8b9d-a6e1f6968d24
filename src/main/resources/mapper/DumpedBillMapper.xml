<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.DumpedBillMapper">

    <select id="queryBillGroupByScode" resultType="com.haier.devops.bill.common.entity.DumpedBill">
        select REPLACE(CONCAT(a.scode, #{billingCycle}), '-', '') as order_id, t.vendor, t.account_name,
        a.scode as sys_code, p.sub_product_name as sys_name, #{billingCycle} as pay_date,
        round(sum(a.summer), 2) as estimated_amount
        from bc_bill_task t
            inner join bc_aggregated_bill a
                on t.task_id = a.task_id and t.vendor = #{vendor} and t.stage = 'aggregation' and t.status = 1
                <![CDATA[
                    and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                    and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
                ]]>
            inner join bc_cmdb_product_overview o
                on a.aggregated_id = o.aggregated_id
            left join bc_hds_sub_products p on a.scode = p.app_scode
            <if test="account != null and account.length > 0">
                and t.account_name in
                <foreach collection="account" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        group by t.vendor, t.account_name, a.scode
    </select>
    <select id="queryDetailBill" resultType="com.haier.devops.bill.common.vo.DumpedDetailBillVo">
        select #{billingCycle}, o.vendor, o.account_name, a.scode,
            o.instance_id, o.instance_name, o.region, o.project_name, o.product_code, o.product_name,
            round(sum(a.summer), 2) as costAmount,
            case o.subscription_type
                when 2 then 'Subscription'
                when 1 then 'PayAsYouGo'
            end as cost_type
        from bc_bill_task t
        inner join bc_aggregated_bill a
        on t.task_id = a.task_id and t.vendor = #{vendor} and t.stage = 'aggregation' and t.status = 1
        <![CDATA[
            and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
            and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
        <if test="account != null and account.length > 0">
            and t.account_name in
            <foreach collection="account" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by a.aggregated_id
    </select>
</mapper>
