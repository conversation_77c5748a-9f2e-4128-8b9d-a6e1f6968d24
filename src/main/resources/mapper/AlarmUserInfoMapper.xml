<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AlarmUserInfoMapper">
    <!-- 根据配置查告警级别 -->
    <select id="getUserListByUserIdArray" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AlarmNoticeObjVo">
        select account notice_obj_id,name notice_obj_name,mobile ,email
        from bc_alarm_user_info where account in
        <foreach collection="array" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and mobile is not null and mobile!='' and email is not null and email!=''
    </select>
    <select id="getUserListByGroupId" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AlarmNoticeObjVo">
        select
        distinct
        baui.account notice_obj_id,
        baui.name notice_obj_name,
        baui.mobile ,
        baui.email
        from
        bc_alarm_user_info baui
        left join bc_alarm_notice_group_detail bangd on
        baui.account = bangd.account
        where
        bangd.alarm_notice_group_id in
        <foreach collection="array" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
