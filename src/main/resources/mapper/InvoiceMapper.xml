<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.InvoiceMapper">

    <select id="getInvoiceList" resultType="com.haier.devops.bill.common.entity.Invoice">
        select * from bc_invoice where vendor = #{vendor} and billing_cycle = #{billingCycle}
        <if test="account != null and account.length > 0">
            and account_name in
            <foreach collection="account" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
