<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BillSubstitutionTaskMapper">
    <update id="updateStageAndProcessByTaskId">
        update bc_bill_substitution_task set stage = #{stage}, process = #{process} where sub_task_id = #{subTaskId}
    </update>
    <update id="updateErrorInfo">
        update bc_bill_substitution_task set err = #{errorInfo} where sub_task_id = #{subTaskId}
    </update>
    <select id="getPendingMigrationTaskByType" resultType="com.haier.devops.bill.common.entity.BillSubstitutionTask">
        select *
        from bc_bill_substitution_task
        where stage = 'migration' and process > 1
        <if test="type != null">
            and sub_type = #{type}
        </if>
        <if test="billingCycle != null">
            and billing_cycle = #{billingCycle}
        </if>
        order by billing_cycle asc
    </select>
    <select id="getPendingCalculationTaskByType" resultType="com.haier.devops.bill.common.entity.BillSubstitutionTask">
        select *
        from bc_bill_substitution_task
        where (stage = 'migration' and process = 1)
            or (stage = 'calculation' and process > 1)
            <if test="type != null">
                and sub_type = #{type}
            </if>
            <if test="billingCycle != null">
                and billing_cycle = #{billingCycle}
            </if>
        order by billing_cycle asc
    </select>
    <select id="getPendingSubstitutionTaskByType" resultType="com.haier.devops.bill.common.entity.BillSubstitutionTask">
        select *
        from bc_bill_substitution_task
        where (stage = 'cleaning' and process = 1)
        or (stage = 'substitution' and process > 1)
        <if test="type != null">
            and sub_type = #{type}
        </if>
        <if test="billingCycle != null">
            and billing_cycle = #{billingCycle}
        </if>
        order by billing_cycle asc
    </select>
    <select id="getPendingCleaningTaskByType"
            resultType="com.haier.devops.bill.common.entity.BillSubstitutionTask">
        select *
        from bc_bill_substitution_task
        where (stage = 'calculation' and process = 1)
        or (stage = 'cleaning' and process > 1)
        <if test="type != null">
            and sub_type = #{type}
        </if>
        <if test="billingCycle != null">
            and billing_cycle = #{billingCycle}
        </if>
        order by billing_cycle asc
    </select>
</mapper>
