<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BcDefaultScodeMapper">

    <resultMap type="com.haier.devops.bill.common.entity.BcDefaultScode" id="BcDefaultScodeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="scode" column="scode" jdbcType="VARCHAR"/>
        <result property="uid" column="uid" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

