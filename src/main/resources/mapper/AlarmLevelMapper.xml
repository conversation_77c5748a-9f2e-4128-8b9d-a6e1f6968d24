<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.CpAlarmLevelMapper">
    <!-- 根据配置查告警级别 -->
    <select id="getAlarmLevelListByAlarmConfigurationId" parameterType="java.lang.Integer" resultType="com.haier.devops.bill.common.vo.CpAlarmLevelVo">
        select
            bcal.*,
            sdi.item_text level_name
        from
            bc_cp_alarm_level bcal
                join sys_dict_item sdi on
                        bcal.`level` = sdi.item_value
                    and sdi.status = 1
                join sys_dict sd on
                sdi.dict_id = sd.id
        where
            sd.dict_code = 'CLOUD_PRODUCT_ALARM_LEVEL'
          and sd.del_flag = '0'
          and bcal.cp_alarm_configuration_id = #{id}
    </select>

    <select id="getAlarmLevelListByGroupProduct" parameterType="java.lang.String" resultType="java.util.Map">
        select
        concat(ifnull(bcac.app_scode,''),bcac.product_id) productId,
        sdi2.item_text alarmTypeName,
        group_concat(concat(bcal.id,'-',sdi.item_value ,'-',sdi.item_text , '-', amplitude_ratio) order by amplitude_ratio) levelInfo
        from
        bc_cp_alarm_level bcal
        join sys_dict_item sdi on
        bcal.`level` = sdi.item_value
        and sdi.status = 1
        join sys_dict sd on
        sdi.dict_id = sd.id
        and sd.del_flag = '0'
        and sd.dict_code = 'CLOUD_PRODUCT_ALARM_LEVEL'
        join bc_cp_alarm_configuration bcac
        on
        bcal.cp_alarm_configuration_id = bcac.id
        and bcac.del_flag = '0'
        and bcac.product_id in
        <foreach collection = "list" separator = "," open = "(" close = ")" item = "item">
            #{item}
        </foreach>
        join sys_dict_item sdi2 on
        bcac.alarm_type = sdi2.item_value
        and sdi2.status = 1
        join sys_dict sd2 on
        sdi2.dict_id = sd2.id
        and sd2.del_flag = '0'
        and sd2.dict_code = 'CLOUD_PRODUCT_ALARM_TYPE'
        group by
        concat(ifnull(bcac.app_scode,''),bcac.product_id)
        order by bcac.create_time desc
    </select>
</mapper>
