<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BudgetConfigMapper">

    <select id="queryByScodesAndBillingCycle" resultType="com.haier.devops.bill.common.vo.BudgetConfigVo">
        select r.scode, c.*
        from bc_budget_config c
            inner join
               (select r1.*
                from bc_budget_app_rel r1
                                     <!-- 找到应用最近账期的预算配置 -->
                         inner join (select vendor, scode, max(billing_cycle) as billing_cycle from bc_budget_app_rel group by vendor, scode) r2
                                    on r1.scode = r2.scode and r1.billing_cycle = r2.billing_cycle
                                    and r1.vendor = #{vendor} and r2.vendor = #{vendor}
                                    and r1.scode in
                                    <foreach collection="scodes" open="(" close=")" separator="," item="scode">
                                        #{scode}
                                    </foreach>
               ) r
             on c.budget_code = r.budget_code
    </select>
    <select id="queryList" resultType="com.haier.devops.bill.common.vo.BudgetConfigVo">
        select
            r.id,
            c.category,
            c.budget_code,
            c.budget_name,
            c.total_amount,
            c.available_amount,
            c.year,
            c.cloud_type,
            r.scode,
            r.billing_cycle,
            p.sub_product_id,
            p.sub_product_name,
            p.owner as user_code,
            p.owner_name as user_name,
            r.vendor,
            r.billing_cycle,
            r.create_time,
            r.update_time
        from bc_budget_config c inner join bc_budget_app_rel r on c.budget_code = r.budget_code
                                left join bc_hds_sub_products p on r.scode = p.app_scode
        <where>
            1 = 1
            <if test="subProductId != null and subProductId != ''">
                and p.sub_product_id = #{subProductId}
            </if>
            <if test="budgetCode != null and budgetCode != ''">
                and r.budget_code = #{budgetCode}
            </if>
            <if test="userCode != null and userCode != ''">
                and p.owner = #{userCode}
            </if>
            <if test="vendor != null and vendor != ''">
                and r.vendor = #{vendor}
            </if>
        </where>
        order by r.create_time desc
    </select>
</mapper>
