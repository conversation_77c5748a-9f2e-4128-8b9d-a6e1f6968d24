<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ServerUsageMapper">
    <select id="getResourceUsageQualified" resultType="java.lang.Integer">
        select count(1) from server_usage su where resource_usage_qualified = #{resourceUsageQualified} and `day` = date_format(now(),'%Y-%m-%d')  and
        <choose>
            <when test="scodes != null and scodes.size > 0">
                s_code in
                <foreach item="item" collection="scodes" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1 = 2
            </otherwise>
        </choose>
    </select>

</mapper>
