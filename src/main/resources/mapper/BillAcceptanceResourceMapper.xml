<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BillAcceptanceResourceMapper">

    <select id="selectBillsNotReservedAmongIdBetweenDate"
            resultType="com.haier.devops.bill.common.entity.BillAcceptanceResource">
        select r.*
        from bc_bill_acceptance_resource r
        where r.aggregated_id in
          <foreach collection="aggregatedIds" item="aggregatedId" separator="," open="(" close=")">
              #{aggregatedId}
          </foreach>
          and r.check_status != 'pending'
          <if test="ranges != null and ranges.length > 0">
              and (
                  <foreach collection="ranges" item="range" separator="or">
                      <![CDATA[
                      (#{range.start} <= r.billing_cycle and r.billing_cycle < #{range.end})
                      ]]>
                  </foreach>
              )
          </if>
    </select>
</mapper>
