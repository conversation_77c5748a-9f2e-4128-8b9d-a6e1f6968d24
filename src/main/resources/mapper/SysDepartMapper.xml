<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.SysDepartMapper">

    <resultMap type="com.haier.devops.bill.common.entity.SysDepart" id="SysDepartMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="departName" column="depart_name" jdbcType="VARCHAR"/>
        <result property="departNameEn" column="depart_name_en" jdbcType="VARCHAR"/>
        <result property="departNameAbbr" column="depart_name_abbr" jdbcType="VARCHAR"/>
        <result property="departOrder" column="depart_order" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="orgCategory" column="org_category" jdbcType="VARCHAR"/>
        <result property="orgType" column="org_type" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="fax" column="fax" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="memo" column="memo" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="qywxIdentifier" column="qywx_identifier" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="INTEGER"/>
        <result property="ifShow" column="if_show" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

