<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.HostInfoMapper">

    <select id="selectByScodes" resultType="com.haier.devops.bill.common.entity.HostInfo">
        select h.* from rc_host_info h inner join cloud_account a on h.vendor = a.vendor and h.account_name = a.account_name
        <foreach collection="currencies" item="currency" separator=" " open="" close="">
            and a.currency = #{currency}
        </foreach>
        where h.is_deleted != 1
        <if test="scodes != null and scodes.size() > 0">
            and h.scode in
            <foreach collection="scodes" item="scode" open="(" close=")" separator=",">
                #{scode}
            </foreach>
        </if>
        <if test="scodes == null or scodes.size() == 0">
            and 1 = 2
        </if>
    </select>
</mapper>
