<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.HdsSubProductsMapper">
    <resultMap id="hdsSubProductsMap" type="com.haier.devops.bill.common.entity.HdsSubProducts">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="app_scode" jdbcType="VARCHAR" property="appScode" />
        <result column="app_short_name" jdbcType="VARCHAR" property="appShortName" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="business_domain_ids" jdbcType="VARCHAR" property="businessDomainIds" />
        <result column="business_domains" jdbcType="VARCHAR" property="businessDomains" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="it_manager_name" jdbcType="VARCHAR" property="itManagerName" />
        <result column="it_manager" jdbcType="VARCHAR" property="itManager" />
        <result column="it_ops_manager" jdbcType="VARCHAR" property="itOpsManager" />
        <result column="it_ops_manager_name" jdbcType="VARCHAR" property="itOpsManagerName" />
        <result column="owner" jdbcType="VARCHAR" property="owner" />
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
        <result column="org_id" jdbcType="VARCHAR" property="orgId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="del_flag" jdbcType="VARCHAR" property="delFlag" />
        <result column="product_id" jdbcType="VARCHAR" property="productId" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="sub_product_id" jdbcType="VARCHAR" property="subProductId" />
        <result column="sub_product_name" jdbcType="VARCHAR" property="subProductName" />
        <result column="sub_product_name_en" jdbcType="VARCHAR" property="subProductNameEn" />
    </resultMap>

    <select id="getAuthorizedScodes" resultType="java.lang.String">
        SELECT app_scode
        FROM bc_hds_sub_products
        WHERE <!-- del_flag = 0 and --> FIND_IN_SET(#{orgId}, org_id)
    </select>
    <select id="getAuthorizedSubProducts" resultMap="hdsSubProductsMap">
        SELECT *
        FROM bc_hds_sub_products
        WHERE <!-- del_flag = 0 and --> FIND_IN_SET(#{orgId}, org_id)
    </select>
    <delete id="deleteByAppScodeIn" parameterType="java.lang.String">
        update bc_hds_sub_products set del_flag = 1 where app_scode in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getAllAppScodeList" resultType="java.lang.String">
        select app_scode
        from bc_hds_sub_products
        <!-- where del_flag = 0; -->
    </select>

    <select id="getDeptFilteredSubProducts" resultMap="hdsSubProductsMap">
        SELECT
            app_scode,
            id,
            app_short_name,
            app_name,
            business_domain_ids,
            business_domains,
            description,
            it_manager_name,
            it_manager,
            it_ops_manager,
            it_ops_manager_name,
            `owner`,
            owner_name,
            create_time,
            org_id,
            create_by,
            update_time,
            update_by,
            del_flag,
            product_id,
            product_name,
            sub_product_id,
            sub_product_name,
            sub_product_name_en
        FROM
            bc_hds_sub_products
        WHERE
            <!-- del_flag = 0 AND -->
        <if test="list != null and list.size() > 0">
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                FIND_IN_SET(
                #{item},
                org_id)
            </foreach>
        </if>
    </select>
    <select id="getDomainSubProductVoList" resultType="com.haier.devops.bill.common.vo.DomainSubProductVo">
        select
            business_domain_ids as domain_code,
            business_domains as domain_name,
            app_short_name,
            app_scode as scode,
            sub_product_id,
            sub_product_name,
            sub_product_name_en
        from bc_hds_sub_products
        where app_scode is not null
        <if test="domainIds != null and domainIds.size() > 0">
            and business_domain_ids in
            <foreach collection="domainIds" item="item" separator="," open="(" close=")">
             #{item}
            </foreach>
        </if>
        <if test="domainIds == null or domainIds.size() == 0">
            and 1 != 1
        </if>
        order by business_domain_ids, sub_product_id
    </select>
    <select id="getAllDomainSubProductVoList" resultType="com.haier.devops.bill.common.vo.DomainSubProductVo">
        select
            business_domain_ids as domain_code,
            business_domains as domain_name,
            app_short_name,
            app_scode as scode,
            sub_product_id,
            sub_product_name,
            sub_product_name_en
        from bc_hds_sub_products
        where app_scode is not null
        order by business_domain_ids, sub_product_id
    </select>
    <select id="searchDomain" resultType="com.haier.devops.bill.common.entity.HdsSubProducts">
        select business_domain_ids, business_domains
        from bc_hds_sub_products
        <if test="query != null and query != ''">
            where business_domain_ids like CONCAT('%', #{query}, '%')
            or business_domains like CONCAT('%', #{query}, '%')
        </if>
        group by business_domain_ids, business_domains
    </select>
</mapper>
