<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.BillGatheredMapper">

    <!-- 获取前两日账单汇总数据 -->
    <select id="getDailyCostInceaseAlarm" parameterType="java.util.Map" resultType="com.haier.devops.bill.common.vo.BillGatheredVo">
        select
        brbp.id product_id,
        bhsp.app_name,
        bcpo.scode,
        bhsp.owner ,
        bcpo.supplement_id ,
        bbg.billing_cycle,
        bbg.aggregated_id,
        bcpo.vendor,
        bcpo.account_id ,
        bcpo.account_name ,
        bcpo.instance_id ,
        bcpo.project_code ,
        bcpo.project_name ,
        bcpo.product_code,
        bcpo.product_name ,
        bcac.app_scode ,
        bcac.warn_amount,
        group_concat(bbg.payable_sum order by bbg.billing_cycle desc) cost_summary
        from
        bc_aggregated_bill bbg
        inner join bc_bill_task a on bbg.task_id = a.task_id
        join bc_cmdb_product_overview bcpo
        on
        bbg.aggregated_id = bcpo.aggregated_id
        join bc_raw_bill_products brbp on
        bcpo.vendor = brbp.vendor
        and bcpo.product_code = brbp.product_code
        join bc_hds_sub_products bhsp on
        bcpo.scode = bhsp.app_scode
        join bc_cp_alarm_configuration bcac on
        bcac.product_id = brbp.id
            <choose>
                <when test="isAllAlarm == 1">
                    and (bcac.app_scode is null or bcac.app_scode = '')
                </when>
                <otherwise>
                    and bcac.app_scode = bbg.scode
                </otherwise>
            </choose>
        and bcac.is_enable_alarm = 1
        and bcac.start_valid_period_time &lt;= date_format(now(),'%Y-%m-%d')
        and bcac.end_valid_period_time &gt;= date_format(now(),'%Y-%m-%d')
        where
        a.status = 1
        and a.stage = 'aggregation'
        and bbg.billing_cycle &gt;= #{startDate}
        and bbg.billing_cycle &lt;= #{endDate}
        and bbg.aggregated_id is not null
  <!--      and bbg.payable_sum &gt;= bcac.warn_amount -->
        and bbg.granularity = 'day'
        group by
        bbg.aggregated_id
        order by bbg.billing_cycle desc
    </select>

    <select id="getConfigDetialList" parameterType="java.lang.String" resultType="java.util.Map">
        select
        brbp.`product_code`,
        brbp.id product_id,
        `app_scode`,
        bcac.`warn_amount`
        from
        `bc_cp_alarm_configuration` bcac JOIN
        `bc_raw_bill_products` brbp on bcac.`product_id` = brbp.`id`
        where
        bcac.is_enable_alarm = 1
        and bcac.start_valid_period_time &lt;= date_format(now(), '%Y-%m-%d')
        and bcac.end_valid_period_time &gt;= date_format(now(), '%Y-%m-%d')
        and brbp.`vendor` = #{vendor}
        and brbp.del_flag = '0'
        and bcac.`del_flag` = '0'
    </select>

    <select id="getDailyCostInceaseAlarmV2" parameterType="java.util.Map" resultType="com.haier.devops.bill.common.vo.BillGatheredVo">
        select
            #{productId} product_id,
            bcpo.scode,
            bcpo.supplement_id,
            bbg.billing_cycle,
            bbg.aggregated_id,
            bcpo.vendor,
            bcpo.account_id,
            bcpo.account_name,
            bcpo.instance_id,
            bcpo.project_code,
            bcpo.project_name,
            bcpo.product_code,
            bcpo.product_name,
            #{appScode} app_scode,
            #{warnAmount} warn_amount,
            group_concat(
                    bbg.payable_sum
                        order by
      bbg.billing_cycle desc
                ) cost_summary
        from
            bc_aggregated_bill bbg
                inner join bc_bill_task a on bbg.task_id = a.task_id
                join bc_cmdb_product_overview bcpo on bbg.aggregated_id = bcpo.aggregated_id
        where
            a.status = 1
          and bcpo.`product_code`  = #{productCode}
          and bcpo.`vendor` = #{vendor}
        <if test="appScode != null and appScode != ''">
            and bbg.scode = #{appScode}
        </if>
          and a.stage = 'aggregation'
          and bbg.`billing_cycle` BETWEEN #{startDate} and #{endDate}
          and bbg.granularity = 'day'
        group by
            bbg.aggregated_id
        order by
            bbg.billing_cycle desc
    </select>

</mapper>
