<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.SysDictItemMapper">
    <!-- 根据配置查告警级别 -->
    <select id="getDictItemByItemValueArray" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AlarmNoticeObjVo">
        select sdi.item_value notice_obj_id,sdi.item_text notice_obj_name,null email,null mobile from sys_dict_item sdi where sdi.item_value in
        <foreach collection="array" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and sdi.dict_id in
        (select sd.id from sys_dict sd where sd.dict_code = #{dictCode} and sd.del_flag = 0)
    </select>

    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.SysDictItemDTO" resultType="com.haier.devops.bill.common.entity.SysDictItem">
        SELECT * FROM sys_dict_item where dict_id = #{dto.dictId}
        <if test="dto.status != null">
            AND status = #{dto.status}
        </if>
        <if test="dto.itemText != null and dto.itemText != ''">
            AND item_text LIKE CONCAT('%',#{dto.itemText},'%')
        </if>
        ORDER BY create_time desc
    </select>
</mapper>
