<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RawBillProductsMapper">
    <select id="queryList" resultType="java.util.Map">
        select distinct alias_code aliasCode,alias_name productName from bc_raw_bill_products
        ${ew.customSqlSegment}
    </select>
</mapper>

