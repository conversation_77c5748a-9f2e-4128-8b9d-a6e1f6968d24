<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.AcceptanceBlacklistMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.haier.devops.bill.common.entity.AcceptanceBlacklist">
        <id column="id" property="id" />
        <result column="vendor" property="vendor" />
        <result column="sys_code" property="sysCode" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vendor, sys_code, remark, create_time, update_time
    </sql>
    
    <!-- 检查指定的云厂商和系统编码是否在黑名单中 -->
    <select id="checkInBlacklist" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM bc_acceptance_blacklist
        WHERE vendor = #{vendor} AND sys_code = #{sysCode}
    </select>

</mapper>
