<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AlarmNoticeGroupDetailMapper">

    <!-- 分页查询云产品告警组列表 -->
    <select id="getAlarmNoticeGroupDetailListByGroupId" parameterType="java.lang.Integer" resultType="com.haier.devops.bill.common.vo.AlarmNoticeGroupDetailVo">
    select
    baui.account,
    baui.name,
    baui.email,
    baui.mobile
    from
    bc_alarm_notice_group_detail bangd
    left join
    bc_alarm_user_info baui
    on
    bangd.account = baui.account
    where bangd.alarm_notice_group_id = #{groupId}
    </select>

    <select id="getAccountListByGroupId" parameterType="java.lang.Integer" resultType="java.lang.String">
        select
            bangd.account
        from
            bc_alarm_notice_group_detail bangd
        where bangd.alarm_notice_group_id = #{groupId}
    </select>
</mapper>
