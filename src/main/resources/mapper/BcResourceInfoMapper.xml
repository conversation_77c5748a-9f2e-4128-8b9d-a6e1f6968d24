<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.BcResourceInfoMapper">

    <resultMap type="com.haier.devops.bill.common.entity.BcResourceInfo" id="BcResourceInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="service" column="service" jdbcType="VARCHAR"/>
        <result property="resourceType" column="resource_type" jdbcType="VARCHAR"/>
        <result property="resourceGroupId" column="resource_group_id" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="regionId" column="region_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

