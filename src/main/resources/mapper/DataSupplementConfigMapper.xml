<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.DataSupplementConfigMapper">

    <resultMap type="com.haier.devops.bill.common.entity.DataSupplementConfig" id="DataSupplementConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="className" column="class_name" jdbcType="VARCHAR"/>
        <result property="methodName" column="method_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

