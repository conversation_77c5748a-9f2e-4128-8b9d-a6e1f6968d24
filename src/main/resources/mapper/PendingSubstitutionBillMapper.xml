<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.PendingSubstitutionBillMapper">

    <select id="getSumBySubTaskId" resultType="java.lang.String">
        select round(sum(summer), 2) from bc_pending_substitution_bill where sub_task_id = #{subTaskId}
    </select>
</mapper>
