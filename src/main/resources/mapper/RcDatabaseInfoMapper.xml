<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RcDatabaseInfoMapper">

    <resultMap type="com.haier.devops.bill.common.entity.RcDatabaseInfo" id="RcDatabaseInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="instanceId" column="instance_id" jdbcType="VARCHAR"/>
        <result property="instanceName" column="instance_name" jdbcType="VARCHAR"/>
        <result property="instanceType" column="instance_type" jdbcType="VARCHAR"/>
        <result property="instanceRole" column="instance_role" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="primaryInstanceId" column="primary_instance_id" jdbcType="VARCHAR"/>
        <result property="hostInsId" column="host_ins_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="classCode" column="class_code" jdbcType="VARCHAR"/>
        <result property="chargeType" column="charge_type" jdbcType="VARCHAR"/>
        <result property="creationTime" column="creation_time" jdbcType="TIMESTAMP"/>
        <result property="expiredTime" column="expired_time" jdbcType="TIMESTAMP"/>
        <result property="host" column="host" jdbcType="VARCHAR"/>
        <result property="port" column="port" jdbcType="INTEGER"/>
        <result property="engineType" column="engine_type" jdbcType="VARCHAR"/>
        <result property="engineVersion" column="engine_version" jdbcType="VARCHAR"/>
        <result property="scode" column="scode" jdbcType="VARCHAR"/>
        <result property="project" column="project" jdbcType="VARCHAR"/>
        <result property="env" column="env" jdbcType="VARCHAR"/>
        <result property="resourceGroup" column="resource_group" jdbcType="VARCHAR"/>
        <result property="cpu" column="cpu" jdbcType="INTEGER"/>
        <result property="memory" column="memory" jdbcType="INTEGER"/>
        <result property="diskSize" column="disk_size" jdbcType="INTEGER"/>
        <result property="diskType" column="disk_type" jdbcType="VARCHAR"/>
        <result property="vpcId" column="vpc_id" jdbcType="VARCHAR"/>
        <result property="subnetId" column="subnet_id" jdbcType="VARCHAR"/>
        <result property="dgDomain" column="dg_domain" jdbcType="VARCHAR"/>
        <result property="dgId" column="dg_id" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="zone" column="zone" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
        <result property="uniRegionId" column="uni_region_id" jdbcType="VARCHAR"/>
        <result property="domain" column="domain" jdbcType="VARCHAR"/>
        <result property="team" column="team" jdbcType="VARCHAR"/>
        <result property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <result property="ownerName" column="owner_name" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

