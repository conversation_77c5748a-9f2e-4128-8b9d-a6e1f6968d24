<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.DataPrivilegeMapper">
    <select id="selectUserDataPrivilegeByDimension"
            resultType="com.haier.devops.bill.common.vo.UserDataPrivilegeVo">
        select p.privilege_code,
               p.privilege_name,
               p.data_range,
               p.privilege_type,
               p.description,
               r.user_id
        from data_privilege_dimension d
                 inner join data_privilege p on d.dimension_code = p.dimension_code
            and d.is_deleted != 1 and p.is_deleted != 1 and d.dimension_code = #{dimension}
                 inner join data_privilege_user_rel r on r.privilege_id = p.id
            and r.user_id = #{userId}
    </select>
</mapper>
