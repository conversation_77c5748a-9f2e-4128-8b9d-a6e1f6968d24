<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RawBillMapper">
    <resultMap id="ColumnValueResultMap" type="com.haier.devops.bill.common.vo.ColumnValueVo">
        <result column="col_value" jdbcType="VARCHAR" property="value" />
        <result column="times" jdbcType="VARCHAR" property="count" />
    </resultMap>

    <select id="selectCostProcedure" resultType="com.haier.devops.bill.common.vo.CostProcedureVo">
        SELECT
            billing_item,
            list_price,
            list_price_unit,
            SUM(`usage`) AS `usage`,
            usage_unit
        FROM bc_raw_bill
        WHERE
        <![CDATA[
            vendor = #{vendor}
            AND list_price != ''
            AND list_price_unit != ''
            AND usage_unit != ''
            AND cost_unit = #{costUnit}
            AND product_code = #{productCode}
            AND billing_cycle >= #{startBillingCycle}
            AND billing_cycle < #{endBillingCycle}
        ]]>
        GROUP BY
            billing_item,
            list_price,
            list_price_unit
    </select>

    <select id="getBillCharges" resultType="com.haier.devops.bill.common.vo.BillChargesVo">
        SELECT
            t.vendor,
            t.product_code,
            t.product_name,
            t.billing_item
        FROM
            bc_refined_raw_bill t
        GROUP BY
            t.vendor,
            t.product_code,
            t.product_name,
            t.billing_item
        ORDER BY
            t.vendor,
            t.product_code
    </select>

    <update id="updateBatchById" parameterType="list">
        <foreach collection="list" item="s" separator=";">
            update
                bc_raw_bill
            set
                <if test="s.scode != null and s.scode != ''">
                scode = #{s.scode},
                </if>
            <if test="s.ip != null and s.ip != ''">
            ip = #{s.ip},
            </if>
            <if test="s.instanceId != null and s.instanceId != ''">
            instance_id = #{s.instanceId},
            </if>
            <if test="s.instanceName != null and s.instanceName != ''">
                instance_name = #{s.instanceName},
            </if>
            <if test="s.projectCode != null and s.projectCode != ''">
            project_code = #{s.projectCode},
            </if>
            <if test="s.projectName != null and s.projectName != ''">
            project_name = #{s.projectName},
            </if>
            <if test="s.expenseType != null and s.expenseType != ''">
            expense_type = #{s.expenseType},
            </if>
            processing_status = 1
            where
            id = #{s.id}
        </foreach>
    </update>

    <select id="getBillByVendor" resultType="com.haier.devops.bill.common.entity.RawBill" parameterType="string">
        SELECT
            t.id,
            t.create_time,
            t.update_time,
            t.vendor,
            t.account_name,
            t.account_id,
            t.granularity,
            t.billing_cycle,
            t.product_code,
            t.product_name,
            t.resource_group,
            t.resource_id,
            t.service_period,
            t.service_period_unit,
            t.region,
            t.zone,
            t.billing_item,
            t.list_price,
            t.list_price_unit,
            t.`usage`,
            t.usage_unit,
            t.cost,
            t.cost_unit,
            t.currency,
            t.content,
            t.subscription_type,
            t.expense_type,
            t.scode,
            t.project_name,
            t.project_code,
            t.product_detail,
            t.split_item,
            t.ip,
            t.instance_id,
            t.instance_name,
            t.billing_type,
            e.env
        FROM
            bc_raw_bill AS t
                LEFT JOIN ( SELECT instance_id, env FROM rc_host_info UNION SELECT instance_id, env FROM rc_database_info UNION SELECT instance_id, env FROM rc_middleware_info ) e ON t.instance_id = e.instance_id
        WHERE
            t.vendor = #{vendor}
          AND t.billing_cycle = #{billingCycle}
    </select>
</mapper>
