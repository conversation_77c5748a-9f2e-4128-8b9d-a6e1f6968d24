<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AlarmConfigurationMapper">
    <sql id="Base_Column_List">
     id, app_scode, product_id, is_enable_alarm, start_valid_period_time, end_valid_period_time, alarm_type, create_time, create_by, update_time, update_by, del_flag,warn_amount
    </sql>

    <!-- 分页查询云产品告警配置列表 -->
    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.AlarmConfigurationDTO" resultType="com.haier.devops.bill.common.vo.CpAlarmConfigurationVo">
        select
        bcac.*,
        brbp.vendor,
        brbp.product_code,
        brbp.product_name,
        bhsp.app_short_name,
        bhsp.app_name,
        sdi.item_text as alarm_type_name,
        sdi2.item_text as is_enable_alarm_name,
        ifnull(m.alarm_num,0) alarm_num
        from
        bc_cp_alarm_configuration bcac
        left join(select bcal.product_id,bcal.app_scode,count(1) alarm_num from bc_cp_alarm_log bcal group by bcal.product_id,bcal.app_scode) m
        on ifnull(bcac.app_scode,0) = ifnull(m.app_scode,0) and bcac.product_id = m.product_id
        join bc_raw_bill_products brbp on
        bcac.product_id = brbp.id
        and brbp.del_flag = 0
        left join bc_hds_sub_products bhsp on
        bcac.app_scode = bhsp.app_scode
        and bhsp.del_flag = 0
        join sys_dict_item sdi on
        bcac.alarm_type = sdi.item_value
        and sdi.status = 1
        join sys_dict sd on
        sdi.dict_id = sd.id
        join sys_dict_item sdi2 on
        bcac.is_enable_alarm = sdi2.item_value
        and sdi2.status = 1
        join sys_dict sd2 on
        sdi2.dict_id = sd2.id
        where
        bcac.del_flag = 0
        and sd.dict_code = 'CLOUD_PRODUCT_ALARM_TYPE'
        and sd.del_flag = 0
        and sd2.dict_code = 'CLOUD_PRODUCT_ALARM_ENABLE_STATUS'
        and sd2.del_flag = 0
        <if test="dto.productId != null and dto.productId != ''">
            and bcac.product_id = #{dto.productId}
        </if>
        <if test="dto.appScode != null and dto.appScode != ''">
            and bcac.app_scode = #{dto.appScode}
        </if>
        <if test="dto.alarmType != null">
            and bcac.alarm_type = #{dto.alarmType}
        </if>
        <if test="dto.isEnableAlarm != null">
            and bcac.is_enable_alarm = #{dto.isEnableAlarm}
        </if>
        <if test="dto.id != null and dto.id != ''">
            and bcac.id = #{dto.id}
        </if>
        <if test="dto.createBy != null and dto.createBy != ''">
            and bcac.create_by = #{dto.createBy}
        </if>
        <if test="dto.vendor != null and dto.vendor != ''">
            and brbp.vendor = #{dto.vendor}
        </if>
    </select>

    <update id="updateAlarmConfigurationStatusByPeriod" parameterType="java.lang.Integer">
       <choose>
           <when test="isEnableAlarm == 1">
               update
               bc_cp_alarm_configuration bcac
               set is_enable_alarm = 1
               where
               bcac.is_enable_alarm = 0
               and start_valid_period_time &lt;= date_format(now(),'%Y-%m-%d')
               and end_valid_period_time &gt;= date_format(now(),'%Y-%m-%d')
           </when>
           <otherwise>
               update
               bc_cp_alarm_configuration bcac
               set is_enable_alarm = 0
               where
               bcac.is_enable_alarm = 1
               and (start_valid_period_time &gt; date_format(now(),'%Y-%m-%d')
               or end_valid_period_time &lt; date_format(now(),'%Y-%m-%d')
           </otherwise>
       </choose>
    </update>

</mapper>
