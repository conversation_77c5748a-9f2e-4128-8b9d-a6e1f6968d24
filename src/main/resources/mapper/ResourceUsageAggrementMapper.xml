<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.aws.commitment.mapper.ResourceUsageAgreementMapper">

    <select id="queryPendingSavingPlanResource" resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">
        select
            a.id,
            a.vendor,
            a.resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            h.vendor,
            h.account_name as account,
            h.region,
            h.os_name as os,
            h.class_code as instance_type
        from bc_resource_usage_agreement a inner join rc_host_info h
            on a.vendor = h.vendor and a.resource_id = h.instance_id
        where
        <![CDATA[
            a.enable_time < now()
        ]]>
          and h.vendor = 'aws'
          <choose>
              <when test="planType != null and planType.length > 0">
                  and a.commit_type in
                  <foreach collection="planType" open="(" close=")" separator="," item="item">
                      #{item}
                  </foreach>
              </when>
              <otherwise>
                  and a.commit_type in ('Compute', 'EC2Instance')
              </otherwise>
          </choose>
        and a.purchased_offering_id is null
    </select>
    <select id="queryPendingReservedInstanceResource"
            resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">
        select
            a.id,
            a.vendor,
            a.resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            r.vendor,
            r.account_name as account,
            r.region,
            r.class_code as instance_type
        from bc_resource_usage_agreement a inner join rc_database_info r
            on a.vendor = r.vendor and a.resource_id = r.instance_id
        where
        <![CDATA[
            a.enable_time < now()
        ]]>
          and r.vendor = 'aws'
          and a.commit_type in ('Ri')
        and a.offering_id is null

    </select>
    <select id="queryById" resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">

        select
            a.id,
            a.vendor,
            a.resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            r.vendor,
            r.account_name as account,
            r.region,
            r.class_code as instance_type
        from bc_resource_usage_agreement a inner join rc_database_info r
                                                      on a.vendor = r.vendor and a.resource_id = r.instance_id
        where
            a.id = #{id}

    </select>
    <select id="queryByIds" resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">
        select
            a.id,
            a.vendor,
            a.resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            r.vendor,
            r.account_name as account,
            r.region,
            r.class_code as instance_type
        from bc_resource_usage_agreement a inner join rc_database_info r
                                                      on a.vendor = r.vendor and a.resource_id = r.instance_id
        where a.id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>
    <select id="query" resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">
        select
            a.id,
            r.vendor,
            r.instance_id as resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            r.vendor,
            r.account_name as account,
            r.cpu,
            r.memory,
            r.creation_time,
            <choose>
                <when test="type == 'ecs'">
                    r.host_type as engine_type,
                    r.os_type as engine_version,
                </when>
                <when test="type == 'rds' or type == 'redis'">
                    r.engine_type,
                    r.engine_version,
                </when>
            </choose>
            r.region,
            r.class_code as instance_type
        from bc_resource_usage_agreement a right join
            <choose>
                <when test="type == 'ecs'">rc_host_info r</when>
                <when test="type == 'rds'">(select * from rc_database_info where engine_type != 'redis') r</when>
                <when test="type == 'redis'">(select * from rc_database_info where engine_type = 'redis') r</when>
            </choose>
        on a.vendor = r.vendor and a.resource_id = r.instance_id and r.vendor in ('aws', 'azure')
        <where>
                <![CDATA[
                    a.enable_time <= now() and now() <= DATE_ADD(a.enable_time, INTERVAL duration SECOND)
                ]]>
            <if test="filter != null">
                <if test="filter.vendor != null and filter.vendor != ''">
                    and r.vendor = #{filter.vendor}
                </if>
                <if test="filter.resourceId != null and filter.resourceId != ''">
                    and r.instance_id = #{filter.resourceId}
                </if>
                <if test="filter.duration != null">
                    and a.duration = #{filter.duration}
                </if>
                <if test="filter.enableTime != null">
                    and a.enable_time = #{filter.enableTime}
                </if>
                <if test="filter.commitType != null and filter.commitType != ''">
                    and a.commit_type = #{filter.commitType}
                </if>
            </if>
            <if test="purchased != null and purchased == 1">
                and a.purchased_offering_id is not null
            </if>
            <if test="purchased != null and purchased == 0">
                and a.purchased_offering_id is null
            </if>
            <if test="start != null">
                <![CDATA[
                    and r.creation_time >= #{start}
                ]]>
            </if>
            <if test="end != null">
                <![CDATA[
                    and r.creation_time <= #{end}
                ]]>
            </if>
        </where>
        order by a.create_time desc, r.update_time desc
    </select>

    <select id="queryHistory" resultType="com.haier.devops.bill.aws.commitment.vo.ResourceUsageAgreementVo">
        select
            a.id,
            r.vendor,
            r.instance_id as resource_id,
            a.duration,
            a.enable_time,
            a.commit_type,
            a.tenancy,
            a.product_description,
            a.commit_rate,
            a.ondemand_rate,
            a.discount,
            a.offering_id,
            a.purchased_offering_id,
            a.prepayment_option,
            a.prepayment_ratio,
            a.purchase_time,
            a.create_time,
            a.update_time,
            r.vendor,
            r.account_name as account,
            r.cpu,
            r.memory,
            r.creation_time,
            <choose>
                <when test="type == 'ecs'">
                    r.host_type as engine_type,
                    r.os_type as engine_version,
                </when>
                <when test="type == 'rds' or type == 'redis'">
                    r.engine_type,
                    r.engine_version,
                </when>
            </choose>
            r.region,
            r.class_code as instance_type
        from bc_resource_usage_agreement a right join
        <choose>
            <when test="type == 'ecs'">rc_host_info r</when>
            <when test="type == 'rds'">(select * from rc_database_info where engine_type != 'redis') r</when>
            <when test="type == 'redis'">(select * from rc_database_info where engine_type = 'redis') r</when>
        </choose>
            on a.vendor = r.vendor and a.resource_id = r.instance_id and r.vendor in ('aws', 'azure')
        where
            a.vendor = #{vendor}
            and a.resource_id = #{resourceId}
            <![CDATA[
                and a.enable_time < now()
            ]]>
            and a.purchased_offering_id is not null
            order by a.create_time desc
    </select>
</mapper>
