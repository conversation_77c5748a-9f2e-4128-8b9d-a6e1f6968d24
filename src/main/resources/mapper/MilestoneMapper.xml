<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.MilestoneMapper">

    <select id="queryByVendorAndBillingCycle" resultType="com.haier.devops.bill.common.vo.MilestoneVo">
        select r.vendor, m.*
        from vendor_milestone_project_code_rel r
                 inner join (select m1.*
                             from bc_milestone m1
                                      inner join (select project_code, start_date, end_date, delivery_date, max(convert(milestone_id, UNSIGNED )) as milestone_id
                                                  from bc_milestone
                                                  group by project_code, start_date, end_date, delivery_date
                                                  ) m2
                                                 on m1.project_code = m2.project_code and m1.milestone_id = m2.milestone_id) m
                            on r.project_code = m.project_code
        where r.vendor = #{vendor}
          <![CDATA[
          and m.start_date <= #{billingCycle}
          and #{billingCycle} <= m.delivery_date
          ]]>
        order by m.milestone_id
        limit 1
    </select>
</mapper>
