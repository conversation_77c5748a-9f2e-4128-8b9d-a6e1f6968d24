<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.CpAlarmRuleDetailMapper">

    <!-- 查询云产品告警规则明细 -->
    <select id="getAlarmRuleDetailListByAlarmLevelId" parameterType="java.lang.Integer" resultType="com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo">
        select
            bcard.notice_way ,
            bcard .notice_obj ,
            group_concat(bcard.notice_obj_id) notice_obj_id
        from
            bc_cp_alarm_rule_detail bcard
        where bcard.cp_alarm_level_id = #{id}
        group by
            bcard.notice_way,
            bcard.notice_obj
    </select>

    <select id="queryAlarmRuleDetailListByAlarmLevelId" parameterType="java.util.Map" resultType="com.haier.devops.bill.common.vo.CpAlarmRuleDetailVo">
        select
            m.notice_way,
            group_concat(distinct m.notice_obj_id) notice_obj_id
        from
            (
                select
                    bcard.notice_way,
                    bangd.account notice_obj_id,
                    bcard.cp_alarm_level_id
                from
                    bc_cp_alarm_rule_detail bcard
                        join bc_alarm_notice_group bang on
                        bcard.notice_obj_id = bang.id
                        left join bc_alarm_notice_group_detail bangd on
                        bang.id = bangd.alarm_notice_group_id
                where
                    notice_obj = #{dto.group}
                union all
                select
                    notice_way ,
                    notice_obj_id,
                    cp_alarm_level_id
                from
                    bc_cp_alarm_rule_detail bcard2
                where
                    notice_obj = #{dto.individual})m
        where m.cp_alarm_level_id = #{dto.alarmLevelId}
        group by
            m.notice_way
    </select>
</mapper>
