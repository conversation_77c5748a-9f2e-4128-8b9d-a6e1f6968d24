<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.IdmOrgMapper">

    <resultMap type="com.haier.devops.bill.common.entity.IdmOrg" id="IdmOrgMap">
        <result property="orgid" column="orgid" jdbcType="VARCHAR"/>
        <result property="orgname" column="orgname" jdbcType="VARCHAR"/>
        <result property="upId" column="up_id" jdbcType="VARCHAR"/>
        <result property="endtime" column="endtime" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

