<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AggregatedBillMapper">
    <insert id="insertBatch">
        INSERT INTO bc_aggregated_bill (
            aggregated_id,
            scode,
            billing_cycle,
            summer,
            payable_sum,
            voucher_sum,
            cash_sum,
            coupon_sum,
            original_sum,
            granularity,
            task_id
        )
        VALUES
        <foreach collection="bills" item="bill" separator=",">
            (
            #{bill.aggregatedId},
            #{bill.scode},
            #{bill.billingCycle},
            #{bill.summer},
            #{bill.payableSum},
            #{bill.voucherSum},
            #{bill.cashSum},
            #{bill.couponSum},
            #{bill.originalSum},
            #{bill.granularity},
            #{bill.taskId}
            )
        </foreach>
    </insert>

    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.BillGatheredDTO" resultType="com.haier.devops.bill.common.vo.AggregatedBillVo">
        select
            bab.id,
            brb.aggregated_id,
            brb.vendor,
            brb.account_id,
            brb.account_name,
            brb.product_code,
            brb.product_name,
            brb.instance_id,
            bab.scode,
            brb.project_code,
            brb.project_name,
            bab.billing_cycle ,
            bab.summer ,
            bab.granularity ,
            bab.task_id,
            bab.create_time ,
            bab.update_time
        from
            bc_aggregated_bill bab
        inner join bc_bill_task a on bab.task_id = a.task_id
                left join bc_cmdb_product_overview
                brb on
                bab.aggregated_id = brb.aggregated_id
        <where>
            and a.status = 1
            and a.stage = 'aggregation'
            <if test="dto.gatheringId != null and dto.gatheringId != ''">
                and bab.aggregated_id = #{dto.gatheringId}
            </if>
            <if test="dto.billingCycle != null and dto.billingCycle != ''">
                and bab.billing_cycle = #{dto.billingCycle}
            </if>
            <if test="dto.granularity != null and dto.granularity != ''">
                and bab.granularity = #{dto.granularity}
            </if>
        </where>
        order by bab.scode
    </select>

    <select id="countByS" resultType="java.util.Map">
        select
            brb.vendor,
            count(1) as num
        from
            bc_cmdb_product_overview brb
        where
            brb.scode is null
           or brb.scode = ''
        group by
            brb.vendor
    </select>

    <select id="getInfoByAggregatedId" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AggregatedBillInfoVo">
        select
            scode,
            bab.billing_cycle
        from
            bc_aggregated_bill bab
            inner join bc_bill_task a on bab.task_id = a.task_id
        where
            bab.aggregated_id = #{aggregatedId}
            and bab.granularity = 'day'
          and a.status = 1
          and a.stage = 'aggregation'
        order by bab.billing_cycle
    </select>

    <select id="getBilingCycleByAggregatedId" parameterType="java.lang.String" resultType="com.haier.devops.bill.common.vo.AggregatedBillInfoVo">
        select
            min(bab.billing_cycle) as start_date,
            max(bab.billing_cycle) as end_date
        from
            bc_aggregated_bill bab
            inner join bc_bill_task a on bab.task_id = a.task_id
        where
            bab.aggregated_id = #{aggregatedId}
          and bab.granularity = 'day'
          and a.status = 1
          and a.stage = 'aggregation'
    </select>

    <update id="updateReconciliation" parameterType="com.haier.devops.bill.common.dto.ReconciliationDTO">
        update bc_aggregated_bill set
            scode = #{dto.scode} where
            aggregated_id = #{aggregatedId}
            and billing_cycle &gt;= #{dto.startDate}
            and billing_cycle &lt;= #{dto.endDate}
            and granularity = 'day'
    </update>

    <update id="updateReconciliationBatch" parameterType="com.haier.devops.bill.common.dto.ReconciliationBatchDTO">
        update bc_aggregated_bill set
            scode = #{dto.scode} where aggregated_id in
            <foreach collection="dto.aggregateIdList" item="aggregatedId" separator="," open="(" close=")">
                  #{aggregatedId}
            </foreach>
            <if test="dto.startDate != null and dto.startDate != ''">
                and billing_cycle &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate != null and dto.endDate != ''">
                and billing_cycle &lt;= #{dto.endDate}
            </if>
            and granularity = 'day'
    </update>

    <select id="getLatestAggregatedIdList" parameterType="java.lang.String" resultType="java.lang.String">
        select m.aggregated_id from (
            select bab.aggregated_id,max(bab.billing_cycle) billing_cycle from bc_aggregated_bill bab
            inner join bc_bill_task a on bab.task_id = a.task_id
            where bab.granularity = 'day'
            and a.status = 1
            and a.stage = 'aggregation'
            group by bab.aggregated_id
            ) m
        where
              (( #{endDate} = DATE_FORMAT(CURRENT_DATE, '%Y-%m-%d')
        and m.billing_cycle &lt; #{startDate})
           or (        m.billing_cycle &lt;= #{endDate}
        and m.billing_cycle &gt;= #{startDate}))
        and m.aggregated_id in
        <foreach collection="list" item="aggregatedId" separator="," open="(" close=")" >
            #{aggregatedId}
        </foreach>
    </select>

    <select id="getCurrentAggregatedIdList" parameterType="java.lang.String" resultType="java.lang.String">
        select m.aggregated_id from (
        select bab.aggregated_id,max(bab.billing_cycle) billing_cycle from bc_aggregated_bill bab
        inner join bc_bill_task a on bab.task_id = a.task_id
        where bab.granularity = 'day'
        and a.status = 1
        and a.stage = 'aggregation'
        group by bab.aggregated_id
        ) m
        where  #{endDate} = DATE_FORMAT(CURRENT_DATE, '%Y-%m-%d')
        and m.billing_cycle &lt; #{startDate}
        and m.aggregated_id in
        <foreach collection="list" item="aggregatedId" separator="," open="(" close=")" >
            #{aggregatedId}
        </foreach>
    </select>

    <select id="DimensionDetails" resultType="java.util.Map">
        select
        <if test="dataType != null and dataType == 'bill'">
            ifnull(round(sum(a.summer), 2), 0) as value,
        </if>
        <if test="dataType != null and dataType == 'instance'">
            count(a.instance_id) as value,
        </if>
        <if test="cycleType == 1"> <!-- 月 -->
            DATE_FORMAT(d.date_value, '%Y-%m') as dimensionalValue,
        </if>
        <if test="cycleType == 2"> <!-- 季 -->
            CONCAT(YEAR(d.date_value), '-Q',
            CASE
            WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
            WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
            WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
            WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
            END) as dimensionalValue,
        </if>
        <if test="cycleType == 3"> <!-- 年 -->
            YEAR(d.date_value) as dimensionalValue,
        </if>
        <if test="list != null and list.size >0">
            <foreach item="item" collection="list" separator=",">
                ${item}
            </foreach>
        </if>
        from bc_date_range d
                 inner join
             (select t1.scode,
                     t1.billing_cycle,
                     t1.summer,
                     t1.aggregated_id,
                     t2.vendor,
                     t2.account_name,
                     t4.alias_code as product_code,
                     t4.alias_name as product_type,
                     t2.instance_id
                    <if test="domains != null and domains.size() > 0">
                        ,t5.business_domains,
                        t5.business_domain_ids
                    </if>
                    <if test="list != null and list.size >0">
                        <foreach item="item" collection="list" separator=",">
                            <if test="item == 'a.sub_product_id as subProductId'">
                                ,t3.sub_product_id
                                ,t3.sub_product_name
                                ,t3.product_id
                                ,t3.product_name
                            </if>
                            <if test="item == 'a.product_id as productId'">
                                ,t3.sub_product_id
                                ,t3.sub_product_name
                                ,t3.product_id
                                ,t3.product_name
                            </if>
                        </foreach>
                    </if>
              from bc_aggregated_bill t1
                       inner join bc_cmdb_product_overview t2
                                  on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
                        <foreach collection="currencies" item="currency" separator=" " open="" close="">
                            and t1.currency = #{currency}
                        </foreach>
                       inner join bc_raw_bill_products t4
                                    on t2.vendor = t4.vendor and t2.product_code = t4.product_code and t4.del_flag != '1'
                    <if test="list != null and list.size >0">
                        <foreach item="item" collection="list" separator=",">
                            <if test="item == 'a.sub_product_id as subProductId'">
                                inner join bc_hds_sub_products t3 on t1.scode = t3.app_scode
                            </if>
                            <if test="item == 'a.product_id as productId'">
                                inner join bc_hds_sub_products t3 on t1.scode = t3.app_scode
                            </if>
                        </foreach>
                    </if>
                    <if test="domains != null and domains.size() > 0">
                        inner join bc_hds_sub_products t5 on t1.scode = t5.app_scode
                    </if>
             ) a
             on d.date_value = a.billing_cycle
        <if test="vendor != null and vendor != ''">
            and a.vendor = #{vendor}
        </if>
        <if test="productCode != null and productCode != ''">
            and a.product_code = #{productCode}
        </if>
        <if test="productList != null and productList.size() > 0">
            AND a.product_code in
            <foreach collection="productList" item="productCode" separator="," open="(" close=")">
                #{productCode}
            </foreach>
        </if>
        <choose>
            <!-- 如果不是导出全部，根据S码筛选 -->
            <when test="exportAll == false">
                <if test="scodes != null and scodes.size() > 0">
                    AND a.scode in
                    <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                        #{scode}
                    </foreach>
                </if>
                <if test="scodes == null or scodes.size() == 0">
                    <!-- 临时方案 -->
                    and 1 = 2
                </if>
            </when>
            <otherwise>
                <if test="domains != null and domains.size() > 0">
                    AND a.business_domain_ids in
                    <foreach collection="domains" item="domain" separator="," open="(" close=")">
                        #{domain}
                    </foreach>
                </if>
                <if test="domains == null or domains.size() == 0">
                    <!-- 临时方案 -->
                    and 1 = 2
                </if>
            </otherwise>
        </choose>

        ${ew.customSqlSegment}
    </select>

    <select id="DimensionDetailsAll" resultType="java.util.Map">
        select
        <if test="dataType != null and dataType == 'bill'">
            ifnull(round(sum(case
            when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
            else summer
            end), 2), 0) as value,
        </if>
        <if test="dataType != null and dataType == 'instance'">
            count(a.instance_id) as value,
        </if>
        <if test="cycleType == 1"> <!-- 月 -->
            DATE_FORMAT(d.date_value, '%Y-%m') as dimensionalValue,
        </if>
        <if test="cycleType == 2"> <!-- 季 -->
            CONCAT(YEAR(d.date_value), '-Q',
            CASE
            WHEN MONTH(d.date_value) IN (1, 2, 3) THEN '1'
            WHEN MONTH(d.date_value) IN (4, 5, 6) THEN '2'
            WHEN MONTH(d.date_value) IN (7, 8, 9) THEN '3'
            WHEN MONTH(d.date_value) IN (10, 11, 12) THEN '4'
            END) as dimensionalValue,
        </if>
        <if test="cycleType == 3"> <!-- 年 -->
            YEAR(d.date_value) as dimensionalValue,
        </if>
        <if test="list != null and list.size >0">
            <foreach item="item" collection="list" separator=",">
                ${item}
            </foreach>
        </if>
        from bc_date_range d
        inner join
        (select t1.scode,
        t1.billing_cycle,
        t1.summer,
        t1.aggregated_id,
        t2.vendor,
        t2.account_name,
        t4.alias_code as product_code,
        t4.alias_name as product_type,
        t2.instance_id,
        t1.currency
        <if test="domains != null and domains.size() > 0">
            ,t5.business_domains,
            t5.business_domain_ids
        </if>
        <if test="list != null and list.size >0">
            <foreach item="item" collection="list" separator=",">
                <if test="item == 'a.sub_product_id as subProductId'">
                    ,t3.sub_product_id
                    ,t3.sub_product_name
                    ,t3.product_id
                    ,t3.product_name
                </if>
                <if test="item == 'a.product_id as productId'">
                    ,t3.sub_product_id
                    ,t3.sub_product_name
                    ,t3.product_id
                    ,t3.product_name
                </if>
            </foreach>
        </if>
        from bc_aggregated_bill t1
        inner join bc_cmdb_product_overview t2
        on t1.aggregated_id = t2.aggregated_id and t1.granularity = 'month'
        inner join bc_raw_bill_products t4
        on t2.vendor = t4.vendor and t2.product_code = t4.product_code and t4.del_flag != '1'
        <if test="list != null and list.size >0">
            <foreach item="item" collection="list" separator=",">
                <if test="item == 'a.sub_product_id as subProductId'">
                    inner join bc_hds_sub_products t3 on t1.scode = t3.app_scode
                </if>
                <if test="item == 'a.product_id as productId'">
                    inner join bc_hds_sub_products t3 on t1.scode = t3.app_scode
                </if>
            </foreach>
        </if>
        <if test="domains != null and domains.size() > 0">
            inner join bc_hds_sub_products t5 on t1.scode = t5.app_scode
        </if>
        ) a
        on d.date_value = a.billing_cycle
        <if test="vendor != null and vendor != ''">
            and a.vendor = #{vendor}
        </if>
        <if test="productCode != null and productCode != ''">
            and a.product_code = #{productCode}
        </if>
        <if test="productList != null and productList.size() > 0">
            AND a.product_code in
            <foreach collection="productList" item="productCode" separator="," open="(" close=")">
                #{productCode}
            </foreach>
        </if>
        <choose>
            <!-- 如果不是导出全部，根据S码筛选 -->
            <when test="exportAll == false">
                <if test="scodes != null and scodes.size() > 0">
                    AND a.scode in
                    <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                        #{scode}
                    </foreach>
                </if>
                <if test="scodes == null or scodes.size() == 0">
                    <!-- 临时方案 -->
                    and 1 = 2
                </if>
            </when>
            <otherwise>
                <if test="domains != null and domains.size() > 0">
                    AND a.business_domain_ids in
                    <foreach collection="domains" item="domain" separator="," open="(" close=")">
                        #{domain}
                    </foreach>
                </if>
                <if test="domains == null or domains.size() == 0">
                    <!-- 临时方案 -->
                    and 1 = 2
                </if>
            </otherwise>
        </choose>

        ${ew.customSqlSegment}
    </select>

    <update id="updateBatch" parameterType="com.haier.devops.bill.common.dto.ReconciliationDTO">
        <foreach collection="list" item="dto" separator=";">
            update
            bc_aggregated_bill
            set scode = #{dto.scode} where aggregated_id = #{dto.aggregatedId}
            and billing_cycle &gt;= #{dto.startDate}
            and billing_cycle &lt;= #{dto.endDate}
            and granularity = 'day'
        </foreach>
    </update>
    <select id="getScodeOfThePreviousCycle" parameterType="com.haier.devops.bill.common.dto.AggregatedBillDTO" resultType="com.haier.devops.bill.common.vo.AggregatedBillVo">
        SELECT
            b.scode
        FROM
            bc_bill_task t,
            bc_aggregated_bill b
        WHERE
            t.task_id = b.task_id
        <if test="dto.accountName != null and dto.accountName != ''">
          AND t.account_name = #{dto.accountName}
        </if>
          AND t.stage = 'aggregation'
          AND t.`status` = 1
        <if test="dto.billingCycle != null and dto.billingCycle != ''">
          AND t.billing_cycle = #{dto.billingCycle}
        </if>
        <if test="dto.aggregatedId != null and dto.aggregatedId != ''">
          AND b.aggregated_id = #{dto.aggregatedId}
        </if>
    </select>

    <select id="getBillOfBillingCycle" parameterType="com.haier.devops.bill.common.dto.AggregatedBillDTO" resultType="com.haier.devops.bill.common.vo.TechnicalArchitectureBillVo">
        SELECT
            t.vendor,
            t.account_id,
            t.account_name,
            t.billing_cycle,
            b.scode,
            b.summer,
            c.product_code,
            c.product_name,
            c.instance_id,
            c.private_ip
        FROM
            bc_bill_task t,
            bc_aggregated_bill b
                LEFT JOIN bc_cmdb_product_overview c on b.aggregated_id = c.aggregated_id
        WHERE
            t.task_id = b.task_id
          AND t.stage = 'aggregation'
          AND t.`status` = 1
        <if test="dto.vendor != null and dto.vendor != ''">
          and t.vendor = #{dto.vendor}
        </if>
        <if test="dto.accountId != null and dto.accountId != ''">
          and t.account_id = #{dto.accountId}
        </if>
        <if test="dto.billingCycle != null and dto.billingCycle != ''">
          and t.billing_cycle = #{dto.billingCycle}
        </if>
        <if test="dto.scode != null and dto.scode != ''">
          and b.scode = #{dto.scode}
        </if>
    </select>

    <select id="listDetailByPageAll" parameterType="com.haier.devops.bill.common.param.DetailBillParam" resultType="com.haier.devops.bill.common.vo.BillDetailVo">
        <include refid="preSql"/>
        <choose>
            <when test='dto.type == "1"'>
                concat(year(bab.billing_cycle), '-', lpad(month(bab.billing_cycle),2,'0')) as billing_cycle,
            </when>
            <otherwise>
                bab.billing_cycle,
            </otherwise>
        </choose>
        case
        when bab.currency = 'USD' then ROUND(bab.summer / 0.********, 4)
        else bab.summer
        end as summer
        from
        bc_aggregated_bill bab
        join bc_hds_sub_products bhsp on bab.scode = bhsp.app_scode
        <if test='dto.type == "4"'>
            join bc_bill_task a on bab.task_id = a.task_id
        </if>
        join bc_cmdb_product_overview bill
        on bab.aggregated_id = bill.aggregated_id
        join bc_raw_bill_products brbp on bill.product_code = brbp.product_code
        and bill.vendor = brbp.vendor
        where <!-- bhsp.del_flag = '0' and --> brbp.del_flag = '0' /*and bab.summer != 0*/
        <choose>
            <when test='dto.type == "4"'>
                and bab.granularity = 'day'
                and bab.billing_cycle &gt;= #{dto.startCycle} and
                bab.billing_cycle &lt;= #{dto.endCycle}
                and a.status = 1
                and a.stage = 'aggregation'
            </when>
            <otherwise>
                and bab.granularity = 'month'
                and bab.billing_cycle &gt;= #{dto.startCycle} and
                bab.billing_cycle &lt;= #{dto.endCycle}
            </otherwise>
        </choose>
        <include refid="whereSql"/>
    </select>

    <select id="listDetailByPage" parameterType="com.haier.devops.bill.common.param.DetailBillParam" resultType="com.haier.devops.bill.common.vo.BillDetailVo">
        <include refid="preSql"/>
        <choose>
            <when test='dto.type == "1"'>
                concat(year(bab.billing_cycle), '-', lpad(month(bab.billing_cycle),2,'0')) as billing_cycle,
            </when>
        <otherwise>
            bab.billing_cycle,
        </otherwise>
        </choose>
        truncate(summer,4) summer
        from
        bc_aggregated_bill bab
        join bc_hds_sub_products bhsp on bab.scode = bhsp.app_scode
        <if test='dto.type == "4"'>
         join bc_bill_task a on bab.task_id = a.task_id
        </if>
       join bc_cmdb_product_overview bill
        on bab.aggregated_id = bill.aggregated_id
        join bc_raw_bill_products brbp on bill.product_code = brbp.product_code
        and bill.vendor = brbp.vendor
        where <!-- bhsp.del_flag = '0' and --> brbp.del_flag = '0' /*and bab.summer != 0*/
        <choose>
            <when test='dto.type == "4"'>
                and bab.granularity = 'day'
                and bab.billing_cycle &gt;= #{dto.startCycle} and
                bab.billing_cycle &lt;= #{dto.endCycle}
                and a.status = 1
                and a.stage = 'aggregation'
            </when>
            <otherwise>
                and bab.granularity = 'month'
                and bab.billing_cycle &gt;= #{dto.startCycle} and
                bab.billing_cycle &lt;= #{dto.endCycle}
            </otherwise>
        </choose>
        <include refid="whereSql"/>
    </select>
    <sql id="whereSql">
        <if test="dto.currency != null and dto.currency != ''">
            and bab.currency = #{dto.currency}
        </if>
        <if test='dto.scodeList != null and dto.scodeList.size > 0'>
            <foreach collection="dto.scodeList" item="item" open="and bab.scode in (" close=")" separator=",">#{item}
            </foreach>
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            and bill.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and brbp.alias_code = #{dto.productCode}
        </if>
        <if test="dto.vendor != null and dto.vendor != ''">
            and bill.vendor = #{dto.vendor}
        </if>
        <if test="dto.account != null and dto.account != ''">
            and bill.account_name like concat('%', #{dto.account},'%')
        </if>
        <if test="dto.privateIp != null and dto.privateIp != ''">
            and bill.private_ip like concat('%',#{dto.privateIp},'%')
        </if>
        <if test="dto.instanceId != null and dto.instanceId != ''">
            and bill.instance_id like concat('%',#{dto.instanceId},'%')
        </if>
        <if test="dto.supplementId != null and dto.supplementId != ''">
            and bill.supplement_id like concat('%',#{dto.supplementId},'%')
        </if>
        <if test="dto.aggregatedId != null and dto.aggregatedId != ''">
            and bill.aggregated_id = #{dto.aggregatedId}
        </if>
        order by
        <choose>
            <when test='dto.sortNo == 1'>
                bab.summer,bab.id
            </when>
            <when test='dto.sortNo == 2'>
                bab.summer desc,bab.id asc
            </when>
            <otherwise>
                bab.billing_cycle desc,bab.id asc
            </otherwise>
        </choose>
    </sql>
    <sql id="preSql">
        select
            ROW_NUMBER() OVER (ORDER BY bab.aggregated_id) AS row_num,
            bill.aggregated_id,
            bill.vendor,
            bill.account_id,
            bill.account_name,
            bill.product_code,
            bill.instance_id,
            bill.instance_name,
            ifnull(bill.private_ip,'') private_ip,
            bill.supplement_id,
            bab.scode,
            bab.currency,
            bill.project_code,
            bill.project_name,
            bill.product_name,
            bill.subscription_type,
            case when bill.subscription_type = '1' then '按量付费'
                 when bill.subscription_type = '2' then '包年包月'
                 else '未知' end as subscription_type_name,
            bill.creation_time,
            bill.releasing_time,
            bill.create_time,
            bill.update_time,bhsp.business_domains
                ,bhsp.app_name,brbp.alias_code,ifnull(brbp.alias_name,bill.product_name ) alias_name,bhsp.product_name as f_app_name,
                case when  substr(bill.creation_time,1,4) <![CDATA[ < ]]> substr(now(),1,4) then '存量'
                      else '新增'
                end as expense_type,
    </sql>
    <select id="listDetailQByPage" parameterType="com.haier.devops.bill.common.param.DetailBillParam" resultType="com.haier.devops.bill.common.vo.BillDetailVo">
        select m.* from ( <include refid="preSql"/>
        concat(year(bab.billing_cycle), '-Q', QUARTER(bab.billing_cycle)) as billing_cycle
        ,truncate(sum(bab.summer),4) summer
        <include refid="lastSql"/>
    </select>
    <sql id = "lastSql">
        from
        bc_aggregated_bill bab
        join bc_hds_sub_products bhsp on bab.scode = bhsp.app_scode
        join bc_cmdb_product_overview bill
        on bab.aggregated_id = bill.aggregated_id
        join bc_raw_bill_products brbp on bill.product_code = brbp.product_code
        and bill.vendor = brbp.vendor
        where <!-- bhsp.del_flag = '0' and --> brbp.del_flag = '0'/* and bab.summer != 0*/
        and bab.granularity = 'month'
        and bab.billing_cycle &gt;= #{dto.startCycle} and
        bab.billing_cycle &lt;= #{dto.endCycle}
        <if test='dto.scodeList != null and dto.scodeList.size > 0'>
            <foreach collection="dto.scodeList" item="item" open="and bab.scode in (" close=")" separator=",">#{item}
            </foreach>
        </if>
        <if test="dto.currency != null and dto.currency != ''">
            and bab.currency = #{dto.currency}
        </if>
        <if test="dto.subscriptionType != null and dto.subscriptionType != ''">
            and bill.subscription_type = #{dto.subscriptionType}
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and brbp.alias_code = #{dto.productCode}
        </if>
        <if test="dto.vendor != null and dto.vendor != ''">
            and bill.vendor = #{dto.vendor}
        </if>
        <if test="dto.account != null and dto.account != ''">
            and bill.account_name like concat('%', #{dto.account},'%')
        </if>
        <if test="dto.privateIp != null and dto.privateIp != ''">
            and bill.private_ip like concat('%',#{dto.privateIp},'%')
        </if>
        <if test="dto.instanceId != null and dto.instanceId != ''">
            and bill.instance_id like concat('%',#{dto.instanceId},'%')
        </if>
        <if test="dto.supplementId != null and dto.supplementId != ''">
            and bill.supplement_id like concat('%',#{dto.supplementId},'%')
        </if>
        group by
        bab.aggregated_id
        ) m
        order by
        <choose>
            <when test='dto.sortNo == 1'>
                m.summer,m.row_num
            </when>
            <when test='dto.sortNo == 2'>
                m.summer desc,m.row_num asc
            </when>
            <otherwise>
                m.billing_cycle desc,m.row_num asc
            </otherwise>
        </choose>
    </sql>
    <select id="listDetailYByPage" parameterType="com.haier.devops.bill.common.param.DetailBillParam" resultType="com.haier.devops.bill.common.vo.BillDetailVo">
        select m.* from (  <include refid="preSql"/>
        year(bab.billing_cycle) as billing_cycle
        ,truncate(sum(bab.summer),4) summer
        <include refid="lastSql"/>
    </select>
    <select id="getGroupedAggregatedBillsBetweenDays" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select aggregated_id,
               a.scode,
               'month' as granularity,
               DATE_FORMAT(a.billing_cycle, '%Y-%m-01') as billing_cycle,
               quarter(a.billing_cycle),
               sum(a.summer) as summer,
               sum(a.payable_sum) as payable_sum,
               sum(a.voucher_sum) as voucher_sum,
               sum(a.coupon_sum) as coupon_sum,
               sum(a.cash_sum) as cash_sum,
               sum(a.original_sum) as original_sum,
               a.currency
        from bc_bill_task t
                 inner join bc_aggregated_bill a
                            on t.task_id = a.task_id
            <if test="aggregatedIds != null and aggregatedIds.length > 0">
                and a.aggregated_id in
                <foreach collection="aggregatedIds" item="aggregatedId" separator="," open="(" close=")">
                    #{aggregatedId}
                </foreach>
            </if>
        where t.status = 1
          <if test="vendor != null and vendor != ''">
              and t.vendor = #{vendor}
          </if>
          and t.stage = 'aggregation'
          <if test="start != null and start != '' and end != null and end != ''">
              and t.billing_cycle between STR_TO_DATE(#{start}, '%Y-%m-%d') and STR_TO_DATE(#{end}, '%Y-%m-%d')
          </if>
          and a.granularity = 'day'
        group by a.aggregated_id, a.scode, DATE_FORMAT(a.billing_cycle, '%Y-%m-01')
    </select>

    <select id="getAggregatedBillsByAccount" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select aggregated_id,
               a.scode,
               'month' as granularity,
               DATE_FORMAT(a.billing_cycle, '%Y-%m-01') as billing_cycle,
               quarter(a.billing_cycle),
               sum(a.summer) as summer,
               sum(a.payable_sum) as payable_sum,
               sum(a.voucher_sum) as voucher_sum,
               sum(a.cash_sum) as cash_sum,
               sum(a.coupon_sum) as coupon_sum,
               sum(a.original_sum) as original_sum
        from bc_bill_task t
                 inner join bc_aggregated_bill a
                            on t.task_id = a.task_id
        where t.status = 1
          <if test="vendor != null and vendor != ''">
              and t.vendor = #{vendor}
          </if>
          and t.account_name = #{account}
          and t.stage = 'aggregation'
          <if test="start != null and start != '' and end != null and end != ''">
              <![CDATA[
                  and t.billing_cycle >= STR_TO_DATE(#{start}, '%Y-%m-%d')
                  and t.billing_cycle <= STR_TO_DATE(#{end}, '%Y-%m-%d')
              ]]>
          </if>
          and a.granularity = 'day'
        group by a.aggregated_id, a.scode, DATE_FORMAT(a.billing_cycle, '%Y-%m')
    </select>

    <select id="getCurrentYearTotalAmount" resultType="java.math.BigDecimal">
        select round(sum(summer), 2)
        from bc_aggregated_bill
        where granularity = 'month'
        <![CDATA[
            and billing_cycle >= DATE_FORMAT(CURRENT_DATE, '%Y-01-01')
            and billing_cycle < DATE_FORMAT(CURRENT_DATE + INTERVAL 1 YEAR, '%Y-01-01')
        ]]>
          <foreach collection="currencies" item="currency" separator=" " open="" close="">
              and currency = #{currency}
          </foreach>
          and scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
    </select>
    <select id="getLastNthMonthTotalAmount" resultType="java.math.BigDecimal">
        select round(sum(a.summer), 2)
        from bc_aggregated_bill a
        inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        where a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL #{nth} MONTH), '%Y-%m-01')
              and a.billing_cycle <  DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL #{nth} - 1 MONTH), '%Y-%m-01')
          ]]>
        <foreach collection="currencies" item="currency" separator=" " open="" close="">
            and a.currency = #{currency}
        </foreach>
          and a.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendor != null and vendor != ''">
            and p.vendor = #{vendor}
        </if>
    </select>
    <select id="getLastNthMonthTotalAmountAll" resultType="java.math.BigDecimal">
        select round(sum(case
        when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
        else summer
        end), 2)
        from bc_aggregated_bill a
        inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        where a.granularity = 'month'
        <![CDATA[
              and a.billing_cycle >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL #{nth} MONTH), '%Y-%m-01')
              and a.billing_cycle <  DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL #{nth} - 1 MONTH), '%Y-%m-01')
          ]]>
        and a.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
        <if test="vendor != null and vendor != ''">
            and p.vendor = #{vendor}
        </if>
    </select>

    <select id="getTotalAmountOfYesterday" resultType="java.math.BigDecimal">
        select round(sum(summer), 2)
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
                 inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        where t.status = 1
          and t.stage = 'aggregation'
          and t.billing_cycle = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
          and p.subscription_type = '1'
          <foreach collection="currencies" item="currency" separator=" " open="" close="">
              and a.currency = #{currency}
          </foreach>
          and a.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
    </select>
    <select id="getCurrentMonthTotalAmountUntilYesterday" resultType="java.math.BigDecimal">
        select round(sum(summer), 2)
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
        where t.status = 1
          and t.stage = 'aggregation'
          and DATE_FORMAT(t.billing_cycle, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
          <foreach collection="currencies" item="currency" separator=" " open="" close="">
              and a.currency = #{currency}
          </foreach>
          <![CDATA[
              AND t.billing_cycle < CURDATE()
          ]]>
          and a.scode in
        <foreach collection="scodes" item="scode" separator="," open="(" close=")">
            #{scode}
        </foreach>
    </select>
    <select id="getLastYearTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(ROUND(SUM(total_summer), 2), 0) AS total_summer
        FROM (
                 SELECT IFNULL(SUM(summer), 0) AS total_summer
                 FROM bc_aggregated_bill
                 WHERE granularity = 'month'
                   <![CDATA[
                       AND billing_cycle >= STR_TO_DATE(#{firstDayOfLastYear}, '%Y-%m-%d')
                       AND billing_cycle < STR_TO_DATE(#{firstDayOfCurrentMonthLastYear}, '%Y-%m-%d')
                    ]]>
                   <foreach collection="currencies" item="currency" separator=" " open="" close="">
                       and currency = #{currency}
                   </foreach>
                   AND scode IN
                   <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                       #{scode}
                   </foreach>
                 UNION ALL
                 SELECT IFNULL(SUM(a.summer), 0) AS total_summer
                 FROM bc_bill_task t
                          INNER JOIN
                      bc_aggregated_bill a
                      ON t.task_id = a.task_id
                          AND t.stage = 'aggregation'
                          AND t.status = 1
                          <![CDATA[
                              AND t.billing_cycle >= STR_TO_DATE(#{firstDayOfCurrentMonthLastYear}, '%Y-%m-%d')
                              AND t.billing_cycle < STR_TO_DATE(#{todayLastYear}, '%Y-%m-%d')
                          ]]>
                          AND a.scode IN
                          <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                              #{scode}
                          </foreach>
        ) t
    </select>
    <select id="getMonthAggregatedBill" resultType="com.haier.devops.bill.common.vo.DayBillAnalysisVo">
        select p.vendor, t.billing_cycle, round(sum(summer), 2) as total
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
                 inner join bc_cmdb_product_overview p on a.aggregated_id = p.aggregated_id
        where t.status = 1
          and t.stage = 'aggregation'
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{month}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{month}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          and p.subscription_type = '1'
          <if test="vendor != null and vendor != ''">
              and p.vendor = #{vendor}
          </if>
          and a.scode in
          <foreach collection="scodes" item="scode" separator="," open="(" close=")">
              #{scode}
          </foreach>
          <foreach collection="currency" item="currency" separator=" " open="" close="">
              and a.currency = #{currency}
          </foreach>
        group by p.vendor, t.billing_cycle
        order by p.vendor, t.billing_cycle asc
    </select>
    <select id="getAliyunPaasPendingSubstitutionBills" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor = 'aliyun' and t.stage = 'aggregation' and t.status = 1 and t.account_name = 'hr690n'
            <![CDATA[
                and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01764'
        union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'aliyun' and o.account_name = 'hr690n'
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01764' and a.sub_task_id is null
    </select>

    <select id="getBigDataHdopPaasPendingSubstitutionBills" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor in ('aliyun', 'aliyun_dedicated') and t.stage = 'aggregation' and t.status = 1
            <![CDATA[
                and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S03055'
        union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor in ('aliyun', 'aliyun_dedicated')
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S03055'
            AND o.product_code != 'bigdata_starrocks'

    </select>
    <select id="getBigDataStarrocksPaasPendingSubstitutionBills"
            resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor in ('aliyun', 'aliyun_dedicated') and t.stage = 'aggregation' and t.status = 1
            <![CDATA[
                and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01403'
        union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor in ('aliyun', 'aliyun_dedicated')
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01403' and o.product_code != 'bigdata_hdop'
    </select>
    <select id="getAliyunDedicatedPaasPendingSubstitutionBills"
            resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor = 'aliyun_dedicated' and t.stage = 'aggregation' and t.status = 1 and t.account_name = 'hr690y'
            <![CDATA[
                and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode in ('S01764', 'S04076')
        union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'aliyun_dedicated' and o.account_name = 'hr690y'
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode in ('S01764', 'S04076') and a.sub_task_id is null
    </select>
    <select id="getPassBill" resultType="com.haier.devops.bill.substitution.vo.PassBillVo">
        select o.vendor, a.billing_cycle, round(sum(summer), 2) as summer
        from bc_cmdb_product_overview o
                 inner join bc_aggregated_bill a on o.aggregated_id = a.aggregated_id
            and o.product_code = 'paas'
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            <foreach collection="currency" item="currency" separator=" " open="" close="">
                and a.currency = #{currency}
            </foreach>
            <if test="scodes != null and scodes.size() > 0">
                and a.scode in
                <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                    #{scode}
                </foreach>
            </if>
            <if test="vendor != null and vendor != ''">
                and o.vendor = #{vendor}
            </if>
        group by o.vendor, a.billing_cycle
        order by a.billing_cycle asc
    </select>

    <select id="getPassBillAll" resultType="com.haier.devops.bill.substitution.vo.PassBillVo">
        select o.vendor, a.billing_cycle, round(sum(case
        when a.currency = 'USD' then ROUND(a.summer / #{exchangeRate}, 4)
        else summer
        end), 2) as summer
        from bc_cmdb_product_overview o
        inner join bc_aggregated_bill a on o.aggregated_id = a.aggregated_id
        and o.product_code = 'paas'
        and a.granularity = 'month'
        <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
        <if test="scodes != null and scodes.size() > 0">
            and a.scode in
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
        <if test="vendor != null and vendor != ''">
            and o.vendor = #{vendor}
        </if>
        group by o.vendor, a.billing_cycle
        order by a.billing_cycle asc
    </select>
    <select id="getHuaweiPaasPendingSubstitutionBills" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor = 'huawei' and t.stage = 'aggregation' and t.status = 1
            <![CDATA[
                and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01764'
        union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'huawei'
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S01764' and a.sub_task_id is null
    </select>
    <select id="getHuaweiUcsPendingSubstitutionBills"
            resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
            from bc_bill_task t
                     inner join bc_aggregated_bill a on t.task_id = a.task_id
                     inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
                and t.vendor = 'huawei' and t.stage = 'aggregation' and t.status = 1
                <![CDATA[
                    and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                    and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
                ]]>
                and t.account_name = 'hr690n'
                and o.product_code = 'hws.service.type.ucs'
        union all
        select a.*
            from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
                and o.vendor = 'huawei'
                and a.granularity = 'month'
                <![CDATA[
                    and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                    and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
                ]]>
                and o.account_name = 'hr690n'
                and o.product_code = 'hws.service.type.ucs'
                and a.sub_task_id is null
    </select>
    <select id="getCloudMonitorPendingSubstitutionBills"
            resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor = 'aliyun' and t.stage = 'aggregation' and t.status = 1
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S03463'
            union all
        select a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'aliyun'
            and a.granularity = 'month'
            <![CDATA[
                and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
                and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and a.scode = 'S03463'
            and o.product_code != 'paas'
    </select>

    <select id="getAwsCommitmentPendingSubstitutionBills"
            resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select a.*
        from bc_bill_task t
             inner join bc_aggregated_bill a on t.task_id = a.task_id
        and t.vendor = 'aws' and t.stage = 'aggregation' and t.status = 1
        <![CDATA[
            and t.billing_cycle >= STR_TO_DATE(#{billingCycle}, '%Y-%m-%d')
            and t.billing_cycle < DATE_ADD(STR_TO_DATE(#{billingCycle}, '%Y-%m-%d'), INTERVAL 1 DAY)
        ]]>
             inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
        and o.vendor = 'aws' and o.product_code in ('AmazonEC2', 'AmazonRDS')
    </select>

    <select id="getAwsPendingSubstitutionBills"
            resultType="com.haier.devops.bill.aws.commitment.vo.AggregatedBillCommitmentVo">
        select a.*, o.vendor, o.account_name, o.product_code, o.product_name, o.instance_id
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
            and t.vendor = 'aws' and t.stage = 'aggregation' and t.status = 1
        <![CDATA[
            and t.billing_cycle >= STR_TO_DATE(#{billingCycle}, '%Y-%m-%d')
            and t.billing_cycle < DATE_ADD(STR_TO_DATE(#{billingCycle}, '%Y-%m-%d'), INTERVAL 1 DAY)
        ]]>
             inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'aws' and o.product_code in ('AmazonEC2', 'AmazonRDS')
    </select>

    <delete id="cleanAliyunPassDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor = 'aliyun'
          AND t.stage = 'aggregation'
          AND t.status = 1
          AND t.account_name = 'hr690n'
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01764'
    </delete>

    <delete id="cleanBigDataHdopPassDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor in ('aliyun', 'aliyun_dedicated')
          AND t.stage = 'aggregation'
          AND t.status = 1
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S03055'
    </delete>
    <delete id="cleanBigDataStarrocksPassDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor in ('aliyun', 'aliyun_dedicated')
          AND t.stage = 'aggregation'
          AND t.status = 1
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01403'
    </delete>
    <delete id="cleanAliyunPassMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor = 'aliyun'
          AND o.account_name = 'hr690n'
          AND a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01764' and a.sub_task_id is null
    </delete>

    <delete id="cleanBigDataHdopPassMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor in ('aliyun', 'aliyun_dedicated')
          AND a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S03055' AND o.product_code != 'bigdata_starrocks'
    </delete>
    <delete id="cleanBigDataStarrocksPassMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
        INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor in ('aliyun', 'aliyun_dedicated')
          AND a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01403'
          AND o.product_code != 'bigdata_hdop'
    </delete>
    <delete id="cleanAliyunDedicatedPassDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor = 'aliyun_dedicated'
          AND t.stage = 'aggregation'
          AND t.status = 1
          AND t.account_name = 'hr690y'
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode in ('S01764', 'S04076')
    </delete>
    <delete id="cleanAliyunDedicatedPassMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor = 'aliyun_dedicated'
          AND o.account_name = 'hr690y'
          AND a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode in ('S01764', 'S04076') and a.sub_task_id is null
    </delete>
    <delete id="cleanHuaweiPassDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor = 'huawei'
          AND t.stage = 'aggregation'
          AND t.status = 1
          <![CDATA[
              and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01764'
    </delete>
    <delete id="cleanHuaweiPassMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor = 'huawei'
          AND a.granularity = 'month'
          <![CDATA[
              and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
              and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S01764' and a.sub_task_id is null
    </delete>
    <delete id="cleanHuaweiUcsDayBills">
        delete a.*
        from bc_bill_task t
                 inner join bc_aggregated_bill a on t.task_id = a.task_id
                 inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and t.vendor = 'huawei' and t.stage = 'aggregation' and t.status = 1
        <![CDATA[
            and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
            and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
        ]]>
        and t.account_name = 'hr690n'
        and o.product_code = 'hws.service.type.ucs'
    </delete>
    <delete id="cleanHuaweiUcsMonthBills">
        delete a.*
        from bc_aggregated_bill a inner join bc_cmdb_product_overview o on a.aggregated_id = o.aggregated_id
            and o.vendor = 'huawei'
            and a.granularity = 'month'
            <![CDATA[
            and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
            and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
            ]]>
            and o.account_name = 'hr690n'
            and o.product_code = 'hws.service.type.ucs'
            and a.sub_task_id is null
    </delete>
    <delete id="clearAdjustedMonthBills">
        delete from bc_aggregated_bill a where a.aggregated_id in
            <foreach collection="aggregatedIds" item="aggregatedId" separator="," open="(" close=")">
                #{aggregatedId}
            </foreach>
          and a.granularity = 'month'
          <if test="start != null and start != '' and end != null and end != ''">
              <![CDATA[
                  and a.billing_cycle >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                  and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
              ]]>
          </if>
          and a.sub_task_id is null
    </delete>
    <delete id="clearMonthBills">
        delete a.* from bc_cmdb_product_overview o
                 inner join bc_aggregated_bill a
                            on o.aggregated_id = a.aggregated_id
                                and o.vendor = #{vendor}
                                and a.granularity = 'month'
                                <![CDATA[
                                    and a.billing_cycle >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                                    and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
                                ]]>
                                and a.sub_task_id is null
    </delete>

    <delete id="clearMonthBillsByAccount">
        delete a.* from bc_cmdb_product_overview o
                 inner join bc_aggregated_bill a
                            on o.aggregated_id = a.aggregated_id
                                and o.vendor = #{vendor}
                                and o.account_name = #{account}
                                and a.granularity = 'month'
                                <![CDATA[
                                    and a.billing_cycle >= STR_TO_DATE(CONCAT(#{start}, '-01'), '%Y-%m-%d')
                                    and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{end}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
                                ]]>
                                and a.sub_task_id is null
    </delete>
    <delete id="cleanCloudMonitorDayBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_bill_task t ON t.task_id = a.task_id
        WHERE t.vendor = 'aliyun'
          AND t.stage = 'aggregation'
          AND t.status = 1
          <![CDATA[
          and t.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
          and t.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S03463'
    </delete>
    <delete id="cleanCloudMonitorMonthBills">
        DELETE a.*
        FROM bc_aggregated_bill a
                 INNER JOIN bc_cmdb_product_overview o ON a.aggregated_id = o.aggregated_id
        WHERE o.vendor = 'aliyun'
          AND a.granularity = 'month'
          <![CDATA[
          and a.billing_cycle >= STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d')
          and a.billing_cycle < DATE_ADD(STR_TO_DATE(CONCAT(#{billingCycle}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH)
          ]]>
          AND a.scode = 'S03463'
    </delete>

    <select id="getAgentResourceApplications" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT 	case
        when a.resource_type = 'ecs' then '服务器'
        when a.resource_type = 'db' then '数据库'
        when a.resource_type = 'quota' then '容器云'
        when a.resource_type = 'storage' then '存储'
        when a.resource_type = 'fuzzy' then '模糊申请'
        else ''
        end as resourceType,date_format(created_at,'%Y-%m-%d %H:%i:%s')  as createdAt,a.*,
        (CASE
        WHEN ins.instance_id != '' THEN ins.instance_id
        WHEN ins.instance_ip != '' THEN ins.instance_ip
        WHEN ins.bucket_domain != '' THEN ins.bucket_domain
        WHEN ins.connection_address != '' THEN ins.connection_address
        END) AS instanceId FROM console_resource_applications a LEFT JOIN
        resource_application_instance ins ON a.sn = ins.sn
        WHERE a.`status` = 1
          <if test="vendor != null and vendor != ''">
              AND a.vendor = #{vendor}
          </if>
        <if test="scodes != null and scodes.size() > 0">
            AND a.scode in
            <foreach collection="scodes" item="scode" separator="," open="(" close=")">
                #{scode}
            </foreach>
        </if>
        <if test="startDate != null and startDate != ''">
            and a.created_at &gt;= #{startDate}
        </if>

        <if test="endDate != null and endDate != ''">
            and a.created_at &lt;= #{endDate}
        </if>
        order by a.created_at desc
    </select>
</mapper>
