<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RefinedRawBillMapper">

    <resultMap type="com.haier.devops.bill.common.entity.RefinedRawBill" id="BcRefinedRawBillMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="granularity" column="granularity" jdbcType="VARCHAR"/>
        <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="productDetail" column="product_detail" jdbcType="VARCHAR"/>
        <result property="resourceGroup" column="resource_group" jdbcType="VARCHAR"/>
        <result property="instanceId" column="instance_id" jdbcType="VARCHAR"/>
        <result property="supplementId" column="supplement_id" jdbcType="VARCHAR"/>
        <result property="costUnit" column="cost_unit" jdbcType="VARCHAR"/>
        <result property="aggregatedId" column="aggregated_id" jdbcType="VARCHAR"/>
        <result property="servicePeriod" column="service_period" jdbcType="VARCHAR"/>
        <result property="servicePeriodUnit" column="service_period_unit" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="zone" column="zone" jdbcType="VARCHAR"/>
        <result property="billingItem" column="billing_item" jdbcType="VARCHAR"/>
        <result property="listPrice" column="list_price" jdbcType="VARCHAR"/>
        <result property="listPriceUnit" column="list_price_unit" jdbcType="VARCHAR"/>
        <result property="usage" column="usage" jdbcType="VARCHAR"/>
        <result property="usageUnit" column="usage_unit" jdbcType="VARCHAR"/>
        <result property="cost" column="cost" jdbcType="VARCHAR"/>
        <result property="payableAmount" column="payable_amount" jdbcType="VARCHAR"/>
        <result property="voucherAmount" column="voucher_amount" jdbcType="VARCHAR"/>
        <result property="cashAmount" column="cash_amount" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="subscriptionType" column="subscription_type" jdbcType="VARCHAR"/>
        <result property="expenseType" column="expense_type" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="scodeUnverified" column="scode_unverified" jdbcType="VARCHAR"/>
        <result property="scodeRuleMatched" column="scode_rule_matched" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="batchUpdateById">
        <foreach collection="bills" item="bill" separator=";">
            UPDATE bc_refined_raw_bill
            SET sub_instance_id = #{bill.subInstanceId},
                sub_product_code  = #{bill.subProductCode},
                sub_product_name  = #{bill.subProductName},
                instance_id       = #{bill.instanceId},
                product_code      = #{bill.productCode},
                product_name      = #{bill.productName},
                aggregated_id     = #{bill.aggregatedId},
                subscription_type = #{bill.subscriptionType},
                cost_unit         = #{bill.costUnit},
                parent_found      = 1
                WHERE id = #{bill.id}
        </foreach>
    </update>

    <select id="getBillingSummaryData" parameterType="string" resultType="com.haier.devops.bill.common.vo.BillingSummaryVo">
        SELECT
            ROUND( sum( t.payable_amount ), 2 ) AS summer,
            t.aggregated_id,
            t.vendor,
            t.account_name,
            t.account_id,
            t.product_code,
            t.product_name,
            t.instance_id,
            t.region,
            t.zone,
            t.cost_unit,
            t.billing_cycle,
            t.subscription_type
        FROM
            bc_bill_task k
            INNER JOIN bc_refined_raw_bill t ON k.task_id = t.task_id
        WHERE
            k.account_name = #{accountName}

          AND k.billing_cycle = #{billingCycle}

          AND k.vendor = #{vendor}

          AND k.`status` = 1
          and k.stage = 'refinement'
        GROUP BY
            t.aggregated_id
    </select>

    <select id="getBillingSummaryCount" parameterType="string" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            bc_bill_task k
                INNER JOIN bc_refined_raw_bill t ON k.task_id = t.task_id
        WHERE
            k.account_name = #{accountName}
          AND k.billing_cycle = #{billingCycle}
          AND k.vendor = #{vendor}
          AND (
              <!-- 预备好汇总的、汇总失败的 -->
            (k.`status` = 1 and k.stage = 'refinement') or (k.`status` = 3 and k.stage = 'aggregation')
          )
        GROUP BY
            t.aggregated_id
    </select>


    <select id="getDetailBillByType"  resultType="com.haier.devops.bill.common.vo.NodeDetailBillsVo">
        select t.billing_cycle
                , t.vendor
                , w.scode
                , t.account_name
                , t.subscription_type
                , t.instance_id
                , t.product_name
                , t.sub_product_name
                , t.product_code
                , t.product_detail
                , w.project_code
                , w.project_name
                , t.cost_unit
                , i.instance_name
                , i.private_ip as ip
                , t.region
                , round(sum(t.payable_amount),2) total_sum
                ,case when  substr(w.creation_time,1,4) <![CDATA[ < ]]> substr(now(),1,4) then '存量'
                 else '新增'
                 end as expense_type
        from bc_bill_task k
            inner join bc_refined_raw_bill t on k.task_id = t.task_id
            left join bc_cmdb_product_overview w on w.aggregated_id = t.aggregated_id
            left join rc_host_info i on t.instance_id = i.instance_id
            ${ew.customSqlSegment}
    </select>
    <select id="getPendingAggregatedId" resultType="java.lang.String">
       select distinct b.aggregated_id
       from bc_bill_task t
               inner join bc_refined_raw_bill b
                          on t.task_id = b.task_id
               left join bc_excluded_product e
                         on b.vendor = e.vendor and b.account_name = e.account_name and b.product_code = e.product_code
       where t.vendor = #{vendor}
         and t.stage = 'pull'
         and t.status = 1
         and t.account_name = #{accountName}
         and t.billing_cycle = #{billingCycle}
         and e.vendor is null
    </select>
    <select id="getSummaryDataWithAggregatedIds" resultType="com.haier.devops.bill.common.vo.BillingSummaryVo">
        select b.aggregated_id,
               sum(b.payable_amount) as payable_sum,
               sum(b.voucher_amount) as voucher_sum,
               sum(b.cash_amount) as cash_sum,
               sum(b.coupon_amount) as coupon_sum,
               sum(b.cost) as original_sum,
               <!-- sum(b.payable_amount) - sum(b.voucher_amount) - sum(cash_amount) as summer, -->
               sum(b.payable_amount) - sum(b.voucher_amount) as summer,
               b.vendor,
               b.account_name,
               b.account_id,
               b.product_code,
               b.product_name,
               b.instance_id,
               b.region,
               b.zone,
               b.cost_unit,
               b.currency,
               b.billing_cycle,
               b.subscription_type,
               b.supplement_id,
               b.scode_unverified,
               b.scode_rule_matched,
               b.parent_found
        from bc_bill_task t
                 inner join bc_refined_raw_bill b on t.task_id = b.task_id
                    and t.vendor = #{vendor}
                    and t.stage = 'pull'
                    and t.status = 1
                    and t.billing_cycle = #{billingCycle}
                    and t.account_name = #{accountName}
        where b.aggregated_id in
        <foreach collection="aggregatedIds" item="aggregatedId" open="(" separator="," close=")">
            #{aggregatedId}
        </foreach>
        group by b.aggregated_id
    </select>
    <select id="getByAggregatedId" resultType="com.haier.devops.bill.common.entity.RefinedRawBill">
        select r.*
        from bc_bill_task t inner join bc_refined_raw_bill r on t.task_id = r.task_id
            and t.stage = 'pull' and t.status = 1 and r.aggregated_id = #{aggregatedId}

    </select>
    <select id="getAggregatedInDay" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select 'day'                                                              as granularity,
               r.billing_cycle,
               r.cost_unit                                                        as scode,
               r.aggregated_id,
               sum(r.payable_amount)                                              as payableSum,
               sum(r.voucher_amount)                                              as voucherSum,
               sum(r.coupon_amount)                                               as couponSum,
               sum(r.cash_amount)                                                 as cashSum,
               <!-- sum(r.payable_amount) - sum(r.voucher_amount) - sum(r.cash_amount) as summer, -->
               sum(r.payable_amount) - sum(r.voucher_amount) as summer,
               t2.task_id
        from bc_refined_raw_bill r
                 inner join bc_bill_task t1 on r.task_id = t1.task_id and t1.stage = 'pull' and t1.status = 1 <!-- 找出明细中间表有效的数据 -->
            and r.aggregated_id = #{aggregatedId}
                 inner join bc_bill_task t2
                            on r.vendor = t2.vendor and r.account_name = t2.account_name and
                               r.billing_cycle = t2.billing_cycle <!-- 找出有效数据当天的到日汇总有效的任务id -->
                                and t2.stage = 'aggregation' and t2.status = 1
        group by r.aggregated_id, r.billing_cycle
    </select>
    <select id="getAggregatedInMonth" resultType="com.haier.devops.bill.common.entity.AggregatedBill">
        select 'month'                                                            as granularity,
               DATE_FORMAT(r.billing_cycle, '%Y-%m-01')                           as billing_cycle,
               r.cost_unit                                                        as scode,
               r.aggregated_id,
               sum(r.payable_amount)                                              as payableSum,
               sum(r.voucher_amount)                                              as voucherSum,
               sum(r.coupon_amount)                                               as couponSum,
               sum(r.cash_amount)                                                 as cashSum,
               <!-- sum(r.payable_amount) - sum(r.voucher_amount) - sum(r.cash_amount) as summer -->
               sum(r.payable_amount) - sum(r.voucher_amount) as summer
        from bc_refined_raw_bill r
                 inner join bc_bill_task t
                            on r.task_id = t.task_id and t.stage = 'pull' and t.status = 1
                                and r.aggregated_id = #{aggregatedId}
        group by r.aggregated_id, DATE_FORMAT(r.billing_cycle, '%Y-%m')
    </select>
</mapper>

