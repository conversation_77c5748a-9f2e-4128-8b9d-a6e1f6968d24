<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ServiceFreeMapper">

    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.ServiceFreeDTO" resultType="com.haier.devops.bill.common.entity.ServiceFree">
        SELECT id, vendor, start_date, end_date, rate, `type`, currency, create_time, create_by, update_time, update_by, del_flag FROM bc_service_free
        WHERE del_flag = '0'
        AND `type` = #{dto.type}
        <if test="dto.vendor != null and dto.vendor != ''">
            and vendor = #{dto.vendor}
        </if>
        order by create_time desc
    </select>

    <select id="queryTotalCycle" parameterType="com.haier.devops.bill.common.dto.ServiceFreeCreateDTO" resultType="com.haier.devops.bill.common.entity.ServiceFree">
        select bsf.vendor,bsf.`type`,min(start_date) start_date ,max(end_date) end_date  from bc_service_free bsf where bsf.vendor = #{dto.vendor} and bsf.`type` = #{dto.type}
        <if test="dto.id != null">
            and bsf.id != #{dto.id}
        </if>
    </select>
    <select id="findOverlapItems" resultType="com.haier.devops.bill.common.entity.ServiceFree">
        select * from bc_service_free where vendor = #{vendor} and `type` = #{type} and del_flag = '0' and ((start_date between #{startDate} and #{endDate}) or (end_date between #{startDate} and #{endDate}))
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
</mapper>
