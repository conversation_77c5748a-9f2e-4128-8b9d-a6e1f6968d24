<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.AdjustmentRecordMapper">
    <select id="getAdjustmentRecordList" resultType="com.haier.devops.bill.common.vo.AdjustmentRecordVo">

        select
            bar.id,
            bcpo.aggregated_id,
            bcpo.vendor,
            bcpo.account_id,
            bcpo.account_name,
            bcpo.product_code,
            bcpo.product_name,
            bcpo.instance_id,
            bcpo.supplement_id,
            bcpo.project_code,
            bcpo.project_name,
            bcpo.subscription_type,
            bcpo.creation_time,
            bcpo.releasing_time,
            bcpo.update_time,
            bcpo.scode_unverified,
            bar.create_time ,
            bar.reconciliation_status ,
            bar.create_by ,
            bar.reconciliation_type,
            bar.original_content ,
            bar.new_content,
               bar.type
        from
            bc_adjustment_record bar join
            bc_cmdb_product_overview bcpo on bar.aggregated_id = bcpo.aggregated_id join
            bc_raw_bill_products brbp on bcpo.product_code = brbp.product_code and bcpo.vendor = brbp.vendor
            ${ew.customSqlSegment}
    </select>
</mapper>
