<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.CmdbProductOverviewMapper">


    <resultMap type="com.haier.devops.bill.common.entity.CmdbProductOverview" id="BcCmdbProductOverviewMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="aggregatedId" column="aggregated_id" jdbcType="VARCHAR"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="instanceId" column="instance_id" jdbcType="VARCHAR"/>
        <result property="scode" column="scode" jdbcType="VARCHAR"/>
        <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="creationTime" column="creation_time" jdbcType="TIMESTAMP"/>
        <result property="releasingTime" column="releasing_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="listByPage" parameterType="com.haier.devops.bill.common.dto.CmdbProductOverviewDTO" resultType="com.haier.devops.bill.common.entity.CmdbProductOverview">
        select
        bcpo.id,
        bcpo.aggregated_id,
        bcpo.vendor,
        bcpo.account_id,
        bcpo.account_name,
        bcpo.product_code,
        bcpo.product_name,
        bcpo.instance_id,
        bcpo.supplement_id,
        bcpo.scode,
        bcpo.project_code,
        bcpo.project_name,
        bcpo.subscription_type,
        bcpo.creation_time,
        bcpo.releasing_time,
        bcpo.create_time,
        bcpo.update_time,
        bcpo.scode_unverified,
        "" as cost_unit,
        ifnull(bar.reconciliation_status, 'done') reconciliation_status,
        bhsp.app_name,
        bhsp.app_short_name
        from
        bc_cmdb_product_overview bcpo
        inner join (select DISTINCT(`aggregated_id` ) aggregated_id from `bc_aggregated_bill` where `granularity` = 'month') a on bcpo.aggregated_id = a.aggregated_id
        left join bc_hds_sub_products bhsp on bcpo.scode = bhsp.app_scode
        join bc_raw_bill_products brbp on bcpo.product_code = brbp.product_code
        and brbp.del_flag = '0'
        and bcpo.vendor = brbp.vendor
        left join (
        select distinct
        bar2.aggregated_id,
        bar2.reconciliation_status
        from
        bc_adjustment_record bar2
        join (
        select
        bar.aggregated_id,
        max(create_time) create_time
        from
        bc_adjustment_record bar
        group by
        bar.aggregated_id
        ) bar on bar2.aggregated_id = bar.aggregated_id
        and bar.create_time = bar2.create_time
        ) bar on bcpo.aggregated_id = bar.aggregated_id
        where
        (
        releasing_time is null
        or releasing_time >= DATE(now() - interval 90 day
        ))
        and bcpo.product_code not in(
        'paas',
        'bigdata_hdop',
        'bigdata_starrocks',
        'cloud_monitor'
        )
        <if test="dto.vendor != null and dto.vendor != ''">
            and bcpo.vendor = #{dto.vendor}
        </if>

        <if test="dto.accountName != null and dto.accountName != ''">
            and bcpo.account_name = #{dto.accountName}
        </if>
        <if test="dto.instanceId != null and dto.instanceId != ''">
            and bcpo.instance_id = #{dto.instanceId}
        </if>

        <if test="dto.scodeUnverified != null and dto.scodeUnverified != ''">
            and bcpo.scode_unverified like concat('%',#{dto.scodeUnverified},'%')
        </if>

        <if test="dto.scode != null and dto.scode != ''">
            <choose>
                <when test="dto.scode == 'abnormal' ">
                    and (bcpo.scode is null or  bcpo.scode = '')
                </when>
                <otherwise>
                    and bcpo.scode = #{dto.scode}
                </otherwise>
            </choose>
        </if>
        <if test="dto.productCode != null and dto.productCode != ''">
            and brbp.alias_code = #{dto.productCode}
        </if>
        <if test="dto.supplementId != null and dto.supplementId != ''">
            and bcpo.supplement_id = #{dto.supplementId}
        </if>
        order by bcpo.scode,bcpo.id
    </select>

    <update id="updateCmdbAggregatedBillBatch" parameterType="com.haier.devops.bill.common.dto.ReconciliationBatchDTO">
        update bc_cmdb_product_overview set scode = #{dto.scode},update_time = now() where aggregated_id in
        <foreach collection="dto.aggregateIdList" item="aggregatedId" separator="," open="(" close=")">
            #{aggregatedId}
        </foreach>
    </update>
    <update id="correctProjectInfoByScode">
        update bc_cmdb_product_overview set project_code = #{projectId}, project_name = #{projectName} where scode = #{scode}
    </update>

    <select id="getListByAggregateIdList" parameterType="com.haier.devops.bill.common.dto.ReconciliationBatchDTO" resultType="com.haier.devops.bill.common.entity.CmdbProductOverview">
        select id, aggregated_id, vendor, account_id, account_name, product_code, product_name, instance_id,supplement_id, scode, project_code, project_name, subscription_type, creation_time, releasing_time, create_time,
               update_time,'' as cost_unit,'' as reconciliation_status
        from bc_cmdb_product_overview where aggregated_id in
        <foreach collection="dto.aggregateIdList" item="aggregatedId" separator="," open="(" close=")">
            #{aggregatedId}
        </foreach>
        and scode != #{dto.scode}
    </select>

    <select id="listParentNotFoundItems" resultType="com.haier.devops.bill.common.entity.CmdbProductOverview">
        select * from bc_cmdb_product_overview where parent_found = -1
                                              <!-- 临时使用，后期无用可删 -->
                                              and product_code != 'polardb' and vendor = 'aliyun'
    </select>

    <select id="getParentInstanceByInstanceId" resultType="com.haier.devops.bill.common.entity.CmdbProductOverview">
        select * from bc_cmdb_product_overview where instance_id = #{instanceId} and supplement_id = '' and vendor = 'aliyun' limit 1
    </select>

    <delete id="deleteOrphanData">
        delete from bc_cmdb_product_overview where aggregated_id = #{aggregatedId} and vendor = 'aliyun'
    </delete>

</mapper>
