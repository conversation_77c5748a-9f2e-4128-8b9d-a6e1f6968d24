<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.haier.devops.bill.common.mapper.AlarmLogMapper">
   <select id="listByPage" resultType="com.haier.devops.bill.common.entity.CpAlarmLog" parameterType="com.haier.devops.bill.common.dto.AlarmLogDTO">
      select
         bcal.id,
         bcal.gathering_id ,
         bcal.product_id,
         bcal.app_scode,
         bcal.level_name,
         bcal.notice_obj_detail,
         bcal.alarm_content,
         bcal.create_time,
         bcal.level,
         bcal.billing_cycle
      from
         bc_cp_alarm_log bcal
            join bc_cp_alarm_configuration bcac
                 on
                          bcal.product_id = bcac.product_id
       and bcal.app_scode is null and bcac.app_scode is null
                       and bcac.id = #{dto.cpAlarmConfigurationId}
      <where>
          <if test="dto.level != null and dto.level != ''">
              and bcal.level = #{dto.level}
          </if>
          <if test="dto.startDate != null">
              and bcal.create_time &gt;= #{dto.startDate}
          </if>
          <if test="dto.endDate != null">
              and bcal.create_time &lt;= #{dto.endDate}
          </if>
      </where>
   </select>
</mapper>
