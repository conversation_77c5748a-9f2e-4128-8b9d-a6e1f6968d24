<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.ZabbixMonitorMapper">
    <select id="getMaxCpuAndMem" parameterType="java.util.List" resultType="java.util.Map">
        select ifnull(round(avg(SUBSTRING(tps_max,1,INSTR(tps_max,'%')-1)),2),0) tpsMax from mq_monitor_data zmd
        where instance_id in
        <foreach collection="instanceIdList" separator="," close=")" open="(" item="instanceId">
            #{instanceId}
        </foreach>
    </select>

</mapper>

