<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haier.devops.bill.common.mapper.RawBillSyncLogMapper">

    <select id="getPendingAggregatedBillSyncLog" resultType="com.haier.devops.bill.common.entity.RawBillSyncLog">
        select
               id,
               vendor,
               account_name,
               account_id,
               billing_cycle
        from bc_raw_bill_sync_log
        where vendor = #{vendor}
          and stage = 'refinement'
          and status = 'finished'
        union all
        select
               id,
               vendor,
               account_name,
               account_id,
               billing_cycle
        from bc_raw_bill_sync_log
        where vendor = #{vendor}
          and stage = 'aggregation'
          and status = 'error'
        order by billing_cycle asc
    </select>
</mapper>
