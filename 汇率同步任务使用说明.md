# 汇率同步任务使用说明

## 概述

本项目实现了一个基于 https://v6.exchangerate-api.com 的汇率同步任务，可以定时从外部API获取最新汇率信息并存储到数据库中。

## 功能特性

- 🔄 定时同步汇率数据（支持XXL-Job调度）
- 💾 汇率数据持久化存储
- 🔍 汇率查询API接口
- 🛡️ 数据去重和错误处理
- 📊 支持多种货币对

## 数据库表结构

### bc_exchange_rate 汇率表

```sql
CREATE TABLE IF NOT EXISTS `bc_exchange_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `base_currency` varchar(8) NOT NULL DEFAULT 'CNY' COMMENT '基础货币代码',
  `target_currency` varchar(8) NOT NULL COMMENT '目标货币代码',
  `exchange_rate` decimal(20,8) NOT NULL COMMENT '汇率值（1基础货币=多少目标货币）',
  `rate_date` date NOT NULL COMMENT '汇率日期',
  `api_update_time` datetime DEFAULT NULL COMMENT 'API最后更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-有效；0-无效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_currency_date` (`base_currency`, `target_currency`, `rate_date`) COMMENT '货币对和日期唯一索引',
  KEY `idx_target_currency_date` (`target_currency`, `rate_date`) COMMENT '目标货币和日期索引',
  KEY `idx_rate_date` (`rate_date`) COMMENT '汇率日期索引',
  KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汇率表，存储每日汇率信息';
```

## 配置说明

在 `application.properties` 中添加以下配置：

```properties
# Exchange Rate API Configuration
exchange.rate.api.url=https://v6.exchangerate-api.com/v6/************************/latest
exchange.rate.api.timeout=10000
```

## 定时任务配置

### XXL-Job 任务配置

1. **任务名称**: `exchangeRateSyncJob`
2. **执行器**: 选择对应的执行器
3. **Cron表达式**: `0 0 1 * * ?` (每日凌晨1点执行)
4. **JobHandler**: `exchangeRateSyncJob`

### 手动同步任务

- **任务名称**: `manualExchangeRateSync`
- **JobHandler**: `manualExchangeRateSync`
- **用途**: 测试或紧急情况下的手动同步

## API接口

### 1. 查询指定货币对的汇率

```http
GET /api/v1/hcms/bill/exchangeRate/query?baseCurrency=CNY&targetCurrency=USD&rateDate=2025-06-30
```

**参数说明:**
- `baseCurrency`: 基础货币代码（默认CNY）
- `targetCurrency`: 目标货币代码（必填）
- `rateDate`: 汇率日期（格式：yyyy-MM-dd）

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "baseCurrency": "CNY",
    "targetCurrency": "USD",
    "exchangeRate": 0.13950000,
    "rateDate": "2025-06-30",
    "apiUpdateTime": "2025-06-30 08:00:01",
    "createTime": "2025-06-30 09:00:00",
    "updateTime": "2025-06-30 09:00:00",
    "status": 1
  }
}
```

### 2. 查询指定日期的所有汇率

```http
GET /api/v1/hcms/bill/exchangeRate/listByDate?rateDate=2025-06-30
```

### 3. 手动触发汇率同步

```http
POST /api/v1/hcms/bill/exchangeRate/sync
```

## 核心类说明

### 1. ExchangeRate 实体类
- 汇率数据的实体映射
- 包含基础货币、目标货币、汇率值等字段

### 2. ExchangeRateApiResponse DTO
- 外部API响应数据的映射
- 包含汇率数据和API元信息

### 3. ExchangeRateService 服务接口
- `fetchLatestExchangeRates()`: 从API获取最新汇率
- `syncExchangeRatesToDatabase()`: 同步数据到数据库
- `performFullSync()`: 执行完整同步流程

### 4. ExchangeRateSyncXxlJob 定时任务
- `exchangeRateSyncJob()`: 定时同步任务
- `manualExchangeRateSync()`: 手动同步任务

## 使用流程

1. **数据库初始化**: 执行建表SQL创建汇率表
2. **配置定时任务**: 在XXL-Job管理后台配置定时任务
3. **启动应用**: 确保应用正常启动并连接到XXL-Job
4. **验证同步**: 查看日志确认汇率数据同步成功
5. **API调用**: 通过REST API查询汇率数据

## 注意事项

1. **API限制**: 免费版API有调用次数限制，请合理设置同步频率
2. **数据去重**: 系统会自动清理当天的旧数据，避免重复
3. **错误处理**: 同步失败时会记录详细日志，便于排查问题
4. **时区处理**: 所有时间均使用GMT+8时区

## 监控和维护

- 通过应用日志监控同步状态
- 定期检查汇率数据的完整性
- 关注API服务的可用性
- 根据业务需要调整同步频率

## 扩展说明

如需支持其他汇率API或添加更多功能，可以：
1. 扩展 `ExchangeRateService` 接口
2. 实现新的API适配器
3. 添加更多的查询接口
4. 集成告警机制
